package com.mc.tool.caesar.vpm.pages.extenderosdupdate;

import com.mc.tool.caesar.api.datamodel.ExtenderData;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.ReadOnlyStringWrapper;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;

/**
 * .
 */
public class OsdUpdateItem {

  private ExtenderData extenderData;
  private StringProperty currentMd5 = new SimpleStringProperty();
  private StringProperty port = new SimpleStringProperty();
  private BooleanProperty selected = new SimpleBooleanProperty();
  private DoubleProperty progress = new SimpleDoubleProperty();
  private ObjectProperty<Double> progressBackup = new SimpleObjectProperty<>();

  /** . */
  public OsdUpdateItem() {
    progress.addListener((obs, oldVal, newVal) -> progressBackup.set(newVal.doubleValue()));
  }

  /**
   * 设置外设信息.
   *
   * @param extenderData 外设信息
   */
  public void setExtenderData(ExtenderData extenderData) {
    this.extenderData = extenderData;
    if (extenderData != null) {
      port.set(String.format("%d/%d", extenderData.getPort(), extenderData.getRdPort()));
    }
  }

  /**
   * 设置当前的MD5信息.
   *
   * @param md5 MD5信息
   */
  public void setCurrentMd5(String md5) {
    this.currentMd5.set(md5);
  }

  /**
   * 获取外设信息.
   *
   * @return 外设信息
   */
  public ExtenderData getExtenderData() {
    return extenderData;
  }

  /**
   * 获取外设名称属性.
   *
   * @return 外设名称属性
   */
  public StringProperty getName() {
    if (extenderData != null) {
      return extenderData.getNameProperty();
    } else {
      return new ReadOnlyStringWrapper("");
    }
  }

  /**
   * 获取对应的CPU/CON名称属性.
   *
   * @return CPU/CON名称属性
   */
  public StringProperty getDevice() {
    if (extenderData != null) {
      if (extenderData.isCpuType() && extenderData.getCpuData() != null) {
        return extenderData.getCpuData().getNameProperty();
      } else if (extenderData.isConType() && extenderData.getConsoleData() != null) {
        return extenderData.getConsoleData().getNameProperty();
      } else {
        return new ReadOnlyStringWrapper();
      }
    } else {
      return new ReadOnlyStringWrapper();
    }
  }

  /**
   * 获取端口属性.
   *
   * @return 端口信息属性
   */
  public StringProperty getPort() {
    return port;
  }

  /**
   * 获取序列号属性.
   *
   * @return 序列号属性
   */
  public StringProperty getSerial() {
    if (extenderData != null) {
      return extenderData.getSerialProperty();
    } else {
      return new ReadOnlyStringWrapper();
    }
  }

  public StringProperty getCurrentMd5() {
    return currentMd5;
  }

  public BooleanProperty getSelected() {
    return selected;
  }

  public DoubleProperty getProgress() {
    return progress;
  }

  public ObjectProperty<Double> getProgressBackup() {
    return progressBackup;
  }
}
