package com.mc.tool.caesar.vpm.pages.operation.irregularcrossscreen.view;

import com.mc.tool.caesar.vpm.pages.operation.irregularcrossscreen.controller.IrregularCrossScreenControllable;
import com.mc.tool.framework.operation.interfaces.OperationView;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.utility.InjectorProvider;
import java.io.IOException;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.layout.VBox;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class IrregularCrossScreenOperationView extends VBox implements OperationView {

  private final IrregularCrossScreenControllable controllable;

  /** Constructor. */
  public IrregularCrossScreenOperationView(VisualEditModel model, VisualEditFunc func) {
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    FXMLLoader loader =
        new FXMLLoader(
            classLoader.getResource(
                "com/mc/tool/framework/operation/crossscreen/irregular/irregularcrossscreen.fxml"));
    controllable =
        InjectorProvider.getInjector().getInstance(IrregularCrossScreenControllable.class);
    controllable.init(model, func);
    loader.setRoot(this);
    loader.setController(controllable);
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load crossscreen.fxml!", exception);
    }
  }

  @Override
  public Node getView() {
    return this;
  }

  @Override
  public IrregularCrossScreenControllable getOperationControllable() {
    return controllable;
  }
}
