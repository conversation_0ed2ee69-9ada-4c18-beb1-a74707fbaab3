package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 全光拼接屏组详细信息.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class VideoWallGroup {
  /**
   * 全光拼接屏组ID.
   **/
  private Integer id;
  private String name;
  /**
   * 诺瓦主机IP.
   */
  private String ip;
  /**
   * 诺瓦主机端口，默认为8000.
   **/
  private Integer port;
  /**
   * 诺瓦接入方ID.
   **/
  @JsonProperty("pId")
  private String pid;
  /**
   * 诺瓦接入方密钥.
   **/
  private String secreteKey;
  /**
   * 诺瓦主机类型，E或者ALPHA.
   */
  private String type;
  private List<VideoWallGroupVideowalls> videowalls = null;
}
