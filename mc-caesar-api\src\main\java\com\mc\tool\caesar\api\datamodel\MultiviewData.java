package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.Version5x;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 多画面数据结构.
 */
@Slf4j
public class MultiviewData extends AbstractData implements CaesarCommunicatable {
  public static final String PROPERTY_BASE = "MultiviewData.";
  public static final String PROPERTY_US_CON_INDEX = PROPERTY_BASE + "UsConIndex";
  public static final String PROPERTY_SOURCE_COUNT = PROPERTY_BASE + "SourceCount";
  public static final String PROPERTY_CONTROL_IDX = PROPERTY_BASE + "ControlIdx";
  public static final String PROPERTY_LAYOUT_TYPE = PROPERTY_BASE + "LayoutType";
  public static final String PROPERTY_USB_TRANS_IDX = PROPERTY_BASE + "UsbTransIdx";
  public static final String PROPERTY_OUTPUT_MODE = PROPERTY_BASE + "OutputMode";
  public static final String PROPERTY_INPUT_PORT_IDX = PROPERTY_BASE + "InputPortIdx";
  public static final int RESERVED_COUNT = 5;

  @Expose
  @Getter
  private int usConIndex; // 对应的四画面/VP7的CON的索引+1，为0时无效
  @Expose
  private MultiviewSource[] sources = new MultiviewSource[Version5x.MAX_MULTIVIEW_SOURCE];
  @Expose
  @Getter
  private int sourceCount; // 信号源个数
  @Expose
  @Getter
  private int controlIdx; // 用于控制的信号源在sources的索引+1，为0时无效
  @Expose
  @Getter
  private MultiviewLayoutType layoutType = MultiviewLayoutType.FULL_SCREEN; // 布局类型
  @Getter
  @Expose
  private ObjectProperty<MultiviewLayoutType> layoutTypeProperty =
      new SimpleObjectProperty<>(MultiviewLayoutType.FULL_SCREEN);
  @Expose
  @Getter
  private int usbTransIdx; // USB透传连接通道索引，为0时无效，四画面RX使用
  /**
   * 输出模式，VP7下，0表示4接口模式，1表示2接口模式，2表示1接口模式.
   */
  @Expose
  @Getter
  private MultiviewOutputMode outputMode = MultiviewOutputMode.FOUR_INTERFACE;
  @Expose
  @Getter
  private ObjectProperty<MultiviewOutputMode> outputModeProperty =
      new SimpleObjectProperty<>(MultiviewOutputMode.FOUR_INTERFACE);
  @Expose
  @Getter
  private int inputPortIdx; // 输入端口索引，预览终端使用

  /**
   * .
   */
  public MultiviewData(CustomPropertyChangeSupport pcs,
                       ConfigDataManager configDataManager, int oid,
                       String fqn) {
    super(pcs, configDataManager, oid, fqn);
    for (int i = 0; i < sources.length; i++) {
      sources[i] = new MultiviewSource(pcs, configDataManager, i,
          getFqn() + ".MultiviewData[" + oid + "].Sources[" + i + "]");
    }
  }

  /**
   * 获取源信息.
   */
  public MultiviewSource getSource(int index) {
    if (index >= 0 && index < sources.length) {
      return sources[index];
    } else {
      return null;
    }
  }

  /**
   * 设置属性.
   *
   * @param property 属性名称.
   * @param value    属性值
   */
  public void setProperty(String property, Object value) {
    if (property.equals(PROPERTY_LAYOUT_TYPE) && value instanceof MultiviewLayoutType) {
      setLayoutType((MultiviewLayoutType) value);
    } else  if (property.equals(PROPERTY_OUTPUT_MODE) && value instanceof MultiviewOutputMode) {
      setOutputMode((MultiviewOutputMode) value);
    }
  }

  @Override
  public void readData(CfgReader cfgReader) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    setUsConIndex(cfgReader.read2ByteValue());
    for (MultiviewSource source : sources) {
      source.readData(cfgReader);
    }
    setSourceCount(cfgReader.readByteValue());
    setControlIdx(cfgReader.readByteValue());
    int whichLayoutType = cfgReader.readByteValue();
    if (whichLayoutType >= 0 && whichLayoutType < 4) {
      setLayoutType(MultiviewLayoutType.getByIndex(whichLayoutType));
    } else {
      log.warn("Error MultiviewData LayoutType");
    }
    setUsbTransIdx(cfgReader.readByteValue());
    int whichOutputMode = cfgReader.readByteValue();
    if (whichOutputMode >= 0 && whichOutputMode < 3) {
      setOutputMode(MultiviewOutputMode.getByIndex(whichOutputMode));
    } else {
      log.warn("Error MultiviewData OutputMode");
    }
    setInputPortIdx(cfgReader.readByteValue());
    cfgReader.readByteArray(RESERVED_COUNT);
  }

  @Override
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    cfgWriter.write2ByteSmallEndian(usConIndex);
    for (MultiviewSource source : sources) {
      source.writeData(cfgWriter);
    }
    cfgWriter.writeByte((byte) sourceCount);
    cfgWriter.writeByte((byte) controlIdx);
    cfgWriter.writeByte((byte) layoutType.getIndex());
    cfgWriter.writeByte((byte) usbTransIdx);
    cfgWriter.writeByte((byte) outputMode.getIndex());
    cfgWriter.writeByte((byte) inputPortIdx);
    cfgWriter.writeByteArray(new byte[RESERVED_COUNT]);
  }

  /**
   * .
   */
  public void setUsConIndex(int usConIndex) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.usConIndex;
    if (oldValue != usConIndex) {
      this.usConIndex = usConIndex;
      //PlatformUtility.runInFxThread(() -> usConIndexProperty.set(usConIndex));
      firePropertyChange(PROPERTY_US_CON_INDEX, oldValue, usConIndex);
    }
  }

  /**
   * .
   */
  public void setSourceCount(int sourceCount) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (this.sourceCount != sourceCount) {
      int oldValue = this.sourceCount;
      this.sourceCount = sourceCount;
      //PlatformUtility.runInFxThread(() -> sourceCountProperty.set(sourceCount));
      firePropertyChange(PROPERTY_SOURCE_COUNT, oldValue, sourceCount);
    }
  }

  /**
   * .
   */
  public void setControlIdx(int controlIdx) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (this.controlIdx != controlIdx) {
      int oldValue = this.controlIdx;
      this.controlIdx = controlIdx;
      //PlatformUtility.runInFxThread(() -> controlIdxProperty.set(controlIdx));
      firePropertyChange(PROPERTY_CONTROL_IDX, oldValue, controlIdx);
    }
  }

  /**
   * .
   */
  public void setLayoutType(MultiviewLayoutType layoutType) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    MultiviewLayoutType oldValue = this.layoutType;
    if (oldValue != layoutType) {
      this.layoutType = layoutType;
      PlatformUtility.runInFxThread(() -> layoutTypeProperty.set(layoutType));
      firePropertyChange(PROPERTY_LAYOUT_TYPE, oldValue, layoutType);
    }
  }

  /**
   * .
   */
  public void setUsbTransIdx(int usbTransIdx) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (this.usbTransIdx != usbTransIdx) {
      int oldValue = this.usbTransIdx;
      this.usbTransIdx = usbTransIdx;
      firePropertyChange(PROPERTY_USB_TRANS_IDX, oldValue, usbTransIdx);
    }
  }

  /**
   * .
   */
  public void setOutputMode(MultiviewOutputMode outputMode) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    MultiviewOutputMode oldValue = this.outputMode;
    if (this.outputMode != outputMode) {
      this.outputMode = outputMode;
      PlatformUtility.runInFxThread(() -> outputModeProperty.set(outputMode));
      firePropertyChange(PROPERTY_OUTPUT_MODE, oldValue, outputMode);
    }
  }

  /**
   * .
   */
  public void setInputPortIdx(int inputPortIdx) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (this.inputPortIdx != inputPortIdx) {
      int oldValue = this.inputPortIdx;
      this.inputPortIdx = inputPortIdx;
      firePropertyChange(PROPERTY_INPUT_PORT_IDX, oldValue, inputPortIdx);
    }
  }

  public ConsoleData getConsoleData() {
    return getConfigDataManager().getConsoleData(usConIndex - 1);
  }
}
