package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 全光拼接屏屏幕信息.
 */
@Setter
@Builder
@JsonPropertyOrder({
    VideoWallScreen.JSON_PROPERTY_X,
    VideoWallScreen.JSON_PROPERTY_Y,
    VideoWallScreen.JSON_PROPERTY_WIDTH,
    VideoWallScreen.JSON_PROPERTY_HEIGHT
})
@NoArgsConstructor
@AllArgsConstructor
public class VideoWallScreen {
  public static final String JSON_PROPERTY_X = "x";
  private Integer xpos;

  public static final String JSON_PROPERTY_Y = "y";
  private Integer ypos;

  public static final String JSON_PROPERTY_WIDTH = "width";
  private Integer width;

  public static final String JSON_PROPERTY_HEIGHT = "height";
  private Integer height;

  /**
   * 横坐标.
   *
   * @return x
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_X)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getXpos() {
    return xpos;
  }

  /**
   * 纵坐标.
   *
   * @return y
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_Y)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getYpos() {
    return ypos;
  }

  /**
   * 宽度.
   *
   * @return width
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_WIDTH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getWidth() {
    return width;
  }


  /**
   * 高度.
   *
   * @return height
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_HEIGHT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getHeight() {
    return height;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    VideoWallScreen videoWallScreen = (VideoWallScreen) o;
    return Objects.equals(this.xpos, videoWallScreen.xpos)
        && Objects.equals(this.ypos, videoWallScreen.ypos)
        && Objects.equals(this.width, videoWallScreen.width)
        && Objects.equals(this.height, videoWallScreen.height);
  }

  @Override
  public int hashCode() {
    return Objects.hash(xpos, ypos, width, height);
  }


  @Override
  public String toString() {
    return "VideoWallScreen {\n"
        + "    x: " + toIndentedString(xpos) + "\n"
        + "    y: " + toIndentedString(ypos) + "\n"
        + "    width: " + toIndentedString(width) + "\n"
        + "    height: " + toIndentedString(height) + "\n" + "}";
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
