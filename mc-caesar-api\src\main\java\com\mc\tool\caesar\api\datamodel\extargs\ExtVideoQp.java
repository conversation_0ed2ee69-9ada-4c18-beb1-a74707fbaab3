package com.mc.tool.caesar.api.datamodel.extargs;

/**
 * .
 */
public enum ExtVideoQp implements ExtArgObject {
  QP_1(1), QP_2(2), QP_3(3), QP_4(4),
  QP_5(5), QP_6(6), QP_7(7), QP_8(8);

  private final int value;

  ExtVideoQp(int value) {
    this.value = value;
  }

  @Override
  public byte[] toBytes() {
    return new byte[]{(byte) getValue()};
  }

  public int getValue() {
    return value;
  }
}
