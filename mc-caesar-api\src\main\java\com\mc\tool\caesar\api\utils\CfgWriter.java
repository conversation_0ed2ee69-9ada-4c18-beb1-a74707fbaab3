package com.mc.tool.caesar.api.utils;

import com.mc.tool.caesar.api.exception.ConfigException;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import lombok.Getter;

/**
 * .
 */
public final class CfgWriter {

  private OutputStream writer;
  @Getter
  private long size = 0;

  public CfgWriter(OutputStream os) {
    this.writer = os;
  }

  public void writeString(String data, int count) throws ConfigException {
    writeString(data, count, 4);
  }

  /**
   * writeString.
   */
  public void writeString(String data, int count, int junkData) throws ConfigException {
    byte[] byteArray = new byte[0];
    if (data != null) {
      byteArray = data.getBytes(StandardCharsets.UTF_8);
    }

    byte[] writeData = new byte[count + junkData];
    System.arraycopy(byteArray, 0, writeData, 0, Math.min(count, byteArray.length));
    writeByteArray(writeData);
  }

  public void writeBoolean(boolean bool) throws ConfigException {
    writeInteger(bool ? 1 : 0);
  }

  public void writeByte(byte data) throws ConfigException {
    writeImpl(data & 0xFF);
  }

  public void write2Byte(int data) throws ConfigException {
    writeImpl((data & 0xFF00) >> 8);
    writeImpl(data & 0xFF);
  }

  public void write2ByteSmallEndian(int data) throws ConfigException {
    writeImpl(data & 0xFF);
    writeImpl((data & 0xFF00) >> 8);
  }

  public void write4Byte(byte data) throws ConfigException {
    writeInteger(0xFF & data);
  }

  /**
   * writeByteArray.
   */
  public void writeByteArray(byte[] data) throws ConfigException {
    if (data.length == 0) {
      return;
    }
    try {
      this.writer.write(data);
      size += data.length;
    } catch (IOException ioe) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, ioe);
    }
  }

  /**
   * writeInteger.
   */
  public void writeInteger(int data) throws ConfigException {
    writeImpl(data & 0xFF);
    writeImpl(data >> 8 & 0xFF);
    writeImpl(data >> 16 & 0xFF);
    writeImpl(data >> 24 & 0xFF);
  }

  /**
   * write long.
   */
  public void writeLong(long data) throws ConfigException {
    writeImpl((int) (data & 0xFF));
    writeImpl((int) (data >> 8 & 0xFF));
    writeImpl((int) (data >> 16 & 0xFF));
    writeImpl((int) (data >> 24 & 0xFF));
    writeImpl((int) (data >> 32 & 0xFF));
    writeImpl((int) (data >> 40 & 0xFF));
    writeImpl((int) (data >> 48 & 0xFF));
    writeImpl((int) (data >> 56 & 0xFF));
  }

  public void writeShort(short data) throws ConfigException {
    writeImpl(data & 0xff);
    writeImpl((data >> 8) & 0xff);
  }

  private void writeImpl(int data) throws ConfigException {
    try {
      this.writer.write(data);
      size += 1;
    } catch (IOException ioe) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, ioe);
    }
  }

  /**
   * encrypt.
   */
  public static byte[] encrypt(ByteArrayOutputStream os) throws ConfigException {
    ByteArrayOutputStream temp = new ByteArrayOutputStream(10240);
    try {
      byte[] raw = {43, 12, -53, 37, -73, -26, 77, -24, -26, -57, 22, 112, 32, 0, -106, 53};
      SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
      Cipher cipher = Cipher.getInstance("AES");
      cipher.init(1, skeySpec);

      temp.write(255);
      temp.write(255);

      temp.write(cipher.doFinal(os.toByteArray()));

      return temp.toByteArray();
    } catch (IllegalBlockSizeException | IOException | NoSuchPaddingException
        | NoSuchAlgorithmException | BadPaddingException | InvalidKeyException ex) {
      throw new ConfigException(CfgError.IO);
    }
  }
}

