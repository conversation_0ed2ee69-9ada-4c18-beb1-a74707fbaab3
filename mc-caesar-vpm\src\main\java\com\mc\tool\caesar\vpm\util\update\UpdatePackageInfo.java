package com.mc.tool.caesar.vpm.util.update;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarConstants.Module.Type;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.VersionDef;
import com.mc.tool.caesar.vpm.util.update.UpdateConstant.IoInterfaceType;
import com.mc.tool.caesar.vpm.util.update.UpdateConstant.ProductType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public class UpdatePackageInfo {
  static class TargetTypeList extends ArrayList<String> {
    private static final long serialVersionUID = 362498820763181261L;

    public TargetTypeList(String[] items) {
      super(Arrays.asList(items));
    }

    @Override
    public boolean contains(Object o) {
      if (o instanceof String) {
        return stream().anyMatch((item) -> item.equalsIgnoreCase((String) o));
      } else {
        return false;
      }
    }
  }

  private List<CaesarConstants.Module.Type> matrixTypes = new ArrayList<>();

  @Getter
  @Setter
  private ProductType productType = null;

  @Getter
  @Setter
  private IoInterfaceType ioInterfaceType = null;

  @Getter
  @Setter
  private Integer ports = null;

  @Getter
  @Setter
  private VersionDef sys = null;

  @Getter
  @Setter
  private VersionDef app = null;

  @Getter
  @Setter
  private VersionDef fpga = null;
  @Getter
  @Setter
  private VersionDef fpgaHdmiTx = null;
  @Getter
  @Setter
  private VersionDef fpgaHdmiRx = null;
  @Getter
  @Setter
  private VersionDef fpgaHdmi4Rx = null;
  @Getter
  @Setter
  private VersionDef fpgaHdmi4RxHalf1G = null;
  @Getter
  @Setter
  private VersionDef fpgaHdmi4RxHalf25G = null;
  @Getter
  @Setter
  private VersionDef fpgaCaesarPreview = null;

  @Getter
  @Setter
  private VersionDef io = null;

  @Setter
  private VersionDef trunk = null;

  @Getter
  @Setter
  private VersionDef branch = null;

  @Getter
  @Setter
  private VersionDef hardWare = null;

  @Getter
  @Setter
  private String logo = null;

  private Map<Type, VersionDef> trunks = new HashMap<>();

  private static TargetTypeList asList(String... item) {
    return new TargetTypeList(item);
  }

  /**
   * 获取升级的主类型.
   *
   * @return 升级的主类型
   */
  public List<String> getTargetType() {
    if (isIoUpdate()) {
      return asList(UpdateConstant.UPDATE_IO_TYPE);
    } else if (isCpuUpdate()) {
      return asList(UpdateConstant.UPDATE_CPU_TYPE);
    } else if (productType == ProductType.CON_CPU) {
      return asList(UpdateConstant.EXTENDER_CPU_TYPE, UpdateConstant.EXTENDER_CON_TYPE);
    } else if (productType == ProductType.CPU) {
      return asList(UpdateConstant.EXTENDER_CPU_TYPE);
    } else if (productType == ProductType.CON) {
      return asList(UpdateConstant.EXTENDER_CON_TYPE);
    } else if (productType == ProductType.VP6) {
      return asList(UpdateConstant.EXTENDER_VPCON_TYPE);
    } else if (productType == ProductType.VP7) {
      return asList(UpdateConstant.EXTENDER_VP7_TYPE);
    } else {
      return Collections.EMPTY_LIST;
    }
  }

  public boolean isIoUpdate() {
    return ioInterfaceType != null && productType == ProductType.MAT;
  }

  public boolean isCpuUpdate() {
    return ioInterfaceType == null && productType == ProductType.MAT;
  }

  /**
   * .
   */
  public boolean isExtenderUpdate() {
    return productType == ProductType.CPU
        || productType == ProductType.CON
        || productType == ProductType.CON_CPU
        || productType == ProductType.VP6
        || productType == ProductType.VP7;
  }

  public void setMatrixType(Collection<Type> matrixType) {
    this.matrixTypes.clear();
    this.matrixTypes.addAll(matrixType);
  }

  public boolean isSupportatrixType(Type matrixType) {
    return matrixTypes.contains(matrixType);
  }

  /**
   * 检查升级包的版本是否正确.
   *
   * @return 如果正确返回true.
   */
  public boolean isVersionValid() {
    if (isCpuUpdate() || isExtenderUpdate()) {
      return sys != null || fpga != null || app != null;
    } else if (isIoUpdate()) {
      return io != null || branch != null || trunk != null;
    } else {
      return false;
    }
  }

  /**
   * 获取正确的可升级的fpga版本.
   *
   * @return fpga版本.
   */
  public VersionDef getRightFpga() {
    if (isIoUpdate() && isSupportatrixType(CaesarConstants.Module.Type.SINGLE_CPU)) {
      return io;
    } else if (isIoUpdate() && !isSupportatrixType(CaesarConstants.Module.Type.SINGLE_CPU)) {
      return branch;
    } else if (isCpuUpdate()) {
      return fpga;
    }
    return null;
  }

  /**
   * 检查主机类型是否适配.
   *
   * @param cpuType 主机类型
   * @return 如果适配返回true
   */
  public boolean checkMatType(int cpuType) {
    for (Type type : matrixTypes) {
      if (cpuType == type.getValue()) {
        return true;
      }
    }
    return false;
  }

  /**
   * 检查是否适合CPU升级.
   *
   * @param cpu cpu数据
   * @return 如果适合，返回true
   */
  public boolean isSuitableForMat(ModuleData cpu) {
    if (!isCpuUpdate()) {
      return false;
    }
    if (!checkMatType(cpu.getType())) {
      return false;
    }
    // 匹配主机类型
    return isCpuHardwareMatch(cpu);
  }

  private boolean isCpuHardwareMatch(ModuleData cpu) {
    if (cpu.getType() == CaesarConstants.Module.Type.SINGLE_CPU.getValue()
        && cpu.getPorts() == 36) {
      return getHardWare().getMasterVersion() == 1 && getHardWare().getSubVersion() == 2;
    } else if (cpu.getType() == CaesarConstants.Module.Type.SINGLE_CPU.getValue()
        && cpu.getPorts() == 18) {
      return getHardWare().getMasterVersion() == 1 && getHardWare().getSubVersion() == 3;
    } else if (cpu.getType() == CaesarConstants.Module.Type.SINGLE_CPU.getValue()
        && cpu.getPorts() == 96) {
      return getHardWare().getMasterVersion() == 2;
    } else {
      return cpu.getType() != Type.SINGLE_CPU.getValue();
    }
  }

  /**
   * 检查是否升级包是否适合指定的io.
   *
   * @param cpu 主控的module
   * @param io  要升级的io的module.
   * @return 如果适合，返回true.
   */
  public boolean isSuitableForIo(ModuleData cpu, ModuleData io) {
    if (!isIoUpdate()) {
      return false;
    }

    if (!checkMatType(cpu.getType())) {
      return false;
    }

    CaesarConstants.Module.Type ioType = CaesarConstants.Module.Type.valueOf(io.getType());
    if (ioType == null) {
      return false;
    }
    // 如果是主干升级，返回true
    if (ioType.isMatrix() && ioType != CaesarConstants.Module.Type.SINGLE_CPU) {
      return true;
    }

    // 匹配接口类型
    if (!(io.getType() == CaesarConstants.Module.Type.IO_CAT.getValue()
        && ioInterfaceType == IoInterfaceType.CAT
        || io.getType() == CaesarConstants.Module.Type.IO_SPF.getValue()
        && ioInterfaceType == IoInterfaceType.SFP)) {
      return false;
    }
    // 匹配主机类型
    if (isSupportatrixType(CaesarConstants.Module.Type.SINGLE_CPU)) {
      return cpu.getPorts() == getPorts();
    } else {
      return true;
    }
  }

  /**
   * 获取可升级的子类型.
   *
   * @return 可升级的子类型的集合.
   */
  public Collection<String> getSubUpdateType() {
    List<String> types = new ArrayList<>();
    if (app != null) {
      types.add(UpdateConstant.UPDATE_APP_TYPE);
    }
    if (sys != null) {
      types.add(UpdateConstant.UPDATE_SYS_TYPE);
    }
    if (getRightFpga() != null) {
      types.add(UpdateConstant.UPDATE_FPGA_TYPE);
    }
    return types;
  }

  /**
   * 获取Trunk版本.
   *
   * @param matrixType 矩阵类型
   * @return 版本信息
   */
  public VersionDef getTrunk(Type matrixType) {
    if (trunks.containsKey(matrixType)) {
      return trunks.get(matrixType);
    } else {
      return trunk;
    }
  }

  /**
   * 获取trunk升级文件路径.
   *
   * @param matrixType 矩阵类型
   * @return 路径
   */
  public String getTrunkFilePath(Type matrixType) {
    if (trunks.containsKey(matrixType)) {
      if (matrixType == Type.PLUGIN_384) {
        return UpdateConstant.UPDATE_FILE_TRUNK_384;
      } else if (matrixType == Type.PLUGIN_816) {
        return UpdateConstant.UPDATE_FILE_TRUNK_816;
      } else if (matrixType == Type.PLUGIN_144) {
        return UpdateConstant.UPDATE_FILE_TRUNK_144;
      } else {
        return UpdateConstant.UPDATE_FILE_TRUNK;
      }
    } else {
      return UpdateConstant.UPDATE_FILE_TRUNK;
    }
  }

  public String getBranchFilePath(Type matrixType) {
    return UpdateConstant.UPDATE_FILE_BRANCH;
  }

  public void setTrunk(Type matrixType, VersionDef version) {
    trunks.put(matrixType, version);
  }

  public boolean hasFpga() {
    return fpga != null || fpgaHdmiTx != null || fpgaHdmiRx != null || fpgaHdmi4Rx != null
        || fpgaHdmi4RxHalf1G != null || fpgaHdmi4RxHalf25G != null || fpgaCaesarPreview != null;
  }
}
