package com.mc.tool.caesar.vpm.device;

import com.mc.tool.caesar.api.DemoSwitchDataModel;

/**
 * .
 */
public class CaesarDemoDataModelFactoryImpl implements CaesarDemoDataModelFactory {

  @Override
  public DemoSwitchDataModel create() {
    return new DemoSwitchDataModel();
  }

  @Override
  public DemoSwitchDataModel create(boolean onlyConfig) {
    return new DemoSwitchDataModel(onlyConfig);
  }
}
