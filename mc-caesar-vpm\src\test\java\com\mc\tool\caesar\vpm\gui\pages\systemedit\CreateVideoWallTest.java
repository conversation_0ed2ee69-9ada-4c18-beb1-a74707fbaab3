package com.mc.tool.caesar.vpm.gui.pages.systemedit;

import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.sun.javafx.scene.control.skin.ContextMenuContent.MenuItemContainer;
import java.io.File;
import java.io.IOException;
import java.util.Optional;
import javafx.scene.Node;
import javafx.scene.control.CheckMenuItem;
import org.junit.Assert;
import org.junit.Test;

/**
 * .
 */
public class CreateVideoWallTest extends AppGuiTestBase {

  @Override
  public void beforeAll() {}

  @Test
  public void testSubMatrixCreate() {
    // 只能创建本矩阵的视频墙
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }
      // 本机可以
      rightClickOn(GuiTestConstants.VP6_DEMO_NAME);
      Optional<Node> menu = lookup(GuiTestConstants.MENU_CREATE_VIDEO_WALL).tryQuery();
      Assert.assertTrue(menu.isPresent());
      clickOn(GuiTestConstants.MENU_CREATE_VIDEO_WALL);
      //
      // 第二页非本机
      clickOn(GuiTestConstants.SWITCH_GRAPH);
      // 点击未选中的菜单项
      Node anotherPageMenu =
          lookup(
                  (node) -> {
                    if (!(node instanceof MenuItemContainer)) {
                      return false;
                    }
                    MenuItemContainer container = (MenuItemContainer) node;
                    if (!(container.getItem() instanceof CheckMenuItem)) {
                      return false;
                    }
                    CheckMenuItem item = (CheckMenuItem) container.getItem();
                    return !item.isSelected()
                        && !item.getText().equals(GuiTestConstants.GRID_TOPOLOGICAL_GRAPH);
                  })
              .query();
      clickOn(anotherPageMenu);
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }
      // 应该没有创建视频墙选项
      rightClickOn(GuiTestConstants.VP6_DEMO_SUB_NAME2);
      menu = lookup(GuiTestConstants.MENU_CREATE_VIDEO_WALL).tryQuery();
      Assert.assertFalse(menu.isPresent());

      File tempFile = loadResourceStatus("com/mc/tool/caesar/vpm/36_hw1.2_grid_full.status");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }
}
