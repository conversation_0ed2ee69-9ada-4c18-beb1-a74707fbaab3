package com.mc.tool.caesar.vpm.util.macrokey.datamodel;

import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.CaesarConstants.FunctionKey.Cmd.Command;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.AccessControlObject;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.vpm.util.TerminalUtility;
import com.mc.tool.framework.utility.TypeWrapper;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import javafx.beans.binding.ListBinding;
import javafx.beans.property.ObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;

/**
 * .
 */
public class Param2List extends ListBinding<MacroParam> {
  private final CaesarSwitchDataModel dataModel;
  private final ObjectProperty<DataObject> object;
  private ObjectProperty<MacroKeyItem> itemProperty;
  private WeakAdapter weakAdapter = new WeakAdapter();

  public Param2List(ObjectProperty<DataObject> object, CaesarSwitchDataModel dataModel) {
    this.dataModel = dataModel;
    this.object = object;
  }

  /**
   * 设置当前macrokeyitem的属性.
   *
   * @param itemProperty 属性
   */
  public void setItemProperty(ObjectProperty<MacroKeyItem> itemProperty) {
    this.itemProperty = itemProperty;
    if (itemProperty.get() != null) {
      bind(itemProperty.get().getCmd(), itemProperty.get().getParam1());
    }
    itemProperty.addListener(
        weakAdapter.wrap(
            (observable, oldValue, newValue) -> {
              if (oldValue != null) {
                unbind(oldValue.getCmd(), oldValue.getParam1());
                invalidate();
              }
              if (newValue != null) {
                bind(newValue.getCmd(), newValue.getParam1());
                invalidate();
              }
            }));
  }

  protected MacroKeyItem getItem() {
    if (itemProperty != null) {
      return itemProperty.get();
    } else {
      return null;
    }
  }

  @Override
  protected ObservableList<MacroParam> computeValue() {
    MacroKeyItem item = getItem();
    return getList(object, item, dataModel);
  }

  /**
   * 获取可选列表.
   *
   * @param object 权限控制对象
   * @param item 宏数据.
   * @param dataModel 数据管理器
   * @return 数据列表
   */
  public static ObservableList<MacroParam> getList(
      ObjectProperty<DataObject> object, MacroKeyItem item, CaesarSwitchDataModel dataModel) {
    if (item == null) {
      return FXCollections.emptyObservableList();
    }
    Command command = item.getCmd().get();
    if (command == null) {
      return FXCollections.emptyObservableList();
    }

    switch (command) {
      case SWITCH_MULTIVIEW_SIGNAL:
        if (object.get() instanceof ConsoleData
            && !((ConsoleData) object.get()).isMultiview()) {
          break;
        }
        return getCpuDataList(object, dataModel, command);
      case Connect:
      case ConnectVideo:
        ConsoleData param1Data = null;
        if (item.getParam1().getValue() instanceof MacroDataObjectParam) {
          MacroDataObjectParam param1 = (MacroDataObjectParam) item.getParam1().getValue();
          if (param1.getDataObject() instanceof ConsoleData) {
            param1Data = (ConsoleData) param1.getDataObject();
          }
        }
        return getCpuDataListForConnect(object, dataModel, command, param1Data);
      case PushVideoWallScenarioWindow:
        return getCpuDataList(object, dataModel, command);
      default:
        break;
    }
    return FXCollections.emptyObservableList();
  }

  private static ObservableList<MacroParam> getCpuDataList(ObjectProperty<DataObject> object,
                                                           CaesarSwitchDataModel dataModel,
                                                           Command command) {
    ObservableList<MacroParam> result = FXCollections.observableArrayList();
    List<CpuData> cpus = new ArrayList<>(dataModel.getConfigDataManager().getActiveCpus());
    cpus.sort(Comparator.comparingInt(CpuData::getId));
    for (CpuData cpuData : cpus) {
      if (object.get() instanceof AccessControlObject) {
        AccessControlObject accessControlObject = (AccessControlObject) object.get();
        if (accessControlObject.isNoAccess(cpuData)) {
          continue;
        }
        if (accessControlObject.isVideoAccess(cpuData) && command == Command.Connect) {
          continue;
        }
      }
      ExtenderData extenderData = cpuData.getExtenderData(0);
      MacroDataObjectParam cpuDataParam = new MacroDataObjectParam(cpuData);
      // TODO:
      // 判断tx是否支持双HDMI
      if (dataModel.getConfigMetaData().getUtilVersion().hasMacroDualHdmi()
          && extenderData != null
          && extenderData.getExtenderStatusInfo().getInterfaceCount() > 1) {
        cpuDataParam.addChild(new MacroDualHdmiParam(cpuData, 1));
        cpuDataParam.addChild(new MacroDualHdmiParam(cpuData, 2));
      }
      result.add(cpuDataParam);
    }
    return result;
  }

  private static ObservableList<MacroParam> getCpuDataListForConnect(ObjectProperty<DataObject> object,
                                                           CaesarSwitchDataModel dataModel,
                                                           Command command, ConsoleData con) {
    ObservableList<MacroParam> result = FXCollections.observableArrayList();
    List<CpuData> cpus = new ArrayList<>(dataModel.getConfigDataManager().getActiveCpus());
    cpus.sort(Comparator.comparingInt(CpuData::getId));
    for (CpuData cpuData : cpus) {
      if (object.get() instanceof AccessControlObject) {
        AccessControlObject accessControlObject = (AccessControlObject) object.get();
        if (accessControlObject.isNoAccess(cpuData)) {
          continue;
        }
        if (accessControlObject.isVideoAccess(cpuData) && command == Command.Connect) {
          continue;
        }
      }
      if (con != null) {
        if (command == Command.ConnectVideo) {
          List<TypeWrapper> connectableType =
              TerminalUtility.getConnectableType(dataModel, con, cpuData);
          if (connectableType.stream().noneMatch(type -> type.getType().equals(TerminalUtility.VIDEO))) {
            continue;
          }
        } else if (command == Command.Connect) {
          List<TypeWrapper> connectableType =
              TerminalUtility.getConnectableType(dataModel, con, cpuData);
          if (connectableType.stream().noneMatch(type -> type.getType().equals(TerminalUtility.FULL))) {
            continue;
          }
        }
      }
      ExtenderData extenderData = cpuData.getExtenderData(0);
      MacroDataObjectParam cpuDataParam = new MacroDataObjectParam(cpuData);
      // TODO:
      // 判断tx是否支持双HDMI
      if (dataModel.getConfigMetaData().getUtilVersion().hasMacroDualHdmi()
          && extenderData != null
          && extenderData.getExtenderStatusInfo().getInterfaceCount() > 1) {
        cpuDataParam.addChild(new MacroDualHdmiParam(cpuData, 1));
        cpuDataParam.addChild(new MacroDualHdmiParam(cpuData, 2));
      }
      result.add(cpuDataParam);
    }
    return result;
  }
}
