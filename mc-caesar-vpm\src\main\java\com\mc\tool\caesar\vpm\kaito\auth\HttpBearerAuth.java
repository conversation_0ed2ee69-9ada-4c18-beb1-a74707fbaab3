package com.mc.tool.caesar.vpm.kaito.auth;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.Getter;
import lombok.Setter;

/**
 * An interceptor that adds the request header needed to use HTTP bearer authentication.
 */
public class HttpBearerAuth implements RequestInterceptor {
  private final String scheme;

  /**
   * -- SETTER --
   *  Sets the token, which together with the scheme, will be sent as the value of the Authorization header.
   * -- GETTER --
   *  Gets the token, which together with the scheme, will be sent as the value of the Authorization header.

   */
  @Getter
  @Setter
  private String bearerToken;

  public HttpBearerAuth(String scheme) {
    this.scheme = scheme;
  }

  @Override
  public void apply(RequestTemplate template) {
    if (bearerToken == null) {
      return;
    }

    template.header("Authorization",
        (scheme != null ? upperCaseBearer(scheme) + " " : "") + bearerToken);
  }

  private static String upperCaseBearer(String scheme) {
    return "bearer".equalsIgnoreCase(scheme) ? "Bearer" : scheme;
  }
}
