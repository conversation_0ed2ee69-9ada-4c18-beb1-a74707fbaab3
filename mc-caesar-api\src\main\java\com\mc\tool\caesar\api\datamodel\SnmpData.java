package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;
import java.util.Arrays;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * snmp配置数据.
 */
public class SnmpData extends AbstractData {

  private static final Logger LOG = Logger.getLogger(SnmpData.class.getName());
  public static final String PROPERTY_BASE = "SystemConfigData.SnmpData.";
  public static final String FIELD_STATUS = "Status";
  public static final String FIELD_ADDRESS = "Address";
  // Trap Receiver1下的 SNMP Server
  public static final String PROPERTY_ADDRESS = "SystemConfigData.SnmpData.Address";
  public static final String FIELD_PORT = "Port";
  // Trap Receiver1下的Port
  public static final String PROPERTY_PORT = "SystemConfigData.SnmpData.Port";
  public static final String FIELD_SNMP_BITS = "SnmpBits";
  public static final String PROPERTY_SNMP_TRAPS = "SystemConfigData.SnmpData.SnmpBits";
  // Trap Receiver1下的 Enable Traps
  public static final String PROPERTY_SNMP_TRAP_ENABLED = "SystemConfigData.SnmpData.SnmpBits.Trap";
  // Trap Receiver1下的 Status
  public static final String PROPERTY_SNMP_STATUS_ENABLED =
      "SystemConfigData.SnmpData.SnmpBits.Status";
  // Trap Receiver1下的Temperature
  public static final String PROPERTY_SNMP_TEMPERATURE_ENABLED =
      "SystemConfigData.SnmpData.SnmpBits.Temperature";
  // Trap Receiver1下的Insert I/O Board
  public static final String PROPERTY_SNMP_INSERT_BOARD_ENABLED =
      "SystemConfigData.SnmpData.SnmpBits.InsertBoard";
  // Trap Receiver1下的Remove I/O Board
  public static final String PROPERTY_SNMP_REMOVE_BOARD_ENABLED =
      "SystemConfigData.SnmpData.SnmpBits.RemoveBoard";
  // System Data 下的 General窗口标题下的Invalid I/O Board
  public static final String PROPERTY_SNMP_INVALID_BOARD_ENABLED =
      "SystemConfigData.SnmpData.SnmpBits.InvalidBoard";
  // Trap Receiver1下的Insert Extender
  public static final String PROPERTY_SNMP_INSERT_EXTENDER_ENABLED =
      "SystemConfigData.SnmpData.SnmpBits.InsertExtender";
  // Trap Receiver1下的 Remove Extender
  public static final String PROPERTY_SNMP_REMOVE_EXTENDER_ENABLED =
      "SystemConfigData.SnmpData.SnmpBits.RemoveExtener";
  // Trap Receiver1下的Switch Command
  public static final String PROPERTY_SNMP_SWITCH_COMMAND_ENABLED =
      "SystemConfigData.SnmpData.SnmpBits.SwitchCommand";
  // Trap Receiver1下的Fan Tray 1
  public static final String PROPERTY_SNMP_FANTRAY1_ENABLED =
      "SystemConfigData.SnmpData.SnmpBits.FanTray1";
  // Trap Receiver1下的Fan Tray 2
  public static final String PROPERTY_SNMP_FANTRAY2_ENABLED =
      "SystemConfigData.SnmpData.SnmpBits.FanTray2";
  // Trap Receiver1下的Power Supply 1
  public static final String PROPERTY_SNMP_POWERSUPPLY1_ENABLED =
      "SystemConfigData.SnmpData.SnmpBits.PowerSupply1";
  // Trap Receiver1下的Power Supply 2
  public static final String PROPERTY_SNMP_POWERSUPPLY2_ENABLED =
      "SystemConfigData.SnmpData.SnmpBits.PowerSupply2";
  // Trap Receiver1下的Power Supply 3
  public static final String PROPERTY_SNMP_POWERSUPPLY3_ENABLED =
      "SystemConfigData.SnmpData.SnmpBits.PowerSupply3";
  @Expose
  private byte[] address = new byte[4]; // server address
  @Expose
  private int port; // server port
  @Expose
  private int status;

  /**
   * .
   */
  public SnmpData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager, int oid,
      String fqn) {
    super(pcs, configDataManager, oid, fqn);

    initCommitRollback();
  }

  @Override
  public void initDefaults() {
    setStatus(0);

    this.port = CaesarConstants.SNMP_TRAP_PORT;
  }

  public byte[] getAddress() {
    return Arrays.copyOf(this.address, this.address.length);
  }

  /**
   * .
   */
  public void setAddress(byte[] address) {
    byte[] oldValue = this.address;
    this.address = Arrays.copyOf(address, address.length);
    firePropertyChange(SnmpData.PROPERTY_ADDRESS, oldValue, this.address, new int[0]);
  }

  public int getPort() {
    return this.port;
  }

  /**
   * .
   */
  public void setPort(int port) {
    int oldValue = this.port;
    this.port = port;
    firePropertyChange(SnmpData.PROPERTY_PORT, Integer.valueOf(oldValue),
        Integer.valueOf(this.port), new int[0]);
  }

  public int getStatus() {
    return this.status;
  }

  public void setStatus(int status) {
    this.status = status;
  }

  public boolean isTrapEnabled() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Network.Snmp.TRAP);
  }

  /**
   * .
   */
  public void setTrapEnabled(boolean enabled) {
    boolean oldValue = isTrapEnabled();
    setStatus(
        Utilities.setBits(this.status, enabled, new int[]{CaesarConstants.Network.Snmp.TRAP}));
    firePropertyChange(SnmpData.PROPERTY_SNMP_TRAP_ENABLED, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  public boolean isStatusEnabled() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Network.Snmp.TRAP01);
  }

  /**
   * .
   */
  public void setStatusEnabled(boolean enabled) {
    boolean oldValue = isStatusEnabled();
    setStatus(
        Utilities.setBits(this.status, enabled, new int[]{CaesarConstants.Network.Snmp.TRAP01}));
    firePropertyChange(SnmpData.PROPERTY_SNMP_STATUS_ENABLED, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  public boolean isTemperatureEnabled() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Network.Snmp.TRAP02);
  }

  /**
   * .
   */
  public void setTemperatureEnabled(boolean enabled) {
    boolean oldValue = isTemperatureEnabled();
    setStatus(
        Utilities.setBits(this.status, enabled, new int[]{CaesarConstants.Network.Snmp.TRAP02}));
    firePropertyChange(SnmpData.PROPERTY_SNMP_TEMPERATURE_ENABLED, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  public boolean isInsertBoardEnabled() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Network.Snmp.TRAP10);
  }

  /**
   * .
   */
  public void setInsertBoardEnabled(boolean enabled) {
    boolean oldValue = isInsertBoardEnabled();
    setStatus(
        Utilities.setBits(this.status, enabled, new int[]{CaesarConstants.Network.Snmp.TRAP10}));
    firePropertyChange(SnmpData.PROPERTY_SNMP_INSERT_BOARD_ENABLED, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  public boolean isRemoveBoardEnabled() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Network.Snmp.TRAP11);
  }

  /**
   * .
   */
  public void setRemoveBoardEnabled(boolean enabled) {
    boolean oldValue = isRemoveBoardEnabled();
    setStatus(
        Utilities.setBits(this.status, enabled, new int[]{CaesarConstants.Network.Snmp.TRAP11}));
    firePropertyChange(SnmpData.PROPERTY_SNMP_REMOVE_BOARD_ENABLED, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  public boolean isInvalidBoardEnabled() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Network.Snmp.TRAP12);
  }

  /**
   * .
   */
  public void setInvalidBoardEnabled(boolean enabled) {
    boolean oldValue = isInvalidBoardEnabled();
    setStatus(
        Utilities.setBits(this.status, enabled, new int[]{CaesarConstants.Network.Snmp.TRAP12}));
    firePropertyChange(SnmpData.PROPERTY_SNMP_INVALID_BOARD_ENABLED, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  public boolean isInsertExtenderEnabled() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Network.Snmp.TRAP20);
  }

  /**
   * .
   */
  public void setInsertExtenderEnabled(boolean enabled) {
    boolean oldValue = isInsertExtenderEnabled();
    setStatus(
        Utilities.setBits(this.status, enabled, new int[]{CaesarConstants.Network.Snmp.TRAP20}));
    firePropertyChange(SnmpData.PROPERTY_SNMP_INSERT_EXTENDER_ENABLED, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  public boolean isRemoveExtenderEnabled() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Network.Snmp.TRAP21);
  }

  /**
   * .
   */
  public void setRemoveExtenderEnabled(boolean enabled) {
    boolean oldValue = isRemoveExtenderEnabled();
    setStatus(
        Utilities.setBits(this.status, enabled, new int[]{CaesarConstants.Network.Snmp.TRAP21}));
    firePropertyChange(SnmpData.PROPERTY_SNMP_REMOVE_EXTENDER_ENABLED, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  public boolean isSwitchCommandEnabled() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Network.Snmp.TRAP30);
  }

  /**
   * .
   */
  public void setSwitchCommandEnabled(boolean enabled) {
    boolean oldValue = isSwitchCommandEnabled();
    setStatus(
        Utilities.setBits(this.status, enabled, new int[]{CaesarConstants.Network.Snmp.TRAP30}));
    firePropertyChange(SnmpData.PROPERTY_SNMP_SWITCH_COMMAND_ENABLED, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  public boolean isFanTray1Enabled() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Network.Snmp.TRAP40);
  }

  /**
   * .
   */
  public void setFanTray1Enabled(boolean enabled) {
    boolean oldValue = isFanTray1Enabled();
    setStatus(
        Utilities.setBits(this.status, enabled, new int[]{CaesarConstants.Network.Snmp.TRAP40}));
    firePropertyChange(SnmpData.PROPERTY_SNMP_FANTRAY1_ENABLED, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  public boolean isFanTray2Enabled() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Network.Snmp.TRAP41);
  }

  /**
   * .
   */
  public void setFanTray2Enabled(boolean enabled) {
    boolean oldValue = isFanTray2Enabled();
    setStatus(
        Utilities.setBits(this.status, enabled, new int[]{CaesarConstants.Network.Snmp.TRAP41}));
    firePropertyChange(SnmpData.PROPERTY_SNMP_FANTRAY2_ENABLED, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  public boolean isPowerSupply1Enabled() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Network.Snmp.TRAP50);
  }

  /**
   * .
   */
  public void setPowerSupply1Enabled(boolean enabled) {
    boolean oldValue = isPowerSupply1Enabled();
    setStatus(
        Utilities.setBits(this.status, enabled, new int[]{CaesarConstants.Network.Snmp.TRAP50}));
    firePropertyChange(SnmpData.PROPERTY_SNMP_POWERSUPPLY1_ENABLED, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  public boolean isPowerSupply2Enabled() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Network.Snmp.TRAP51);
  }

  /**
   * .
   */
  public void setPowerSupply2Enabled(boolean enabled) {
    boolean oldValue = isPowerSupply2Enabled();
    setStatus(
        Utilities.setBits(this.status, enabled, new int[]{CaesarConstants.Network.Snmp.TRAP51}));
    firePropertyChange(SnmpData.PROPERTY_SNMP_POWERSUPPLY2_ENABLED, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  public boolean isPowerSupply3Enabled() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Network.Snmp.TRAP52);
  }

  /**
   * .
   */
  public void setPowerSupply3Enabled(boolean enabled) {
    boolean oldValue = isPowerSupply3Enabled();
    setStatus(
        Utilities.setBits(this.status, enabled, new int[]{CaesarConstants.Network.Snmp.TRAP52}));
    firePropertyChange(SnmpData.PROPERTY_SNMP_POWERSUPPLY3_ENABLED, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (SnmpData.PROPERTY_SNMP_TRAP_ENABLED.equals(propertyName)) {
      setTrapEnabled(Boolean.class.cast(value).booleanValue());
    } else if (SnmpData.PROPERTY_SNMP_STATUS_ENABLED.equals(propertyName)) {
      setStatusEnabled(Boolean.class.cast(value).booleanValue());
    } else if (SnmpData.PROPERTY_SNMP_TEMPERATURE_ENABLED.equals(propertyName)) {
      setTemperatureEnabled(Boolean.class.cast(value).booleanValue());
    } else if (SnmpData.PROPERTY_SNMP_INSERT_BOARD_ENABLED.equals(propertyName)) {
      setInsertBoardEnabled(Boolean.class.cast(value).booleanValue());
    } else if (SnmpData.PROPERTY_SNMP_REMOVE_BOARD_ENABLED.equals(propertyName)) {
      setRemoveBoardEnabled(Boolean.class.cast(value).booleanValue());
    } else if (SnmpData.PROPERTY_SNMP_INVALID_BOARD_ENABLED.equals(propertyName)) {
      setInvalidBoardEnabled(Boolean.class.cast(value).booleanValue());
    } else if (SnmpData.PROPERTY_SNMP_INSERT_EXTENDER_ENABLED.equals(propertyName)) {
      setInsertExtenderEnabled(Boolean.class.cast(value).booleanValue());
    } else if (SnmpData.PROPERTY_SNMP_REMOVE_EXTENDER_ENABLED.equals(propertyName)) {
      setRemoveExtenderEnabled(Boolean.class.cast(value).booleanValue());
    } else if (SnmpData.PROPERTY_SNMP_SWITCH_COMMAND_ENABLED.equals(propertyName)) {
      setSwitchCommandEnabled(Boolean.class.cast(value).booleanValue());
    } else if (SnmpData.PROPERTY_SNMP_FANTRAY1_ENABLED.equals(propertyName)) {
      setFanTray1Enabled(Boolean.class.cast(value).booleanValue());
    } else if (SnmpData.PROPERTY_SNMP_FANTRAY2_ENABLED.equals(propertyName)) {
      setFanTray2Enabled(Boolean.class.cast(value).booleanValue());
    } else if (SnmpData.PROPERTY_SNMP_POWERSUPPLY1_ENABLED.equals(propertyName)) {
      setPowerSupply1Enabled(Boolean.class.cast(value).booleanValue());
    } else if (SnmpData.PROPERTY_SNMP_POWERSUPPLY2_ENABLED.equals(propertyName)) {
      setPowerSupply2Enabled(Boolean.class.cast(value).booleanValue());
    } else if (SnmpData.PROPERTY_SNMP_POWERSUPPLY3_ENABLED.equals(propertyName)) {
      setPowerSupply3Enabled(Boolean.class.cast(value).booleanValue());
    } else if (SnmpData.PROPERTY_ADDRESS.equals(propertyName)) {
      setAddress(byte[].class.cast(value));
    }
  }

  /**
   * .
   */
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    Level logLevel = Level.FINER;
    boolean isLoggable = LOG.isLoggable(logLevel);
    if (isLoggable) {
      LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), "Status"});
    }
    cfgWriter.writeInteger(getStatus());
    for (int idx = this.address.length; idx > 0; idx--) {
      if (isLoggable) {
        LOG.log(logLevel, "writing {0}.{1}[{2}]",
            new Object[]{getFqn(), "Address", Integer.valueOf(idx - 1)});
      }
      cfgWriter.write4Byte(this.address[idx - 1]);
    }
    if (getConfigDataManager().getConfigMetaData().getUtilVersion().hasSnmpPortOption()) {
      if (isLoggable) {
        LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), SnmpData.PROPERTY_PORT});
      }
      cfgWriter.writeInteger(this.port);
    }
  }

  /**
   * .
   */
  public void readData(CfgReader cfgReader) throws ConfigException {
    Level logLevel = Level.FINER;
    boolean isLoggable = LOG.isLoggable(logLevel);
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), "Status"});
    }
    int status = cfgReader.readInteger();
    setStatus(status);

    byte[] newAddress = new byte[this.address.length];
    for (int idx = newAddress.length; idx > 0; idx--) {
      if (isLoggable) {
        LOG.log(logLevel, "reading {0}.{1}[{2}]",
            new Object[]{getFqn(), "Address", Integer.valueOf(idx - 1)});
      }
      newAddress[idx - 1] = cfgReader.read4ByteValue();
    }
    if (!isPropertyChangedByUi(SnmpData.PROPERTY_ADDRESS)) {
      setAddress(newAddress);
    }
    if (getConfigDataManager().getConfigMetaData().getUtilVersion().hasSnmpPortOption()) {
      if (isLoggable) {
        LOG.log(logLevel, "reading {0}.{1}[{2}]", new Object[]{getFqn(), SnmpData.PROPERTY_PORT});
      }
      int port = cfgReader.readInteger();
      if (!isPropertyChangedByUi(SnmpData.PROPERTY_PORT)) {
        setPort(port);
      }
    }
  }
}

