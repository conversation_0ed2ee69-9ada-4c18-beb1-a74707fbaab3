package com.mc.tool.caesar.vpm.pages.systemedit.view;

import com.mc.graph.interfaces.CellBehavior;
import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.SkinManager;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.utils.SwitchType;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarTerminalBase;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.view.TerminalCellSkin;
import com.mc.tool.framework.systemedit.view.TerminalConnectorSkin;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.ResourceBundle;
import javafx.beans.binding.Bindings;
import javafx.beans.binding.ObjectBinding;
import javafx.geometry.Insets;
import javafx.scene.Parent;
import javafx.scene.image.Image;
import javafx.scene.layout.Background;
import javafx.scene.layout.BackgroundFill;
import javafx.scene.layout.BackgroundImage;
import javafx.scene.layout.BackgroundPosition;
import javafx.scene.layout.BackgroundRepeat;
import javafx.scene.layout.Border;
import javafx.scene.layout.BorderStroke;
import javafx.scene.layout.BorderStrokeStyle;
import javafx.scene.paint.Color;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarTerminalCellSkin extends TerminalCellSkin {

  private static final int CONNECTED_TYPE_MAX_SIZE = 8;

  /** Constructor. */
  public CaesarTerminalCellSkin(
      CellObject cellobject, Parent parent, Parent container, SkinManager skinManager) {
    super(cellobject, parent, container, skinManager);
    connectedTypes.resize(CONNECTED_TYPE_MAX_SIZE);
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    super.initialize(location, resources);
    connectorContainer.setSpacing(1);
    connectorContainer.setPadding(new Insets(1));
    connectorContainer.setBorder(
        new Border(new BorderStroke(Color.web("#00d10a"), BorderStrokeStyle.SOLID, null, null)));
    connectorContainer
        .maxHeightProperty()
        .bind(
            Bindings.createDoubleBinding(
                () -> {
                  int size = connectorContainer.getChildren().size();
                  return (double) (size * TerminalConnectorSkin.CONNECTOR_HEIGHT + (size - 1) + 4);
                },
                connectorContainer.getChildren()));
    connectorContainer.setMinWidth(8);
    connectorContainer.setMaxWidth(8);
    updateConnectionColorBinding();
    getCell()
        .getBindedObjectProperty()
        .addListener(
            weakAdapter.wrap((observable, oldVal, newVal) -> updateConnectorConnectedBinding()));
  }

  @Override
  protected void onConnectorChange() {
    if (cellObject == null) {
      return;
    }
    // 删除没有用的connectorskin，创建新的
    List<ConnectorSkin> newSkins = new ArrayList<>();
    for (Connector connector : cellObject.getConnectors()) {
      ConnectorSkin cs = null;
      for (ConnectorSkin skin : connectorSkins) {
        if (skin.getConnector() == connector) {
          cs = skin;
          break;
        }
      } // end for
      if (cs == null) {
        cs = new CaesarTerminalConnectorSkin(connector, connectorContainer, container);
        skinManager.setConnectorSkin(connector, cs);
        for (CellBehavior cellBehavior : cellBehaviors) {
          cellBehavior.createConnectorBehavior(cs);
        }
        if (getRegion().getParent() != null) {
          cs.add();
        }
      }
      newSkins.add(cs);
    } // end for

    for (ConnectorSkin skin : connectorSkins) {
      if (!newSkins.contains(skin)) {
        skin.remove();
      }
    }

    connectorSkins = newSkins;

    updateConnectorDirection();
    updateConnectorConnectedBinding();
  }

  @Override
  protected void updateListeners(CellBindedObject object, boolean attach) {
    super.updateListeners(object, attach);
    if (!(object instanceof VisualEditTerminal)) {
      return;
    }
    VisualEditTerminal terminal = (VisualEditTerminal) object;
    if (attach) {
      terminal.targetDeviceConnectedProperty(1).addListener(targetConnectedChangeListener);
    } else {
      terminal.targetDeviceConnectedProperty(1).removeListener(targetConnectedChangeListener);
    }
  }

  @Override
  protected void updateCellBackground() {
    BackgroundImage[] images = new BackgroundImage[0];
    if (getCell().getBindedObject() instanceof VisualEditTerminal) {
      VisualEditTerminal terminal = (VisualEditTerminal) getCell().getBindedObject();
      List<Image> img1s = new ArrayList<>();
      List<Image> img2s = new ArrayList<>();
      BackgroundPosition imgPos1;
      BackgroundPosition imgPos2;
      Image onlineTargetDevice = getTargetDeviceOnlineBgImg(terminal.getTargetDeviceType());
      Image offlineTargetDevice = getTargetDeviceOffsetBgImg(terminal.getTargetDeviceType());
      if (isLeftTerminal()) {
        img1s.add(terminal.isTargetDeviceConnected(0) ? onlineTargetDevice : offlineTargetDevice);
        if (terminal.canSeperate()) {
          img1s.add(terminal.isTargetDeviceConnected(1) ? onlineTargetDevice : offlineTargetDevice);
        }
        img2s.add(getMultimediaIntefaceBgImg(terminal.getMultimediaInterfaceType()));
        imgPos1 = leftImage1Pos;
        imgPos2 = leftImage2Pos;
      } else {
        img1s.add(getMultimediaIntefaceBgImg(terminal.getMultimediaInterfaceType()));
        img2s.add(terminal.isTargetDeviceConnected(0) ? onlineTargetDevice : offlineTargetDevice);
        if (terminal.canSeperate()) {
          // 放在右边的话，顺序倒序
          img2s.add(
              0, terminal.isTargetDeviceConnected(1) ? onlineTargetDevice : offlineTargetDevice);
        }
        imgPos1 = rightImage1Pos;
        imgPos2 = rightImage2Pos;
      }

      List<BackgroundImage> bgimgs = new ArrayList<>();
      final int space = 2;
      double img1sTotalWidth =
          img1s.stream().map(Image::getWidth).reduce(Double::sum).get()
              + space * (img1s.size() - 1);
      double img2sTotalWidth =
          img2s.stream().map(Image::getWidth).reduce(Double::sum).get()
              + space * (img2s.size() - 1);

      double horzPosOffset = 0;
      for (Image img1 : img1s) {
        BackgroundPosition newImgPos1 =
            new BackgroundPosition(
                imgPos1.getHorizontalSide(),
                imgPos1.getHorizontalPosition()
                    + (IMAGE_WIDTH - img1sTotalWidth) / 2
                    + horzPosOffset,
                imgPos1.isHorizontalAsPercentage(),
                imgPos1.getVerticalSide(),
                imgPos1.getVerticalPosition(),
                imgPos1.isVerticalAsPercentage());
        BackgroundImage bgImg1 =
            new BackgroundImage(
                img1, BackgroundRepeat.NO_REPEAT, BackgroundRepeat.NO_REPEAT, newImgPos1, null);
        bgimgs.add(bgImg1);
        horzPosOffset += img1.getWidth() + space;
      }

      horzPosOffset = 0;
      for (Image img2 : img2s) {
        BackgroundPosition newImgPos2 =
            new BackgroundPosition(
                imgPos2.getHorizontalSide(),
                imgPos2.getHorizontalPosition()
                    + (IMAGE_WIDTH - img2sTotalWidth) / 2
                    + horzPosOffset,
                imgPos2.isHorizontalAsPercentage(),
                imgPos2.getVerticalSide(),
                imgPos2.getVerticalPosition(),
                imgPos2.isVerticalAsPercentage());
        BackgroundImage bgImg2 =
            new BackgroundImage(
                img2, BackgroundRepeat.NO_REPEAT, BackgroundRepeat.NO_REPEAT, newImgPos2, null);
        bgimgs.add(bgImg2);
        horzPosOffset += img2.getWidth() + space;
      }

      images = bgimgs.toArray(new BackgroundImage[0]);
    }
    Background background;

    if (isLeftTerminal()) {
      Collection<BackgroundFill> connectedTypeFills =
          connectedTypes.createBackgroundFills(
              1, 1, 1, CELL_CONTAINER_WIDTH - CONNECTED_TYPE_WIDTH + 1, CELL_HEIGHT - 2);
      List<BackgroundFill> fills = new ArrayList<>();
      fills.add(cellBgFill);
      fills.add(leftSeperator1Fill);
      fills.add(leftSeperator2Fill);
      fills.addAll(connectedTypeFills);
      background = new Background(fills.toArray(new BackgroundFill[0]), images);
    } else {
      Collection<BackgroundFill> connectedTypeFills =
          connectedTypes.createBackgroundFills(
              1, CELL_CONTAINER_WIDTH - CONNECTED_TYPE_WIDTH + 1, 1, 1, CELL_HEIGHT - 2);
      List<BackgroundFill> fills = new ArrayList<>();
      fills.add(cellBgFill);
      fills.add(rightSeperator1Fill);
      fills.add(rightSeperator2Fill);
      fills.addAll(connectedTypeFills);
      background = new Background(fills.toArray(new BackgroundFill[0]), images);
    }
    cellContainer.setBackground(background);
  }

  protected void updateConnectorConnectedBinding() {
    if (getCell().getBindedObject() instanceof CaesarTerminalBase) {
      CaesarTerminalBase terminalBase = (CaesarTerminalBase) getCell().getBindedObject();
      Connector normalConnector =
          getCell()
              .getConnector(
                  ConnectorIdentifier.getIdentifier(CaesarTerminalBase.CAESAR_NORMAL_PORT));
      Connector rdConnector =
          getCell()
              .getConnector(ConnectorIdentifier.getIdentifier(CaesarTerminalBase.CAESAR_RD_PORT));
      for (ConnectorSkin skin : connectorSkins) {
        if (skin == null || skin.getConnector() == null) {
          continue;
        }
        if (skin instanceof CaesarTerminalConnectorSkin) {
          CaesarTerminalConnectorSkin connectorSkin = (CaesarTerminalConnectorSkin) skin;
          connectorSkin.getConnectedProperty().unbind();
          if (skin.getConnector().equals(normalConnector)) {
            connectorSkin.getConnectedProperty().bind(terminalBase.getPort1ConnectedProperty());
          } else if (skin.getConnector().equals(rdConnector)) {
            connectorSkin.getConnectedProperty().bind(terminalBase.getPort2ConnectedProperty());
          }
        }
      }
    } else {
      for (ConnectorSkin skin : connectorSkins) {
        if (skin == null || skin.getConnector() == null) {
          continue;
        }
        if (skin instanceof CaesarTerminalConnectorSkin) {
          ((CaesarTerminalConnectorSkin) skin).getConnectedProperty().set(true);
        }
      }
    }
  }

  protected void updateConnectionColorBinding() {
    getCell()
        .getBindedObjectProperty()
        .addListener(
            weakAdapter.wrap(
                (observable, oldVal, newVal) -> {
                  if (oldVal != null) {
                    for (int i = 0; i < CONNECTED_TYPE_MAX_SIZE; i++) {
                      connectedTypes.getColorProperty(i).unbind();
                    }
                    connectedTypes.getMaxGridSize().unbind();
                  }
                  CellBindedObject cellObject = getCell().getBindedObject();
                  bindTerminal(cellObject);
                }));
  }

  private void bindTerminal(CellBindedObject object) {

    if (object instanceof CaesarTerminalBase) {
      CaesarTerminalBase terminal = (CaesarTerminalBase) object;
      for (int i = 0; i < CONNECTED_TYPE_MAX_SIZE; i++) {
        final int index = i;
        ObjectBinding<Color> bgColorBinding =
                Bindings.createObjectBinding(
                        () -> {
                          if (terminal.getConnectionType().size() <= index) {
                            return Color.TRANSPARENT;
                          }
                          return getBgColor(terminal.getConnectionType().get(index), getCell().highLightProperty().get());
                        },
                        terminal.getConnectionType(),
                        getCell().highLightProperty());
        connectedTypes.getColorProperty(i).bind(bgColorBinding);
      }
      connectedTypes.getMaxGridSize().bind(Bindings.size(terminal.getConnectionType()));
    }
  }

  private Color getBgColor(SwitchType type, Boolean isHighLight) {
    if (type == null) {
      return null;
    }
    switch (type) {
      case FULL:
        if (isHighLight) {
          return Color.web(CaesarConstants.SWITCH_TYPE_COLOR_FULL);
        }
        return Color.web(CaesarConstants.SWITCH_TYPE_COLOR_FULL, 0.3);
      case VIDEO:
        if (isHighLight) {
          return Color.web(CaesarConstants.SWITCH_TYPE_COLOR_VIDEO);
        }
        return Color.web(CaesarConstants.SWITCH_TYPE_COLOR_VIDEO, 0.3);
      case PRIVATE:
        if (isHighLight) {
          return Color.web(CaesarConstants.SWITCH_TYPE_COLOR_PRIVATE);
        }
        return Color.web(CaesarConstants.SWITCH_TYPE_COLOR_PRIVATE, 0.3);
      case SHARED:
        return isHighLight
            ? Color.web(CaesarConstants.SWITCH_TYPE_COLOR_SHARED)
            : Color.web(CaesarConstants.SWITCH_TYPE_COLOR_SHARED, 0.3);
      case FULL_SHARED:
        return isHighLight
            ? Color.web(CaesarConstants.SWITCH_TYPE_COLOR_FULL_SHARED)
            : Color.web(CaesarConstants.SWITCH_TYPE_COLOR_FULL_SHARED, 0.3);
      case NOT_SWITCHED:
        return Color.TRANSPARENT;
      default:
        //todo
        String typeString = type.toString();
        if (typeString.startsWith("MULTIVIEW_FULL")) {
          return isHighLight
              ? Color.web(CaesarConstants.SWITCH_TYPE_COLOR_FULL)
              : Color.web(CaesarConstants.SWITCH_TYPE_COLOR_FULL, 0.3);
        } else if (typeString.startsWith("MULTIVIEW_VIDEO")) {
          return isHighLight
              ? Color.web(CaesarConstants.SWITCH_TYPE_COLOR_VIDEO)
              : Color.web(CaesarConstants.SWITCH_TYPE_COLOR_VIDEO, 0.3);
        }
        break;
    }
    return null;
  }

  @Override
  public void destroy() {
    for (ConnectorSkin skin : getConnectorSkins()) {
      if (skin instanceof CaesarTerminalConnectorSkin) {
        ((CaesarTerminalConnectorSkin) skin).getConnectedProperty().unbind();
      }
    }
    super.destroy();
    connectorSkins.clear();
  }
}
