#root {
  -fx-background-color: #ffffff;
}

#saveas-btn, #save-btn {
  -fx-graphic: url("../menu_normal.png");
  -fx-text-fill: #333333;
}

#saveas-btn:hover, #save-btn:hover {
  -fx-graphic: url("../menu_hover.png");
  -fx-text-fill: #f08519;
}

#pre-window {
  -fx-graphic: url("../prewindow.png");
  -fx-text-fill: #333333;
}

#pre-window:hover, #pre-window:selected {
  -fx-graphic: url("../prewindow_hover.png");
  -fx-text-fill: #f08519;
}

#publish-btn {
  -fx-graphic: url("../publish.png");
  -fx-text-fill: #333333;
}

#publish-btn:hover {
  -fx-graphic: url("../publish_hover.png");
  -fx-text-fill: #f08519;
}


#config-btn {
  -fx-graphic: url("../config_normal.png");
  -fx-text-fill: #333333;
}

#config-btn:hover {
  -fx-graphic: url("../config_hover.png");
  -fx-text-fill: #f08519;
}

#switch-btn {
  -fx-graphic: url("../switch_normal.png");
  -fx-text-fill: #333333;
}

#switch-btn:hover {
  -fx-graphic: url("../switch_hover.png");
  -fx-text-fill: #f08519;
}

#screen-btn {
  -fx-graphic: url("../display_mode.png");
  -fx-text-fill: #333333;
}

#screen-btn:hover {
  -fx-graphic: url("../display_mode_hover.png");
  -fx-text-fill: #f08519;
}

#source-list-title-container {
  -fx-background-color: #cccccc;
}

#source-list-title {
  -fx-label-padding: 0 0 0 10;
}

#vpcon-table {
  -fx-border-size: 0px;
}

#windowsTable .column-header-background {
  -fx-border-color: -fx-table-cell-border-color;
  -fx-border-width: 0 0 1 0;
}

#windowsTable .column-header {
  -fx-background-color: white;
  -fx-border-color: transparent -fx-table-cell-border-color transparent transparent;
  -fx-border-width: 1;
}

#windowsTable .column-header:last-visible {
  -fx-background-color: white;
  -fx-border-width: 0;
}
