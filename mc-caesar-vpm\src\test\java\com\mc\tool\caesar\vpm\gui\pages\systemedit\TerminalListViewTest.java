package com.mc.tool.caesar.vpm.gui.pages.systemedit;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mc.graph.interfaces.CellSkin;
import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.view.TerminalListView;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.dialogues.FileDialogue;
import com.mc.tool.framework.utility.dialogues.FileDialogueFactory;
import com.mc.tool.framework.view.FrameView;
import java.io.File;
import java.io.IOException;
import javafx.scene.Node;
import javafx.scene.control.Label;
import javafx.scene.control.Labeled;
import javafx.scene.control.TableView;
import javafx.scene.input.KeyCode;
import javafx.stage.Stage;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * .
 */
@RunWith(MockitoJUnitRunner.class)
public class TerminalListViewTest extends AppGuiTestBase {

  @Mock private FileDialogueFactory fileDialogueFactory;

  @Mock private FileDialogue fileDialogue;

  @Override
  public void start(Stage stage) throws Exception {
    FrameView.setModule(
        binder -> binder.bind(FileDialogueFactory.class).toInstance(fileDialogueFactory));
    when(fileDialogueFactory.createFileDialogue()).thenReturn(fileDialogue);
    super.start(stage);
  }

  @Override
  public void beforeAll() {}

  @Test
  public void testSwitch() {
    // 切换
    try {

      Labeled button = lookup(GuiTestConstants.SWITCH_LIST_GRAPH_BTN).queryLabeled();
      Assert.assertEquals(GuiTestConstants.SWITCH_LIST_GRAPH_BTN_TEXT_LIST, button.getText());

      clickOn(GuiTestConstants.SWITCH_LIST_GRAPH_BTN);
      Assert.assertEquals(GuiTestConstants.SWITCH_LIST_GRAPH_BTN_TEXT_graph, button.getText());
      Node terminalListView = lookup(GuiTestConstants.TERMINAL_LIST_VIEW_CONTAINER).query();
      Assert.assertTrue(terminalListView.isVisible());

      clickOn(GuiTestConstants.SWITCH_LIST_GRAPH_BTN);
      Node terminalGraphView = lookup(GuiTestConstants.TERMINAL_GRAPH_VIEW_CONTAINER).query();
      Assert.assertTrue(terminalGraphView.isVisible());

      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testProperty() {
    // 选中项，显示属性框
    try {

      clickOn(GuiTestConstants.SWITCH_LIST_GRAPH_BTN);
      Node cellNode =
          lookup(GuiTestConstants.CON_01)
              .match((node) -> checkParentType(node, TerminalListView.class))
              .query();
      clickOn(cellNode);

      Node nameProperty = lookup(GuiTestConstants.RX_PROP_NAME).query();
      Label label = (Label) nameProperty.lookup(".label");
      Assert.assertEquals(GuiTestConstants.CON_01, label.getText());

      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testSwitchSelection() {
    // 选中项，显示属性框
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }
      clickOn(GuiTestConstants.CON_01);
      // 切到列表，检查选中的是否为con_01
      clickOn(GuiTestConstants.SWITCH_LIST_GRAPH_BTN);
      TableView<VisualEditTerminal> tableView =
          lookup(GuiTestConstants.TERMINAL_LIST_VIEW).queryTableView();
      Assert.assertEquals(
          GuiTestConstants.CON_01, tableView.getSelectionModel().getSelectedItem().getName());

      // 选中cpu_01，然后切到graph，检查选中项
      Node cellNode =
          lookup(GuiTestConstants.CPU_01)
              .match((node) -> checkParentType(node, TerminalListView.class))
              .query();
      clickOn(cellNode);
      clickOn(GuiTestConstants.SWITCH_LIST_GRAPH_BTN);
      Node cpu01Node = findTerminalNode(CaesarCpuTerminal.class, GuiTestConstants.CPU_01);
      CellSkin skin = (CellSkin) cpu01Node.getUserData();
      Assert.assertTrue(skin.getCell().highLightProperty().get());

      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testChangeIdName() {
    // 选中项，显示属性框
    try {
      clickOn(GuiTestConstants.SWITCH_LIST_GRAPH_BTN);
      // 修改cpu id name
      Node cputIdCell =
          lookupTableCell(1, GuiTestConstants.TERMINAL_LIST_VIEW_ID_COL, TerminalListView.class);
      doubleClickOn(cputIdCell);
      Node textField = targetWindow(GuiTestConstants.DIALOG_SET_ID).lookup(".text-field").query();
      targetWindow(GuiTestConstants.DIALOG_SET_ID).clickOn(textField);
      targetWindow(GuiTestConstants.DIALOG_SET_ID).type(KeyCode.END);
      targetWindow(GuiTestConstants.DIALOG_SET_ID).eraseText(10);
      targetWindow(GuiTestConstants.DIALOG_SET_ID).write("111"); // 因为不能清空，只需输入最后3位
      targetWindow(GuiTestConstants.DIALOG_SET_ID).clickOn(GuiTestConstants.BUTTON_CONFIRM);
      Node cpu01Node = findTerminalNode(CaesarCpuTerminal.class, GuiTestConstants.CPU_01);
      CaesarCpuTerminal cpu01Terminal =
          (CaesarCpuTerminal) ((CellSkin) cpu01Node.getUserData()).getCell().getBindedObject();
      Assert.assertEquals(1111, cpu01Terminal.getCpuData().getId());

      Node cpuNameCell =
          lookupTableCell(1, GuiTestConstants.TERMINAL_LIST_VIEW_NAME_COL, TerminalListView.class);
      doubleClickOn(cpuNameCell);
      textField = targetWindow(GuiTestConstants.DIALOG_SET_NAME).lookup(".text-field").query();
      targetWindow(GuiTestConstants.DIALOG_SET_NAME).clickOn(textField);
      targetWindow(GuiTestConstants.DIALOG_SET_NAME).type(KeyCode.END);
      targetWindow(GuiTestConstants.DIALOG_SET_NAME).eraseText(10);
      targetWindow(GuiTestConstants.DIALOG_SET_NAME).write("1111");
      targetWindow(GuiTestConstants.DIALOG_SET_NAME).clickOn(GuiTestConstants.BUTTON_CONFIRM);
      Assert.assertEquals("1111", cpu01Terminal.getName());
      // 修改con id name
      Node conIdCell =
          lookupTableCell(3, GuiTestConstants.TERMINAL_LIST_VIEW_ID_COL, TerminalListView.class);
      doubleClickOn(conIdCell);
      textField = targetWindow(GuiTestConstants.DIALOG_SET_ID).lookup(".text-field").query();
      clickOn(textField);
      targetWindow(GuiTestConstants.DIALOG_SET_ID).type(KeyCode.END);
      targetWindow(GuiTestConstants.DIALOG_SET_ID).eraseText(10);
      targetWindow(GuiTestConstants.DIALOG_SET_ID).write("111"); // 因为不能清空，只需输入最后3位
      targetWindow(GuiTestConstants.DIALOG_SET_ID).clickOn(GuiTestConstants.BUTTON_CONFIRM);
      Node con01Node = findTerminalNode(CaesarConTerminal.class, GuiTestConstants.CON_01);
      CaesarConTerminal con01Terminal =
          (CaesarConTerminal) ((CellSkin) con01Node.getUserData()).getCell().getBindedObject();
      Assert.assertEquals(3111, con01Terminal.getConsoleData().getId());

      Node conNameCell =
          lookupTableCell(3, GuiTestConstants.TERMINAL_LIST_VIEW_NAME_COL, TerminalListView.class);
      doubleClickOn(conNameCell);
      textField = targetWindow(GuiTestConstants.DIALOG_SET_NAME).lookup(".text-field").query();
      targetWindow(GuiTestConstants.DIALOG_SET_NAME).clickOn(textField);
      targetWindow(GuiTestConstants.DIALOG_SET_NAME).type(KeyCode.END);
      targetWindow(GuiTestConstants.DIALOG_SET_NAME).eraseText(10);
      targetWindow(GuiTestConstants.DIALOG_SET_NAME).write("3111");
      targetWindow(GuiTestConstants.DIALOG_SET_NAME).clickOn(GuiTestConstants.BUTTON_CONFIRM);
      Assert.assertEquals("3111", con01Terminal.getName());

      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testExportImport() {
    try {
      clickOn(GuiTestConstants.SWITCH_LIST_GRAPH_BTN);

      File targetFile = File.createTempFile("export", ".csv");
      when(fileDialogue.showSaveDialog(any())).thenReturn(targetFile);

      clickOn(GuiTestConstants.EXPORT_EXTENDER_INFO_BTN);
      // 等待导出完成
      Thread.sleep(1000);
      lookup("导出成功").query();
      clickOn(GuiTestConstants.BUTTON_CONFIRM);
      //
      when(fileDialogue.showOpenDialog(any())).thenReturn(targetFile);
      clickOn(GuiTestConstants.IMPORT_EXTENDER_INFO_BTN);
      // 等待导入完成
      Thread.sleep(1000);
      lookup("导入成功").query();
      clickOn(GuiTestConstants.BUTTON_CONFIRM);

      targetFile.delete();
      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException | InterruptedException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testImportSuccess() {
    try {
      clickOn(GuiTestConstants.SWITCH_LIST_GRAPH_BTN);


      File targetFile =
          copyResourceToTemp(
              "com/mc/tool/caesar/vpm/systemedit/extender_import_success.csv", "hello", ".csv");
      //
      when(fileDialogue.showOpenDialog(any())).thenReturn(targetFile);
      clickOn(GuiTestConstants.IMPORT_EXTENDER_INFO_BTN);
      // 等待导入完成
      Thread.sleep(1000);
      clickOn(GuiTestConstants.BUTTON_CONFIRM);

      Node cpu01Node = findTerminalNode(CaesarCpuTerminal.class, GuiTestConstants.CPU_01);
      CaesarCpuTerminal cpu01Terminal =
          (CaesarCpuTerminal) ((CellSkin) cpu01Node.getUserData()).getCell().getBindedObject();
      Assert.assertEquals(1111, cpu01Terminal.getCpuData().getId());
      Assert.assertEquals(GuiTestConstants.CPU_01 + "_modified", cpu01Terminal.getName());
      Node con01Node = findTerminalNode(CaesarConTerminal.class, GuiTestConstants.CON_01);
      CaesarConTerminal con01Terminal =
          (CaesarConTerminal) ((CellSkin) con01Node.getUserData()).getCell().getBindedObject();
      Assert.assertEquals(3111, con01Terminal.getConsoleData().getId());
      Assert.assertEquals(GuiTestConstants.CON_01 + "_modified", con01Terminal.getName());

      targetFile.delete();
      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException | InterruptedException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testImportExportWithUsb() {
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }
      createUsbImpl(1, "30", 1);
      createUsbImpl(2, "31", 1);

      clickOn(GuiTestConstants.SWITCH_LIST_GRAPH_BTN);

      File targetFile = File.createTempFile("export", ".csv");
      when(fileDialogue.showSaveDialog(any())).thenReturn(targetFile);

      clickOn(GuiTestConstants.EXPORT_EXTENDER_INFO_BTN);
      // 等待导出完成
      Thread.sleep(1000);
      lookup("导出成功").query();
      clickOn(GuiTestConstants.BUTTON_CONFIRM);
      //
      when(fileDialogue.showOpenDialog(any())).thenReturn(targetFile);
      clickOn(GuiTestConstants.IMPORT_EXTENDER_INFO_BTN);
      // 等待导入完成
      Thread.sleep(1000);
      lookup("导入成功").query();
      clickOn(GuiTestConstants.BUTTON_CONFIRM);

      targetFile.delete();
      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException | InterruptedException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testImportError() {
    try {
      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      clickOn(GuiTestConstants.SWITCH_LIST_GRAPH_BTN);

      String[] errorFiles =
          new String[] {
            "extender_import_format_error.csv",
            "extender_import_name_error.csv",
            "extender_import_id_error.csv",
            "extender_import_id_repeat.csv",
            "extender_import_id_repeat2.csv",
            "extender_import_type_error.csv",
            "extender_import_not_exist_error.csv"
          };

      for (String errorFile : errorFiles) {
        System.out.println(errorFile);
        File targetFile =
            copyResourceToTemp("com/mc/tool/caesar/vpm/systemedit/" + errorFile, "hello", ".csv");
        //
        when(fileDialogue.showOpenDialog(any())).thenReturn(targetFile);
        clickOn(GuiTestConstants.IMPORT_EXTENDER_INFO_BTN);
        // 等待导入完成
        Thread.sleep(1000);
        targetWindow("失败").clickOn(GuiTestConstants.BUTTON_CONFIRM);

        targetFile.delete();
      }
      tempFile.delete();
    } catch (IOException | InterruptedException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }
}
