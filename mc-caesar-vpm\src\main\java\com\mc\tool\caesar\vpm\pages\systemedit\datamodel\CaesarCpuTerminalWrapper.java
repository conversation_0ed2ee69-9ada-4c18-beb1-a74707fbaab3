package com.mc.tool.caesar.vpm.pages.systemedit.datamodel;

import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import lombok.Getter;
import lombok.experimental.Delegate;

/**
 * .
 */
public class CaesarCpuTerminalWrapper implements VisualEditTerminal {

  @Delegate(types = {VisualEditTerminal.class, VisualEditNode.class})
  @Getter
  private CaesarCpuTerminal terminal;
  /** 信号源的输入索引，如果为0，则表示不区分，1表示第一个输入，如此类推. */
  @Getter private Integer index;

  public CaesarCpuTerminalWrapper(CaesarCpuTerminal item, int index) {
    this.terminal = item;
    this.index = index;
  }

  @Override
  public int hashCode() {
    return terminal.hashCode() ^ index.hashCode();
  }

  @Override
  public boolean equals(Object obj) {
    if (obj instanceof CaesarCpuTerminalWrapper) {
      CaesarCpuTerminalWrapper input = (CaesarCpuTerminalWrapper) obj;
      return input.terminal == terminal && input.index.equals(index);
    } else {
      return false;
    }
  }
}
