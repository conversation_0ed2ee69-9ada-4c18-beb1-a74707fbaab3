package com.mc.tool.caesar.vpm.pages.systemedit.view;

import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.utility.UndecoratedDialog;
import java.io.IOException;
import java.net.URL;
import java.util.Collections;
import java.util.ResourceBundle;
import java.util.function.UnaryOperator;
import javafx.beans.binding.BooleanBinding;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.control.ButtonBar;
import javafx.scene.control.ButtonType;
import javafx.scene.control.CheckBox;
import javafx.scene.control.Label;
import javafx.scene.control.TextField;
import javafx.scene.control.TextFormatter;
import javafx.scene.control.TextInputControl;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class OffsetSettingsDialog extends UndecoratedDialog<String>
    implements Initializable, ViewControllable {

  private CaesarDeviceController deviceController = null;
  private final CaesarCpuTerminal cpuTerminal;
  private final CpuData cpuData;

  @FXML private CheckBox offsetSettings;
  @FXML private Label totalResolutionLabel;
  @FXML private TextField deviceName;
  @FXML private TextField totalResolutionX;
  @FXML private TextField totalResolutionY;

  @FXML private Label resolutionLabel1;
  @FXML private Label resolutionOffsetLabelX1;
  @FXML private Label resolutionOffsetLabelY1;
  @FXML private TextField resolutionX1;
  @FXML private TextField resolutionY1;
  @FXML private TextField resolutionOffsetX1;
  @FXML private TextField resolutionOffsetY1;

  private final BooleanProperty dualHdmiProperty = new SimpleBooleanProperty();

  @FXML private Label resolutionLabel2;
  @FXML private Label resolutionOffsetLabelX2;
  @FXML private Label resolutionOffsetLabelY2;
  @FXML private TextField resolutionX2;
  @FXML private TextField resolutionY2;
  @FXML private TextField resolutionOffsetX2;
  @FXML private TextField resolutionOffsetY2;

  /** . */
  public OffsetSettingsDialog(CaesarCpuTerminal cpuTerminal) {
    this.cpuTerminal = cpuTerminal;
    this.cpuData = cpuTerminal.getCpuData();
    URL location =
        Thread.currentThread()
            .getContextClassLoader()
            .getResource("com/mc/tool/caesar/vpm/pages/systemedit/tx_offset.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    loader.setController(this);
    loader.setResources(ResourceBundle.getBundle("com.mc.tool.caesar.vpm.pages.systemedit.Bundle"));
    try {
      getDialogPane().setContent(loader.load());
    } catch (IOException ex) {
      log.warn("Can not load tx_offset.fxml", ex);
    }
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
    } else {
      log.warn("Error device controller type : {} !", deviceController.getClass().getName());
    }
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceController;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    initData();
    setTitle(resources.getString("txOffset.dialog.title"));
    setComponentDisabled();
    getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);
    setTextFieldFormatter();
    setResultConverter(
        (dialogButton) -> {
          ButtonBar.ButtonData data = dialogButton == null ? null : dialogButton.getButtonData();
          if (ButtonBar.ButtonData.OK_DONE == data) {
            deviceController.execute(
                () -> {
                  try {
                    cpuData
                        .getScreenOffsetConfig()
                        .setActiveOffset(offsetSettings.isSelected() ? 1 : 0);
                    cpuData
                        .getScreenOffsetConfig()
                        .setTotalResolutionX(Integer.parseInt(totalResolutionX.getText()));
                    cpuData
                        .getScreenOffsetConfig()
                        .setTotalResolutionY(Integer.parseInt(totalResolutionY.getText()));
                    if (offsetSettings.isSelected()) {
                      cpuData
                          .getScreenOffsetConfig()
                          .getScreenInfo1()
                          .setHorizontalResolution(Integer.parseInt(resolutionX1.getText()));
                      cpuData
                          .getScreenOffsetConfig()
                          .getScreenInfo1()
                          .setVerticalResolution(Integer.parseInt(resolutionY1.getText()));
                      cpuData
                          .getScreenOffsetConfig()
                          .getScreenInfo1()
                          .setOffsetX(Integer.parseInt(resolutionOffsetX1.getText()));
                      cpuData
                          .getScreenOffsetConfig()
                          .getScreenInfo1()
                          .setOffsetY(Integer.parseInt(resolutionOffsetY1.getText()));
                      if (cpuTerminal.canSeperate()) {
                        cpuData
                            .getScreenOffsetConfig()
                            .getScreenInfo2()
                            .setHorizontalResolution(Integer.parseInt(resolutionX2.getText()));
                        cpuData
                            .getScreenOffsetConfig()
                            .getScreenInfo2()
                            .setVerticalResolution(Integer.parseInt(resolutionY2.getText()));
                        cpuData
                            .getScreenOffsetConfig()
                            .getScreenInfo2()
                            .setOffsetX(Integer.parseInt(resolutionOffsetX2.getText()));
                        cpuData
                            .getScreenOffsetConfig()
                            .getScreenInfo2()
                            .setOffsetY(Integer.parseInt(resolutionOffsetY2.getText()));
                      }
                    }
                    deviceController.getDataModel().sendCpuData(Collections.singletonList(cpuData));
                  } catch (DeviceConnectionException | BusyException exception) {
                    log.error("Fail to send value!", exception);
                  }
                });
            return offsetSettings.isSelected()
                ? resources.getString("txOffset.activated")
                : resources.getString("txOffset.unactivated");
          }
          return null;
        });
  }

  private void setComponentDisabled() {
    BooleanBinding offsetSettingsBinding = offsetSettings.selectedProperty().not();
    totalResolutionLabel.disableProperty().bind(offsetSettingsBinding);
    deviceName.setDisable(true);
    totalResolutionX.disableProperty().bind(offsetSettingsBinding);
    totalResolutionY.disableProperty().bind(offsetSettingsBinding);

    resolutionLabel1.disableProperty().bind(offsetSettingsBinding);
    resolutionOffsetLabelX1.disableProperty().bind(offsetSettingsBinding);
    resolutionOffsetLabelY1.disableProperty().bind(offsetSettingsBinding);
    resolutionX1.disableProperty().bind(offsetSettingsBinding);
    resolutionY1.disableProperty().bind(offsetSettingsBinding);
    resolutionOffsetX1.disableProperty().bind(offsetSettingsBinding);
    resolutionOffsetY1.disableProperty().bind(offsetSettingsBinding);

    resolutionLabel2.disableProperty().bind(offsetSettingsBinding.or(dualHdmiProperty));
    resolutionOffsetLabelX2.disableProperty().bind(offsetSettingsBinding.or(dualHdmiProperty));
    resolutionOffsetLabelY2.disableProperty().bind(offsetSettingsBinding.or(dualHdmiProperty));
    resolutionX2.disableProperty().bind(offsetSettingsBinding.or(dualHdmiProperty));
    resolutionY2.disableProperty().bind(offsetSettingsBinding.or(dualHdmiProperty));
    resolutionOffsetX2.disableProperty().bind(offsetSettingsBinding.or(dualHdmiProperty));
    resolutionOffsetY2.disableProperty().bind(offsetSettingsBinding.or(dualHdmiProperty));
  }

  private void setTextFieldFormatter() {
    UnaryOperator<TextFormatter.Change> integerFilter =
        change -> {
          String input = change.getText();
          if (input.matches("[0-9]*")) {
            return change;
          }
          return null;
        };
    setTextFormatter(totalResolutionX, integerFilter);
    setTextFormatter(totalResolutionY, integerFilter);
    setTextFormatter(resolutionX1, integerFilter);
    setTextFormatter(resolutionY1, integerFilter);
    setTextFormatter(resolutionOffsetX1, integerFilter);
    setTextFormatter(resolutionOffsetY1, integerFilter);
    setTextFormatter(resolutionX2, integerFilter);
    setTextFormatter(resolutionY2, integerFilter);
    setTextFormatter(resolutionOffsetX2, integerFilter);
    setTextFormatter(resolutionOffsetY2, integerFilter);
  }

  private void setTextFormatter(
      TextInputControl textInputControl, UnaryOperator<TextFormatter.Change> integerFilter) {
    if (integerFilter instanceof TextFormatter) {
      textInputControl.setTextFormatter((TextFormatter<?>) integerFilter);
    }
  }

  private void initData() {
    offsetSettings.setSelected(cpuData.getScreenOffsetConfig().getActiveOffset() == 1);
    dualHdmiProperty.set(!cpuTerminal.canSeperate());
    deviceName.setText(cpuTerminal.getCpuData().getName());
    totalResolutionX.setText(String.valueOf(cpuData.getScreenOffsetConfig().getTotalResolutionX()));
    totalResolutionY.setText(String.valueOf(cpuData.getScreenOffsetConfig().getTotalResolutionY()));
    resolutionX1.setText(
        String.valueOf(cpuData.getScreenOffsetConfig().getScreenInfo1().getHorizontalResolution()));
    resolutionY1.setText(
        String.valueOf(cpuData.getScreenOffsetConfig().getScreenInfo1().getVerticalResolution()));
    resolutionOffsetX1.setText(
        String.valueOf(cpuData.getScreenOffsetConfig().getScreenInfo1().getOffsetX()));
    resolutionOffsetY1.setText(
        String.valueOf(cpuData.getScreenOffsetConfig().getScreenInfo1().getOffsetY()));
    resolutionX2.setText(
        String.valueOf(cpuData.getScreenOffsetConfig().getScreenInfo2().getHorizontalResolution()));
    resolutionY2.setText(
        String.valueOf(cpuData.getScreenOffsetConfig().getScreenInfo2().getVerticalResolution()));
    resolutionOffsetX2.setText(
        String.valueOf(cpuData.getScreenOffsetConfig().getScreenInfo2().getOffsetX()));
    resolutionOffsetY2.setText(
        String.valueOf(cpuData.getScreenOffsetConfig().getScreenInfo2().getOffsetY()));
  }
}
