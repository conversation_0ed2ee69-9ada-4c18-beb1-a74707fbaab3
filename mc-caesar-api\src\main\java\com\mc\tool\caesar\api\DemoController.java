package com.mc.tool.caesar.api;

import com.mc.tool.caesar.api.datamodel.ConfigData.IoMode;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.utils.CfgWriter;
import java.io.ByteArrayOutputStream;
import javafx.application.Platform;

/**
 * .
 */
public class DemoController extends CaesarController {

  public DemoController(CaesarSwitchDataModel switchDataModel) {
    super(switchDataModel);
  }

  @Override
  public void setUpdateOpen(byte level1, byte level2, byte level3, byte... bytes)
      throws DeviceConnectionException, ConfigException, BusyException {
    checkThreadState();
  }

  @Override
  public void setUpdateWrite(byte level1, byte level2, byte level3, int offset, byte[] data,
      boolean checksumEnabled) throws DeviceConnectionException, ConfigException, BusyException {
    checkThreadState();
  }

  @Override
  public void setUpdateClose(byte level1, byte level2, byte level3)
      throws DeviceConnectionException, ConfigException, BusyException {
    checkThreadState();
  }

  @Override
  public String getOsdMd5(byte level1, byte level2) {
    checkThreadState();
    return "i'm md5";
  }

  @Override
  public void setSystemFileOpen(int fileType)
      throws DeviceConnectionException, ConfigException, BusyException {
    checkThreadState();
  }

  @Override
  public void setSystemFileClose(int fileType)
      throws DeviceConnectionException, ConfigException, BusyException {
    checkThreadState();
  }

  @Override
  public byte[] readSystemFile(int fileType, int len)
      throws DeviceConnectionException, ConfigException, BusyException {
    checkThreadState();
    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    CfgWriter writer = new CfgWriter(baos);
    switchDataModel.getConfigData().writeData(writer, IoMode.Available);
    return baos.toByteArray();
  }

  @Override
  public void writeSystemFile(int fileType, byte[] data)
      throws DeviceConnectionException, ConfigException, BusyException {
    checkThreadState();
  }

  @Override
  public void resetVpSdCard(byte level1, byte level2)
      throws DeviceConnectionException, BusyException, ConfigException {
    checkThreadState();
  }

  @Override
  public void setVpSdCard(byte level1, byte level2, int offset, byte[] data,
      boolean checksumEnabled) throws DeviceConnectionException, BusyException, ConfigException {
    checkThreadState();
  }

  private void checkThreadState() {
    if (Platform.isFxApplicationThread()) {
      throw new IllegalStateException("Should not be in javafx thread!");
    }
  }
}
