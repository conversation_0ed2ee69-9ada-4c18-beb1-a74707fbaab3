package com.mc.tool.caesar.api;

import com.mc.tool.caesar.api.communication.IoController;
import com.mc.tool.caesar.api.exception.UnexpectedResponseException;
import java.io.ByteArrayOutputStream;
import java.io.EOFException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * .
 */
public final class CommunicationBuilder {

  private static final Logger LOG = Logger.getLogger(CommunicationBuilder.class.getName());
  //private static final int ESRS_HEADER_SIZE = 5;
  private static final boolean ENCRYPT_DATA = true;

  public static Req newRequestBuilder() {
    return new Impl();
  }

  /**
   * .
   */
  public interface Chk {

    void setChecksumEnabled(boolean paramBoolean);
  }

  /**
   * .
   */
  public interface Rec {

    boolean checkResponse(IoController.ReadController paramReadController,
        int paramInt) throws IOException;

    boolean checkAcknowledged(IoController.ReadController paramReadController)
        throws IOException;

    int readResponse(IoController.ReadController paramReadController)
        throws IOException;

    byte[] getResponse(IoController.ReadController paramReadController)
        throws IOException;
  }

  /**
   * .
   */
  public interface Tra {

    void transmit(IoController.WriteController paramWriteController)
        throws IOException;

    CommunicationBuilder.Rec transmit(
        IoController.ReadWriteController paramReadWriteController) throws IOException;
  }

  /**
   * .
   */
  public interface Add extends CommunicationBuilder.Tra, CommunicationBuilder.Chk {

    Add add1Byte(Number paramNumber);

    Add add2Byte(Number paramNumber);

    Add add4Byte(Number paramNumber);

    Add add(byte[] paramArrayOfByte);

    Add add(String paramString);
  }

  /**
   * .
   */
  public interface Req {

    CommunicationBuilder.Add setRequest(
        CaesarControllerConstants.Request paramRequest);
  }

  private static class Impl
      implements CommunicationBuilder.Req, CommunicationBuilder.Add, CommunicationBuilder.Rec {

    private List<Number> bodyValues = new ArrayList<>();
    private boolean checksumEnabled = false;
    private CaesarControllerConstants.Request request = null;

    @Override
    public Impl setRequest(CaesarControllerConstants.Request request) {
      this.request = request;
      return this;
    }

    @Override
    public Impl add1Byte(Number number) {
      this.bodyValues.add(number);
      return this;
    }

    @Override
    public Impl add2Byte(Number number) {
      this.bodyValues.add(0xFF & number.intValue());
      this.bodyValues.add(0xFF & number.intValue() >> 8);
      return this;
    }

    @Override
    public Impl add4Byte(Number number) {
      this.bodyValues.add(0xFF & number.intValue());
      this.bodyValues.add(0xFF & number.intValue() >> 8);
      this.bodyValues.add(0xFF & number.intValue() >> 16);
      this.bodyValues.add(0xFF & number.intValue() >> 24);
      return this;
    }

    @Override
    public Impl add(byte[] value) {
      for (byte b : value) {
        add1Byte(b);
      }
      return this;
    }

    @Override
    public Impl add(String string) {
      for (int idx = 0; idx < string.length(); idx++) {
        add1Byte((int) string.charAt(idx));
      }
      return this;
    }

    private void transmitImpl(IoController.WriteController controller) throws IOException {
      controller.write(getCommunique());
    }

    @Override
    public void transmit(IoController.WriteController controller) throws IOException {
      transmitImpl(controller);
    }

    @Override
    public Impl transmit(IoController.ReadWriteController controller) throws IOException {
      transmitImpl(controller);
      return this;
    }

    private byte[] getCommunique() {
      ByteArrayOutputStream baos = new ByteArrayOutputStream(CaesarControllerConstants.BUFFER_SIZE);

      baos.write(0xFF & this.request.getEscapeByte());
      baos.write(0xFF & this.request.getServerRequestByte());
      baos.write(0xFF & this.request.getByteValue());
      if (!this.bodyValues.isEmpty()) {
        int sizeLength = request.getRequestSizeFieldLength();
        int size = 3 + (this.checksumEnabled ? 2 : 0) + this.bodyValues.size() + sizeLength;
        for (int i = 0; i < sizeLength; i++) {
          baos.write(0xFF & (size >> (8 * i)));
        }

        for (Number number : this.bodyValues) {
          baos.write(0xFF & number.intValue());
        }
      }
      if (this.checksumEnabled) {
        int checksum = 0;

        byte[] data = baos.toByteArray();
        for (byte datum : data) {
          checksum += 0xFF & datum;
        }
        baos.write(0xFF & checksum);
        baos.write(0xFF & checksum >> 8);
      }
      if (ENCRYPT_DATA) {
        try {
          byte[] bytes = DkmProtocalCrypto.encrypt(baos.toByteArray());
          return bytes;
        } catch (RuntimeException ex) {
          throw ex;
        } catch (Exception exception) {
          return baos.toByteArray();
        }
      } else {
        return baos.toByteArray();
      }
    }

    @Override
    public boolean checkAcknowledged(IoController.ReadController controller) throws IOException {
      int readByte = this.readResponse(controller);
      if (readByte == CaesarControllerConstants.BIN_ESC && controller.available() >= 2) {
        readByte = controller.read();
        readByte = controller.read();
        if (controller.available() >= 2
            && (readByte = controller.read()) != CaesarControllerConstants.BIN_ESC) {
          int size = readByte;
          size += this.readResponse(controller) << 8;
          size -= 5;
          while (size > 0) {
            readByte = this.readResponse(controller);
            --size;
          }
        }
        if (controller.available() > 0) {
          readByte = controller.read();
        }
      }
      while (readByte != CaesarControllerConstants.BIN_ACK
          && readByte != CaesarControllerConstants.BIN_STX
          && readByte != CaesarControllerConstants.BIN_NAK
          && readByte != CaesarControllerConstants.BIN_BSY
          && controller.available() > 0) {
        readByte = controller.read();
      }
      if (CaesarControllerConstants.BIN_ACK == readByte) {
        return true;
      }
      if (CaesarControllerConstants.BIN_STX == readByte) {
        while (controller.available() > 0) {
          controller.read();
        }
        return true;
      }
      if (CaesarControllerConstants.BIN_NAK == readByte) {
        throw new UnexpectedResponseException(CaesarControllerConstants.BIN_ACK,
            CaesarControllerConstants.BIN_NAK);
      }
      if (CaesarControllerConstants.BIN_BSY == readByte) {
        throw new UnexpectedResponseException(CaesarControllerConstants.BIN_ACK,
            CaesarControllerConstants.BIN_BSY);
      }
      while (controller.available() > 0) {
        controller.read();
      }
      readByte = this.readResponse(controller);
      if (CaesarControllerConstants.BIN_ACK == readByte) {
        return true;
      }
      throw new UnexpectedResponseException(CaesarControllerConstants.BIN_ACK, readByte);
    }

    @Override
    public boolean checkResponse(IoController.ReadController controller, int expectedResponse)
        throws IOException {
      int readByte = readResponse(controller);
      if (expectedResponse == readByte) {
        return true;
      }
      if (CaesarControllerConstants.BIN_BSY == readByte) {
        throw new UnexpectedResponseException(expectedResponse, CaesarControllerConstants.BIN_BSY);
      }
      while (controller.available() > 0) {
        controller.read();
      }
      throw new UnexpectedResponseException(expectedResponse, readByte);
    }

    @Override
    public int readResponse(IoController.ReadController controller) throws IOException {
      int data = controller.read();
      if (-1 == data) {
        throw new EOFException();
      }
      return data;
    }

    @Override
    public byte[] getResponse(IoController.ReadController controller)
        throws IOException {
      checkResponse(controller, this.request.getEscapeByte());

      int readByte = readResponse(controller);
      while (this.request.getServerResponseByte() != readByte) {
        boolean foundNextResponseByte = false;
        while (controller.available() > 0) {
          readByte = controller.read();
          if (this.request.getEscapeByte() == readByte) {
            readByte = readResponse(controller);
            if (readByte == this.request.getServerResponseByte()) {
              foundNextResponseByte = true;
            }
          }
        }
        if (!foundNextResponseByte) {
          checkResponse(controller, this.request.getEscapeByte());
          readByte = readResponse(controller);
        }
      }
      checkResponse(controller, this.request.getByteValue());
      int count = 0;
      count += 3;
      int size;
      if (this.request.isReponse() == CaesarControllerConstants.Request.Response.DATA) {
        if (ENCRYPT_DATA) {
          int lenFieldSize = 4;
          int sz = 0;
          for (int i = 0; i < lenFieldSize; i++) {
            sz += readResponse(controller) << (i * 8);
          }
          count += lenFieldSize;

          size = sz;
        } else {
          int sz = 0;
          for (int i = 0; i < request.getResponseSizeFieldLength(); i++) {
            sz += readResponse(controller) << (i * 8);
          }
          count += request.getResponseSizeFieldLength();

          size = sz;
        }
      } else {
        size = CaesarControllerConstants.BUFFER_SIZE;
      }
      ByteArrayOutputStream baos = new ByteArrayOutputStream(size - count);
      while (count < size) {
        int available;
        while ((available = controller.available()) == 0) {
          try {
            Thread.sleep(10L);
          } catch (InterruptedException ex) {
            LOG.log(Level.WARNING, "Sleep is interrupted!", ex);
          }
        }
        if (available <= size - count) {
          byte[] data = new byte[available];
          controller.read(data);
          baos.write(data);
          count += available;
        } else {
          baos.write(readResponse(controller));
          count++;
        }
      }
      if (ENCRYPT_DATA) {
        try {
          byte[] decrypted = DkmProtocalCrypto.decryptWithoutHead(baos.toByteArray());
          return Arrays
              .copyOfRange(decrypted, request.getResponseSizeFieldLength(), decrypted.length);
        } catch (Exception exception) {
          throw new IOException(exception.getMessage());
        }
      } else {
        return baos.toByteArray();
      }
    }

    @Override
    public void setChecksumEnabled(boolean enabled) {
      this.checksumEnabled = enabled;
    }
  }
}

