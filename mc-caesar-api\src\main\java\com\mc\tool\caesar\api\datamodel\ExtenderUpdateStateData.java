package com.mc.tool.caesar.api.datamodel;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * .
 */
public class ExtenderUpdateStateData {

  private Map<Integer, Step> steps = new HashMap<Integer, Step>();
  private int port;
  private int module;

  public ExtenderUpdateStateData(int port, int module) {
    this.port = port;
    this.module = module;
  }

  public void addStep(int id, Step step) {
    this.steps.put(Integer.valueOf(id), step);
  }

  public Step getStep(int id) {
    return this.steps.get(Integer.valueOf(id));
  }

  public int getModule() {
    return this.module;
  }

  public int getPort() {
    return this.port;
  }

  /**
   * .
   */
  public static final class Step {

    private int index;
    private int type;
    private LocalDateTime date;
    private String message;

    /**
     * .
     */
    public Step(int index, int type, LocalDateTime date, String message) {
      this.index = index;
      this.type = type;
      this.date = date;
      this.message = message;
    }

    public int getIndex() {
      return this.index;
    }

    public LocalDateTime getDate() {
      return this.date;
    }

    public String getMessage() {
      return this.message;
    }

    public int getType() {
      return this.type;
    }
  }
}

