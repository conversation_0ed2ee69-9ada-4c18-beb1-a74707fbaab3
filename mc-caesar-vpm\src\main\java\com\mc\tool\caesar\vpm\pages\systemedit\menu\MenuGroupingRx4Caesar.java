package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.util.stream.Collectors;

/**
 * .
 */
public class MenuGroupingRx4Caesar extends MenuQuickCreate {

  /** 快速创建RX分组. */
  public MenuGroupingRx4Caesar(SystemEditControllable controllable) {
    super(controllable);
    this.setText(Bundle.NbBundle.getMessage("menu.grouping.rx"));
  }

  @Override
  protected void initTerminal() {
    nodes =
        controllable.getModel().getAllTerminals().stream()
            .filter(VisualEditTerminal::isRx)
            .filter(VisualEditTerminal::isOnline)
            .filter(item -> item.getParent() instanceof CaesarMatrix)
            .collect(Collectors.toList());
  }

  @Override
  protected void handleAction() {
    dialog
        .showAndWait()
        .ifPresent(
            res -> {
              if (!res.isEmpty()) {
                String groupName = nameField.getText();
                controllable.addGroup(
                    groupName, VisualEditGroup.class, res.toArray(new VisualEditNode[0]));
              }
            });
  }

  @Override
  protected void initBundle() {
    groupNameLabel.setText(Bundle.NbBundle.getMessage("menu.grouping.rx.name"));
    dialog.setTitle(Bundle.NbBundle.getMessage("menu.grouping.rx"));
  }

  @Override
  protected void setNameFieldText() {
    nameField.setText("Group");
  }
}
