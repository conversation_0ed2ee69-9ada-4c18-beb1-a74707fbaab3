package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;
import java.util.Arrays;

/**
 * .
 *
 * @brief 自动id配置，用于配置连接新的cpu与con时分派的id
 */
public final class AutoIdData extends AbstractData {

  public static final String PROPERTY_BASE = "SystemConfigData.AutoIdData.";
  public static final String FIELD_AUTO_ID = "AutoId";
  public static final String PROPERTY_AUTO_ID = "SystemConfigData.AutoIdData.AutoId";
  // System Data 下的 Automatic 窗口标题下的Enable Auto Config
  public static final String PROPERTY_FORCE_BITS_AUTO_CONFIG =
      "SystemConfigData.AutoIdData.ForceBits.AutoConfig";
  // System Data 下的 Automatic 窗口标题下的 ID Real CPU Device
  public static final String PROPERTY_AUTO_ID_REAL_CPU_ID =
      "SystemConfigData.AutoIdData.AutoId.RealCpuId";
  // System Data 下的 Automatic 窗口标题下的 ID Virtual CPU Device
  public static final String PROPERTY_AUTO_ID_VIRTUAL_CPU_ID =
      "SystemConfigData.AutoIdData.AutoId.VirtualCpuId";
  // System Data 下的 Automatic 窗口标题下的ID Real CON Device
  public static final String PROPERTY_AUTO_ID_REAL_CON_ID =
      "SystemConfigData.AutoIdData.AutoId.RealConId";
  // System Data 下的 Automatic 窗口标题下的 ID Virtual CON Device
  public static final String PROPERTY_AUTO_ID_VIRTUAL_CON_ID =
      "SystemConfigData.AutoIdData.AutoId.VirtualConId";
  private final SystemConfigData systemConfigData;
  @Expose
  private final int[] autoId = createArrayInt(4); // 4个id分别对应
  // real cpu
  // virtual cpu
  // real con
  // virtual con

  /**
   * .
   */
  public AutoIdData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager,
      String fqn, SystemConfigData systemConfigData) {
    super(pcs, configDataManager, -1, fqn);
    this.systemConfigData = systemConfigData;

    initCommitRollback();
  }

  @Override
  public void initDefaults() {
    setAutoConfig(true);
    setAutoId(0, 1001);
    setAutoId(1, 2001);
    setAutoId(2, 3001);
    setAutoId(3, 4001);
  }

  public boolean isAutoConfig() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.AUTOCONFIG);
  }

  /**
   * .
   */
  public void setAutoConfig(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(
        Utilities.setBits(this.systemConfigData.getForceBits(), enabled, new int[]{8}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public int[] getAutoIdArray() {
    return Arrays.copyOf(this.autoId, this.autoId.length);
  }

  public int getAutoId(int idx) {
    return this.autoId[idx];
  }

  /**
   * .
   */
  public void setAutoId(int idx, int autoId) {
    int oldValue = this.autoId[idx];
    this.autoId[idx] = autoId;
    firePropertyChange(AutoIdData.PROPERTY_AUTO_ID, Integer.valueOf(oldValue),
        Integer.valueOf(this.autoId[idx]), new int[]{idx});
  }

  public int getRealCpuId() {
    return getAutoId(0);
  }

  /**
   * .
   */
  public void setRealCpuId(int autoId) {
    int oldValue = getAutoId(0);
    setAutoId(0, autoId);
    firePropertyChange(AutoIdData.PROPERTY_AUTO_ID_REAL_CPU_ID, Integer.valueOf(oldValue),
        Integer.valueOf(autoId), new int[0]);
  }

  public int getVirtualCpuId() {
    return getAutoId(1);
  }

  /**
   * .
   */
  public void setVirtualCpuId(int autoId) {
    int oldValue = getAutoId(1);
    setAutoId(1, autoId);
    firePropertyChange(AutoIdData.PROPERTY_AUTO_ID_VIRTUAL_CPU_ID, Integer.valueOf(oldValue),
        Integer.valueOf(autoId), new int[0]);
  }

  public int getRealConId() {
    return getAutoId(2);
  }

  /**
   * .
   */
  public void setRealConId(int autoId) {
    int oldValue = getAutoId(2);
    setAutoId(2, autoId);
    firePropertyChange(AutoIdData.PROPERTY_AUTO_ID_REAL_CON_ID, Integer.valueOf(oldValue),
        Integer.valueOf(autoId), new int[0]);
  }

  public int getVirtualConId() {
    return getAutoId(3);
  }

  /**
   * .
   */
  public void setVirtualConId(int autoId) {
    int oldValue = getAutoId(3);
    setAutoId(3, autoId);
    firePropertyChange(AutoIdData.PROPERTY_AUTO_ID_VIRTUAL_CON_ID, Integer.valueOf(oldValue),
        Integer.valueOf(autoId), new int[0]);
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (AutoIdData.PROPERTY_AUTO_ID.equals(propertyName)) {
      setAutoId(indizes[0], Integer.class.cast(value).intValue());
    }
  }
}

