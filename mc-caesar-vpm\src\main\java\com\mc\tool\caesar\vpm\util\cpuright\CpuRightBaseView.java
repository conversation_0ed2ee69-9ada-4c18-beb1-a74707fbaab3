package com.mc.tool.caesar.vpm.util.cpuright;

import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.datamodel.AccessControlObject;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.control.SearchField;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.ViewControllable;
import java.beans.PropertyChangeListener;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;
import java.util.function.Predicate;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.beans.value.ObservableValue;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredList;
import javafx.event.ActionEvent;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Label;
import javafx.scene.control.MenuItem;
import javafx.scene.control.SelectionMode;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableColumn.CellDataFeatures;
import javafx.scene.control.TableRow;
import javafx.scene.control.TableSelectionModel;
import javafx.scene.control.TableView;
import javafx.scene.control.TableView.TableViewSelectionModel;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;
import javafx.scene.layout.VBox;
import javafx.util.Callback;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public abstract class CpuRightBaseView<T extends DataObject & AccessControlObject> extends VBox
    implements Initializable, ViewControllable {
  protected CaesarDeviceController deviceController = null;

  @FXML protected TableColumn<CpuData, Number> fullIdColumn;
  @FXML protected TableColumn<CpuData, String> fullNameColumn;
  @FXML protected TableColumn<CpuData, Number> videoIdColumn;
  @FXML protected TableColumn<CpuData, String> videoNameColumn;
  @FXML protected TableColumn<CpuData, Number> noIdColumn;
  @FXML protected TableColumn<CpuData, String> noNameColumn;

  @FXML protected TableView<CpuData> fullTable;
  @FXML protected TableView<CpuData> videoTable;
  @FXML protected TableView<CpuData> noTable;

  @FXML protected TableColumn<T, Number> sourceIdColumn;
  @FXML protected TableColumn<T, String> sourceNameColumn;
  @Getter @FXML protected TableView<T> sourceList;

  @FXML protected Label sourceListTitleLabel;
  @FXML protected Label accessBlockTitleLabel;

  @FXML protected Button applyBtn;
  @FXML protected Button cancelBtn;

  @FXML protected VBox searchFieldBox;

  protected SearchField<T> searchField;

  protected ObservableList<CpuData> fullAccessList = FXCollections.observableArrayList();
  protected ObservableList<CpuData> videoAccessList = FXCollections.observableArrayList();
  protected ObservableList<CpuData> noAccessList = FXCollections.observableArrayList();

  protected StringProperty sourceListTitle = new SimpleStringProperty();
  protected StringProperty accessBlockTitle = new SimpleStringProperty();

  protected ObjectProperty<AccessControlObject> selectedItem = new SimpleObjectProperty<>();

  protected CustomPropertyChangeSupport pcs = new CustomPropertyChangeSupport(this);

  protected ObservableList<T> sourceRawList = FXCollections.observableArrayList();
  protected FilteredList<T> sourceFilteredList = new FilteredList<>(sourceRawList);

  protected WeakAdapter weakAdapter = new WeakAdapter();

  protected BooleanProperty editableProperty = new SimpleBooleanProperty(true);

  protected BooleanProperty updatingProperty = new SimpleBooleanProperty(false);

  /** Constructor. */
  public CpuRightBaseView() {
    URL location =
        Thread.currentThread()
            .getContextClassLoader()
            .getResource("com/mc/tool/caesar/vpm/util/cpuright/cpuright_view.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    loader.setRoot(this);
    loader.setController(this);
    loader.setResources(ResourceBundle.getBundle("com.mc.tool.caesar.vpm.i18n.common"));
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load cpuright_view.fxml", exception);
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {

    // 检索框
    searchField = new SearchField<>();
    searchFieldBox.getChildren().add(1, searchField);

    applyBtn.setDisable(true);
    cancelBtn.setDisable(true);

    fullTable.setItems(fullAccessList);
    videoTable.setItems(videoAccessList);
    noTable.setItems(noAccessList);
    fullTable.setRowFactory(new AccessRowFactory(selectedItem, AccessType.FULL, editableProperty));
    videoTable.setRowFactory(
        new AccessRowFactory(selectedItem, AccessType.VIDEO, editableProperty));
    noTable.setRowFactory(new AccessRowFactory(selectedItem, AccessType.NO, editableProperty));
    // 设置列的值
    Callback<CellDataFeatures<CpuData, String>, ObservableValue<String>> nameValueFactory =
        (feat) -> feat.getValue().getNameProperty();
    fullNameColumn.setCellValueFactory(nameValueFactory);
    videoNameColumn.setCellValueFactory(nameValueFactory);
    noNameColumn.setCellValueFactory(nameValueFactory);

    Callback<CellDataFeatures<CpuData, Number>, ObservableValue<Number>> idValueFactory =
        (feat) -> feat.getValue().getIdProperty();
    fullIdColumn.setCellValueFactory(idValueFactory);
    videoIdColumn.setCellValueFactory(idValueFactory);
    noIdColumn.setCellValueFactory(idValueFactory);

    // 设置标题
    sourceListTitleLabel.textProperty().bind(sourceListTitle);
    accessBlockTitleLabel.textProperty().bind(accessBlockTitle);
    // 监听变化
    getTableSelectionModel().setSelectionMode(SelectionMode.SINGLE);
    getTableSelectionModel()
        .selectedItemProperty()
        .addListener(weakAdapter.wrap((observable, oldValue, newValue) -> updateSelection()));

    // 监听按键
    fullTable.setOnKeyPressed(
        weakAdapter.wrap(
            new AccessKeyEventHandler(
                selectedItem, AccessType.FULL, fullTable.getSelectionModel(), editableProperty)));
    videoTable.setOnKeyPressed(
        weakAdapter.wrap(
            new AccessKeyEventHandler(
                selectedItem, AccessType.VIDEO, videoTable.getSelectionModel(), editableProperty)));
    noTable.setOnKeyPressed(
        weakAdapter.wrap(
            new AccessKeyEventHandler(
                selectedItem, AccessType.NO, noTable.getSelectionModel(), editableProperty)));
    // 应用与取消
    selectedItem.addListener(
        weakAdapter.wrap(
            (obs, oldVal, newval) -> {
              if (newval == null) {
                applyBtn.disableProperty().unbind();
                applyBtn.setDisable(true);
                cancelBtn.disableProperty().unbind();
                cancelBtn.setDisable(true);
              } else if (newval instanceof TempAccessControlObject) {
                applyBtn
                    .disableProperty()
                    .bind(
                        ((TempAccessControlObject) selectedItem.get())
                            .isChangeProperty()
                            .not()
                            .or(updatingProperty));
                cancelBtn
                    .disableProperty()
                    .bind(
                        ((TempAccessControlObject) selectedItem.get())
                            .isChangeProperty()
                            .not()
                            .or(updatingProperty));
              }
            }));

    sourceList.setItems(sourceFilteredList);

    searchField
        .filterPredicateProperty()
        .addListener(
            weakAdapter.wrap(
                (obs, oldVal, newVal) ->
                    sourceFilteredList.setPredicate(
                        searchField.getFilterPredicate().and(getSourcePrediate()))));
    sourceFilteredList.setPredicate(getSourcePrediate());
  }

  protected void updateSelection() {
    if (getTableSelectionModel().getSelectedItem() == null) {
      selectedItem.set(null);
    } else if (selectedItem.get() == null
        || selectedItem.get() instanceof TempAccessControlObject
            && getTableSelectionModel().getSelectedItem()
                != ((TempAccessControlObject) selectedItem.get()).getWrappedObeject()) {
      TempAccessControlObject temp =
          new TempAccessControlObject(
              pcs,
              deviceController.getDataModel().getConfigDataManager(),
              getTableSelectionModel().getSelectedItem());
      selectedItem.set(temp);
    }

    if (selectedItem.get() == null) {
      videoAccessList.clear();
      noAccessList.clear();
      fullAccessList.clear();
    } else {
      AccessControlObject object = selectedItem.get();
      List<CpuData> videoCpu = new ArrayList<>(object.getVideoAccessCpuDatas());
      videoCpu.removeAll(object.getNoAccessCpuDatas());
      videoCpu.retainAll(deviceController.getDataModel().getConfigDataManager().getActiveCpus());
      videoAccessList.setAll(videoCpu);

      List<CpuData> noCpu = new ArrayList<>(object.getNoAccessCpuDatas());
      noCpu.retainAll(deviceController.getDataModel().getConfigDataManager().getActiveCpus());
      noAccessList.setAll(noCpu);

      fullAccessList.setAll(
          Utilities.getFullAccessCpus(
              object, deviceController.getDataModel().getConfigDataManager()));
    }
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
      // 监听数据的变化
      this.deviceController
          .getDataModel()
          .addPropertyChangeListener(
              new String[] {
                AccessControlObject.PROPERTY_NO_ACCESS,
                AccessControlObject.PROPERTY_VIDEO_ACCESS,
                CpuData.PROPERTY_NAME,
                CpuData.PROPERTY_ID,
                CpuData.PROPERTY_STATUS
              },
              weakAdapter.wrap(
                  (PropertyChangeListener)
                      evt -> PlatformUtility.runInFxThread(this::updateSelection)));
      pcs.addPropertyChangeListener(
          new String[] {
            AccessControlObject.PROPERTY_NO_ACCESS, AccessControlObject.PROPERTY_VIDEO_ACCESS
          },
          weakAdapter.wrap(
              (PropertyChangeListener)
                  evt -> PlatformUtility.runInFxThread(this::updateSelection)));

      updateEditableStatus();
    }
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceController;
  }

  /**
   * 选择源.
   *
   * @param item 源
   */
  public void selectSourceItem(T item) {
    searchField.setSearchText("");
    getSourceList().requestFocus();
    getSourceList().scrollTo(item);
    getSourceList().getSelectionModel().select(item);
  }

  @FXML
  protected void onApply(ActionEvent event) {
    AccessControlObject object = selectedItem.get();
    if (object instanceof TempAccessControlObject) {
      TempAccessControlObject tempAccessControlObject = (TempAccessControlObject) object;
      updatingProperty.set(true);
      deviceController
          .submitAsync(
              () -> {
                tempAccessControlObject.commitData();
                onSendNewAccess(tempAccessControlObject.getWrappedObeject());
              })
          .whenComplete(
              (result, throwable) -> {
                if (throwable != null) {
                  log.warn("Failed to update access control object", throwable);
                }
                PlatformUtility.runInFxThread(
                    () -> {
                      updatingProperty.set(false);
                      tempAccessControlObject.commitStatus();
                    });
              });
    }
  }

  @FXML
  protected void onCancel(ActionEvent event) {
    AccessControlObject object = selectedItem.get();
    if (object instanceof TempAccessControlObject) {
      TempAccessControlObject tempAccessControlObject = (TempAccessControlObject) object;
      tempAccessControlObject.cancel();
    }
  }

  @Override
  public void reload() {
    AccessControlObject object = selectedItem.get();
    if (object instanceof TempAccessControlObject) {
      TempAccessControlObject tempAccessControlObject = (TempAccessControlObject) object;
      tempAccessControlObject.cancel();
    }
  }

  protected abstract void onSendNewAccess(AccessControlObject item);

  protected TableViewSelectionModel<T> getTableSelectionModel() {
    return sourceList.getSelectionModel();
  }

  enum AccessType {
    FULL,
    VIDEO,
    NO
  }

  protected Predicate<T> getSourcePrediate() {
    return (item) -> true;
  }

  public abstract void updateEditableStatus();

  static class AccessKeyEventHandler implements EventHandler<KeyEvent> {
    private final ObjectProperty<AccessControlObject> selectionModel;
    private final AccessType type;
    private final TableSelectionModel<CpuData> cpuSelection;
    private final BooleanProperty editableProperty;

    public AccessKeyEventHandler(
        ObjectProperty<AccessControlObject> selectionModel,
        AccessType type,
        TableSelectionModel<CpuData> cpuSelection,
        BooleanProperty editableProperty) {
      this.selectionModel = selectionModel;
      this.type = type;
      this.cpuSelection = cpuSelection;
      this.editableProperty = editableProperty;
    }

    @Override
    public void handle(KeyEvent event) {
      if (!editableProperty.get()) {
        return;
      }
      int index = cpuSelection.getSelectedIndex();
      if (event.getCode().equals(KeyCode.F) && type != AccessType.FULL) {
        FullAccessMenu.onAction(selectionModel.get(), cpuSelection.getSelectedItem());
      } else if (event.getCode().equals(KeyCode.V) && type != AccessType.VIDEO) {
        VideoAccessMenu.onAction(selectionModel.get(), cpuSelection.getSelectedItem());
      } else if (event.getCode().equals(KeyCode.N) && type != AccessType.NO) {
        NoAccessMenu.onAction(selectionModel.get(), cpuSelection.getSelectedItem());
      } else {
        return;
      }
      cpuSelection.select(index > 0 ? index - 1 : 0);
    }
  }

  static class AccessRowFactory implements Callback<TableView<CpuData>, TableRow<CpuData>> {
    private final ObjectProperty<AccessControlObject> selectionModel;
    private final AccessType type;
    private final BooleanProperty editableProperty;

    public AccessRowFactory(
        ObjectProperty<AccessControlObject> selectionModel,
        AccessType type,
        BooleanProperty editableProperty) {
      this.selectionModel = selectionModel;
      this.type = type;
      this.editableProperty = editableProperty;
    }

    @Override
    public TableRow<CpuData> call(TableView<CpuData> param) {
      final TableRow<CpuData> row = new TableRow<>();
      ContextMenu menu = new ContextMenu();
      switch (type) {
        case FULL:
          menu.getItems().add(new VideoAccessMenu(selectionModel, row));
          menu.getItems().add(new NoAccessMenu(selectionModel, row));
          break;
        case VIDEO:
          menu.getItems().add(new FullAccessMenu(selectionModel, row));
          menu.getItems().add(new NoAccessMenu(selectionModel, row));
          break;
        case NO:
          menu.getItems().add(new FullAccessMenu(selectionModel, row));
          menu.getItems().add(new VideoAccessMenu(selectionModel, row));
          break;
        default:
          break;
      }

      for (MenuItem item : menu.getItems()) {
        item.disableProperty().bind(editableProperty.not());
      }
      row.contextMenuProperty().set(menu);
      return row;
    }
  }

  static class FullAccessMenu extends MenuItem {

    public FullAccessMenu(
        ObjectProperty<AccessControlObject> selectionModel, TableRow<CpuData> cpuRow) {
      super(CaesarI18nCommonResource.getString("cpu_right.menu.full"));
      setOnAction((event) -> onAction(selectionModel.get(), cpuRow.getItem()));
    }

    public static void onAction(AccessControlObject object, CpuData cpuData) {
      if (object == null || cpuData == null) {
        return;
      }
      object.setNoAccess(cpuData, false);
      object.setVideoAccess(cpuData, false);
    }
  }

  static class VideoAccessMenu extends MenuItem {

    public VideoAccessMenu(
        ObjectProperty<AccessControlObject> selectionModel, TableRow<CpuData> cpuRow) {
      super(CaesarI18nCommonResource.getString("cpu_right.menu.video"));
      setOnAction((event) -> onAction(selectionModel.get(), cpuRow.getItem()));
    }

    public static void onAction(AccessControlObject object, CpuData cpuData) {
      if (object == null || cpuData == null) {
        return;
      }
      object.setNoAccess(cpuData, false);
      object.setVideoAccess(cpuData, true);
    }
  }

  static class NoAccessMenu extends MenuItem {

    public NoAccessMenu(
        ObjectProperty<AccessControlObject> selectionModel, TableRow<CpuData> cpuRow) {
      super(CaesarI18nCommonResource.getString("cpu_right.menu.no"));
      setOnAction((event) -> onAction(selectionModel.get(), cpuRow.getItem()));
    }

    public static void onAction(AccessControlObject object, CpuData cpuData) {
      if (object == null || cpuData == null) {
        return;
      }
      object.setNoAccess(cpuData, true);
      object.setVideoAccess(cpuData, false);
    }
  }
}
