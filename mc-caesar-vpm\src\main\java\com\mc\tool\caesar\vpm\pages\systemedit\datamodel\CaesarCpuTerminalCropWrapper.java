package com.mc.tool.caesar.vpm.pages.systemedit.datamodel;

import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import lombok.Getter;
import lombok.experimental.Delegate;

/**
 * .
 */
public class CaesarCpuTerminalCropWrapper implements VisualEditTerminal {

  @Delegate(types = {VisualEditTerminal.class})
  @Getter
  private CaesarCpuTerminalWrapper terminalWrapper;

  @Getter
  private final int sourceCropIndex;

  public CaesarCpuTerminalCropWrapper(CaesarCpuTerminalWrapper terminalWrapper, int sourceCropIndex) {
    this.terminalWrapper = terminalWrapper;
    this.sourceCropIndex = sourceCropIndex;
  }

  @Override
  public int hashCode() {
    return terminalWrapper.hashCode() ^ sourceCropIndex;
  }

  @Override
  public boolean equals(Object obj) {
    if (obj instanceof CaesarCpuTerminalCropWrapper) {
      CaesarCpuTerminalCropWrapper input = (CaesarCpuTerminalCropWrapper) obj;
      return input.terminalWrapper.equals(terminalWrapper) && input.sourceCropIndex == sourceCropIndex;
    } else {
      return false;
    }
  }
}
