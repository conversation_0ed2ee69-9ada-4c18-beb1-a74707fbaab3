package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.Version;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.utils.CfgError;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;
import java.util.logging.Level;
import java.util.logging.Logger;
import lombok.Getter;

/**
 * .
 */
public final class ConfigMetaData extends AbstractData implements CaesarCommunicatable {

  private static final Logger LOG = Logger.getLogger(ConfigMetaData.class.getName());
  public static final String PROPERTY_BASE = "ConfigMetaData.";
  public static final String FIELD_VERSION = "Version";
  public static final String PROPERTY_VERSION = "ConfigMetaData.Version";
  public static final String FIELD_SYSTEM_SIZE = "SystemSize";
  public static final String PROPERTY_SYSTEM_SIZE = "ConfigMetaData.SystemSize";
  public static final String FIELD_CPU_SIZE = "CpuSize";
  public static final String PROPERTY_CPU_SIZE = "ConfigMetaData.CpuSize";
  public static final String FIELD_CPU_COUNT = "CpuCount";
  public static final String PROPERTY_CPU_COUNT = "ConfigMetaData.CpuCount";
  public static final String FIELD_CPU_COUNT_ACTIVE = "CpuCountActive";
  public static final String PROPERTY_CPU_COUNT_ACTIVE = "ConfigMetaData.CpuCountActive";
  public static final String FIELD_CONSOLE_SIZE = "ConsoleSize";
  public static final String PROPERTY_CONSOLE_SIZE = "ConfigMetaData.ConsoleSize";
  public static final String FIELD_CONSOLE_COUNT = "ConsoleCount";
  public static final String PROPERTY_CONSOLE_COUNT = "ConfigMetaData.ConsoleCount";
  public static final String FIELD_CONSOLE_COUNT_ACTIVE = "ConsoleCountActive";
  public static final String PROPERTY_CONSOLE_COUNT_ACTIVE = "ConfigMetaData.ConsoleCountActive";

  public static final String PROPERTY_VPCONSOLE_SIZE = "ConfigMetaData.VpconsoleSize";
  public static final String PROPERTY_VPCONSOLE_COUNT = "ConfigMetaData.VpconsoleCount";
  public static final String PROPERTY_VPCONSOLE_COUNT_ACTIVE =
      "ConfigMetaData.VpconsoleCountActive";

  public static final String FIELD_EXTENDER_SIZE = "ExtenderSize";
  public static final String PROPERTY_EXTENDER_SIZE = "ConfigMetaData.ExtenderSize";
  public static final String FIELD_EXTENDER_COUNT = "ExtenderCount";
  public static final String PROPERTY_EXTENDER_COUNT = "ConfigMetaData.ExtenderCount";
  public static final String FIELD_EXTENDER_COUNT_ACTIVE = "ExtenderCountActive";
  public static final String PROPERTY_EXTENDER_COUNT_ACTIVE = "ConfigMetaData.ExtenderCountActive";
  public static final String FIELD_USER_SIZE = "UserSize";
  public static final String PROPERTY_USER_SIZE = "ConfigMetaData.UserSize";
  public static final String FIELD_USER_COUNT = "UserCount";
  public static final String PROPERTY_USER_COUNT = "ConfigMetaData.UserCount";
  public static final String FIELD_USER_COUNT_ACTIVE = "UserCountActive";
  public static final String PROPERTY_USER_COUNT_ACTIVE = "ConfigMetaData.UserCountActive";

  public static final String PROPERTY_USER_GROUP_SIZE = PROPERTY_BASE + "UserGroupSize";
  public static final String PROPERTY_USER_GROUP_COUNT = PROPERTY_BASE + "UserGroupCount";
  public static final String PROPERTY_USER_GROUP_ACTIVE = PROPERTY_BASE + "UserGroupActive";

  public static final String PROPERTY_MULTI_SCREEN_SIZE = PROPERTY_BASE + "MultiScreenSize";
  public static final String PROPERTY_MULTI_SCREEN_COUNT = PROPERTY_BASE + "MultiScreenCount";
  public static final String PROPERTY_MULTI_SCREEN_ACTIVE = PROPERTY_BASE + "MultiScreenActive";

  public static final String FIELD_FUNCTION_KEY_SIZE = "FunctionKeySize";
  public static final String PROPERTY_FUNCTION_KEY_SIZE = "ConfigMetaData.FunctionKeySize";
  public static final String FIELD_FUNCTION_KEY_COUNT = "FunctionKeyCount";
  public static final String PROPERTY_FUNCTION_KEY_COUNT = "ConfigMetaData.FunctionKeyCount";
  public static final String FIELD_FUNCTION_KEY_COUNT_ACTIVE = "FunctionKeyCountActive";
  public static final String PROPERTY_FUNCTION_KEY_COUNT_ACTIVE =
      "ConfigMetaData.FunctionKeyCountActive";
  public static final String FIELD_PORT_SIZE = "PortSize";
  public static final String PROPERTY_PORT_SIZE = "ConfigMetaData.PortSize";
  public static final String FIELD_PORT_COUNT = "PortCount";
  public static final String PROPERTY_PORT_COUNT = "ConfigMetaData.PortCount";
  public static final String FIELD_PORT_COUNT_ACTIVE = "PortCountActive";
  public static final String PROPERTY_PORT_COUNT_ACTIVE = "ConfigMetaData.PortCountActive";
  public static final String FIELD_MATRIX_SIZE = "MatrixSize";
  public static final String PROPERTY_MATRIX_SIZE = "ConfigMetaData.MatrixSize";
  public static final String FIELD_MATRIX_COUNT = "MatrixCount";
  public static final String PROPERTY_MATRIX_COUNT = "ConfigMetaData.MatrixCount";
  public static final String FIELD_MATRIX_COUNT_ACTIVE = "MatrixCountActive";
  public static final String PROPERTY_MATRIX_COUNT_ACTIVE = "ConfigMetaData.MatrixCountActive";
  public static final String FIELD_CONTROL_GROUP_COUNT = "ControlGroupCount";
  public static final String PROPERTY_CONTROL_GROUP_COUNT = "ConfigMetaData.ControlGroupCount";
  public static final String PROPERTY_TX_GROUP_SIZE = "ConfigMetaData.TxGroupSize";
  public static final String PROPERTY_TX_GROUP_COUNT = "ConfigMetaData.TxGroupCount";
  public static final String PROPERTY_TX_GROUP_COUNT_ACTIVE = "ConfigMetaData.TxGroupCountActive";

  public static final String PROPERTY_BRANCH_DATA_SIZE = "ConfigMetaData.BranchDataSize";
  public static final String PROPERTY_BRANCH_DATA_COUNT = "ConfigMetaData.BranchDataCount";
  public static final String PROPERTY_BRANCH_DATA_COUNT_ACTIVE =
      "ConfigMetaData.BranchDataCountActive";

  public static final String PROPERTY_MULTIVIEW_DATA_SIZE = "ConfigMetaData.MultiviewDataSize";
  public static final String PROPERTY_MULTIVIEW_DATA_COUNT = "ConfigMetaData.MultiviewDataCount";
  public static final String PROPERTY_MULTIVIEW_DATA_COUNT_ACTIVE =
      "ConfigMetaData.MultiviewDataCountActive";
  public static final String PROPERTY_SOURCECROP_DATA_SIZE = "ConfigMetaData.SourceCropDataSize";
  public static final String PROPERTY_SOURCECROP_DATA_COUNT = "ConfigMetaData.SourceCropDataCount";
  public static final String PROPERTY_SOURCECROP_DATA_COUNT_ACTIVE =
      "ConfigMetaData.SourceCropDataCountActive";
  @Expose
  private int version = 0;
  @Getter
  @Expose
  private int systemSize;
  @Getter
  @Expose
  private int cpuSize;
  @Getter
  @Expose
  private int cpuCount;
  @Getter
  @Expose
  private int cpuCountActive;
  @Getter
  @Expose
  private int consoleSize;
  @Getter
  @Expose
  private int consoleCount;
  @Getter
  @Expose
  private int consoleCountActive;
  @Getter
  @Expose
  private int vpconsoleSize;
  @Getter
  @Expose
  private int vpconsoleCount;
  @Getter
  @Expose
  private int vpconsoleCountActive;
  @Getter
  @Expose
  private int extenderSize;
  @Getter
  @Expose
  private int extenderCount;
  @Getter
  @Expose
  private int extenderCountActive;
  @Getter
  @Expose
  private int userSize;
  @Getter
  @Expose
  private int userCount;
  @Getter
  @Expose
  private int userCountActive;
  @Expose
  @Getter
  private int userGroupSize; // for DKM2
  @Expose
  @Getter
  private int userGroupCount; // for DKM2
  @Expose
  @Getter
  private int userGroupActive; // for DKM2
  @Expose
  @Getter
  private int multiScreenSize; // for DKM2
  @Expose
  @Getter
  private int multiScreenCount; // for DKM2
  @Expose
  @Getter
  private int multiScreenActive; // for DKM2
  @Getter
  @Expose
  private int functionKeySize;
  @Getter
  @Expose
  private int functionKeyCount;
  @Getter
  @Expose
  private int functionKeyCountActive;
  @Getter
  @Expose
  private int portSize;
  @Getter
  @Expose
  private int portCount;
  @Getter
  @Expose
  private int portCountActive;
  @Getter
  @Expose
  private int matrixSize;
  @Getter
  @Expose
  private int matrixCount;
  @Getter
  @Expose
  private int matrixCountActive;
  @Getter
  @Expose
  private int controlGroupCount;
  @Getter
  @Expose
  private int txRxGroupSize;
  @Getter
  @Expose
  private int txRxGroupCount;
  @Getter
  @Expose
  private int txRxGroupCountActive;
  @Getter
  @Expose
  private int branchDataSize;
  @Getter
  @Expose
  private int branchDataCount;
  @Getter
  @Expose
  private int branchDataActive;
  @Getter
  @Expose
  private int multiviewDataSize;
  @Getter
  @Expose
  private int multiviewDataCount;
  @Getter
  @Expose
  private int multiviewDataActive;
  @Getter
  @Expose
  private int sourceCropDataSize;
  @Getter
  @Expose
  private int sourceCropDataCount;
  @Getter
  @Expose
  private int sourceCropDataActive;

  /**
   * .
   */
  public ConfigMetaData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager,
      String fqn) {
    super(pcs, configDataManager, -1, fqn);
    initDefaults();
    initCommitRollback();
  }

  @Override
  public void initDefaults() {
    Version latestVersion = Version.getLatestVersion();
    initWithVersion(latestVersion);
  }

  /**
   * 检查数据大小是否跟版本一致.
   *
   * @throws ConfigException 如果不一致，抛出异常
   */
  public void checkSize() throws ConfigException {
    Version version = getUtilVersion();
    if (version.isVersionTooNew()) {
      throw new ConfigException(CfgError.VERSION, "Version error");
    }

    int[] actuals = new int[]{getSystemSize(), getCpuSize(), getCpuCount(), getConsoleSize(),
        getConsoleCount(), getExtenderSize(), getExtenderCount(), getUserSize(), getUserCount(),
        getUserGroupSize(), getUserGroupCount(), getMultiScreenSize(), getMultiScreenCount(),
        getFunctionKeySize(), getFunctionKeyCount(), getPortSize(), getPortCount(), getMatrixSize(),
        getMatrixCount(), getTxRxGroupSize(), getTxRxGroupCount()};

    int[] exptected = new int[]{version.getClassSize(SystemConfigData.class),
        version.getClassSize(CpuData.class), version.getClassCount(CpuData.class),
        version.getClassSize(ConsoleData.class), version.getClassCount(ConsoleData.class),
        version.getClassSize(ExtenderData.class), version.getClassCount(ExtenderData.class),
        version.getClassSize(UserData.class), version.getClassCount(UserData.class),
        version.getClassSize(UserGroupData.class), version.getClassCount(UserGroupData.class),
        version.getClassSize(MultiScreenData.class), version.getClassCount(MultiScreenData.class),
        version.getClassSize(FunctionKeyData.class), version.getClassCount(FunctionKeyData.class),
        version.getClassSize(PortData.class), version.getClassCount(PortData.class),
        version.getClassSize(MatrixData.class), version.getClassCount(MatrixData.class),
        version.getClassSize(TxRxGroupData.class), version.getClassCount(TxRxGroupData.class)};

    String[] msgs = new String[]{"SystemConfigData's Size is error. ", "CpuData's Size is error. ",
        "CpuData's Count is error. ", "ConsoleData's Size is error. ",
        "ConsoleData's Count is error. ", "ExtenderData's Size is error. ",
        "ExtenderData's Count is error. ", "UserData's Size is error. ",
        "UserData's Count is error. ", "UserGroupData's Size is error. ",
        "UserGroupData's Count is error. ", "MultiScreenData's Size is error. ",
        "MultiScreenData's Count is error. ", "FunctionKeyData's Size is error. ",
        "FunctionKeyData's Count is error. ", "PortData's Size is error. ",
        "PortData's Count is error. ", "MatrixData's Size is error. ",
        "MatrixData's Count is error. ", "TxGroupData's Size is error. ",
        "TxGroupData's Count is error. "};

    int count = actuals.length;
    for (int i = 0; i < count; i++) {
      if (actuals[i] != exptected[i]) {
        throw new ConfigException(CfgError.VERSION,
            msgs[i] + String.format("Actual is %d. %d is expected.", actuals[i], exptected[i]));
      }
    }
  }

  /**
   * 根据版本初始化.
   *
   * @param version 版本信息.
   */
  public void initWithVersion(Version version) {
    setVersion(version.getVersionValue());
    setSystemSize(version.getClassSize(SystemConfigData.class));
    setCpuSize(version.getClassSize(CpuData.class));
    setCpuCount(version.getClassCount(CpuData.class));
    setCpuCountActive(0);
    setConsoleSize(version.getClassSize(ConsoleData.class));
    setConsoleCount(version.getClassCount(ConsoleData.class));
    setConsoleCountActive(0);

    setVpconsoleSize(CaesarConstants.VpConsole.SIZE);
    setVpconsoleCount(CaesarConstants.VpConsole.COUNT);
    setVpconsoleCountActive(0);

    setExtenderSize(version.getClassSize(ExtenderData.class));
    setExtenderCount(version.getClassCount(ExtenderData.class));
    setExtenderCountActive(0);
    setUserSize(version.getClassSize(UserData.class));
    setUserCount(version.getClassCount(UserData.class));
    setUserCountActive(0);
    setUserGroupSize(version.getClassSize(UserGroupData.class));
    setUserGroupCount(version.getClassCount(UserGroupData.class));
    setUserGroupActive(0);
    setMultiScreenSize(version.getClassSize(MultiScreenData.class));
    setMultiScreenCount(version.getClassCount(MultiScreenData.class));
    setMultiScreenActive(0);
    setFunctionKeySize(version.getClassSize(FunctionKeyData.class));
    setFunctionKeyCount(version.getClassCount(FunctionKeyData.class));
    setFunctionKeyCountActive(0);
    setPortSize(version.getClassSize(PortData.class));
    setPortCount(version.getClassCount(PortData.class));
    setPortCountActive(0);
    setMatrixSize(version.getClassSize(MatrixData.class));
    setMatrixCount(version.getClassCount(MatrixData.class));
    setMatrixCountActive(0);
    setControlGroupCount(CaesarConstants.ControlGroup.COUNT);
    if (version.hasTxRxGroupData()) {
      setTxRxGroupSize(version.getClassSize(TxRxGroupData.class));
      setTxRxGroupCount(version.getClassCount(TxRxGroupData.class));
      setTxRxGroupCountActive(0);
    }
    if (version.hasBranchData()) {
      setBranchDataSize(version.getClassSize(BranchData.class));
      setBranchDataCount(version.getClassCount(BranchData.class));
      setBranchDataActive(0);
    }
    if (version.hasMultiviewData()) {
      setMultiviewDataSize(version.getClassSize(MultiviewData.class));
      setMultiviewDataCount(version.getClassCount(MultiviewData.class));
      setMultiviewDataActive(0);
    }
    if (version.hasSourceCropData()) {
      setSourceCropDataSize(version.getClassSize(SourceCropData.class));
      setSourceCropDataCount(version.getClassCount(SourceCropData.class));
      setSourceCropDataActive(0);
    }
  }

  /**
   * .
   */
  public int getModuleCount() {
    if (this.version == 0 && Boolean.getBoolean("developerAccess")) {
      for (StackTraceElement ste : Thread.currentThread().getStackTrace()) {
        LOG.log(Level.SEVERE, ste.toString());
      }
    }
    return getUtilVersion().getModuleCount();
  }

  /**
   * .
   */
  public void setSystemSize(int systemSize) {
    int oldValue = this.systemSize;
    this.systemSize = systemSize;
    firePropertyChange(ConfigMetaData.PROPERTY_SYSTEM_SIZE, oldValue, this.systemSize);
  }

  /**
   * .
   */
  public int getVersion() {
    if (this.version == 0) {
      LOG.log(Level.SEVERE, "DataModel version not set!");
    }
    return 0xFFFFFF & this.version;
  }

  public Version getUtilVersion() {
    return Version.getVersion(getVersion());
  }

  private Version getPrivateUtilVersion() {
    return Version.getVersion(version);
  }

  /**
   * .
   */
  public void setVersion(int version) {
    int oldValue = this.version;
    this.version = version;
    firePropertyChange(ConfigMetaData.PROPERTY_VERSION, oldValue, this.version);
  }

  /**
   * .
   */
  public void setConsoleCount(int consoleCount) {
    int oldValue = this.consoleCount;
    this.consoleCount = consoleCount;
    firePropertyChange(ConfigMetaData.PROPERTY_CONSOLE_COUNT, oldValue, this.consoleCount);
  }

  /**
   * .
   */
  public void setConsoleCountActive(int consoleCountActive) {
    int oldValue = this.consoleCountActive;
    this.consoleCountActive = consoleCountActive;
    firePropertyChange(ConfigMetaData.PROPERTY_CONSOLE_COUNT_ACTIVE, oldValue,
        this.consoleCountActive);
  }

  /**
   * .
   */
  public void setConsoleSize(int consoleSize) {
    int oldValue = this.consoleSize;
    this.consoleSize = consoleSize;
    firePropertyChange(ConfigMetaData.PROPERTY_CONSOLE_SIZE, oldValue, this.consoleSize);
  }

  /**
   * .
   */
  public void setVpconsoleCount(int vpconsoleCount) {
    int oldValue = this.vpconsoleCount;
    this.vpconsoleCount = Math.min(vpconsoleCount, CaesarConstants.VpConsole.COUNT);
    firePropertyChange(ConfigMetaData.PROPERTY_VPCONSOLE_COUNT, oldValue, this.vpconsoleCount);
  }

  /**
   * .
   */
  public void setVpconsoleCountActive(int vpconsoleCountActive) {
    int oldValue = this.vpconsoleCountActive;
    this.vpconsoleCountActive = vpconsoleCountActive;
    firePropertyChange(ConfigMetaData.PROPERTY_VPCONSOLE_COUNT_ACTIVE, oldValue,
        this.vpconsoleCountActive);
  }

  /**
   * .
   */
  public void setVpconsoleSize(int vpconsoleSize) {
    int oldValue = this.vpconsoleSize;
    this.vpconsoleSize = vpconsoleSize;
    firePropertyChange(ConfigMetaData.PROPERTY_VPCONSOLE_SIZE, oldValue, this.vpconsoleSize);
  }

  /**
   * .
   */
  public void setCpuCount(int cpuCount) {
    int oldValue = this.cpuCount;
    this.cpuCount = cpuCount;
    firePropertyChange(ConfigMetaData.PROPERTY_CPU_COUNT, oldValue, this.cpuCount);
  }

  /**
   * .
   */
  public void setCpuCountActive(int cpuCountActive) {
    int oldValue = this.cpuCountActive;
    this.cpuCountActive = cpuCountActive;
    firePropertyChange(ConfigMetaData.PROPERTY_CPU_COUNT_ACTIVE, oldValue, this.cpuCountActive);
  }

  /**
   * .
   */
  public void setCpuSize(int cpuSize) {
    int oldValue = this.cpuSize;
    this.cpuSize = cpuSize;
    firePropertyChange(ConfigMetaData.PROPERTY_CPU_SIZE, oldValue, this.cpuSize);
  }

  /**
   * .
   */
  public void setExtenderCount(int extenderCount) {
    int oldValue = this.extenderCount;
    this.extenderCount = extenderCount;
    firePropertyChange(ConfigMetaData.PROPERTY_EXTENDER_COUNT, oldValue, this.extenderCount);
  }

  /**
   * .
   */
  public void setExtenderCountActive(int extenderCountActive) {
    int oldValue = this.extenderCountActive;
    this.extenderCountActive = extenderCountActive;
    firePropertyChange(ConfigMetaData.PROPERTY_EXTENDER_COUNT_ACTIVE, oldValue,
        this.extenderCountActive);
  }

  /**
   * .
   */
  public void setExtenderSize(int extenderSize) {
    int oldValue = this.extenderSize;
    this.extenderSize = extenderSize;
    firePropertyChange(ConfigMetaData.PROPERTY_EXTENDER_SIZE, oldValue, this.extenderSize);
  }

  /**
   * .
   */
  public void setFunctionKeyCount(int functionKeyCount) {
    int oldValue = this.functionKeyCount;
    this.functionKeyCount = Math.min(functionKeyCount, CaesarConstants.FunctionKey.COUNT);
    firePropertyChange(ConfigMetaData.PROPERTY_FUNCTION_KEY_COUNT, oldValue, this.functionKeyCount);
  }

  /**
   * .
   */
  public void setFunctionKeyCountActive(int functionKeyCountActive) {
    int oldValue = this.functionKeyCountActive;
    this.functionKeyCountActive = functionKeyCountActive;
    firePropertyChange(ConfigMetaData.PROPERTY_FUNCTION_KEY_COUNT_ACTIVE, oldValue,
        this.functionKeyCountActive);
  }

  /**
   * .
   */
  public void setFunctionKeySize(int functionKeySize) {
    int oldValue = this.functionKeySize;
    this.functionKeySize = functionKeySize;
    firePropertyChange(ConfigMetaData.PROPERTY_FUNCTION_KEY_SIZE, oldValue, this.functionKeySize);
  }

  /**
   * .
   */
  public void setMatrixCount(int matrixCount) {
    int oldValue = this.matrixCount;
    this.matrixCount = Math.min(matrixCount, getUtilVersion().getMatrixDataMaxCount());
    firePropertyChange(ConfigMetaData.PROPERTY_PORT_COUNT, oldValue, this.matrixCount);
  }

  /**
   * .
   */
  public void setMatrixCountActive(int matrixCountActive) {
    int oldValue = this.matrixCountActive;
    this.matrixCountActive = matrixCountActive;
    firePropertyChange(ConfigMetaData.PROPERTY_MATRIX_COUNT_ACTIVE, oldValue,
        this.matrixCountActive);
  }

  /**
   * .
   */
  public void setMatrixSize(int matrixSize) {
    int oldValue = this.matrixSize;
    this.matrixSize = matrixSize;
    firePropertyChange(ConfigMetaData.PROPERTY_MATRIX_SIZE, oldValue, this.matrixSize);
  }

  /**
   * .
   */
  public void setPortCount(int portCount) {
    int oldValue = this.portCount;
    this.portCount = portCount;
    firePropertyChange(ConfigMetaData.PROPERTY_PORT_COUNT, oldValue, this.portCount);
  }

  /**
   * .
   */
  public void setPortCountActive(int portCountActive) {
    int oldValue = this.portCountActive;
    this.portCountActive = portCountActive;
    firePropertyChange(ConfigMetaData.PROPERTY_PORT_COUNT_ACTIVE, oldValue, this.portCountActive);
  }

  /**
   * .
   */
  public void setPortSize(int portSize) {
    int oldValue = this.portSize;
    this.portSize = portSize;
    firePropertyChange(ConfigMetaData.PROPERTY_PORT_SIZE, oldValue, this.portSize);
  }

  /**
   * .
   */
  public void setUserCount(int userCount) {
    int oldValue = this.userCount;
    this.userCount = Math.min(userCount, CaesarConstants.User.COUNT);
    firePropertyChange(ConfigMetaData.PROPERTY_USER_COUNT, oldValue, this.userCount);
  }

  /**
   * .
   */
  public void setUserCountActive(int userCountActive) {
    int oldValue = this.userCountActive;
    this.userCountActive = userCountActive;
    firePropertyChange(ConfigMetaData.PROPERTY_USER_COUNT_ACTIVE, oldValue, this.userCountActive);
  }

  /**
   * .
   */
  public void setUserSize(int userSize) {
    int oldValue = this.userSize;
    this.userSize = userSize;
    firePropertyChange(ConfigMetaData.PROPERTY_USER_SIZE, oldValue, this.userSize);
  }

  /**
   * .
   */
  public void setUserGroupSize(int size) {
    int oldValue = this.userGroupSize;
    this.userGroupSize = size;
    fireIntegerPropertyChange(PROPERTY_USER_GROUP_SIZE, oldValue, size);
  }

  /**
   * .
   */
  public void setUserGroupCount(int count) {
    int oldValue = this.userGroupCount;
    this.userGroupCount = count;
    fireIntegerPropertyChange(PROPERTY_USER_GROUP_COUNT, oldValue, count);
  }

  /**
   * .
   */
  public void setUserGroupActive(int active) {
    int oldValue = this.userGroupActive;
    this.userGroupActive = active;
    fireIntegerPropertyChange(PROPERTY_USER_GROUP_SIZE, oldValue, active);
  }

  /**
   * .
   */
  public void setMultiScreenSize(int size) {
    int oldValue = this.multiScreenSize;
    this.multiScreenSize = size;
    fireIntegerPropertyChange(PROPERTY_MULTI_SCREEN_SIZE, oldValue, size);
  }

  /**
   * .
   */
  public void setMultiScreenCount(int count) {
    int oldValue = this.multiScreenCount;
    this.multiScreenCount = count;
    fireIntegerPropertyChange(PROPERTY_MULTI_SCREEN_COUNT, oldValue, count);
  }

  /**
   * .
   */
  public void setMultiScreenActive(int active) {
    int oldValue = this.multiScreenActive;
    this.multiScreenActive = active;
    fireIntegerPropertyChange(PROPERTY_MULTI_SCREEN_SIZE, oldValue, active);
  }

  /**
   * .
   */
  public void setControlGroupCount(int controlGroupCount) {
    int oldValue = this.controlGroupCount;
    this.controlGroupCount = Math.min(controlGroupCount, CaesarConstants.ControlGroup.COUNT);
    firePropertyChange(ConfigMetaData.PROPERTY_CONTROL_GROUP_COUNT, oldValue,
        this.controlGroupCount);
  }

  protected void fireIntegerPropertyChange(String property, int oldValue, int newValue) {
    firePropertyChange(property, oldValue, newValue);
  }

  public int getConfigDataSize() {
    return getUtilVersion().getConfigDataSize();
  }

  public int getCurrentSize(Class clazz) {
    return getPrivateUtilVersion().getClassSize(clazz);
  }

  public String getCurrentName() {
    return getPrivateUtilVersion().getName();
  }

  /**
   * .
   */
  public void setTxRxGroupCount(int txRxGroupCount) {
    int oldValue = this.txRxGroupCount;
    this.txRxGroupCount = txRxGroupCount;
    firePropertyChange(ConfigMetaData.PROPERTY_TX_GROUP_COUNT, oldValue, this.txRxGroupCount);
  }

  /**
   * .
   */
  public void setTxRxGroupSize(int txRxGroupSize) {
    int oldValue = this.txRxGroupSize;
    this.txRxGroupSize = txRxGroupSize;
    firePropertyChange(ConfigMetaData.PROPERTY_TX_GROUP_SIZE, oldValue, this.txRxGroupSize);
  }

  /**
   * .
   */
  public void setTxRxGroupCountActive(int active) {
    int oldValue = this.txRxGroupCountActive;
    this.txRxGroupCountActive = active;
    fireIntegerPropertyChange(PROPERTY_TX_GROUP_COUNT_ACTIVE, oldValue, active);
  }


  /**
   * .
   */
  public void setBranchDataCount(int branchDataCount) {
    int oldValue = this.branchDataCount;
    this.branchDataCount = branchDataCount;
    firePropertyChange(ConfigMetaData.PROPERTY_BRANCH_DATA_COUNT, oldValue, this.branchDataCount);
  }

  /**
   * .
   */
  public void setBranchDataSize(int branchDataSize) {
    int oldValue = this.branchDataSize;
    this.branchDataSize = branchDataSize;
    firePropertyChange(ConfigMetaData.PROPERTY_BRANCH_DATA_SIZE, oldValue, this.branchDataSize);
  }

  /**
   * .
   */
  public void setBranchDataActive(int active) {
    int oldValue = this.branchDataActive;
    this.branchDataActive = active;
    fireIntegerPropertyChange(PROPERTY_BRANCH_DATA_COUNT_ACTIVE, oldValue, active);
  }

  /**
   * .
   */
  public void setMultiviewDataSize(int multiviewDataSize) {
    int oldValue = this.multiviewDataSize;
    this.multiviewDataSize = multiviewDataSize;
    firePropertyChange(ConfigMetaData.PROPERTY_MULTIVIEW_DATA_SIZE,
        oldValue, this.multiviewDataSize);
  }

  /**
   * .
   */
  public void setMultiviewDataCount(int multiviewDataCount) {
    int oldValue = this.multiviewDataCount;
    this.multiviewDataCount = multiviewDataCount;
    firePropertyChange(ConfigMetaData.PROPERTY_MULTIVIEW_DATA_COUNT,
        oldValue, this.multiviewDataCount);
  }


  /**
   * .
   */
  public void setMultiviewDataActive(int active) {
    int oldValue = this.multiviewDataActive;
    this.multiviewDataActive = active;
    fireIntegerPropertyChange(PROPERTY_MULTIVIEW_DATA_COUNT_ACTIVE, oldValue, active);
  }


  /**
   * .
   */
  public void setSourceCropDataSize(int sourceCropDataSize) {
    int oldValue = this.sourceCropDataSize;
    this.sourceCropDataSize = sourceCropDataSize;
    firePropertyChange(ConfigMetaData.PROPERTY_SOURCECROP_DATA_SIZE,
        oldValue, this.sourceCropDataSize);
  }

  /**
   * .
   */
  public void setSourceCropDataCount(int sourceCropDataCount) {
    int oldValue = this.sourceCropDataCount;
    this.sourceCropDataCount = sourceCropDataCount;
    firePropertyChange(ConfigMetaData.PROPERTY_SOURCECROP_DATA_COUNT,
        oldValue, this.sourceCropDataCount);
  }


  /**
   * .
   */
  public void setSourceCropDataActive(int active) {
    int oldValue = this.sourceCropDataActive;
    this.sourceCropDataActive = active;
    fireIntegerPropertyChange(PROPERTY_SOURCECROP_DATA_COUNT_ACTIVE, oldValue, active);
  }

  /**
   * .
   */
  public boolean isSnmpVersion() {
    if (this.version == 0 && Boolean.getBoolean("developerAccess")) {
      for (StackTraceElement ste : Thread.currentThread().getStackTrace()) {
        LOG.log(Level.SEVERE, ste.toString());
      }
    }
    return Utilities.areBitsSet(this.version, Version.SNMP_FLAG);
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (ConfigMetaData.PROPERTY_VERSION.equals(propertyName)) {
      setVersion((Integer) value);
    } else if (ConfigMetaData.PROPERTY_SYSTEM_SIZE.equals(propertyName)) {
      setSystemSize((Integer) value);
    } else if (ConfigMetaData.PROPERTY_CPU_SIZE.equals(propertyName)) {
      setCpuSize((Integer) value);
    } else if (ConfigMetaData.PROPERTY_CPU_COUNT.equals(propertyName)) {
      setCpuCount((Integer) value);
    } else if (ConfigMetaData.PROPERTY_CPU_COUNT_ACTIVE.equals(propertyName)) {
      setCpuCountActive((Integer) value);
    } else if (ConfigMetaData.PROPERTY_CONSOLE_SIZE.equals(propertyName)) {
      setConsoleSize((Integer) value);
    } else if (ConfigMetaData.PROPERTY_CONSOLE_COUNT.equals(propertyName)) {
      setConsoleCount((Integer) value);
    } else if (ConfigMetaData.PROPERTY_CONSOLE_COUNT_ACTIVE.equals(propertyName)) {
      setConsoleCountActive((Integer) value);
    } else if (ConfigMetaData.PROPERTY_EXTENDER_SIZE.equals(propertyName)) {
      setExtenderSize((Integer) value);
    } else if (ConfigMetaData.PROPERTY_EXTENDER_COUNT.equals(propertyName)) {
      setExtenderCount((Integer) value);
    } else if (ConfigMetaData.PROPERTY_EXTENDER_COUNT_ACTIVE.equals(propertyName)) {
      setExtenderCountActive((Integer) value);
    } else if (ConfigMetaData.PROPERTY_USER_SIZE.equals(propertyName)) {
      setUserSize((Integer) value);
    } else if (ConfigMetaData.PROPERTY_USER_COUNT.equals(propertyName)) {
      setUserCount((Integer) value);
    } else if (ConfigMetaData.PROPERTY_USER_COUNT_ACTIVE.equals(propertyName)) {
      setUserCountActive((Integer) value);
    } else if (ConfigMetaData.PROPERTY_FUNCTION_KEY_SIZE.equals(propertyName)) {
      setFunctionKeySize((Integer) value);
    } else if (ConfigMetaData.PROPERTY_FUNCTION_KEY_COUNT.equals(propertyName)) {
      setFunctionKeyCount((Integer) value);
    } else if (ConfigMetaData.PROPERTY_FUNCTION_KEY_COUNT_ACTIVE.equals(propertyName)) {
      setFunctionKeyCountActive((Integer) value);
    } else if (ConfigMetaData.PROPERTY_MATRIX_SIZE.equals(propertyName)) {
      setMatrixSize((Integer) value);
    } else if (ConfigMetaData.PROPERTY_MATRIX_COUNT.equals(propertyName)) {
      setMatrixCount((Integer) value);
    } else if (ConfigMetaData.PROPERTY_MATRIX_COUNT_ACTIVE.equals(propertyName)) {
      setMatrixCountActive((Integer) value);
    } else if (ConfigMetaData.PROPERTY_PORT_SIZE.equals(propertyName)) {
      setPortSize((Integer) value);
    } else if (ConfigMetaData.PROPERTY_PORT_COUNT.equals(propertyName)) {
      setPortCount((Integer) value);
    } else if (ConfigMetaData.PROPERTY_PORT_COUNT_ACTIVE.equals(propertyName)) {
      setPortCountActive((Integer) value);
    } else if (ConfigMetaData.PROPERTY_CONTROL_GROUP_COUNT.equals(propertyName)) {
      setControlGroupCount((Integer) value);
    } else if (ConfigMetaData.PROPERTY_TX_GROUP_SIZE.equals(propertyName)) {
      setTxRxGroupSize((Integer) value);
    } else if (ConfigMetaData.PROPERTY_TX_GROUP_COUNT.equals(propertyName)) {
      setTxRxGroupCount((Integer) value);
    } else if (ConfigMetaData.PROPERTY_TX_GROUP_COUNT_ACTIVE.equals(propertyName)) {
      setTxRxGroupCountActive((Integer) value);
    }
  }

  @Override
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    cfgWriter.writeInteger(this.version);
    cfgWriter.writeInteger(getSystemSize());
    cfgWriter.writeInteger(getCpuSize());
    cfgWriter.writeInteger(getCpuCount());
    cfgWriter.writeInteger(getCpuCountActive());
    cfgWriter.writeInteger(getConsoleSize());
    cfgWriter.writeInteger(getConsoleCount());
    cfgWriter.writeInteger(getConsoleCountActive());
    cfgWriter.writeInteger(getExtenderSize());
    cfgWriter.writeInteger(getExtenderCount());
    cfgWriter.writeInteger(getExtenderCountActive());
    cfgWriter.writeInteger(getUserSize());
    cfgWriter.writeInteger(getUserCount());
    cfgWriter.writeInteger(getUserCountActive());
    // FOR DKM2
    cfgWriter.writeInteger(getUserGroupSize());
    cfgWriter.writeInteger(getUserGroupCount());
    cfgWriter.writeInteger(getUserGroupActive());

    cfgWriter.writeInteger(getMultiScreenSize());
    cfgWriter.writeInteger(getMultiScreenCount());
    cfgWriter.writeInteger(getMultiScreenActive());

    cfgWriter.writeInteger(getFunctionKeySize());
    cfgWriter.writeInteger(getFunctionKeyCount());
    cfgWriter.writeInteger(getFunctionKeyCountActive());
    cfgWriter.writeInteger(getPortSize());
    cfgWriter.writeInteger(getPortCount());
    cfgWriter.writeInteger(getPortCountActive());
    if (getUtilVersion().hasMatrixData()) {
      cfgWriter.writeInteger(getMatrixSize());
      cfgWriter.writeInteger(getMatrixCount());
      cfgWriter.writeInteger(getMatrixCountActive());
    }
    if (getUtilVersion().hasTxRxGroupData()) {
      cfgWriter.writeInteger(getTxRxGroupSize());
      cfgWriter.writeInteger(getTxRxGroupCount());
      cfgWriter.writeInteger(getTxRxGroupCountActive());
    }
    if (getUtilVersion().hasBranchData()) {
      cfgWriter.writeInteger(getBranchDataSize());
      cfgWriter.writeInteger(getBranchDataCount());
      cfgWriter.writeInteger(getBranchDataActive());
    }
    if (getUtilVersion().hasMultiviewData()) {
      cfgWriter.writeInteger(getMultiviewDataSize());
      cfgWriter.writeInteger(getMultiviewDataCount());
      cfgWriter.writeInteger(getMultiviewDataActive());
    }
    if (getUtilVersion().hasSourceCropData()) {
      cfgWriter.writeInteger(getSourceCropDataSize());
      cfgWriter.writeInteger(getSourceCropDataCount());
      cfgWriter.writeInteger(getSourceCropDataActive());
    }
  }

  @Override
  public void readData(CfgReader cfgReader) throws ConfigException {
    int version = cfgReader.readInteger();
    setVersion(version);
    int systemSize = cfgReader.readInteger();
    setSystemSize(systemSize);
    int cpuSize = cfgReader.readInteger();
    setCpuSize(cpuSize);
    int cpuCount = cfgReader.readInteger();
    setCpuCount(cpuCount);
    int cpuCountActive = cfgReader.readInteger();
    setCpuCountActive(cpuCountActive);
    int consoleSize = cfgReader.readInteger();
    setConsoleSize(consoleSize);
    int consoleCount = cfgReader.readInteger();
    setConsoleCount(consoleCount);
    int consoleCountActive = cfgReader.readInteger();
    setConsoleCountActive(consoleCountActive);
    int extenderSize = cfgReader.readInteger();
    setExtenderSize(extenderSize);
    int extenderCount = cfgReader.readInteger();
    setExtenderCount(extenderCount);
    int extenderCountActive = cfgReader.readInteger();
    setExtenderCountActive(extenderCountActive);
    int userSize = cfgReader.readInteger();
    setUserSize(userSize);
    int userCount = cfgReader.readInteger();
    setUserCount(userCount);
    int userCountActive = cfgReader.readInteger();
    setUserCountActive(userCountActive);

    // FOR DKM2
    int userGroupSize = cfgReader.readInteger();
    setUserGroupSize(userGroupSize);

    int userGroupCount = cfgReader.readInteger();
    setUserGroupCount(userGroupCount);

    int userGroupCountActive = cfgReader.readInteger();
    setUserGroupActive(userGroupCountActive);

    int multiScreenSize = cfgReader.readInteger();
    setMultiScreenSize(multiScreenSize);

    int multiScreenCount = cfgReader.readInteger();
    setMultiScreenCount(multiScreenCount);

    int multiScreenCountActive = cfgReader.readInteger();
    setMultiScreenActive(multiScreenCountActive);

    int functionKeySize = cfgReader.readInteger();
    setFunctionKeySize(functionKeySize);

    int functionKeyCount = cfgReader.readInteger();
    setFunctionKeyCount(functionKeyCount);

    int functionKeyCountActive = cfgReader.readInteger();
    setFunctionKeyCountActive(functionKeyCountActive);

    int portSize = cfgReader.readInteger();
    setPortSize(portSize);

    int portCount = cfgReader.readInteger();
    setPortCount(portCount);

    int portCountActive = cfgReader.readInteger();
    setPortCountActive(portCountActive);

    if (getUtilVersion().hasMatrixData()) {
      int matrixSize = cfgReader.readInteger();
      setMatrixSize(matrixSize);
      int matrixCount = cfgReader.readInteger();
      setMatrixCount(matrixCount);
      int matrixCountActive = cfgReader.readInteger();
      setMatrixCountActive(matrixCountActive);
    }
    if (getUtilVersion().hasTxRxGroupData()) {
      int txRxGroupSize = cfgReader.readInteger();
      setTxRxGroupSize(txRxGroupSize);
      int txRxGroupCount = cfgReader.readInteger();
      setTxRxGroupCount(txRxGroupCount);
      int txRxGroupCountActive = cfgReader.readInteger();
      setTxRxGroupCountActive(txRxGroupCountActive);
    }
    if (getUtilVersion().hasBranchData()) {
      int branchDataSize = cfgReader.readInteger();
      setBranchDataSize(branchDataSize);
      int branchDataCount = cfgReader.readInteger();
      setBranchDataCount(branchDataCount);
      int branchDataActive = cfgReader.readInteger();
      setBranchDataActive(branchDataActive);
    }
    if (getUtilVersion().hasMultiviewData()) {
      int multiviewDataSize = cfgReader.readInteger();
      setMultiviewDataSize(multiviewDataSize);
      int multiviewDataCount = cfgReader.readInteger();
      setMultiviewDataCount(multiviewDataCount);
      int multiviewDataActive = cfgReader.readInteger();
      setMultiviewDataActive(multiviewDataActive);
    }
    if (getUtilVersion().hasSourceCropData()) {
      int sourceCropDataSize = cfgReader.readInteger();
      setSourceCropDataSize(sourceCropDataSize);
      int sourceCropDataCount = cfgReader.readInteger();
      setSourceCropDataCount(sourceCropDataCount);
      int sourceCropDataActive = cfgReader.readInteger();
      setSourceCropDataActive(sourceCropDataActive);
    }
  }
}

