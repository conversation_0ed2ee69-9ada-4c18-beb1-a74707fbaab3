package com.mc.tool.caesar.vpm.pages.systemedit.menu.usb;

import com.dooapp.fxform.annotation.FormFactory;
import com.mc.tool.caesar.api.interfaces.DataObject;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javax.validation.constraints.NotNull;

/**
 * .
 */
public class UsbRebindConfigBean {
  @FormFactory(DataObjectFieldFactory.class)
  public ObjectProperty<DataObject> binding = new SimpleObjectProperty<>(null);

  @NotNull
  public DataObject getBinding() {
    return binding.get();
  }

  public void setBinding(DataObject item) {
    binding.set(item);
  }
}
