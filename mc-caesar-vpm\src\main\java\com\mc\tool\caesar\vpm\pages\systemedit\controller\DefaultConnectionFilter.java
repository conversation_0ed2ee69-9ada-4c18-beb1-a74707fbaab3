package com.mc.tool.caesar.vpm.pages.systemedit.controller;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.vpm.pages.systemedit.controller.Bundle.NbBundle;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.layout.Background;
import javafx.scene.layout.BackgroundFill;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Region;
import javafx.scene.paint.Color;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class DefaultConnectionFilter implements Initializable {

  @FXML private HBox rootContainer;
  @FXML private Region fullRegion;
  @FXML private Region videoRegion;
  @FXML private Region privateRegion;
  @FXML private Region shareRegion;
  @FXML private Region shareFullRegion;

  /** Constructor. */
  public DefaultConnectionFilter() {
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    String path = "com/mc/tool/caesar/vpm/pages/systemedit/connection_filter.fxml";
    FXMLLoader loader = new FXMLLoader(classLoader.getResource(path));
    loader.setResources(NbBundle.getResourceBundle());
    loader.setController(this);
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load connection_filter.fxml");
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    fullRegion.setBackground(
        new Background(
            new BackgroundFill(Color.web(CaesarConstants.SWITCH_TYPE_COLOR_FULL), null, null)));
    videoRegion.setBackground(
        new Background(
            new BackgroundFill(Color.web(CaesarConstants.SWITCH_TYPE_COLOR_VIDEO), null, null)));
    privateRegion.setBackground(
        new Background(
            new BackgroundFill(Color.web(CaesarConstants.SWITCH_TYPE_COLOR_PRIVATE), null, null)));
    shareRegion.setBackground(
        new Background(
            new BackgroundFill(Color.web(CaesarConstants.SWITCH_TYPE_COLOR_SHARED), null, null)));
    shareFullRegion.setBackground(
        new Background(
            new BackgroundFill(
                Color.web(CaesarConstants.SWITCH_TYPE_COLOR_FULL_SHARED), null, null)));
  }

  public Parent getRoot() {
    return rootContainer;
  }
}
