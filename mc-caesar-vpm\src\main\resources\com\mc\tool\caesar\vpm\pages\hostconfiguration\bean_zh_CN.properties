autoRejectTime-label=\u6FC0\u6D3B\u5728OSD\u63A8\u9001\u6293\u53D6\u6388\u6743
autoRejectTime-tooltip=\u79D2\u540E\u81EA\u52A8\u62D2\u7EDD
buzzerWarning-label=\u58F0\u97F3\u63D0\u9192
configurationName-label=\u4E3B\u673A\u914D\u7F6E\u540D\u79F0\uFF1A
gateway1-label=\u7F51\u5173:
gateway2-label=\u7F51\u5173:
hostName-label=\u7F51\u7EDC\u4E3B\u673A\u540D\u79F0\uFF1A
ipAddress1-label=IP\u5730\u5740:
ipAddress1-tooltip=\u4FEE\u6539\u8BBE\u5907IP\u9700\u8981\u91CD\u542F\u4E3B\u673A
ipAddress2-label=IP\u5730\u5740:
ipAddress2-tooltip=\u4FEE\u6539\u8BBE\u5907IP\u9700\u8981\u91CD\u542F\u4E3B\u673A
isComEcho-label=\u5141\u8BB8RS232\u4E32\u53E3\u56DE\u4F20\u5207\u6362\u547D\u4EE4
isConAccess-label=\u5141\u8BB8\u65B0\u8FDE\u63A5\u7BA1\u63A7\u7AEF\u5BF9\u63A5\u5165\u7AEF\u6240\u6709\u6743\u9650
isConDisconnect-label=\u6FC0\u6D3B\u7BA1\u63A7\u7AEF\u5728\u5B8C\u5168\u8BBF\u95EE\u60C5\u51B5\uFF0C\u5F3A\u5236\u65AD\u5F00\u5176\u4ED6\u7BA1\u63A7\u7AEF\u8FDE\u63A5
isConLock-label=\u5141\u8BB8\u7BA1\u63A7\u7AEFOSD\u6743\u9650\u7BA1\u7406\uFF08\u5168\u5C40\u914D\u7F6E\uFF09
isCpuConnect-label=\u6FC0\u6D3B\u7BA1\u63A7\u7AEF\u5728\u5B8C\u5168\u8BBF\u95EE\u60C5\u51B5\uFF0C\u652F\u6301\u5176\u4ED6\u7BA1\u63A7\u7AEF\u652F\u6301\u89C6\u9891\u6A21\u5F0F\u89C2\u770B
isCpuDisconnect-label=\u5141\u8BB8\u6253\u5F00OSD\u81EA\u52A8\u65AD\u5F00\u63A5\u5165\u7AEF\u4FE1\u53F7\u6E90
isCpuWatch-label=\u6FC0\u6D3B\u89C6\u9891\u5171\u4EAB\u529F\u80FD
isDebug-label=\u8C03\u8BD5
isDhcp-label=\u662F\u5426\u4E3ADHCP\u6A21\u5F0F
isEchoOnly-tooltip=\u53EA\u540C\u6B65\u4E3B\u673A\u56DE\u4F20\u4FE1\u606F
isEnableRemoteLog-label=\u542F\u7528\u8FDC\u7A0B\u65E5\u5FD7
isEnableLocalLog-label=\u542F\u7528\u672C\u5730\u65E5\u5FD7
isError-label=\u9519\u8BEF
isForcePushGet-label=\u6FC0\u6D3B\u5728OSD\u63A8\u9001\u754C\u9762\u9AD8\u7EA7\u7528\u6237\u6743\u9650\u76F4\u63A5\u5411\u4F4E\u7EA7\u7528\u6237\u6743\u9650\u76F4\u63A5\u63A8\u9001\u89C6\u9891\u548C\u6293\u53D6\u64CD\u4F5C
isInfo-label=\u4FE1\u606F
isLanEcho-label=\u5141\u8BB8\u7F51\u53E3\u56DE\u4F20\u5207\u6362\u547D\u4EE4
connectMode-label=RX\u65E0\u767B\u5F55\u4E0B,\u63A8\u9001\u4E0E\u6293\u53D6\u7684\u9ED8\u8BA4\u6A21\u5F0F:
isLogin-label=\u5F3A\u5236\u7528\u6237\u767B\u5F55\u8FDB\u5165OSD\uFF08\u5168\u5C40\u914D\u7F6E\uFF09
isNotice-label=\u901A\u77E5
isRedundancy-tooltip=\u6FC0\u6D3B\u63A5\u5165\u7AEF\u7BA1\u63A7\u7AEF\u5197\u4F59\u94FE\u8DEF\u81EA\u52A8\u5207\u6362\u529F\u80FD
isSnmp-label=\u6FC0\u6D3BSNMP\u529F\u80FD
isSynchronize-tooltip=\u540C\u6B65\u5907\u4EFD\u4E3B\u673A\u4E0E\u4E3B\u7528\u4E3B\u673A\u4E4B\u95F4\u7F51\u7EDC\u5907\u4EFD\u4FE1\u606F
isUart-label=\u6FC0\u6D3B\u7B2C\u4E09\u65B9\u4E32\u53E3\u63A7\u5236
isUserAccess-label=\u5141\u8BB8\u65B0\u7528\u6237\u5BF9\u63A5\u5165\u7AEF\u6240\u6709\u6743\u9650
isUserLock-label=\u5141\u8BB8\u7528\u6237OSD\u6743\u9650\u7BA1\u7406\uFF08\u5168\u5C40\u914D\u7F6E\uFF09
isWarning-label=\u8B66\u544A
logServerIp-label=\u8FDC\u7A0B\u670D\u52A1\u5730\u5740:
macAddress1-label=MAC\u5730\u5740:
masterIp-label=\u4E3B\u7528/\u5907\u7528\u4E3B\u673AIP\u5730\u5740\u8BBE\u7F6E
isDefaultBackup-tooltip=\u9ED8\u8BA4\u4E3A\u5907\u7528\u4E3B\u673A
virtualIp-label=\u865A\u62DFIP
virtualRouteId-label=\u865A\u62DF\u8DEF\u7531ID
virtualRouteId-tooltip=\u865A\u62DF\u8DEF\u7531ID\uFF0C\u4E3B\u5907\u4E3B\u673A\u4F7F\u7528\u540C\u4E00\u4E2AID
netMask1-label=\u5B50\u7F51\u63A9\u7801:
netMask2-label=\u5B50\u7F51\u63A9\u7801:
notes-label=\u4E3B\u673A\u5907\u6CE8\u4FE1\u606F\uFF1A
osdWarning-label=OSD\u5F39\u7A97
snmp1Address-label=SNMP Trap1\u670D\u52A1 IP\u8BBE\u7F6E:
snmp1Address-tooltip=\uFF08\u4FEE\u6539IP\u9700\u8981\u91CD\u542F\u4E3B\u673A\uFF09
snmp1IsFanTray1-label=\u98CE\u62471
snmp1IsFanTray2-label=\u98CE\u62472
snmp1IsInsertBoard-label=\u63D2\u5165I/O\u677F\u5361
snmp1IsInsertExtender-label=\u63D2\u5165\u6269\u5C55\u5668
snmp1IsInvalidBoard-label=\u65E0\u6548I/O\u677F\u5361
snmp1IsPowerSupply1-label=\u7535\u6E90\u6A21\u57571
snmp1IsPowerSupply2-label=\u7535\u6E90\u6A21\u57572
snmp1IsPowerSupply3-label=\u7535\u6E90\u6A21\u57573
snmp1IsRemoveBoard-label=\u79FB\u9664I/O\u677F\u5361
snmp1IsRemoveExtender-label=\u79FB\u9664\u6269\u5C55\u5668
snmp1IsStatus-label=\u72B6\u6001
snmp1IsSwitchCommand-label=\u5207\u6362\u547D\u4EE4
snmp1IsTemperature-label=\u6E29\u5EA6
snmp1IsTrap-label=\u5141\u8BB8\u8FC7\u6EE4
snmp1TrapPort-label=\u7AEF\u53E3
snmp2Address-label=SNMP Trap2\u670D\u52A1 IP\u8BBE\u7F6E:
snmp2Address-tooltip=\uFF08\u4FEE\u6539IP\u9700\u8981\u91CD\u542F\u4E3B\u673A\uFF09
snmp2IsFanTray1-label=\u98CE\u62471
snmp2IsFanTray2-label=\u98CE\u62472
snmp2IsInsertBoard-label=\u63D2\u5165I/O\u677F\u5361
snmp2IsInsertExtender-label=\u63D2\u5165\u6269\u5C55\u5668
snmp2IsInvalidBoard-label=\u65E0\u6548I/O\u677F\u5361
snmp2IsPowerSupply1-label=\u7535\u6E90\u6A21\u57571
snmp2IsPowerSupply2-label=\u7535\u6E90\u6A21\u57572
snmp2IsPowerSupply3-label=\u7535\u6E90\u6A21\u57573
snmp2IsRemoveBoard-label=\u79FB\u9664I/O\u677F\u5361
snmp2IsRemoveExtender-label=\u79FB\u9664\u6269\u5C55\u5668
snmp2IsStatus-label=\u72B6\u6001
snmp2IsSwitchCommand-label=\u5207\u6362\u547D\u4EE4
snmp2IsTemperature-label=\u6E29\u5EA6
snmp2IsTrap-label=\u5141\u8BB8\u8FC7\u6EE4
snmp2TrapPort-label=\u7AEF\u53E3
snmpPort-label=\u7AEF\u53E3
time-label=\u65F6\u95F4:
time-tooltip=\u83B7\u53D6\u672C\u5730\u65F6\u95F4
timeoutDisconnect-label=\u63A5\u5165\u7AEF\u4E0D\u64CD\u4F5C
timeoutDisconnect-tooltip=\u5206\u949F\u540E\u65AD\u5F00\u8FDE\u63A5(0 = \u672A\u6FC0\u6D3B)
timeoutDisplay-label=\u8C03\u51FAOSD
timeoutDisplay-tooltip=\u79D2\u540E\u9000\u51FAOSD\u754C\u9762(0 = \u672A\u6FC0\u6D3B)
timeoutLogout-label=\u7528\u6237\u4E0D\u6D3B\u52A8
timeoutLogout-tooltip=\u5206\u949F\u540E\u9000\u51FA\u7528\u6237(0 = \u672A\u6FC0\u6D3B, -1 = \u4E0D\u53D7\u9650\u5236)
timeoutShare-label=\u952E\u76D8\u9F20\u6807\u6743\u9650\u62A2\u5360\uFF0C\u6743\u9650\u91CA\u653E\u65F6\u95F4\u4E3A
timeoutShare-tooltip=\u79D2
isPwdLogin-label=\u5BC6\u7801
isFaceLogin-label=\u4EBA\u8138
dualMasterVirtualIp-label=\u63A7\u5236\u677F\u5361\u6570\u636E\u540C\u6B65IP:
dualMasterVirtualIp-tooltip=\u540C\u53F0\u4E3B\u673A\u4E3B\u7528/\u5907\u7528\u63A7\u5236\u677F\u5361\u6570\u636E\u540C\u6B65
dualMasterVirtualRouteId-label=\u63A7\u5236\u677F\u5361\u6570\u636E\u540C\u6B65\u8DEF\u7531ID: