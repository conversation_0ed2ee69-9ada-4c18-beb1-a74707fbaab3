package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.datamodel.vp.VpType;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CommitRollback;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.ArrayIterable;
import com.mc.tool.caesar.api.utils.BitFieldEntry;
import com.mc.tool.caesar.api.utils.BitFieldPropertyChangeListener;
import com.mc.tool.caesar.api.utils.CfgError;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.DefaultCommitRollback;
import com.mc.tool.caesar.api.utils.Utilities;
import java.io.ByteArrayOutputStream;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import javafx.util.Pair;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public final class ConfigData extends AbstractData {

  private static final Logger LOG = Logger.getLogger(ConfigData.class.getName());
  public static final String PROPERTY_BASE = "ConfigData.";
  public static final String PROPERTY_GRID_DATA = "ConfigData.GridData";
  public static final String PROPERTY_GRID_INDEX = "ConfigData.GridIndex";
  public static final String PROPERTY_UPDATE = "ConfigData.Update";
  public static final String PROPERTY_TIMEOUT_FLASH = "ConfigData.TimeoutFlash";
  private transient CrWrapper crWrapperConsole;
  private transient CrWrapper crWrapperCpu;
  private transient CrWrapper crWrapperExtender;
  private transient CrWrapper crWrapperFunctionKey;
  private transient CrWrapper crWrapperMatrix;
  private transient CrWrapper crWrapperPort;
  private transient CrWrapper crWrapperUser;
  private transient CrWrapper crWrapperUserGroup;
  private transient CrWrapper crWrapperMultiScreen;
  private transient CrWrapper crWrapperTxRxGroup;

  @Getter
  @Expose
  private final ConfigMetaData configMetaData;
  @Getter
  @Expose
  private final SystemConfigData systemConfigData;
  @Expose
  private CpuData[] cpuData;
  @Expose
  private ConsoleData[] consoleData;

  @Expose
  private VpConsoleData[] vpConsoleData;
  private Map<Pair<VpType, Integer>, Integer> vpIpIndexMap = new HashMap<>();
  @Expose
  private ExtenderData[] extenderData;
  @Expose
  private UserData[] userData;
  @Expose
  private UserGroupData[] userGroupData;
  @Expose
  private MultiScreenData[] multiScreenData;
  @Expose
  private FunctionKeyData[] functionKeyData;
  @Expose
  private TxRxGroupData[] txRxGroupData;
  @Expose
  private BranchData[] branchData;
  @Expose
  private MultiviewData[] multiviewData;
  @Expose
  private SourceCropData[] sourceCropData;

  /**
   * .
   */
  public enum DataPart {
    SYSTEM, CPU, CONSOLE, EXTENDER, USER, USER_GROUP,
    MULTISCREEN, FUNCTION_KEY, PORT, MATRIX, TXRXGROUP, BRANCH, MULTIVIEW, SOURCE_CROP;

    DataPart() {
    }

    public static boolean isValid(ConfigMetaData configMetaData, Collection<DataPart> dataParts) {
      return dataParts.containsAll(EnumSet.of(CONSOLE, CPU, EXTENDER, FUNCTION_KEY,
          PORT, USER, MATRIX, TXRXGROUP, BRANCH, MULTIVIEW, SOURCE_CROP));
    }
  }

  @Expose
  private MatrixData[] matrixData = null;
  @Expose
  private PortData[] portData;
  @Getter
  @Expose
  private final MatrixData gridData; // 当前设备的矩阵数据
  @Getter
  @Expose
  private int gridIndex = -1; //级联中的主用矩阵的索引
  @Getter
  @Expose
  private int update;
  @Getter
  @Expose
  private int timeoutFlash;
  @Expose
  private final Collection<DataPart> dataParts = EnumSet.noneOf(DataPart.class);
  @Getter
  private boolean verbose;
  @Getter
  private boolean initialized = false;
  @Getter
  @Setter
  @Expose
  private MatrixStatus matrixStatus = new MatrixStatus(false, false, false);

  /**
   * .
   */
  public ConfigData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager) {
    super(pcs, configDataManager, 0, "ConfigData");

    this.configMetaData = new ConfigMetaData(pcs, configDataManager, getFqn() + ".ConfigMetaData");
    configDataManager.setConfigMetaData(this.configMetaData);

    this.systemConfigData = new SystemConfigData(pcs, configDataManager, getFqn() + ".SystemData");
    this.gridData = new MatrixData(pcs, configDataManager, 0, getFqn() + ".GridData[" + 0 + "]");
  }

  /**
   * 初始化数据.
   */
  public void initArrays() {
    this.cpuData = new CpuData[this.configMetaData.getCpuCount()];
    this.consoleData = new ConsoleData[this.configMetaData.getConsoleCount()];
    this.vpConsoleData = new VpConsoleData[this.configMetaData.getVpconsoleCount()];
    this.extenderData = new ExtenderData[this.configMetaData.getExtenderCount()];
    this.userData = new UserData[this.configMetaData.getUserCount()];
    this.userGroupData = new UserGroupData[this.configMetaData.getUserGroupCount()];
    this.multiScreenData = new MultiScreenData[this.configMetaData.getMultiScreenCount()];
    this.functionKeyData = new FunctionKeyData[this.configMetaData.getFunctionKeyCount()];
    this.matrixData = new MatrixData[this.configMetaData.getMatrixCount()];
    this.portData = new PortData[this.configMetaData.getPortCount()];
    this.txRxGroupData = new TxRxGroupData[this.configMetaData.getTxRxGroupCount()];
    this.branchData = new BranchData[this.configMetaData.getBranchDataCount()];
    this.multiviewData = new MultiviewData[this.configMetaData.getMultiviewDataCount()];
    this.sourceCropData = new SourceCropData[this.configMetaData.getSourceCropDataCount()];

    CustomPropertyChangeSupport pcs = getPropertyChangeSupport();
    ConfigDataManager configDataManager = getConfigDataManager();
    for (int idx = 0; idx < this.cpuData.length; idx++) {
      this.cpuData[idx] =
          new CpuData(pcs, configDataManager, idx, getFqn() + ".CpuData[" + idx + "]");
    }
    for (Map.Entry<String, Collection<BitFieldEntry>> entry : CpuData.BIT_FIELD_MAP.entrySet()) {
      if (null != entry.getValue() && !entry.getValue().isEmpty()) {
        pcs.addPropertyChangeListener(entry.getKey(),
            new BitFieldPropertyChangeListener(pcs, entry.getValue()));
      }
    }
    for (int idx = 0; idx < this.consoleData.length; idx++) {
      this.consoleData[idx] =
          new ConsoleData(pcs, configDataManager, idx, getFqn() + ".ConsoleData[" + idx + "]");
    }

    for (int idx = 0; idx < this.vpConsoleData.length; idx++) {
      this.vpConsoleData[idx] = new VpConsoleData(pcs, configDataManager, idx,
          getFqn() + ".VpConsoleData[" + idx + "]", VpType.VP6);
    }

    for (Map.Entry<String, Collection<BitFieldEntry>> entry : ConsoleData.BIT_FIELD_MAP
        .entrySet()) {
      if (null != entry.getValue() && !entry.getValue().isEmpty()) {
        pcs.addPropertyChangeListener(entry.getKey(),
            new BitFieldPropertyChangeListener(pcs, entry.getValue()));
      }
    }
    for (int idx = 0; idx < this.extenderData.length; idx++) {
      this.extenderData[idx] =
          new ExtenderData(pcs, configDataManager, idx, getFqn() + ".ExtenderData[" + idx + "]");
    }
    for (Map.Entry<String, Collection<BitFieldEntry>> entry : ExtenderData.BIT_FIELD_MAP
        .entrySet()) {
      if (null != entry.getValue() && !entry.getValue().isEmpty()) {
        pcs.addPropertyChangeListener(entry.getKey(),
            new BitFieldPropertyChangeListener(pcs, entry.getValue()));
      }
    }
    for (int idx = 0; idx < this.userData.length; idx++) {
      this.userData[idx] =
          new UserData(pcs, configDataManager, idx, getFqn() + ".UserData[" + idx + "]");
    }
    for (int idx = 0; idx < this.userGroupData.length; idx++) {
      this.userGroupData[idx] =
          new UserGroupData(pcs, configDataManager, idx, getFqn() + ".UserGroupData[" + idx + "]");
    }
    for (int idx = 0; idx < this.multiScreenData.length; idx++) {
      this.multiScreenData[idx] = new MultiScreenData(pcs, configDataManager, idx,
          getFqn() + ".MultiScreenData[" + idx + "]");
    }

    for (Map.Entry<String, Collection<BitFieldEntry>> entry : UserData.BIT_FIELD_MAP.entrySet()) {
      if (null != entry.getValue() && !entry.getValue().isEmpty()) {
        pcs.addPropertyChangeListener(entry.getKey(),
            new BitFieldPropertyChangeListener(pcs, entry.getValue()));
      }
    }
    for (int idx = 0; idx < this.functionKeyData.length; idx++) {
      this.functionKeyData[idx] = new FunctionKeyData(pcs, configDataManager, idx,
          getFqn() + ".FunctionKeyData[" + idx + "]");
    }
    for (int idx = 0; idx < this.matrixData.length; idx++) {
      this.matrixData[idx] =
          new MatrixData(pcs, configDataManager, idx, getFqn() + ".MatrixData[" + idx + "]");
    }
    for (int idx = 0; idx < this.portData.length; idx++) {
      this.portData[idx] =
          new PortData(pcs, configDataManager, idx, getFqn() + ".PortData[" + idx + "]");
    }
    for (int idx = 0; idx < this.txRxGroupData.length; idx++) {
      this.txRxGroupData[idx] =
          new TxRxGroupData(pcs, configDataManager, idx, getFqn() + ".TxRxGroupData[" + idx + "]");
    }
    for (int idx = 0; idx < this.branchData.length; idx++) {
      this.branchData[idx] =
          new BranchData(pcs, configDataManager, idx, getFqn() + ".BranchData[" + idx + "]");
    }
    for (int idx = 0; idx < this.multiviewData.length; idx++) {
      MultiviewData md =
          new MultiviewData(pcs, configDataManager, idx, getFqn() + ".MultiviewData[" + idx + "]");
      this.multiviewData[idx] = md;

    }
    for (int idx = 0; idx < this.sourceCropData.length; idx++) {
      this.sourceCropData[idx] = new SourceCropData(
          pcs, configDataManager, idx, getFqn() + ".SourceCropData[" + idx + "]");
    }
    for (Map.Entry<String, Collection<BitFieldEntry>> entry : PortData.BIT_FIELD_MAP.entrySet()) {
      if (null != entry.getValue() && !entry.getValue().isEmpty()) {
        pcs.addPropertyChangeListener(entry.getKey(),
            new BitFieldPropertyChangeListener(pcs, entry.getValue()));
      }
    }
    this.crWrapperConsole = new CrWrapper(this.consoleData);
    this.crWrapperCpu = new CrWrapper(this.cpuData);
    this.crWrapperExtender = new CrWrapper(this.extenderData);
    this.crWrapperFunctionKey = new CrWrapper(this.functionKeyData);
    this.crWrapperMatrix = new CrWrapper(this.matrixData);
    this.crWrapperPort = new CrWrapper(this.portData);
    this.crWrapperUser = new CrWrapper(this.userData);
    this.crWrapperUserGroup = new CrWrapper(this.userGroupData);
    this.crWrapperMultiScreen = new CrWrapper(this.multiScreenData);
    this.crWrapperTxRxGroup = new CrWrapper(this.txRxGroupData);

    initCommitRollback();

    this.initialized = true;
  }

  /**
   * 检查数据大小是否符合.
   */
  public boolean checkArrays() {
    if (this.cpuData.length != this.configMetaData.getCpuCount()) {
      LOG.warning(Bundle.configData_checkArrays("CPU"));
      return false;
    }
    if (this.consoleData.length != this.configMetaData.getConsoleCount()) {
      LOG.warning(Bundle.configData_checkArrays("CONSOLE"));
      return false;
    }
    if (this.extenderData.length != this.configMetaData.getExtenderCount()) {
      LOG.warning(Bundle.configData_checkArrays("EXTENDER"));
      return false;
    }
    if (this.userData.length != this.configMetaData.getUserCount()) {
      LOG.warning(Bundle.configData_checkArrays("USER"));
      return false;
    }
    if (this.userGroupData.length != this.configMetaData.getUserGroupCount()) {
      LOG.warning(Bundle.configData_checkArrays("USER_GROUP"));
    }
    if (this.multiScreenData.length != this.configMetaData.getMultiScreenCount()) {
      LOG.warning(Bundle.configData_checkArrays("MULTI_SCREEN"));
    }
    if (this.functionKeyData.length != this.configMetaData.getFunctionKeyCount()) {
      LOG.warning(Bundle.configData_checkArrays("FUNCTIONKEY"));
      return false;
    }
    if (this.matrixData.length != this.configMetaData.getMatrixCount()) {
      LOG.warning(Bundle.configData_checkArrays("MATRIX"));
      return false;
    }
    if (this.portData.length != this.configMetaData.getPortCount()) {
      LOG.warning(Bundle.configData_checkArrays("PORT"));
      return false;
    }
    if (this.txRxGroupData.length != this.configMetaData.getTxRxGroupCount()) {
      LOG.warning(Bundle.configData_checkArrays("TX_GROUP"));
      return false;
    }
    if (this.branchData.length != this.configMetaData.getBranchDataCount()) {
      LOG.warning(Bundle.configData_checkArrays("BRANCH_DATA"));
      return false;
    }
    if (this.multiviewData.length != this.configMetaData.getMultiviewDataCount()) {
      LOG.warning(Bundle.configData_checkArrays("MULTIVIEW_DATA"));
      return false;
    }
    if (this.sourceCropData.length != this.configMetaData.getSourceCropDataCount()) {
      LOG.warning(Bundle.configData_checkArrays("SOURCE_CROP_DATA"));
      return false;
    }
    return true;
  }

  public Collection<DataPart> getDataParts() {
    return Collections.unmodifiableCollection(this.dataParts);
  }

  @Override
  public void initDefaults() {
    this.configMetaData.initDefaults();
    this.configMetaData.commit(Threshold.ALL);

    if (!this.initialized) {
      initArrays();
    }
    this.systemConfigData.initDefaults();
    this.systemConfigData.commit(Threshold.ALL);

    UserData adminUser = getUserData(0);

    adminUser.setName(CaesarConstants.User.ADMIN_NAME);
    adminUser.setPassword(CaesarConstants.User.ADMIN_PASS_WORD);
    adminUser.setRights(-1);
    adminUser.setStatus(CaesarConstants.User.Status.ACTIVE);
    adminUser.commit(Threshold.ALL);

    this.dataParts.add(DataPart.SYSTEM);
    this.dataParts.add(DataPart.CPU);
    this.dataParts.add(DataPart.CONSOLE);
    this.dataParts.add(DataPart.EXTENDER);
    this.dataParts.add(DataPart.USER);
    this.dataParts.add(DataPart.FUNCTION_KEY);
    this.dataParts.add(DataPart.TXRXGROUP);
    this.dataParts.add(DataPart.BRANCH);
    this.dataParts.add(DataPart.PORT);
    this.dataParts.add(DataPart.SOURCE_CROP);
  }

  public void setVerbose(boolean verbose) {
    this.verbose = verbose;
  }

  /**
   * .
   */
  public void setGridIndex(int gridIndex) {
    int oldValue = this.gridIndex;
    this.gridIndex = gridIndex;
    firePropertyChange(ConfigData.PROPERTY_GRID_INDEX, oldValue, this.gridIndex);
  }

  /**
   * .
   */
  public void setTimeoutFlash(int timeoutFlash) {
    int oldValue = this.timeoutFlash;
    this.timeoutFlash = timeoutFlash;
    firePropertyChange(ConfigData.PROPERTY_TIMEOUT_FLASH, oldValue, this.timeoutFlash);
  }

  /**
   * .
   */
  public void setUpdate(int update) {
    int oldValue = this.update;
    this.update = update;
    firePropertyChange(ConfigData.PROPERTY_UPDATE, oldValue, this.update);
  }

  public ConsoleData getConsoleData(int idx) {
    return this.consoleData[idx];
  }

  public VpConsoleData getVpConsoleData(int idx) {
    return this.vpConsoleData[idx];
  }

  public CpuData getCpuData(int idx) {
    return this.cpuData[idx];
  }

  public ExtenderData getExtenderData(int idx) {
    return this.extenderData[idx];
  }

  public FunctionKeyData getFunctionKeyData(int idx) {
    return this.functionKeyData[idx];
  }

  public MatrixData getMatrixData(int idx) {
    return this.matrixData[idx];
  }

  public PortData getPortData(int idx) {
    return this.portData[idx];
  }

  public UserData getUserData(int idx) {
    return this.userData[idx];
  }

  public UserGroupData getUserGroupData(int idx) {
    return this.userGroupData[idx];
  }

  public MultiScreenData getMultiScreenData(int idx) {
    return this.multiScreenData[idx];
  }

  public TxRxGroupData getTxRxGroupData(int idx) {
    return this.txRxGroupData[idx];
  }

  public BranchData getBranchData(int idx) {
    return this.branchData[idx];
  }

  public MultiviewData getMultiviewData(int idx) {
    return this.multiviewData[idx];
  }

  public SourceCropData getSourceCropData(int idx) {
    return this.sourceCropData[idx];
  }

  /**
   * .
   */
  public Iterable<BranchData> getBranchDatas() {
    if (this.branchData == null) {
      return new ArrayIterable<>(new BranchData[0]);
    }
    return new ArrayIterable<>(this.branchData);
  }

  /**
   * .
   */
  public Iterable<MultiviewData> getMultiviewDatas() {
    if (this.multiviewData == null) {
      return new ArrayIterable<>(new MultiviewData[0]);
    }
    return new ArrayIterable<>(this.multiviewData);
  }

  /**
   * .
   */
  public Iterable<SourceCropData> getSourceCropDatas() {
    if (this.sourceCropData == null) {
      return new ArrayIterable<>(new SourceCropData[0]);
    }
    return new ArrayIterable<>(this.sourceCropData);
  }

  @Override
  protected Collection<CommitRollback> getDependentCommitRollbacks() {
    Collection<CommitRollback> commitRollbacks = new ArrayList<>();

    commitRollbacks.add(this.configMetaData);
    commitRollbacks.add(this.systemConfigData);
    commitRollbacks.add(this.crWrapperConsole);
    commitRollbacks.add(this.crWrapperCpu);
    commitRollbacks.add(this.crWrapperExtender);
    commitRollbacks.add(this.crWrapperFunctionKey);
    commitRollbacks.add(this.crWrapperMatrix);
    commitRollbacks.add(this.crWrapperPort);
    commitRollbacks.add(this.gridData);
    commitRollbacks.add(this.crWrapperUser);
    commitRollbacks.add(this.crWrapperUserGroup);
    commitRollbacks.add(this.crWrapperMultiScreen);
    commitRollbacks.add(this.crWrapperTxRxGroup);

    return commitRollbacks;
  }

  public CommitRollback getCrWrapperConsole() {
    return this.crWrapperConsole;
  }

  public CommitRollback getCrWrapperCpu() {
    return this.crWrapperCpu;
  }

  public CommitRollback getCrWrapperExtender() {
    return this.crWrapperExtender;
  }

  public CommitRollback getCrWrapperFunctionKey() {
    return this.crWrapperFunctionKey;
  }

  public CommitRollback getCrWrapperMatrix() {
    return this.crWrapperMatrix;
  }

  public CommitRollback getCrWrapperPort() {
    return this.crWrapperPort;
  }

  public CommitRollback getCrWrapperUser() {
    return this.crWrapperUser;
  }

  public CommitRollback getCrWrapperTxRxGroup() {
    return this.crWrapperTxRxGroup;
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (ConfigData.PROPERTY_GRID_INDEX.equals(propertyName)) {
      setGridIndex((Integer) value);
    } else if (ConfigData.PROPERTY_UPDATE.equals(propertyName)) {
      setUpdate((Integer) value);
    } else if (ConfigData.PROPERTY_TIMEOUT_FLASH.equals(propertyName)) {
      setTimeoutFlash((Integer) value);
    }
  }

  /**
   * .
   */
  public void writeData(CfgWriter cfgWriter, IoMode ioMode) throws ConfigException {
    ByteArrayOutputStream baosCpu = new ByteArrayOutputStream();
    ByteArrayOutputStream baosConsole = new ByteArrayOutputStream();
    ByteArrayOutputStream baosExtender = new ByteArrayOutputStream();
    ByteArrayOutputStream baosUser = new ByteArrayOutputStream();
    ByteArrayOutputStream baosUserGroup = new ByteArrayOutputStream();
    ByteArrayOutputStream baosMultiScreen = new ByteArrayOutputStream();
    ByteArrayOutputStream baosFunctionKey = new ByteArrayOutputStream();
    ByteArrayOutputStream baosMatrix = new ByteArrayOutputStream();
    ByteArrayOutputStream baosTxRxGroup = new ByteArrayOutputStream();
    ByteArrayOutputStream baosBranchData = new ByteArrayOutputStream();
    ByteArrayOutputStream baosMultiviewData = new ByteArrayOutputStream();
    ByteArrayOutputStream baosSourceCropData = new ByteArrayOutputStream();
    ByteArrayOutputStream baosPort = new ByteArrayOutputStream();

    CfgWriter writerCpu = new CfgWriter(baosCpu);
    CfgWriter writerConsole = new CfgWriter(baosConsole);
    CfgWriter writerExtender = new CfgWriter(baosExtender);
    CfgWriter writerUser = new CfgWriter(baosUser);
    CfgWriter writerUserGroup = new CfgWriter(baosUserGroup);
    CfgWriter writerMultiScreen = new CfgWriter(baosMultiScreen);
    CfgWriter writerFunctionKey = new CfgWriter(baosFunctionKey);
    CfgWriter writerMatrix = new CfgWriter(baosMatrix);
    CfgWriter writerTxRxGroup = new CfgWriter(baosTxRxGroup);
    CfgWriter writerBranchData = new CfgWriter(baosBranchData);
    CfgWriter writerMultiviewData = new CfgWriter(baosMultiviewData);
    CfgWriter writerSourceCliData = new CfgWriter(baosSourceCropData);
    CfgWriter writerPort = new CfgWriter(baosPort);

    Collection<CpuData> activeCpus =
        ioMode.getObjects(Arrays.asList(this.cpuData), getConfigDataManager().getActiveCpus());
    for (CpuData activeCpu : activeCpus) {
      if (ioMode.isAvailable()) {
        writerCpu.write2ByteSmallEndian(activeCpu.getOid());
      }
      activeCpu.writeData(writerCpu);
    }
    Collection<ConsoleData> activeConsoles = ioMode.getObjects(Arrays.asList(this.consoleData),
        getConfigDataManager().getActiveConsoles());
    for (ConsoleData activeConsole : activeConsoles) {
      if (ioMode.isAvailable()) {
        writerConsole.write2ByteSmallEndian(activeConsole.getOid());
      }
      activeConsole.writeData(writerConsole);
    }
    Collection<ExtenderData> activeExtenders = ioMode.getObjects(Arrays.asList(this.extenderData),
        getConfigDataManager().getActiveExtenders());
    for (ExtenderData activeExtender : activeExtenders) {
      if (ioMode.isAvailable()) {
        writerExtender.write2ByteSmallEndian(activeExtender.getOid());
      }
      activeExtender.writeData(writerExtender);
    }
    Collection<MatrixData> activeMatrices = ioMode.getObjects(Arrays.asList(this.matrixData),
        getConfigDataManager().getActiveMatrices());
    for (MatrixData activeMatrix : activeMatrices) {
      if (ioMode.isAvailable()) {
        writerMatrix.write2ByteSmallEndian(activeMatrix.getOid());
      }
      activeMatrix.writeData(writerMatrix);
    }
    Collection<TxRxGroupData> activeTxRxGroups = ioMode.getObjects(Arrays.asList(this.txRxGroupData),
        getConfigDataManager().getActiveTxRxGroups());
    for (TxRxGroupData activeTxRxGroup : activeTxRxGroups) {
      if (ioMode.isAvailable()) {
        writerTxRxGroup.write2ByteSmallEndian(activeTxRxGroup.getOid());
      }
      activeTxRxGroup.writeData(writerTxRxGroup);
    }
    Collection<BranchData> activeBranchData = Arrays.asList(this.branchData);
    for (BranchData branchData : activeBranchData) {
      if (ioMode.isAvailable()) {
        writerBranchData.write2ByteSmallEndian(branchData.getOid());
      }
      branchData.writeData(writerBranchData);
    }
    Collection<MultiviewData> activeMultiviewData = Arrays.asList(this.multiviewData);
    for (MultiviewData multiviewData : activeMultiviewData) {
      if (ioMode.isAvailable()) {
        writerMultiviewData.write2ByteSmallEndian(multiviewData.getOid());
      }
      multiviewData.writeData(writerMultiviewData);
    }
    Collection<SourceCropData> activeSourceCropData = Arrays.asList(this.sourceCropData);
    for (SourceCropData sourceCropData : activeSourceCropData) {
      if (ioMode.isAvailable()) {
        writerSourceCliData.write2ByteSmallEndian(sourceCropData.getOid());
      }
      sourceCropData.writeData(writerSourceCliData);
    }
    Collection<PortData> activePorts =
        ioMode.getObjects(Arrays.asList(this.portData), Collections.emptyList());
    for (PortData activePort : activePorts) {
      if (ioMode.isAvailable()) {
        writerPort.write2ByteSmallEndian(activePort.getOid());
      }
      activePort.writeData(writerPort);
    }
    Collection<UserData> activeUsers =
        ioMode.getObjects(Arrays.asList(this.userData), getConfigDataManager().getActiveUsers());
    for (UserData activeUser : activeUsers) {
      if (ioMode.isAvailable()) {
        writerUser.write2ByteSmallEndian(activeUser.getOid());
      }
      activeUser.writeData(writerUser);
    }
    Collection<UserGroupData> activeUserGroups = ioMode.getObjects(
        Arrays.asList(this.userGroupData), getConfigDataManager().getActiveUserGroups());
    for (UserGroupData activeUserGroup : activeUserGroups) {
      if (ioMode.isAvailable()) {
        writerUserGroup.write2ByteSmallEndian(activeUserGroup.getOid());
      }
      activeUserGroup.writeData(writerUserGroup);
    }

    Collection<MultiScreenData> activeMultiScreens = ioMode.getObjects(
        Arrays.asList(this.multiScreenData), getConfigDataManager().getActiveMultiScreen());
    for (MultiScreenData activeMultiScreen : activeMultiScreens) {
      if (ioMode.isAvailable()) {
        writerMultiScreen.write2ByteSmallEndian(activeMultiScreen.getOid());
      }
      activeMultiScreen.writeData(writerMultiScreen);
    }

    this.configMetaData.setFunctionKeyCountActive(0);
    for (int idxFunctionKey = 0; idxFunctionKey < getConfigMetaData()
        .getFunctionKeyCount(); idxFunctionKey++) {
      FunctionKeyData functionKey = this.functionKeyData[idxFunctionKey];
      if (functionKey.isStatusActive()) {
        this.configMetaData
            .setFunctionKeyCountActive(this.configMetaData.getFunctionKeyCountActive() + 1);
        if (ioMode.isAvailable()) {
          writerFunctionKey.write2ByteSmallEndian(functionKey.getOid());
        }
        functionKey.writeData(writerFunctionKey);
      }
    }
    this.configMetaData.setConsoleCountActive(activeConsoles.size());
    this.configMetaData.setCpuCountActive(activeCpus.size());
    this.configMetaData.setExtenderCountActive(activeExtenders.size());
    this.configMetaData.setPortCountActive(activePorts.size());
    this.configMetaData.setMatrixCountActive(activeMatrices.size());
    this.configMetaData.setUserCountActive(activeUsers.size());
    this.configMetaData.setUserGroupActive(activeUserGroups.size());
    this.configMetaData.setMultiScreenActive(activeMultiScreens.size());
    this.configMetaData.setTxRxGroupCountActive(activeTxRxGroups.size());
    this.configMetaData.setBranchDataActive(activeBranchData.size());
    this.configMetaData.setMultiviewDataActive(activeMultiviewData.size());
    this.configMetaData.setSourceCropDataActive(activeSourceCropData.size());
    this.configMetaData.writeData(cfgWriter);
    this.configMetaData.commit();
    this.systemConfigData.writeData(cfgWriter);

    cfgWriter.writeByteArray(baosCpu.toByteArray());
    cfgWriter.writeByteArray(baosConsole.toByteArray());
    cfgWriter.writeByteArray(baosExtender.toByteArray());
    cfgWriter.writeByteArray(baosUser.toByteArray());
    cfgWriter.writeByteArray(baosUserGroup.toByteArray());
    cfgWriter.writeByteArray(baosMultiScreen.toByteArray());
    cfgWriter.writeByteArray(baosFunctionKey.toByteArray());
    cfgWriter.writeByteArray(baosMatrix.toByteArray());
    cfgWriter.writeByteArray(baosTxRxGroup.toByteArray());
    cfgWriter.writeByteArray(baosBranchData.toByteArray());
    cfgWriter.writeByteArray(baosMultiviewData.toByteArray());
    cfgWriter.writeByteArray(baosSourceCropData.toByteArray());
    cfgWriter.writeByteArray(baosPort.toByteArray());
  }

  /**
   * .
   */
  public boolean isCurrentVersion() throws ConfigException {
    return checkSize(SystemData.class, this.configMetaData.getSystemSize(), "SystemData")
        && checkSize(CpuData.class, this.configMetaData.getCpuSize(), "CpuData") && checkSize(
        ConsoleData.class, this.configMetaData.getConsoleSize(), "ConsoleData") && checkSize(
        ExtenderData.class, this.configMetaData.getExtenderSize(), "ExtenderData") && checkSize(
        UserData.class, this.configMetaData.getUserSize(), "UserData") && checkSize(
        FunctionKeyData.class, this.configMetaData.getFunctionKeySize(), "FunctionKeyData")
        && checkSize(MatrixData.class, this.configMetaData.getMatrixSize(), "MatrixData")
        && checkSize(TxRxGroupData.class, this.configMetaData.getTxRxGroupSize(), "TxRxGroupData")
        && checkSize(PortData.class, this.configMetaData.getPortSize(), "PortData");
  }

  private boolean checkSize(Class<?> clazz, int typeSize, String objectName)
      throws ConfigException {
    int refSize = configMetaData.getUtilVersion().getClassSize(clazz);
    if (refSize != typeSize) {
      throw new ConfigException(CfgError.IO,
          String.format("The size of %s does not match! expected: %d, received: %d", objectName,
              refSize, typeSize));
    }
    int currentTypeSize = this.configMetaData.getCurrentSize(clazz);
    return currentTypeSize == typeSize;
  }

  /**
   * .
   */
  public void readData(CfgReader cfgReader, IoMode ioMode) throws ConfigException {
    this.dataParts.clear();
    if (cfgReader.available() > 2) {
      this.configMetaData.readData(cfgReader);
      this.configMetaData.checkSize();
      if (!this.initialized) {
        initArrays();
      } else if (!checkArrays()) {
        initArrays();
      }
    }
    if (this.configMetaData.getExtenderCount() != this.extenderData.length) {
      this.extenderData = new ExtenderData[this.configMetaData.getExtenderCount()];
      for (int idx = 0; idx < this.extenderData.length; idx++) {
        this.extenderData[idx] = new ExtenderData(getPropertyChangeSupport(),
            getConfigDataManager(), idx, getFqn() + ".ExtenderData[" + idx + "]");
      }
      this.crWrapperExtender = new CrWrapper(this.extenderData);
    }
    this.systemConfigData.readData(cfgReader);
    this.dataParts.add(DataPart.SYSTEM);
    if (!this.configMetaData.getUtilVersion().isConnectable()) {
      return;
    }
    for (int idx = 0; idx < ioMode.getReadCount(this.configMetaData.getCpuCount(),
        this.configMetaData.getCpuCountActive()); idx++) {
      int index = ioMode.isAvailable() ? cfgReader.read2ByteValue() : idx;
      getCpuData(index).readData(cfgReader);
    }
    this.dataParts.add(DataPart.CPU);
    for (int idx = 0; idx < ioMode.getReadCount(this.configMetaData.getConsoleCount(),
        this.configMetaData.getConsoleCountActive()); idx++) {
      int index = ioMode.isAvailable() ? cfgReader.read2ByteValue() : idx;
      getConsoleData(index).readData(cfgReader);
    }
    this.dataParts.add(DataPart.CONSOLE);

    for (int idx = 0; idx < ioMode.getReadCount(this.configMetaData.getExtenderCount(),
        this.configMetaData.getExtenderCountActive()); idx++) {
      int index = ioMode.isAvailable() ? cfgReader.read2ByteValue() : idx;
      getExtenderData(index).readData(cfgReader);
    }
    this.dataParts.add(DataPart.EXTENDER);
    for (int idx = 0; idx < ioMode.getReadCount(this.configMetaData.getUserCount(),
        this.configMetaData.getUserCountActive()); idx++) {
      int index = ioMode.isAvailable() ? cfgReader.read2ByteValue() : idx;
      getUserData(index).readData(cfgReader);
    }
    this.dataParts.add(DataPart.USER);

    for (int idx = 0; idx < ioMode.getReadCount(this.configMetaData.getUserGroupCount(),
        this.configMetaData.getUserGroupActive()); idx++) {
      int index = ioMode.isAvailable() ? cfgReader.read2ByteValue() : idx;
      getUserGroupData(index).readData(cfgReader);
    }
    this.dataParts.add(DataPart.USER_GROUP);

    for (int idx = 0; idx < ioMode.getReadCount(this.configMetaData.getMultiScreenCount(),
        this.configMetaData.getMultiScreenActive()); idx++) {
      int index = ioMode.isAvailable() ? cfgReader.read2ByteValue() : idx;
      getMultiScreenData(index).readData(cfgReader);
    }
    this.dataParts.add(DataPart.MULTISCREEN);

    for (int idx = 0; idx < ioMode.getReadCount(this.configMetaData.getFunctionKeyCount(),
        this.configMetaData.getFunctionKeyCountActive()); idx++) {
      if (ioMode.isAvailable()) {
        cfgReader.read2ByteValue();
      }
      getFunctionKeyData(idx).readData(cfgReader);
    }
    this.dataParts.add(DataPart.FUNCTION_KEY);
    for (int idx = 0; idx < ioMode.getReadCount(this.configMetaData.getMatrixCount(),
        this.configMetaData.getMatrixCountActive()); idx++) {
      int index = ioMode.isAvailable() ? cfgReader.read2ByteValue() : idx;
      getMatrixData(index).readData(cfgReader);
    }
    this.dataParts.add(DataPart.MATRIX);

    for (int idx = 0; idx < ioMode.getReadCount(this.configMetaData.getTxRxGroupCount(),
        this.configMetaData.getTxRxGroupCountActive()); idx++) {
      int index = ioMode.isAvailable() ? cfgReader.read2ByteValue() : idx;
      getTxRxGroupData(index).readData(cfgReader);
    }
    this.dataParts.add(DataPart.TXRXGROUP);
    for (int idx = 0; idx < ioMode.getReadCount(this.configMetaData.getBranchDataCount(),
        this.configMetaData.getBranchDataActive()); idx++) {
      int index = ioMode.isAvailable() ? cfgReader.read2ByteValue() : idx;
      getBranchData(index).readData(cfgReader);
    }
    this.dataParts.add(DataPart.BRANCH);
    for (int idx = 0; idx < ioMode.getReadCount(this.configMetaData.getMultiviewDataCount(),
        this.configMetaData.getMultiviewDataActive()); idx++) {
      int index = ioMode.isAvailable() ? cfgReader.read2ByteValue() : idx;
      getMultiviewData(index).readData(cfgReader);
    }
    this.dataParts.add(DataPart.MULTIVIEW);
    for (int idx = 0; idx < ioMode.getReadCount(this.configMetaData.getSourceCropDataCount(),
        this.configMetaData.getSourceCropDataActive()); idx++) {
      int index = ioMode.isAvailable() ? cfgReader.read2ByteValue() : idx;
      getSourceCropData(index).readData(cfgReader);
    }
    this.dataParts.add(DataPart.SOURCE_CROP);
    for (int idx = 0; idx < ioMode.getReadCount(this.configMetaData.getPortCount(),
        this.configMetaData.getPortCountActive()); idx++) {
      int index = ioMode.isAvailable() ? cfgReader.read2ByteValue() : idx;
      getPortData(index).readData(cfgReader);
    }
    this.dataParts.add(DataPart.PORT);

    // parse vpcon
    readVpcon();
  }


  protected VpConsoleData getFreeVpConsoleData() {
    Set<Integer> indexs = new HashSet<>(vpIpIndexMap.values());
    for (int i = 0; i < vpConsoleData.length; i++) {
      if (!indexs.contains(i)) {
        return vpConsoleData[i];
      }
    }
    return null;
  }

  /**
   * 从consoledata中分析vpcon的数据.
   */
  public void readVpcon() {
    for (VpConsoleData item : this.vpConsoleData) {
      item.setStatusActive(false);
      item.setStatusOnline(false);
    }

    for (int idx = 0; idx < configMetaData.getConsoleCount(); idx++) {
      ConsoleData consoleData = getConsoleData(idx);
      if (!consoleData.isStatusActive() || !consoleData.isStatusVpcon()) {
        continue;
      }
      ExtenderData extenderData = consoleData.getExtenderData(0);
      if (extenderData == null) {
        continue;
      }
      if (!extenderData.isStatusActive()) {
        continue;
      }
      VpType vpType = extenderData.getVpType();
      if (vpType == null) {
        LOG.log(
            Level.SEVERE,
            MessageFormat.format("Extender {0}'s vp type is null!", extenderData.getName()));
        continue;
      }
      int vpConId = Utilities.getVpconId(extenderData);
      VpConsoleData vpCon;
      if (!vpIpIndexMap.containsKey(new Pair<>(vpType, vpConId))) {
        vpCon = getFreeVpConsoleData();

        vpCon.reBuild(vpType);
        vpCon.setStatusActive(true);
        vpCon.setStatusOnline(true);
        vpCon.setId(vpConId);
        vpCon.setName(vpType.getBuilder().formatName(vpConId));
        vpIpIndexMap.put(new Pair<>(vpType, vpConId), vpCon.getOid());
        // 创建VP6的子输出数据
        if (vpType == VpType.VP6) {
          for (int i = 0; i < vpType.getOutPortCount(); i++) {

            VpConsoleData subCon = vpCon.getOutPortList()[i];
            if (subCon == null) {
              subCon = new VpConsoleData(getPropertyChangeSupport(), getConfigDataManager(), i,
                  vpCon.getFqn() + "." + vpConId, VpType.VP6_SUB);
            }
            subCon.reBuild(VpType.VP6_SUB, vpCon);
            subCon.setStatusActive(true);
            subCon.setStatusOnline(true);
            subCon.setId(vpConId + i);
            subCon.setName(VpType.VP6_SUB.getBuilder().formatName(subCon.getId()));
            vpCon.setOutPort(i, subCon);
          }
        } else if (vpType == VpType.VP7) {
          for (int i = 0; i < vpType.getOutPortCount(); i++) {
            VpConsoleData subCon = vpCon.getOutPortList()[i];
            if (subCon == null) {
              subCon = new VpConsoleData(getPropertyChangeSupport(), getConfigDataManager(), i,
                  vpCon.getFqn() + "." + vpConId, VpType.VP7_SUB);
            }
            subCon.reBuild(VpType.VP7_SUB, vpCon);
            subCon.setStatusActive(true);
            subCon.setStatusOnline(true);
            // VP7的输出ID由CON ID + 序号组成
            subCon.setId((i << 16) + consoleData.getId());
            subCon.setName(VpType.VP7_SUB.getBuilder().formatName(subCon.getId()));
            vpCon.setOutPort(i, subCon);
          }
        }
      } else {
        vpCon = vpConsoleData[vpIpIndexMap.get(new Pair<>(vpType, vpConId))];
        vpCon.setStatusActive(true);
        vpCon.setStatusOnline(true);
      }
      vpCon.setInPort(Utilities.getVpconInputIndex(extenderData), consoleData);
    }

    //如果第一个端口不在线，那么online设置为false
    int activeCount = 0;
    for (Map.Entry<Pair<VpType, Integer>, Integer> entry : vpIpIndexMap.entrySet()) {
      int index = entry.getValue();
      if (index < 0 || index >= vpConsoleData.length) {
        continue;
      }
      VpConsoleData vpcon = vpConsoleData[index];
      ConsoleData firstPort = vpcon.getInPort(0);
      if (firstPort == null || firstPort.getExtenderData(0) == null
          || firstPort.getExtenderData(0).getPort() == 0) {
        vpcon.setStatusOnline(false);
      } else {
        activeCount++;
      }
    }

    configMetaData.setVpconsoleCountActive(activeCount);
  }

  /**
   * .
   */
  public Iterable<ConsoleData> getConsoleDatas() {
    if (this.consoleData == null) {
      return new ArrayIterable<>(new ConsoleData[0]);
    }
    return new ArrayIterable<>(this.consoleData);
  }

  /**
   * .
   */
  public Iterable<VpConsoleData> getVpConsoleDatas() {
    if (this.vpConsoleData == null) {
      return new ArrayIterable<>(new VpConsoleData[0]);
    }
    return new ArrayIterable<>(this.vpConsoleData);
  }

  /**
   * .
   */
  public Iterable<CpuData> getCpuDatas() {
    if (this.cpuData == null) {
      return new ArrayIterable<>(new CpuData[0]);
    }
    return new ArrayIterable<>(this.cpuData);
  }

  /**
   * .
   */
  public Iterable<ExtenderData> getExtenderDatas() {
    if (this.extenderData == null) {
      return new ArrayIterable<>(new ExtenderData[0]);
    }
    return new ArrayIterable<>(this.extenderData);
  }

  /**
   * .
   */
  public Iterable<FunctionKeyData> getFunctionKeyDatas() {
    if (this.functionKeyData == null) {
      return new ArrayIterable<>(new FunctionKeyData[0]);
    }
    return new ArrayIterable<>(this.functionKeyData);
  }

  /**
   * .
   */
  public Iterable<MatrixData> getMatrixDatas() {
    if (this.matrixData == null) {
      return new ArrayIterable<>(new MatrixData[0]);
    }
    return new ArrayIterable<>(this.matrixData);
  }

  /**
   * .
   */
  public Iterable<PortData> getPortDatas() {
    if (this.portData == null) {
      return new ArrayIterable<>(new PortData[0]);
    }
    return new ArrayIterable<>(this.portData);
  }

  /**
   * .
   */
  public Iterable<UserData> getUserDatas() {
    if (this.userData == null) {
      return new ArrayIterable<>(new UserData[0]);
    }
    return new ArrayIterable<>(this.userData);
  }

  /**
   * .
   */
  public Iterable<UserGroupData> getUserGroupDatas() {
    if (this.userGroupData == null) {
      return new ArrayIterable<>(new UserGroupData[0]);
    }
    return new ArrayIterable<>(this.userGroupData);
  }

  /**
   * .
   */
  public Iterable<MultiScreenData> getMultiScreenDatas() {
    if (this.multiScreenData == null) {

      return new ArrayIterable<>(new MultiScreenData[0]);
    } else {
      return new ArrayIterable<>(this.multiScreenData);
    }
  }

  /**
   * .
   */
  public Iterable<TxRxGroupData> getTxRxGroupDatas() {
    if (this.txRxGroupData == null) {
      return new ArrayIterable<>(new TxRxGroupData[0]);
    }
    return new ArrayIterable<>(this.txRxGroupData);
  }

  /**
   * .
   */
  public enum IoMode {
    All, Available, None;

    IoMode() {

    }

    /**
     * .
     */
    public int getReadCount(int all, int available) {
      switch (this) {
        case All: {
          return all;
        }
        case Available: {
          return available;
        }
        case None: {
          return -1;
        }
        default:
          break;
      }
      throw new IllegalStateException();
    }

    public boolean isAvailable() {
      return Available.equals(this);
    }

    /**
     * .
     */
    public <T> Collection<T> getObjects(Collection<T> all, Collection<T> available) {
      switch (this) {
        case All: {
          return all;
        }
        case Available: {
          return available;
        }
        case None: {
          return Collections.emptyList();
        }
        default:
          break;
      }
      throw new IllegalStateException();
    }
  }

  private static final class CrWrapper extends DefaultCommitRollback {

    private final CommitRollback[] commitRollbacks;

    public CrWrapper(CommitRollback... commitRollbacks) {
      this.commitRollbacks = commitRollbacks;
      initCommitRollback();
    }

    @Override
    protected Collection<? extends CommitRollback> getDependentCommitRollbacks() {
      return Arrays.asList(this.commitRollbacks);
    }
  }
}

