package com.mc.tool.caesar.api;

import com.mc.tool.caesar.api.communication.BasicController;
import com.mc.tool.caesar.api.communication.IoController;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.exception.UnexpectedResponseException;
import com.mc.tool.caesar.api.utils.CfgReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.concurrent.locks.Lock;
import java.util.logging.Logger;

/**
 * BasicTeraController.
 */
public class BasicTeraController extends BasicController implements CaesarControllerConstants {

  public static final Logger LOG = Logger.getLogger(BasicTeraController.class.getName());
  private long identifier;

  public void setIdentifier(long identifier) {
    this.identifier = identifier;
  }

  protected long getIdentifier() {
    return this.identifier;
  }

  public void setSystemFileOpen(int fileType)
      throws DeviceConnectionException, ConfigException, BusyException {

  }

  public void setSystemFileClose(int fileType)
      throws DeviceConnectionException, ConfigException, BusyException {

  }

  public byte[] readSystemFile(int fileType, int len)
      throws DeviceConnectionException, ConfigException, BusyException {
    return new byte[0];
  }

  public void writeSystemFile(int fileType, byte[] data)
      throws DeviceConnectionException, ConfigException, BusyException {

  }

  /**
   * Worker.
   */
  protected abstract static class Worker<T> {

    private final BasicTeraController tc;
    private final CaesarControllerConstants.Request request;

    public Worker(BasicTeraController tc, CaesarControllerConstants.Request request) {
      this.tc = tc;
      this.request = request;
    }

    public final T work() throws DeviceConnectionException, ConfigException, BusyException {
      Lock lock = this.tc.getLock();

      this.tc.ensureConnection(this.tc.getIdentifier());

      lock.lock();
      try {
        IoController.ReadWriteController ioController = this.tc.getIoController();
        CommunicationBuilder.Tra transmitter =
            buildRequest(CommunicationBuilder.newRequestBuilder().setRequest(this.request));
        CommunicationBuilder.Rec receiver = transmitter.transmit(ioController);
        byte[] response;
        if (this.request.isReponse() == CaesarControllerConstants.Request.Response.DATA) {
          response = receiver.getResponse(ioController);
          CfgReader cfgReader = new CfgReader(new ByteArrayInputStream(response));
          boolean done = false;
          try {
            // MTODO
            done = true;
            workDone();
            if (done && cfgReader.available() > 0) {
              throw new DeviceConnectionException(
                  "There are " + cfgReader.available() + " bytes left to be read!");
            }
            T rsp = getResponse(cfgReader);
            return rsp;
          } finally {
            if (done && cfgReader.available() > 0) {
              throw new DeviceConnectionException(
                  "There are " + cfgReader.available() + " bytes left to be read!");
            }
          }
        }
        if (this.request.isReponse() == CaesarControllerConstants.Request.Response.ACK) {
          receiver.checkAcknowledged(ioController);
          workDone();
          return null;
        }
        return null;
      } catch (UnexpectedResponseException ure) {
        if (ure.getUnexpectedByte() == 7) {
          throw new BusyException();
        }
        try {
          while (this.tc.getIoController().available() > 0) {
            this.tc.getIoController().read();
          }
        } catch (IOException ex) {
          ex.printStackTrace();
        }
        throw new DeviceConnectionException("Error! Command failed! " + this.request.name(), ure);
      } catch (IOException ioe) {
        this.tc.disconnect(this.tc.getIdentifier());
        throw new DeviceConnectionException("Error! Command failed! " + this.request.name(), ioe);
      } finally {
        lock.unlock();
      }
    }

    protected abstract CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add paramAdd)
        throws IOException, ConfigException;

    protected T getResponse(CfgReader cfgReader) throws IOException, ConfigException {
      throw new UnsupportedOperationException(
          "Request Object says it expects a data response. So implement it dummy!");
    }

    protected void workDone() {

    }
  }
}

