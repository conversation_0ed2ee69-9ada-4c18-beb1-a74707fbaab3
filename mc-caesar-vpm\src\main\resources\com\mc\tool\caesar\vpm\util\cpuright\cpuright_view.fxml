<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<fx:root id="main-container" stylesheets="@cpuright_view.css" type="javafx.scene.layout.VBox"
  xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
  <children>
    <HBox id="main-panel" VBox.vgrow="ALWAYS">
      <VBox fx:id="searchFieldBox" styleClass="border-panel">
        <HBox styleClass="title-box">
          <Label fx:id="sourceListTitleLabel" styleClass="title"/>
        </HBox>
        <TableView fx:id="sourceList" styleClass="right-table" VBox.vgrow="ALWAYS">
          <columns>
            <TableColumn fx:id="sourceIdColumn" text="ID"/>
            <TableColumn fx:id="sourceNameColumn"/>
          </columns>
          <columnResizePolicy>
            <TableView fx:constant="CONSTRAINED_RESIZE_POLICY"/>
          </columnResizePolicy>
        </TableView>
      </VBox>
      <VBox styleClass="border-panel" HBox.hgrow="ALWAYS">
        <HBox styleClass="title-box">
          <Label fx:id="accessBlockTitleLabel" styleClass="title"/>
        </HBox>
        <HBox id="right-table-box" VBox.vgrow="ALWAYS">
          <VBox HBox.hgrow="ALWAYS">
            <HBox id="full-title-box" styleClass="title-box">
              <Label styleClass="title" text="%cpu_right.full_access"/>
            </HBox>
            <TableView id="full-table" fx:id="fullTable" styleClass="right-table"
              VBox.vgrow="ALWAYS">
              <columns>
                <TableColumn fx:id="fullIdColumn" text="ID"/>
                <TableColumn fx:id="fullNameColumn" text="%cpu_right.name"/>
              </columns>
              <columnResizePolicy>
                <TableView fx:constant="CONSTRAINED_RESIZE_POLICY"/>
              </columnResizePolicy>
            </TableView>
          </VBox>
          <VBox HBox.hgrow="ALWAYS">
            <HBox id="video-title-box" styleClass="title-box">
              <Label styleClass="title" text="%cpu_right.video_access"/>
            </HBox>
            <TableView id="video-table" fx:id="videoTable" styleClass="right-table"
              VBox.vgrow="ALWAYS">
              <columns>
                <TableColumn fx:id="videoIdColumn" text="ID"/>
                <TableColumn fx:id="videoNameColumn" text="%cpu_right.name"/>
              </columns>
              <columnResizePolicy>
                <TableView fx:constant="CONSTRAINED_RESIZE_POLICY"/>
              </columnResizePolicy>
            </TableView>
          </VBox>
          <VBox HBox.hgrow="ALWAYS">
            <HBox id="no-title-box" styleClass="title-box">
              <Label styleClass="title" text="%cpu_right.no_access"/>
            </HBox>
            <TableView id="no-table" fx:id="noTable" styleClass="right-table" VBox.vgrow="ALWAYS">
              <columns>
                <TableColumn fx:id="noIdColumn" text="ID"/>
                <TableColumn fx:id="noNameColumn" text="%cpu_right.name"/>
              </columns>
              <columnResizePolicy>
                <TableView fx:constant="CONSTRAINED_RESIZE_POLICY"/>
              </columnResizePolicy>
            </TableView>
          </VBox>
        </HBox>
        <HBox id="tip-box" styleClass="title-box">
          <Label styleClass="title" text="%cpu_right.tip"/>
        </HBox>
      </VBox>
    </HBox>
    <HBox id="tool-box" fx:id="btnBox">
      <Button fx:id="applyBtn" onAction="#onApply" styleClass="common-button"
        text="%cpu_right.apply"/>
      <Button fx:id="cancelBtn" onAction="#onCancel" styleClass="common-button"
        text="%cpu_right.cancel"/>
    </HBox>
  </children>
</fx:root>
