package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.utils.BitFieldEntry;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

/**
 * .
 */
public final class PortData extends AbstractData implements CaesarCommunicatable {

  public static final String PROPERTY_BASE = "PortData.";
  public static final String FIELD_USE = "Use";
  public static final String PROPERTY_USE = "PortData.Use";
  public static final String FIELD_TYPE = "Type";
  public static final String PROPERTY_TYPE = "PortData.Type";
  public static final String FIELD_Status = "Status";
  public static final String PROPERTY_Status = "PortData.Status";
  public static final String PROPERTY_Status_AVAILABLE = "PortData.Status.Available";
  public static final String PROPERTY_Status_COMPLETE = "PortData.Status.Complete";
  public static final String PROPERTY_Status_TX_ENABLED = "PortData.Status.TxEnabled";
  public static final String PROPERTY_Status_RX_ENABLED = "PortData.Status.RxEnabled";
  public static final String PROPERTY_Status_CONNECTED = "PortData.Status.Connected";
  public static final String PROPERTY_Status_WITH_EXT_ID = "PortData.Status.WithExtenderId";
  public static final String PROPERTY_Status_WITH_HOST_ID = "PortData.Status.WithHostId";
  public static final String PROPERTY_Status_RESET_DONE = "PortData.Status.ResetDone";
  public static final String PROPERTY_Status_OSD_ACTIVE = "PortData.Status.OsdActive";
  public static final String PROPERTY_Status_VIDEO_ONLY = "PortData.Status.VideoOnly";
  public static final String PROPERTY_Status_WITHSIGNAL = "PortData.Status.WithSignal";
  public static final String PROPERTY_Status_SECONDARY = "PortData.Status.Secondary";
  public static final String PROPERTY_Status_EXTENDER = "PortData.Status.Extender";
  public static final String PROPERTY_Status_MATRIX = "PortData.Status.Matrix";
  public static final String PROPERTY_Status_TX_DISABLE = "PortData.Status.TxDisable";
  public static final String PROPERTY_Status_TX_ENABLE = "PortData.Status.TxEnable";
  public static final String PROPERTY_Status_RX_DISABLE = "PortData.Status.RxDisable";
  public static final String PROPERTY_Status_RX_ENABLE = "PortData.Status.RxEnable";
  public static final String PROPERTY_Status_GET_EXT_ID = "PortData.Status.GetExtenderId";
  public static final String PROPERTY_Status_SET_HOST_ID = "PortData.Status.SetHostId";
  public static final String PROPERTY_Status_ACTIVATE = "PortData.Status.Activate";
  public static final String PROPERTY_Status_DELETE = "PortData.Status.Delete";
  public static final String PROPERTY_Status_NEW_DATA = "PortData.Status.NewData";
  public static final String PROPERTY_Status_ACTIVE = "PortData.Status.Active";
  public static final String FIELD_OUTPUT = "Output";
  public static final String PROPERTY_OUTPUT = "PortData.Output";
  public static final String FIELD_EXTENDER = "Extender";
  public static final String PROPERTY_EXTENDER = "PortData.Extender";
  public static final Map<String, Collection<BitFieldEntry>> BIT_FIELD_MAP;
  @Expose
  private int use; // @unkown 估计已弃用
  @Expose
  private int type; // extender的type，如果是grid line，那么是所连接的port的序号
  @Expose
  private int status;
  @Expose
  private int output;
  // 如果是gridline，那么是使用gridline的extender的port的序号，如果是extender，那么是使用的gridline的port的序号

  @Expose
  private int extender;

  static {
    Collection<BitFieldEntry> bitMapStatus = new HashSet<>();

    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_AVAILABLE,
        CaesarConstants.Port.Status.AVAILABLE));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_COMPLETE,
        CaesarConstants.Port.Status.COMPLETE));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_TX_ENABLED,
        CaesarConstants.Port.Status.TXENABLED));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_RX_ENABLED,
        CaesarConstants.Port.Status.RXENABLED));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_CONNECTED,
        CaesarConstants.Port.Status.CONNECTED));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_WITH_EXT_ID,
        CaesarConstants.Port.Status.WITHEXTID));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_WITH_HOST_ID,
        CaesarConstants.Port.Status.WITHHOSTID));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_RESET_DONE,
        CaesarConstants.Port.Status.RESETDONE));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_OSD_ACTIVE,
        CaesarConstants.Port.Status.OSDACTIVE));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_VIDEO_ONLY,
        CaesarConstants.Port.Status.VIDEOONLY));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_WITHSIGNAL,
        CaesarConstants.Port.Status.WITHSIGNAL));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_SECONDARY,
        CaesarConstants.Port.Status.SECONDARY));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_EXTENDER,
        CaesarConstants.Port.Status.EXTENDER));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_MATRIX,
        CaesarConstants.Port.Status.MATRIX));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_TX_DISABLE,
        CaesarConstants.Port.Status.TXDISABLE));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_TX_ENABLE,
        CaesarConstants.Port.Status.TXENABLE));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_RX_DISABLE,
        CaesarConstants.Port.Status.RXDISABLE));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_RX_ENABLE,
        CaesarConstants.Port.Status.RXENABLE));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_GET_EXT_ID,
        CaesarConstants.Port.Status.GETEXTID));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_SET_HOST_ID,
        CaesarConstants.Port.Status.SETHOSTID));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_ACTIVATE,
        CaesarConstants.Port.Status.ACTIVATE));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_DELETE,
        CaesarConstants.Port.Status.DELETE));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_NEW_DATA,
        CaesarConstants.Port.Status.NEWDATA));
    bitMapStatus.add(new BitFieldEntry(PortData.PROPERTY_Status_ACTIVE,
        CaesarConstants.Port.Status.ACTIVE));

    Map<String, Collection<BitFieldEntry>> bitMaps =
        new HashMap<>();

    bitMaps.put(PortData.PROPERTY_Status, Collections.unmodifiableCollection(bitMapStatus));

    BIT_FIELD_MAP = Collections.unmodifiableMap(bitMaps);
  }

  public PortData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager, int oid,
      String fqn) {
    super(pcs, configDataManager, oid, fqn);
    initCommitRollback();
  }

  @Override
  public void initDefaults() {
    super.initDefaults();

    setExtenderData(null);
    setOutput(0);
    setStatus(0);
    setType(0);
    setUse(0);
  }

  public int getExtender() {
    return this.extender;
  }

  /**
   * .
   */
  public void setExtender(int extender) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.extender;
    this.extender = extender;
    firePropertyChange(PortData.PROPERTY_EXTENDER, oldValue, this.extender);
  }

  public int getOutput() {
    return this.output;
  }

  /**
   * 获取output对应的portdata.
   *
   * @return port data
   */
  public PortData getOutputPortData() {
    if (isStatusMatrix()) {
      return null;
    } else {
      return getConfigDataManager().getPortData(getOutput() - 1);
    }
  }

  /**
   * .
   */
  public void setOutput(int output) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.output;
    this.output = output;
    firePropertyChange(PortData.PROPERTY_OUTPUT, oldValue, this.output);
  }

  public int getStatus() {
    return this.status;
  }

  /**
   * .
   */
  public void setStatus(int status) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.status;
    this.status = status;
    firePropertyChange(PortData.PROPERTY_Status, oldValue, this.status);
  }

  public boolean isStatusAvailable() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.AVAILABLE);
  }

  public boolean isStatusComplete() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.COMPLETE);
  }

  public boolean isStatusTxEnabled() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.TXENABLED);
  }

  public boolean isStatusRxEnabled() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.RXENABLED);
  }

  public boolean isStatusConnected() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.CONNECTED);
  }

  public boolean isStatusWithExtenderId() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.WITHEXTID);
  }

  public boolean isStatusWithHostId() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.WITHHOSTID);
  }

  public boolean isStatusResetDone() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.RESETDONE);
  }

  public boolean isStatusOsdActive() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.OSDACTIVE);
  }

  public boolean isStatusVideoOnly() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.VIDEOONLY);
  }

  public boolean isStatusWithSignal() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.WITHSIGNAL);
  }

  public boolean isStatusSecondary() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.SECONDARY);
  }

  public boolean isStatusExtender() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.EXTENDER);
  }

  public boolean isStatusMatrix() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.MATRIX);
  }

  public boolean isStatusTxDisable() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.TXDISABLE);
  }

  public boolean isStatusTxEnable() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.TXENABLE);
  }

  public boolean isStatusRxDisable() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.RXDISABLE);
  }

  public boolean isStatusRxEnable() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.RXENABLE);
  }

  public boolean isStatusGetExtenderId() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.GETEXTID);
  }

  public boolean isStatusSetHostId() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.SETHOSTID);
  }

  public boolean isStatusActivate() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.ACTIVATE);
  }

  public boolean isStatusError() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.ERROR);
  }

  public boolean isStatusDelete() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.DELETE);
  }

  public boolean isStatusNewData() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.NEWDATA);
  }

  public boolean isStatusActive() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Port.Status.ACTIVE);
  }

  public void setStatusAvailable(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.AVAILABLE));
  }

  public void setStatusComplete(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.COMPLETE));
  }

  public void setStatusTxEnabled(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.TXENABLED));
  }

  public void setStatusRxEnabled(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.RXENABLED));
  }

  public void setStatusConnected(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.CONNECTED));
  }

  public void setStatusWithExtenderId(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.WITHEXTID));
  }

  public void setStatusWithHostId(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.WITHHOSTID));
  }

  public void setStatusResetDone(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.RESETDONE));
  }

  public void setStatusOsdActive(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.OSDACTIVE));
  }

  public void setStatusVideoOnly(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.VIDEOONLY));
  }

  public void setStatusWithSignal(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.WITHSIGNAL));
  }

  public void setStatusSecondary(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.SECONDARY));
  }

  public void setStatusExtender(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.EXTENDER));
  }

  public void setStatusMatrix(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.MATRIX));
  }

  public void setStatusTxDisable(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.TXDISABLE));
  }

  public void setStatusTxEnable(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.TXENABLE));
  }

  public void setStatusRxDisable(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.RXDISABLE));
  }

  public void setStatusRxEnable(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.RXENABLE));
  }

  public void setStatusGetExtenderId(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.GETEXTID));
  }

  public void setStatusSetHostId(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.SETHOSTID));
  }

  public void setStatusActivate(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.ACTIVATE));
  }

  public void setStatusDelete(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.DELETE));
  }

  public void setStatusNewData(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.NEWDATA));
  }

  public void setStatusActive(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Port.Status.ACTIVE));
  }

  public int getType() {
    return this.type;
  }

  /**
   * .
   */
  public void setType(int type) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.type;
    this.type = type;
    firePropertyChange(PortData.PROPERTY_TYPE, oldValue, this.type);
  }

  public int getUse() {
    return this.use;
  }

  /**
   * .
   */
  public void setUse(int use) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.use;
    this.use = use;
    firePropertyChange(PortData.PROPERTY_USE, oldValue, this.use);
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (PortData.PROPERTY_USE.equals(propertyName)) {
      setUse((Integer) value);
    } else if (PortData.PROPERTY_TYPE.equals(propertyName)) {
      setType((Integer) value);
    } else if (PortData.PROPERTY_Status.equals(propertyName)) {
      setStatus((Integer) value);
    } else if (PortData.PROPERTY_OUTPUT.equals(propertyName)) {
      setOutput((Integer) value);
    } else if (PortData.PROPERTY_EXTENDER.equals(propertyName)) {
      setExtender((Integer) value);
    }
  }

  @Override
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    cfgWriter.writeInteger(getUse());
    cfgWriter.writeInteger(getType());
    cfgWriter.writeInteger(getStatus());
    cfgWriter.writeInteger(getOutput());
    cfgWriter.writeInteger(getExtender());
  }

  @Override
  public void readData(CfgReader cfgReader) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int use = cfgReader.readInteger();
    setUse(use);
    int type = cfgReader.readInteger();
    setType(type);
    int status = cfgReader.readInteger();
    setStatus(status);
    int output = cfgReader.readInteger();
    setOutput(output);
    int extender = cfgReader.readInteger();
    setExtender(extender);
  }

  public ExtenderData getExtenderData() {
    return getConfigDataManager().getExtenderData(getExtender() - 1);
  }

  public void setExtenderData(ExtenderData extenderData) {
    setExtender(null == extenderData ? 0 : extenderData.getOid() + 1);
  }

  public void delete() {
    initDefaults();
    commit(THRESHOLD_LOCAL_CHANGES);
  }
}

