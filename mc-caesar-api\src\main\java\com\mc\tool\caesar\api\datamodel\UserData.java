package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarConstants.User;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.api.interfaces.NamepropertyAble;
import com.mc.tool.caesar.api.utils.AdvancedBitSet;
import com.mc.tool.caesar.api.utils.BitFieldEntry;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.api.version.ApiDataConverter;
import java.beans.IndexedPropertyChangeEvent;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import lombok.Getter;

/**
 * 用户配置数据.
 */
public final class UserData extends AbstractData implements DataObject, AccessControlObject,
    FavoriteObject, CaesarCommunicatable, NamepropertyAble {

  public static final String PROPERTY_BASE = "UserData.";
  public static final String FIELD_VIDEO_ACCESS = "VideoAccess";
  public static final String PROPERTY_VIDEO_ACCESS = "UserData.VideoAccess";
  public static final String FIELD_NO_ACCESS = "NoAccess";
  public static final String PROPERTY_NO_ACCESS = "UserData.NoAccess";
  public static final String PROPERTY_USB_NO_ACCESS = "UserData.UsbNoAccess";
  public static final String FIELD_ID = "ID";
  public static final String PROPERTY_ID = "UserData.ID";
  public static final String FIELD_NAME = "Name";
  public static final String PROPERTY_NAME = "UserData.Name";
  public static final String FIELD_PASSWORD = "Password";
  public static final String PROPERTY_PASSWORD = "UserData.Password";
  public static final String FIELD_STATUS = "Status";
  public static final String PROPERTY_STATUS = "UserData.Status";
  public static final String PROPERTY_STATUS_DELETE = "UserData.Status.Delete";
  public static final String PROPERTY_STATUS_NEW = "UserData.Status.New";
  public static final String PROPERTY_STATUS_ACTIVE = "UserData.Status.Active";
  public static final String FIELD_RIGHTS = "Rights";
  public static final String PROPERTY_RIGHTS = "UserData.Rights";
  public static final String PROPERTY_CON_INDEX = PROPERTY_BASE + "ConIndex";
  public static final String PROPERTY_USER_GROUP = PROPERTY_BASE + "UserGroup";
  public static final String PROPERTY_MOUSE_SPEED = PROPERTY_BASE + "MouseSpeed";
  public static final String PROPERTY_VIDEOWALL_RIGHTS = PROPERTY_BASE + "VideoWallRights";
  public static final String PROPERTY_CONTROL_BITES = PROPERTY_BASE + "ControlBits";
  public static final String FIELD_FULLNAME = "FullName";
  public static final String PROPERTY_FULLNAME = "UserData.FullName";
  public static final String FIELD_FAVORITE = "Favorite";
  public static final Map<String, Collection<BitFieldEntry>> BIT_FIELD_MAP;


  static {
    Collection<BitFieldEntry> bitMapStatus = new HashSet<>();

    bitMapStatus.add(new BitFieldEntry(UserData.PROPERTY_STATUS_ACTIVE, User.Status.ACTIVE));
    bitMapStatus.add(new BitFieldEntry(UserData.PROPERTY_STATUS_DELETE, User.Status.DELETE));
    bitMapStatus.add(new BitFieldEntry(UserData.PROPERTY_STATUS_NEW, User.Status.NEW));

    Collection<BitFieldEntry> bitMapRights = new HashSet<>();

    Map<String, Collection<BitFieldEntry>> bitMaps = new HashMap<>();

    bitMaps.put(UserData.PROPERTY_RIGHTS, Collections.unmodifiableCollection(bitMapRights));
    bitMaps.put(UserData.PROPERTY_STATUS, Collections.unmodifiableCollection(bitMapStatus));

    BIT_FIELD_MAP = Collections.unmodifiableMap(bitMaps);
  }

  public static final int USER_GROUP_BIT_COUNT = 13 * 8;

  public static final int SOUND_MIXING_COUNT = 8;
  @Expose
  private StringProperty name = new SimpleStringProperty(""); // 用户名，最多16个字符
  @Getter
  @Expose
  private String password = ""; // 密码 明文，最多16个字符
  @Getter
  @Expose
  private int status = 0;
  @Getter
  @Expose
  private int rights = 1;  // 范围是1到100
  @Getter
  @Expose
  private int controlBits = 0;
  @Getter
  @Expose
  private int videoWallRights = 0;
  @Expose
  @Getter
  private int conIndex = 0; // CON登录时，这里保存CON的索引加1
  @Getter
  private int mouseSpeed = 10; //取值范围为4~16
  @Expose
  private final transient AdvancedBitSet videoAccess; // 如果对某个cpu的权限是videoaccess，那么cpu.oid对应的位为1
  @Expose
  private final transient AdvancedBitSet noAccess; // 如果对某个cpu的权限是noaccess，那么cpu.oid对应的位为1
  @Expose
  private final transient AdvancedBitSet userGroup;
  @Expose
  private final transient AdvancedBitSet usbNoAccess; // USB透传权限无访问权限列表
  @Expose
  private final int[] favorite = createArrayInt(CaesarConstants.Cpu.FAVORITE);
  // favorite的cpu的oid+1的列表

  @Expose
  private boolean init = false;

  @Expose
  private SoundMixing[] soundMixing = new SoundMixing[SOUND_MIXING_COUNT];

  @Expose
  private int audioMode;

  /**
   * .
   */
  public UserData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager, int oid,
      String fqn) {
    super(pcs, configDataManager, oid, fqn);
    initCommitRollback();
    for (int i = 0; i < SOUND_MIXING_COUNT; i++) {
      soundMixing[i] = new SoundMixing();
    }
    if (configDataManager == null) {
      this.videoAccess = new AdvancedBitSet();
      this.noAccess = new AdvancedBitSet();
      this.userGroup = new AdvancedBitSet();
      this.usbNoAccess = new AdvancedBitSet();
      return;
    }
    this.videoAccess = new AdvancedBitSet(getConfigDataManager().getConfigMetaData().getCpuCount());
    this.noAccess = new AdvancedBitSet(getConfigDataManager().getConfigMetaData().getCpuCount());
    this.usbNoAccess = new AdvancedBitSet(
        getConfigDataManager().getConfigMetaData().getCpuCount());
    this.userGroup = new AdvancedBitSet(USER_GROUP_BIT_COUNT);
    this.videoAccess.addPropertyChangeListener(AdvancedBitSet.PROPERTY_BIT, evt -> {
      if (this.init) {
        return;
      }
      if (evt instanceof IndexedPropertyChangeEvent) {
        this.firePropertyChange(UserData.PROPERTY_VIDEO_ACCESS, evt.getOldValue(),
            evt.getNewValue(),
            ((IndexedPropertyChangeEvent) evt).getIndex());
        this.firePropertyChange(AccessControlObject.PROPERTY_VIDEO_ACCESS,
            evt.getOldValue(), evt.getNewValue(),
            ((IndexedPropertyChangeEvent) evt).getIndex());
      }
    });
    this.noAccess.addPropertyChangeListener(AdvancedBitSet.PROPERTY_BIT, evt -> {
      if (this.init) {
        return;
      }
      if (evt instanceof IndexedPropertyChangeEvent) {
        this.firePropertyChange(UserData.PROPERTY_NO_ACCESS, evt.getOldValue(),
            evt.getNewValue(),
            ((IndexedPropertyChangeEvent) evt).getIndex());
        this.firePropertyChange(AccessControlObject.PROPERTY_NO_ACCESS,
            evt.getOldValue(), evt.getNewValue(),
            ((IndexedPropertyChangeEvent) evt).getIndex());
      }
    });
    getPropertyChangeSupport().addPropertyChangeListener(UserData.PROPERTY_RIGHTS, evt -> {
      if (evt instanceof IndexedPropertyChangeEvent
          && ((IndexedPropertyChangeEvent) evt).getIndex() == this.getOid()) {
        Integer oldValue = (Integer) evt.getOldValue();
        Integer newValue = (Integer) evt.getNewValue();
        if (oldValue.equals(0) && newValue.equals(1)) {
          for (int i = 0; i < this.getConfigDataManager().getConfigMetaData()
              .getCpuCount(); i++) {
            this.noAccess.set(i, false);
            this.videoAccess.set(i, false);
          }
        }
      }
    });
  }

  @Override
  public void initDefaults() {
    super.initDefaults();

    this.init = true;
    this.userGroup.clear();
    this.videoAccess.clear();
    this.noAccess.clear();
    this.usbNoAccess.clear();
    setName("");
    setPassword("");
    setRights(0);
    setStatus(0);
    for (int i = 0; i < getConfigDataManager().getConfigMetaData().getCpuCount(); i++) {
      this.noAccess.set(i, true);
    }
    for (int i = 0; i < this.favorite.length; i++) {
      setFavoriteData(i, null);
    }
    this.init = false;
  }

  public void setInitMode(boolean initMode) {
    this.init = initMode;
  }

  public boolean isInitMode() {
    return this.init;
  }

  /**
   * .
   */
  public void setLockControlCpus(Collection<CpuData> cpuDatas) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (null == cpuDatas) {
      this.videoAccess.setBits();
    } else {
      int[] bitIndex = new int[cpuDatas.size()];
      CpuData[] cds = cpuDatas.toArray(new CpuData[0]);
      for (int i = 0; i < bitIndex.length; i++) {
        bitIndex[i] = cds[i].getOid();
      }
      this.videoAccess.setBits(bitIndex);
    }
  }

  /**
   * .
   */
  public void setLockVideoCpus(Collection<CpuData> cpuDatas) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (null == cpuDatas) {
      this.noAccess.setBits();
    } else {
      int[] bitIndex = new int[cpuDatas.size()];
      CpuData[] cds = cpuDatas.toArray(new CpuData[0]);
      for (int i = 0; i < bitIndex.length; i++) {
        bitIndex[i] = cds[i].getOid();
      }
      this.noAccess.setBits(bitIndex);
    }
  }

  @Override
  public int getId() {
    return getOid() + 1;
  }

  public IntegerProperty getIdProperty() {
    return new SimpleIntegerProperty(getOid() + 1);
  }

  @Override
  public String getName() {
    return this.name.get();
  }

  /**
   * .
   */
  public void setName(String name) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    String oldValue = this.name.get();
    this.name.set(name);
    firePropertyChange(UserData.PROPERTY_NAME, oldValue, this.name.get());
  }

  @Override
  public StringProperty getNameProperty() {
    return this.name;
  }

  /**
   * .
   */
  public void setPassword(String password) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    String oldValue = this.password;
    this.password = password;
    firePropertyChange(UserData.PROPERTY_PASSWORD, oldValue, this.password);
  }

  public String getFullName() {
    return "";
  }


  /**
   * .
   */
  public void setRights(int rights) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.rights;
    this.rights = rights;
    firePropertyChange(UserData.PROPERTY_RIGHTS, oldValue, this.rights);
  }

  /**
   * .
   */
  public void setConIndex(int conIndex) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.conIndex;
    this.conIndex = conIndex;
    firePropertyChange(UserData.PROPERTY_CON_INDEX, oldValue, this.conIndex);
  }

  /**
   * 设置control的值.
   *
   * @param controlBits control的值
   */
  public void setControlBits(int controlBits) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = controlBits;
    this.controlBits = controlBits;
    firePropertyChange(UserData.PROPERTY_CONTROL_BITES, oldValue, this.controlBits);
  }

  public boolean isAutoConnect() {
    return Utilities.areBitsSet(controlBits, 1);
  }

  /**
   * 设置用户登录自动连接.
   *
   * @param auto 自动连接
   */
  public void setAutoConnect(boolean auto) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = controlBits;
    setControlBits(Utilities.setBits(controlBits, auto, 1));
    firePropertyChange(UserData.PROPERTY_CONTROL_BITES, oldValue, this.controlBits);
  }

  public boolean hasRightAdmin() {
    return rights <= 1 || getName().equals(User.ADMIN_NAME);
  }

  public boolean hasRightAdministrator() {
    return rights <= 1 || getName().equals(User.ADMINISTRATOR_NAME);
  }

  /**
   * .
   */
  public void setStatus(int status) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.status;
    this.status = status;
    firePropertyChange(UserData.PROPERTY_STATUS, oldValue, this.status);
  }

  public boolean isStatusDelete() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.User.Status.DELETE);
  }

  public boolean isStatusNew() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.User.Status.NEW);
  }

  public boolean isStatusActive() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.User.Status.ACTIVE);
  }

  public void setStatusDelete(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, User.Status.DELETE));
  }

  public void setStatusNew(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, User.Status.NEW));
  }

  public void setStatusActive(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, User.Status.ACTIVE));
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (UserData.PROPERTY_VIDEO_ACCESS.equals(propertyName)) {
      setVideoAccess(getConfigDataManager().getCpuData(indizes[0]), Boolean.TRUE.equals(value));
    } else if (UserData.PROPERTY_NO_ACCESS.equals(propertyName)) {
      setNoAccess(getConfigDataManager().getCpuData(indizes[0]), Boolean.TRUE.equals(value));
    } else if (UserData.PROPERTY_NAME.equals(propertyName)) {
      setName((String) value);
    } else if (UserData.PROPERTY_PASSWORD.equals(propertyName)) {
      setPassword((String) value);
    } else if (UserData.PROPERTY_STATUS.equals(propertyName)) {
      setStatus((Integer) value);
    } else if (UserData.PROPERTY_RIGHTS.equals(propertyName)) {
      setRights((Integer) value);
    } else if (FavoriteObject.PROPERTY_FAVORITE.equals(propertyName)) {
      setFavorite(indizes[0], (Integer) value);
    }
  }

  @Override
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    ApiDataConverter<UserData> converter =
        getConfigDataManager().getConfigMetaData().getUtilVersion()
            .getDataConverter(UserData.class);
    converter.writeData(this, cfgWriter);
  }

  @Override
  public void readData(CfgReader cfgReader) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    ApiDataConverter<UserData> converter =
        getConfigDataManager().getConfigMetaData().getUtilVersion()
            .getDataConverter(UserData.class);
    converter.readData(this, cfgReader);
  }

  @Override
  public boolean isVideoAccess(CpuData cpuData) {
    return null != cpuData && this.videoAccess.get(cpuData.getOid());
  }

  @Override
  public void setVideoAccess(CpuData cpuData, boolean locked) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (null == cpuData) {
      return;
    }
    boolean oldValue = this.videoAccess.get(cpuData.getOid());
    this.videoAccess.set(cpuData.getOid(), locked);
    firePropertyChange(UserData.PROPERTY_VIDEO_ACCESS, oldValue, locked, cpuData.getOid());
  }

  @Override
  public Collection<CpuData> getVideoAccessCpuDatas() {
    return getConfigDataManager().getCpuData(this.videoAccess.getBits());
  }

  @Override
  public boolean isNoAccess(CpuData cpuData) {
    return null != cpuData && this.noAccess.get(cpuData.getOid());
  }

  @Override
  public void setNoAccess(CpuData cpuData, boolean locked) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (null == cpuData) {
      return;
    }
    boolean oldValue = this.noAccess.get(cpuData.getOid());
    this.noAccess.set(cpuData.getOid(), locked);
    firePropertyChange(UserData.PROPERTY_NO_ACCESS, oldValue, locked, cpuData.getOid());
  }

  @Override
  public boolean isUsbNoAccess(CpuData cpuData) {
    return null != cpuData && this.usbNoAccess.get(cpuData.getOid());
  }

  @Override
  public void setUsbNoAccess(CpuData cpuData, boolean locked) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (null == cpuData) {
      return;
    }
    boolean oldValue = this.usbNoAccess.get(cpuData.getOid());
    this.usbNoAccess.set(cpuData.getOid(), locked);
    firePropertyChange(UserData.PROPERTY_USB_NO_ACCESS, oldValue, locked, cpuData.getOid());
  }

  /**
   * .
   */
  public void setUserGroup(UserGroupData userGroupData, boolean locked) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (null == userGroupData) {
      return;
    }
    boolean oldValue = this.userGroup.get(userGroupData.getOid());
    this.userGroup.set(userGroupData.getOid(), locked);
    firePropertyChange(UserData.PROPERTY_USER_GROUP, oldValue, locked, userGroupData.getOid());
  }

  /**
   * .
   */
  public void setUserGroup(int idx, boolean value) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    boolean oldValue = this.userGroup.get(idx);
    this.userGroup.set(idx, value);
    firePropertyChange(UserData.PROPERTY_USER_GROUP, oldValue, value, idx);
  }

  /**
   * 设置鼠标速度.
   *
   * @param speed 鼠标速度.
   */
  public void setMouseSpeed(int speed) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.mouseSpeed;
    this.mouseSpeed = speed;
    firePropertyChange(UserData.PROPERTY_MOUSE_SPEED, oldValue, speed);
  }

  /**
   * 设置视频墙权限.
   *
   * @param rights 权限
   */
  public void setVideoWallRights(int rights) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.videoWallRights;
    this.videoWallRights = rights;
    firePropertyChange(UserData.PROPERTY_VIDEOWALL_RIGHTS, oldValue, rights);
  }

  /**
   * 检查是否拥有指定视频墙的权限.
   *
   * @param videoWallIndex 视频墙索引（0-based）
   * @return 当对应bit位为1时返回true
   */
  public boolean hasVideoWallAccess(int videoWallIndex) {
    return (videoWallRights & (1 << videoWallIndex)) != 0;
  }

  public boolean isUserGroup(UserGroupData userGroupData) {
    return userGroupData != null && this.userGroup.get(userGroupData.getOid());
  }

  public boolean isUserGroup(int idx) {
    return this.userGroup.get(idx);
  }

  public Collection<UserGroupData> getUserGroupDatas() {
    return getConfigDataManager().getUserGroupData(this.userGroup.getBits());

  }

  @Override
  public Collection<CpuData> getNoAccessCpuDatas() {
    return getConfigDataManager().getCpuData(this.noAccess.getBits());
  }

  /**
   * .
   */
  public void setNoAccessCpus(Collection<CpuData> cpuDatas) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (null == cpuDatas) {
      this.noAccess.setBits();
    } else {
      int[] bitIndex = new int[cpuDatas.size()];
      CpuData[] cds = cpuDatas.toArray(new CpuData[0]);
      for (int i = 0; i < bitIndex.length; i++) {
        bitIndex[i] = cds[i].getOid();
      }
      this.noAccess.setBits(bitIndex);
    }
  }

  @Override
  public int getFavorite(int idx) {
    return this.favorite[idx];
  }

  /**
   * .
   *
   * @param idx      轮询的序号，0~15
   * @param favorite 轮询的cpu的oid+1
   * @brief 设置轮询的cpu
   */
  @Override
  public void setFavorite(int idx, int favorite) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.favorite[idx];
    this.favorite[idx] = favorite;
    firePropertyChange(FavoriteObject.PROPERTY_FAVORITE, oldValue, this.favorite[idx], idx);
  }

  @Override
  public CpuData getFavoriteData(int idx) {
    return getConfigDataManager().getCpuData(this.favorite[idx] - 1);
  }

  @Override
  public Collection<CpuData> getFavoriteDatas() {
    Collection<CpuData> cpuDatas = new ArrayList<>();
    for (int i = 0; i < this.favorite.length; i++) {
      CpuData cpuData = getFavoriteData(i);
      if (null != cpuData) {
        cpuDatas.add(cpuData);
      }
    }
    return cpuDatas;
  }

  @Override
  public void setFavoriteData(int idx, CpuData cpuData) {
    setFavorite(idx, null == cpuData ? 0 : cpuData.getOid() + 1);
  }

  @Override
  public void delete(boolean internalCommit) {
    setStatusDelete(true);
    setVideoWallRights(0);
    initDefaults();
    if (internalCommit) {
      commit(getThreshold());
    }
  }

  @Override
  public void delete() {
    delete(true);
  }

  @Override
  public AdvancedBitSet getVideoAccessBitSet() {
    return videoAccess;
  }

  @Override
  public void setVideoAccessBits(int... bits) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    getVideoAccessBitSet().setBits(bits);
  }

  @Override
  public AdvancedBitSet getNoAccessBitSet() {
    return noAccess;
  }

  @Override
  public void setNoAccessBits(int... bits) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    getNoAccessBitSet().setBits(bits);
  }

  @Override
  public AdvancedBitSet getUsbNoAccessBitSet() {
    return usbNoAccess;
  }

  @Override
  public void setUsbNoAccessBits(int... bits) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    getUsbNoAccessBitSet().setBits(bits);
  }

  @Override
  public int[] getFavoriteArray() {
    return favorite.clone();
  }

  public AdvancedBitSet getUserGroupBitSet() {
    return userGroup;
  }

  /**
   * 设置混音音量.
   */
  public void setSoundMixing(int index, SoundMixing data) {
    if (index >= 0 && index < SOUND_MIXING_COUNT) {
      soundMixing[index] = data;
    }
  }

  /**
   * 获取混音音量.
   */
  public SoundMixing getSoundMixing(int index) {
    if (index >= 0 && index < SOUND_MIXING_COUNT) {
      return soundMixing[index];
    }
    return null;
  }

  public void setAudioMode(int audioMode) {
    this.audioMode = audioMode;
  }

  public int getAudioMode() {
    return audioMode;
  }
}

