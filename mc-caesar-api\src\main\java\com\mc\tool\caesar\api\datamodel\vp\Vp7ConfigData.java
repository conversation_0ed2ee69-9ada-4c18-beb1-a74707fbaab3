package com.mc.tool.caesar.api.datamodel.vp;

import com.mc.tool.caesar.api.io.NormalStruct;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.util.Arrays;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * VP7配置信息.
 */
@SuppressFBWarnings({"URF_UNREAD_PUBLIC_OR_PROTECTED_FIELD", "VA_FORMAT_STRING_USES_NEWLINE"})
public class Vp7ConfigData extends VpConConfigData {
  static final Logger LOG = Logger.getLogger(Vp7ConfigData.class.getName());

  public static final byte CLIP_L_PIECE = 0x01;
  public static final byte CLIP_R_PIECE = 0x02;
  public static final byte CLIP_T_PIECE = 0x04;
  public static final byte CLIP_B_PIECE = 0x08;

  /**
   * 格式化输出配置信息.
   */
  public String formatAvConfig() {
    StringBuilder ss = new StringBuilder();
    ss.append("tw:").append(totalWidth.get())
        .append(",th:").append(totalHeight.get())
        .append(",im:").append(overlapConfig.interfaceMode.get())
        .append(",aas:").append(overlapConfig.analogAudioSel.get())
        .append(",aav:").append(overlapConfig.analogAudioVolume.get())
        .append("\r\n");

    long vpssCnt = IntStream.range(0, vpssLayerConfig.length)
        .filter(i -> vpssLayerConfig[i].vidSelect.get() > 0)
        .count();

    ss.append("vpss[").append(vpssCnt)
        .append("]:vid,pre(x,y,w,h),piece(l,r,t,b),down(w,h),up(w,h),post(x,y,w,h),pos(x,y,w,h)\r\n");

    for (int i = 0; i < vpssLayerConfig.length; i++) {
      if (vpssLayerConfig[i].vidSelect.get() == 0) {
        continue;
      }
      ss.append("\t[").append(i).append("]:")
          .append(vpssLayerConfig[i].vidSelect.get()).append(",")
          .append("(").append(vpssLayerConfig[i].preClipxOffset.get()).append(",")
          .append(vpssLayerConfig[i].preClipyOffset.get()).append(",")
          .append(vpssLayerConfig[i].preClipxWidth.get()).append(",")
          .append(vpssLayerConfig[i].preClipyHeight.get()).append("),")
          .append("(").append((vpssLayerConfig[i].pieceEnable.get() & CLIP_L_PIECE) != 0 ? 1 : 0).append(",")
          .append((vpssLayerConfig[i].pieceEnable.get() & CLIP_R_PIECE) != 0 ? 1 : 0).append(",")
          .append((vpssLayerConfig[i].pieceEnable.get() & CLIP_T_PIECE) != 0 ? 1 : 0).append(",")
          .append((vpssLayerConfig[i].pieceEnable.get() & CLIP_B_PIECE) != 0 ? 1 : 0).append("),")
          .append("(").append(vpssLayerConfig[i].scaleDownWidth.get()).append(",")
          .append(vpssLayerConfig[i].scaleDownHeight.get()).append("),")
          .append("(").append(vpssLayerConfig[i].scaleUpWidth.get()).append(",")
          .append(vpssLayerConfig[i].scaleUpHeight.get()).append("),")
          .append("(").append(vpssLayerConfig[i].postClipxOffset.get()).append(",")
          .append(vpssLayerConfig[i].postClipyOffset.get()).append(",")
          .append(vpssLayerConfig[i].postClipxWidth.get()).append(",")
          .append(vpssLayerConfig[i].postClipyHeight.get()).append("),")
          .append("(").append(vpssLayerConfig[i].layerX.get()).append(",")
          .append(vpssLayerConfig[i].layerY.get()).append(",")
          .append(vpssLayerConfig[i].layerWidth.get()).append(",")
          .append(vpssLayerConfig[i].layerHeight.get()).append(")\r\n");
    }

    ss.append("port[").append(portConfig.length)
        .append("]:en,f_en,pos(x,y,w,h)\r\n");

    for (int i = 0; i < portConfig.length; i++) {
      ss.append("\t[").append(i).append("]:").append(portConfig[i].enable.get() & 0x01).append(",")
          .append((portConfig[i].enable.get() >> 1) & 0x01).append(",(")
          .append(portConfig[i].backCaptionConfig.offsetX.get()).append(",")
          .append(portConfig[i].backCaptionConfig.offsetY.get()).append(",").append(portConfig[i].backCaptionConfig.width.get())
          .append(",").append(portConfig[i].backCaptionConfig.height.get()).append(")\r\n");
    }
    ss.append("prio[").append(portConfig[0].layerPriority.length).append("]\r\n");
    ss.append("\t").append(Arrays.stream(portConfig[0].layerPriority).map(unsigned16 -> String.valueOf(unsigned16.get()))
        .collect(Collectors.joining(","))).append("\r\n");
    return ss.toString();
  }

  /**
   * 格式化LOGO配置信息.
   */
  public String formatLogoTpgConfig() {
    // 输出名称与值对齐
    String result = String.format("logo&tpg:\r\n\t%-8s,%-8s,%-4s,%-4s,%-4s,%-4s,%-5s,%-5s,%-5s,%-5s,tpg(%-4s,%-8s,%-4s)\r\n",
        "color", "bg", "oid1", "oid2", "oid3", "oid4", "left", "top", "width", "height", "mode", "color", "speed");
    result += String.format("\t%08x,%08x,%-4d,%-4d,%-4d,%-4d,%-5d,%-5d,%-5d,%-6d,   (%-4d,%08x,%-5d)\r\n",
        logoConfig.logoColor.get(), logoConfig.bgColor.get(),
        logoConfig.oid[0].get(), logoConfig.oid[1].get(), logoConfig.oid[2].get(), logoConfig.oid[3].get(),
        logoConfig.logoLeft.get(), logoConfig.logoTop.get(), logoConfig.logoWidth.get(), logoConfig.logoHeight.get(),
        tpgConfig.mode.get(), tpgConfig.color.get(), tpgConfig.speed.get());
    return result;
  }


  /**
   * 格式化输出分辨率配置信息.
   */
  public String formatOutputConfig() {
    String result = String.format("output_res:\r\n\t%-10s,horz(%-5s,%-5s,%-5s,%-5s,%-5s),vert(%-5s,%-5s,%-5s,%-5s,%-5s)\r\n",
        "clock", "sync", "back", "disp", "total", "polar", "sync", "back", "disp", "total", "polar");
    result += String.format("\t%-10d,    (%-5d,%-5d,%-5d,%-5d,%-5d),    (%-5d,%-5d,%-5d,%-5d,%-5d)\r\n",
        outputResData.clock.get(),
        outputResData.horzOutput.sync.get(), outputResData.horzOutput.backPorch.get(), outputResData.horzOutput.disp.get(),
        outputResData.horzOutput.total.get(), outputResData.horzOutput.polarity.get(),
        outputResData.vertOutput.sync.get(), outputResData.vertOutput.backPorch.get(), outputResData.vertOutput.disp.get(),
        outputResData.vertOutput.total.get(), outputResData.vertOutput.polarity.get());
    return result;
  }

  /**
   * 格式化叠加配置信息.
   */
  public String formatOverlapConfig() {
    String result =
        "overlap:\r\n\tbgimg_en,bgimg_a,layer_en,layer_a,cap_en,cap_a,logo_en,logo_a,tpg_en,tpg_a,cap_bg_en\r\n";
    result += String.format("\t%-8d,%-7d,%-8d,%-7d,%-6d,%-5d,%-7d,%-6d,%-6d,%-5d,%-9d\r\n",
        overlapConfig.bgimgEnable.get(), overlapConfig.bgimgAlpha.get(),
        overlapConfig.layerEnable.get(), overlapConfig.layerAlpha.get(),
        overlapConfig.captionEnable.get(), overlapConfig.captionAlpha.get(),
        overlapConfig.logoEnable.get(), overlapConfig.logoAlpha.get(),
        overlapConfig.tpgEnable.get(), overlapConfig.tpgAlpha.get(),
        overlapConfig.captionBgEnable.get());
    return result;
  }

  @Override
  public String toString() {
    return formatAvConfig() + formatOutputConfig() + formatLogoTpgConfig() + formatOverlapConfig();
  }

  @Override
  public void print() {
    String msg = toString();
    for (String line : msg.split("\r\n")) {
      LOG.info(line);
    }
  }

  /**
   * 叠加配置.
   */
  public static class Vp7OverlapConfig extends NormalStruct {
    public Unsigned8 bgimgEnable = new Unsigned8(); // 对应叠加参数的底图使能(PORT0_BACK_PICTUER_EN * 4)
    public Unsigned8 bgimgAlpha = new Unsigned8(); // 对应叠加参数的底图透明度(PORT0_BACK_PICTUER_ALPHA * 4)
    public Unsigned8 layerEnable = new Unsigned8(); // 对应叠加参数的OSD图层使能(PORT0_OSD_EN * 4)
    public Unsigned8 layerAlpha = new Unsigned8(); // 对应叠加参数的OSD图层透明度(PORT0_OSD_ALPHA * 4)
    public Unsigned8 captionEnable = new Unsigned8(); // 对应叠加参数的条幅使能(PORT0_CAPTION_EN * 4)
    public Unsigned8 captionAlpha = new Unsigned8(); // 对应叠加参数的条幅透明度(PORT0_CAPTION_ALPHA * 4)，应该忽略，使用云视的配置
    public Unsigned8 logoEnable = new Unsigned8(); // 对应叠加参数的LOGO使能(PORT0_LOGO_EN * 4)
    public Unsigned8 logoAlpha = new Unsigned8(); // 对应叠加参数的LOGO透明度(PORT0_LOGO_ALPHA * 4)
    public Unsigned8 tpgEnable = new Unsigned8(); // 对应叠加参数的TPG测试图层使能(PORT0_TPG_EN * 4)
    public Unsigned8 tpgAlpha = new Unsigned8(); // 对应叠加参数的TPG测试图层透明度(PORT0_TPG_ALPHA * 4)
    public Unsigned8 captionBgEnable = new Unsigned8(); // 条幅背景图开关，需VP7的SOC处理
    public Unsigned8 interfaceMode = new Unsigned8(); // 对应视频输出参数的VOUT_PORT_MODE_SEL，4口:0，双口:1，单口: 2
    public Unsigned8 analogAudioSel = new Unsigned8(); // 对应模拟音频选择参数ANALOG_AUDIO_SELECT，0~8
    public Unsigned8 analogAudioVolume = new Unsigned8(); // 对应模拟音频音量参数ANALOG_AUDIO_VOLUME，0~255
    public Unsigned8[] reserved = array(new Unsigned8[2]);
  }

  /**
   * LOGO配置.
   */
  public static class Vp7LogoConfig extends NormalStruct {
    public Unsigned32 logoColor = new Unsigned32(); // bit[16-23]对应LOGO参数的R分量(PORT0_LOGO_COLOR_R * 4)
    public Unsigned32 bgColor = new Unsigned32(); // bit[16-23]对应LOGO参数的R分量(PORT0_BACKGROUND_R * 4)
    public Unsigned16[] oid = array(new Unsigned16[4]); // 对应LOGO参数的显示的数字(PORT0_LOGO_NUM * 4)
    public Unsigned16 logoLeft = new Unsigned16(); // 对应LOGO参数的水平起始位置(PORT0_LOGO_LAYER_HOR_START * 4)
    public Unsigned16 logoTop = new Unsigned16(); // 对应LOGO参数的垂直起始位置(PORT0_LOGO_LAYER_VER_START * 4)
    public Unsigned16 logoWidth = new Unsigned16(); // 对应LOGO参数的水平宽度(PORT0_LOGO_LAYER_HOR_WIDTH * 4)
    public Unsigned16 logoHeight = new Unsigned16(); // 对应LOGO参数的垂直高度(PORT0_LOGO_LAYER_VER_HEIGHT * 4)
    public Unsigned8[] reserved = array(new Unsigned8[8]);
  }

  /**
   * 测试图配置.
   */
  public static class Vp7TpgConfig extends NormalStruct {
    public Unsigned8 mode = new Unsigned8(); // 对应测试图模式参数的模式设置(VOUT_TPG_MODE_SEL)
    // bit[16-23]对应测试图模式参数的R分量(VOUT_TPG_COLOR_R)
    // bit[8-15]对应测试图模式参数的G分量(VOUT_TPG_COLOR_G)
    // bit[0-7]对应测试图模式参数的B分量(VOUT_TPG_COLOR_B)
    // bit[24-31]是RGB的alpha值，计算配置用，VP7的SOC不需要
    public Unsigned32 color = new Unsigned32(); 
    public Unsigned16 speed = new Unsigned16(); // 对应测试图模式参数的移动速度(VOUT_TPG_MOVE_SPEED)
    public Unsigned8[] reserved = array(new Unsigned8[9]);
  }

  /**
   * VPSS视频处理参数.
   */
  public static class Vp7VpssLayerConfig extends NormalStruct {
    public Unsigned16 vidSelect = new Unsigned16(); // 对应VPSS视频参数的视频选择(VPSS0_LAYER0_VID_SEL)，v1~v16，0表示无视频
    public Unsigned16 preClipxOffset = new Unsigned16(); // 对应VPSS视频参数的前级clip_x_offset(VPSS0_LAYER0_PRE_CLIP_X_OFFSET)
    public Unsigned16 preClipxWidth = new Unsigned16(); // 对应VPSS视频参数的前级clip_x_width(VPSS0_LAYER0_PRE_CLIP_X_WIDTH)
    public Unsigned16 preClipyOffset = new Unsigned16(); // 对应VPSS视频参数的前级clip_y_offset(VPSS0_LAYER0_PRE_CLIP_Y_OFFSET)
    public Unsigned16 preClipyHeight = new Unsigned16(); // 对应VPSS视频参数的前级clip_y_height(VPSS0_LAYER0_PRE_CLIP_Y_HEIGHT)
    public Unsigned16 scaleDownWidth = new Unsigned16(); // 对应VPSS视频参数的缩小后的宽度(VPSS0_LAYER0_SCALER_DOWN_HOR_WIDTH)
    public Unsigned16 scaleDownHeight = new Unsigned16(); // 对应VPSS视频参数的缩小后的高度(VPSS0_LAYER0_SCALER_DOWN_VER_HEIGHT)
    public Unsigned16 scaleUpWidth = new Unsigned16(); // 对应VPSS视频参数的放大后的宽度(VPSS0_LAYER0_SCALER_UP_HOR_WIDTH)
    public Unsigned16 scaleUpHeight = new Unsigned16(); // 对应VPSS视频参数的放大后的高度(VPSS0_LAYER0_SCALER_UP_VER_HEIGHT)
    public Unsigned16 postClipxOffset = new Unsigned16(); // 对应VPSS视频参数的后级clip_x_offset(VPSS0_LAYER0_POST_CLIP_X_OFFSET)
    public Unsigned16 postClipxWidth = new Unsigned16(); // 对应VPSS视频参数的后级clip_x_width(VPSS0_LAYER0_POST_CLIP_X_WIDTH)
    public Unsigned16 postClipyOffset = new Unsigned16(); // 对应VPSS视频参数的后级clip_y_offset(VPSS0_LAYER0_POST_CLIP_Y_OFFSET)
    public Unsigned16 postClipyHeight = new Unsigned16(); // 对应VPSS视频参数的后级clip_y_height(VPSS0_LAYER0_POST_CLIP_Y_HEIGHT)
    public Unsigned16 layerX = new Unsigned16(); // 对应VPSS视频参数的图层叠加起始位置(VPSS0_LAYER0_OSD_HOR_START)
    public Unsigned16 layerY = new Unsigned16(); // 对应VPSS视频参数的图层叠加起始高度(VPSS0_LAYER0_OSD_VER_START)
    public Unsigned16 layerWidth = new Unsigned16(); // 对应VPSS视频参数的图层叠加宽度(VPSS0_LAYER0_OSD_HOR_WIDTH)
    public Unsigned16 layerHeight = new Unsigned16(); // 对应VPSS视频参数的图层叠加高度(VPSS0_LAYER0_OSD_VER_HEIGHT)
    public Unsigned8 pieceEnable = new Unsigned8(); // bit[0]对应VPSS视频参数的前级裁剪模块左边拼缝处理使能(VPSS0_LAYER1_HOR_CLIP_L_PIECE)
    public Unsigned8[] reserved = array(new Unsigned8[5]);
  }

  /**
   * 底图条幅配置.
   */
  public static class Vp7BackCaptionConfig extends NormalStruct {
    public Unsigned16 offsetX = new Unsigned16(); // 屏幕在整个大屏上横坐标
    public Unsigned16 offsetY = new Unsigned16(); // 屏幕在整个大屏上纵坐标
    public Unsigned16 width = new Unsigned16(); // 屏幕宽度
    public Unsigned16 height = new Unsigned16(); // 屏幕高度
  }

  /**
   * 输出端口配置.
   */
  public static class Vp7PortConfig extends NormalStruct {
    public Unsigned8 enable = new Unsigned8(); // 对应视频输出参数的端口开关(POTR0_VOUT_EN)
    public Unsigned16[] layerPriority = array(new Unsigned16[16]); // 对应叠加参数的图层优先级(PORT0_OSD_LAYER0_PRIORITY)，1~16，0表示关闭
    public Vp7BackCaptionConfig backCaptionConfig = inner(new Vp7BackCaptionConfig()); // 底图条幅配置，需VP7的SOC计算对应的值
    public Unsigned8[] reserved = array(new Unsigned8[23]);
  }

  /**
   * 横向或纵向输出分辨率配置.
   */
  public static class Vp7OutputResImpl extends NormalStruct {
    public Unsigned16 sync = new Unsigned16(); // 对应视频输出参数的hor_sync/ver_sync(POTR0_VOUT_HOR_SYNC * 4)
    public Unsigned16 backPorch = new Unsigned16(); // 对应视频输出参数的hor_back/ver_back(POTR0_VOUT_HOR_BACK * 4)
    public Unsigned16 disp = new Unsigned16(); // 对应视频输出参数的hor_disp/ver_disp(POTR0_VOUT_HOR_DISP * 4)
    public Unsigned16 total = new Unsigned16(); // 对应视频输出参数的hor_total/ver_total(POTR0_VOUT_HOR_TOTAL * 4)
    public Unsigned8 polarity = new Unsigned8(); // 对应视频输出参数的极性设置(POTR0_VOUT_HVS_POL * 4)，0表示正极性，1表示负极性
  }

  /**
   * 全部输出分辨率配置.
   */
  public static class Vp7OutputResData extends NormalStruct {
    public Unsigned32 clock = new Unsigned32(); // 时钟，单位为Hz
    public Vp7OutputResImpl horzOutput = inner(new Vp7OutputResImpl());
    public Vp7OutputResImpl vertOutput = inner(new Vp7OutputResImpl());
    public Unsigned8[] reserved = array(new Unsigned8[10]);
  }

  public Unsigned32 totalWidth = new Unsigned32(); // 大屏总宽度
  public Unsigned32 totalHeight = new Unsigned32(); // 大屏总高度
  public Vp7LogoConfig logoConfig = inner(new Vp7LogoConfig()); // LOGO配置，所有输出共用
  public Vp7TpgConfig tpgConfig = inner(new Vp7TpgConfig()); // 测试画面配置
  public Vp7VpssLayerConfig[] vpssLayerConfig = array(new Vp7VpssLayerConfig[16]); // VPSS视频处理参数
  public Vp7PortConfig[] portConfig = array(new Vp7PortConfig[4]); // 输出端口配置
  public Vp7OutputResData outputResData = inner(new Vp7OutputResData()); // 输出分辨率配置，所有输出共用
  public Vp7OverlapConfig overlapConfig = inner(new Vp7OverlapConfig()); // 叠加配置，所有输出共用


}
