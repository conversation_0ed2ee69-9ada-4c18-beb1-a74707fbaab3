package com.mc.tool.caesar.vpm.gui.pages.hostconfig;

import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.caesar.vpm.pages.hostconfiguration.CaesarHostConfigurationPage;
import java.io.File;
import java.io.IOException;
import javafx.scene.control.Button;
import javafx.scene.control.TextField;
import javafx.scene.input.KeyCode;
import org.junit.Assert;
import org.junit.Test;

/**
 * .
 */
public class NetworkConfigTest extends AppGuiTestBase {

  @Override
  public void beforeAll() {}

  @Test
  public void simpleTest() {
    try {
      clickOn("#" + CaesarHostConfigurationPage.NAME);
      clickOn(GuiTestConstants.NETWORK_FORM_TAB);
      Button commitBtn = lookup(GuiTestConstants.NETWORK_FORM_COMMIT_BTN).queryButton();
      Button cancelBtn = lookup(GuiTestConstants.NETWORK_FORM_CANCEL_BTN).queryButton();
      // 检查格式
      checkIp(GuiTestConstants.NETWORK_FORM_IP, commitBtn, cancelBtn);
      checkIp(GuiTestConstants.NETWORK_FORM_MASK, commitBtn, cancelBtn);
      checkIp(GuiTestConstants.NETWORK_FORM_GATEWAY, commitBtn, cancelBtn);
      // 检查mac
      Assert.assertTrue(lookup(GuiTestConstants.NETWORK_FORM_MAC).query().isDisable());
      // 检查dhcp
      clickOn(GuiTestConstants.NETWORK_FORM_DHCP);
      Assert.assertTrue(lookup(GuiTestConstants.NETWORK_FORM_IP).query().isDisable());
      Assert.assertTrue(lookup(GuiTestConstants.NETWORK_FORM_MASK).query().isDisable());
      Assert.assertTrue(lookup(GuiTestConstants.NETWORK_FORM_GATEWAY).query().isDisable());
      clickOn(GuiTestConstants.NETWORK_FORM_DHCP);
      Assert.assertFalse(lookup(GuiTestConstants.NETWORK_FORM_IP).query().isDisable());
      Assert.assertFalse(lookup(GuiTestConstants.NETWORK_FORM_MASK).query().isDisable());
      Assert.assertFalse(lookup(GuiTestConstants.NETWORK_FORM_GATEWAY).query().isDisable());

      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException e) {
      e.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testGridStatus() {
    // 级联状态下不能修改名称
    try {
      clickOn("#" + CaesarHostConfigurationPage.NAME);
      clickOn(GuiTestConstants.NETWORK_FORM_TAB);
      Assert.assertTrue(lookup(GuiTestConstants.NETWORK_FORM_DHCP).query().isDisabled());
      Assert.assertTrue(lookup(GuiTestConstants.NETWORK_FORM_IP).query().isDisabled());
      Assert.assertTrue(lookup(GuiTestConstants.NETWORK_FORM_MASK).query().isDisabled());
      Assert.assertTrue(lookup(GuiTestConstants.NETWORK_FORM_GATEWAY).query().isDisabled());

      File tempFile = loadResourceStatus("com/mc/tool/caesar/vpm/36_hw1.2_grid_full.status");
      tempFile.delete();
    } catch (IOException e) {
      e.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  private void checkIp(String ipSelector, Button commitBtn, Button cancelBtn) {
    TextField textField = lookup(ipSelector).query();
    scrollToShow(textField);
    clickOn(textField);
    type(KeyCode.END);
    eraseText(20);
    Assert.assertTrue(commitBtn.isDisabled());
    Assert.assertFalse(cancelBtn.isDisabled());
    write("192.");
    Assert.assertTrue(commitBtn.isDisabled());
    Assert.assertFalse(cancelBtn.isDisabled());
    eraseText(5);
    write("192.168.");
    Assert.assertTrue(commitBtn.isDisabled());
    Assert.assertFalse(cancelBtn.isDisabled());
    eraseText(10);
    write("************");
    Assert.assertFalse(commitBtn.isDisabled());
    Assert.assertFalse(cancelBtn.isDisabled());

    clickOn(cancelBtn);
    String oldValue = textField.getText();
    Assert.assertEquals(oldValue, textField.getText());
  }
}
