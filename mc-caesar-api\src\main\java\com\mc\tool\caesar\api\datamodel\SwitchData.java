package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;

/**
 * SwitchData.
 *
 * @brief 连接交换的配置.
 */
public final class SwitchData extends AbstractData {

  public static final String PROPERTY_BASE = "SystemConfigData.SwitchData.";
  public static final String FIELD_TIMEOUT_DISCONNECT = "TimeoutDisconnect";
  public static final String FIELD_TIMEOUT_SHARE = "TimeoutShare";
  // System - Switch窗口下的 CPU Timeout [min] 的自定义值
  public static final String PROPERTY_TIMEOUT_DISCONNECT =
      "SystemConfigData.SwitchData.TimeoutDisconnect";
  // System - Switch窗口下的 Release Time [sec] 的自定义值
  public static final String PROPERTY_TIMEOUT_SHARE = "SystemConfigData.SwitchData.TimeoutShare";
  // System - Switch窗口下的 Enable Video Sharing 是否选中
  public static final String PROPERTY_FORCE_BITS_CPU_WATCH =
      "SystemConfigData.SwitchData.ForceBits.CpuWatch";
  // System - Switch窗口下的 Force Connect 是否选中
  public static final String PROPERTY_FORCE_BITS_CPU_CONNECT =
      "SystemConfigData.SwitchData.ForceBits.CpuConnect";
  // System - Switch窗口下的 Force Disconnect 是否选中
  public static final String PROPERTY_FORCE_BITS_CON_DISCONNECT =
      "SystemConfigData.SwitchData.ForceBits.ConDisconnect";
  // System - Switch窗口下的 CPU Auto Connect 是否选中
  public static final String PROPERTY_FORCE_BITS_AUTO_CONNECT =
      "SystemConfigData.SwitchData.ForceBits.AutoConnect";
  // System - Switch窗口下的 Keyboard Connect 是否选中
  public static final String PROPERTY_FORCE_BITS_KEYBOARD_MOUSE_CONNECT =
      "SystemConfigData.SwitchData.ForceBits.KeyboardConnect";
  // System - Switch窗口下的 Macro Single Step 是否选中
  public static final String PROPERTY_FORCE_BITS_FKEYSINGLE =
      "SystemConfigData.SwitchData.ForceBits.FKeySingle";
  private final SystemConfigData systemConfigData;
  @Expose
  private int timeoutDisconnect; // cpu timeout[min]
  // specify inactivity period at currently
  // connected CPU after which CPU will be
  // disconnected automatically(0=deactivated)
  @Expose
  private int timeoutShare; // release time[sec]
  // specify inactivity time to accept CPU control
  // request from another control

  /**
   * .
   */
  public SwitchData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager,
      String fqn, SystemConfigData systemConfigData) {
    super(pcs, configDataManager, -1, fqn);
    this.systemConfigData = systemConfigData;

    initCommitRollback();
  }

  @Override
  public void initDefaults() {
    setTimeoutDisconnect(0);
    setTimeoutShare(10);
  }

  public boolean isAutoConnect() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.AUTOCONNECT);
  }

  /**
   * .
   */
  public void setAutoConnect(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.AUTOCONNECT}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isConDisconnect() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.CONDISCONNECT);
  }

  /**
   * .
   */
  public void setConDisconnect(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.CONDISCONNECT}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isCpuConnect() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.CPUCONNECT);
  }

  /**
   * .
   */
  public void setCpuConnect(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.CPUCONNECT}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isCpuWatch() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.CPUWATCH);
  }

  /**
   * .
   */
  public void setCpuWatch(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.CPUWATCH}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isKeyboardMouseConnect() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.KEYBOARD_MOUSE);
  }

  /**
   * .
   */
  public void setKeyboardMouseConnect(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.KEYBOARD_MOUSE}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public int getTimeoutDisconnect() {
    return this.timeoutDisconnect;
  }

  /**
   * .
   */
  public void setTimeoutDisconnect(int timeoutDisconnect) {
    int oldValue = this.timeoutDisconnect;
    this.timeoutDisconnect = timeoutDisconnect;
    firePropertyChange(SwitchData.PROPERTY_TIMEOUT_DISCONNECT, Integer.valueOf(oldValue),
        Integer.valueOf(this.timeoutDisconnect), new int[0]);
  }

  public int getTimeoutShare() {
    return this.timeoutShare;
  }

  /**
   * .
   */
  public void setTimeoutShare(int timeoutShare) {
    int oldValue = this.timeoutShare;
    this.timeoutShare = timeoutShare;
    firePropertyChange(SwitchData.PROPERTY_TIMEOUT_SHARE, Integer.valueOf(oldValue),
        Integer.valueOf(this.timeoutShare), new int[0]);
  }

  public boolean isFkeySingle() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.FKEYSINGLE);
  }

  /**
   * .
   */
  public void setFkeySingle(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.FKEYSINGLE}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (SwitchData.PROPERTY_TIMEOUT_DISCONNECT.equals(propertyName)) {
      setTimeoutDisconnect(Integer.class.cast(value).intValue());
    } else if (SwitchData.PROPERTY_TIMEOUT_SHARE.equals(propertyName)) {
      setTimeoutShare(Integer.class.cast(value).intValue());
    }
  }
}

