package com.mc.tool.caesar.vpm.pages.hostupdate;

import com.mc.tool.caesar.api.CaesarCpuTypeProperty;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.MatrixDefinitionData;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.VersionDef;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.exception.ReconnectException;
import com.mc.tool.caesar.api.utils.IoUpdateInfo;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.update.UpdateData;
import com.mc.tool.caesar.vpm.pages.update.UpdateLogger;
import com.mc.tool.caesar.vpm.pages.update.UpdateTask;
import com.mc.tool.caesar.vpm.util.update.UpdateConstant;
import com.mc.tool.caesar.vpm.util.update.UpdatePackageInfo;
import com.mc.tool.caesar.vpm.util.update.UpdateUtilities;
import com.mc.tool.framework.utility.UndecoratedAlert;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ExecutionException;
import javafx.application.Platform;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.TreeItem;
import javafx.scene.control.TreeTableView;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.cpio.CpioArchiveEntry;
import org.apache.commons.compress.archivers.cpio.CpioArchiveInputStream;

/**
 * .
 */
@Slf4j
public class UpdateHostUpdater {
  private static final int IO_PAGE_COUNT = 256;
  private static final int MAT_PAGE_COUNT = 1024;
  private static final int IO_TRUNK_SEPERATE_UPDATE_SUCCESS_CLOSE_DATA = 0;
  private static final int IO_TRUNK_UPDATE_FAIL_CLOSE_DATA = 1;
  private static final int IO_TRUNK_BOTH_UPDATE_SUCCESS_CLOSE_DATA = 2;
  private String filePath = null;
  private TreeTableView<UpdateData> tableView;
  private HostUpdatePageController hostPanel;
  List<TreeItem<UpdateData>> updateGroup = new ArrayList<>();
  List<TreeItem<UpdateData>> updateSlotGroup = new ArrayList<>();
  List<TreeItem<UpdateData>> availableGroup = new ArrayList<>();

  List<UpdateTask> updateTasks = new ArrayList<>();

  CaesarDeviceController deviceController;
  private UpdateLogger logger;

  boolean updateCancelled = false;

  UpdatePackageInfo updatePackageInfo = null;

  /** . */
  public UpdateHostUpdater(
      CaesarDeviceController deviceController,
      UpdateLogger logger,
      HostUpdatePageController panel) {
    this.logger = logger;
    this.hostPanel = panel;
    this.tableView = panel.getTreeView();
    this.deviceController = deviceController;
  }

  public void setFilePath(String path) {
    filePath = path;
  }

  public void setUpdatePackageInfo(UpdatePackageInfo info) {
    this.updatePackageInfo = info;
  }

  public List<TreeItem<UpdateData>> getAvailableGroup() {
    return this.availableGroup;
  }

  public void setTreeTableView(TreeTableView<UpdateData> tableView) {
    this.tableView = tableView;
  }

  public void setAvailableGroup(List<TreeItem<UpdateData>> group) {
    this.availableGroup = group;
  }

  /** . */
  public void update() {
    updateSlotGroup.clear();
    updateGroup.clear();

    for (TreeItem<UpdateData> dkmItem : tableView.getRoot().getChildren()) {
      for (TreeItem<UpdateData> slotItem : dkmItem.getChildren()) {
        boolean canUpdate = false;
        for (TreeItem<UpdateData> typeItem : slotItem.getChildren()) {
          if (typeItem.getValue().getSelected()) {
            canUpdate = true;
            updateGroup.add(typeItem);
          }
        }
        if (canUpdate && !updateSlotGroup.contains(slotItem)) {
          updateSlotGroup.add(slotItem);
        }
      }
    }

    if (updateSlotGroup.isEmpty()) {
      return;
    }

    hostPanel.updateStart();
    updateImpl(updateSlotGroup);
  }

  /** . */
  public void updateImpl(List<TreeItem<UpdateData>> updateSlotGroup) {
    for (TreeItem<UpdateData> slotItem : updateSlotGroup) {
      UpdateData slotData = slotItem.getValue();
      if (slotData.getType().equals("CPU")) {
        log.info("update mat,size: " + updateSlotGroup.size());
        Thread thread = new Thread(() -> updateMatImpl(updateSlotGroup));
        thread.setDaemon(true);
        thread.start();
        break;
      } else if (slotData.getType().equals("io")) {
        log.info("update mat,size: " + updateSlotGroup.size());

        Thread thread = new Thread(() -> updateIoImpl(updateSlotGroup));
        thread.setDaemon(true);
        thread.start();
        break;
      }
    }
  }

  /** . */
  public void updateIoImpl(List<TreeItem<UpdateData>> updateSlotGroup) {
    if (filePath.isEmpty()) {
      logger.addLog(String
          .format("[%s] update file: %s is empty!", deviceController.getLoginUser(), filePath));
    }
    ArrayList<UpdateData> updateDatas = new ArrayList<>();

    for (TreeItem<UpdateData> slotItem : updateSlotGroup) {
      updateDatas.add(slotItem.getValue());
    }

    beginUpdate();
    CaesarSwitchDataModel model = deviceController.getDataModel();
    Collection<IoUpdateInfo> ioUpdateInfos =
        getIoUpdateInfos(model, updatePackageInfo, updateDatas.toArray(new UpdateData[0]));
    for (IoUpdateInfo ioUpdateInfo : ioUpdateInfos) {
      if (null == ioUpdateInfo) {
        continue;
      }
      CaesarSwitchDataModel matModel = ioUpdateInfo.getModel();
      String matAddress =
          IpUtil.getAddressString(
              matModel.getConfigData().getSystemConfigData().getNetworkDataCurrent1().getAddress());

      UpdateIoTask updateIoTask =
          new UpdateIoTask(
              matModel,
              ioUpdateInfo,
              ioUpdateInfo.getIoStartAddress(),
              ioUpdateInfo.getUpdateIoFileName());
      updateTasks.add(updateIoTask);

      UpdateTrunkTask updateTrunkTask =
          new UpdateTrunkTask(
              matModel,
              ioUpdateInfo,
              ioUpdateInfo.getTrunkStartAddress(),
              ioUpdateInfo.getUpdateTrunkFileName());
      updateTasks.add(updateTrunkTask);
      Platform.runLater(
          () -> {
            for (TreeItem<UpdateData> slotItem : updateSlotGroup) {
              for (TreeItem<UpdateData> typeItem : slotItem.getChildren()) {
                if (typeItem.getValue().getSelected()
                    && typeItem.getValue().getAddress().equals(matAddress)) {
                  log.info(
                      matAddress
                          + " "
                          + slotItem.getValue().getName()
                          + " "
                          + typeItem.getValue().getType()
                          + " bind");

                  if (typeItem.getValue().getLevel1() == 0) {
                    if (updateTrunkTask != null) {
                      typeItem
                          .getValue()
                          .getProgressBar()
                          .progressProperty()
                          .bind(updateTrunkTask.progressProperty());
                    } else {
                      log.warn("Update trunk task is null!");
                    }
                  } else {
                    typeItem
                        .getValue()
                        .getProgressBar()
                        .progressProperty()
                        .bind(updateIoTask.progressProperty());
                  }
                }
              }
            }
          });
      Thread thread =
          new Thread(
              () -> {
                int successfulCloseData = IO_TRUNK_SEPERATE_UPDATE_SUCCESS_CLOSE_DATA;
                if (ioUpdateInfo.getUpdateParams().length != 0 && ioUpdateInfo.isUpdateTrunk()) {
                  successfulCloseData = IO_TRUNK_BOTH_UPDATE_SUCCESS_CLOSE_DATA;
                }

                if (ioUpdateInfo.getUpdateParams().length != 0) {
                  logger.addLog(
                      String.format("[%s] start to update io.", deviceController.getLoginUser()));
                  updateIoTask.setSuccessfulCloseData(successfulCloseData);
                  updateIoTask.run();
                } else {
                  updateIoTask.setFinished(true);
                }

                if (ioUpdateInfo.isUpdateTrunk()) {
                  logger.addLog(
                      String.format("[%s] Start to update trunk.", deviceController.getLoginUser()));
                  updateTrunkTask.setSuccessfulCloseData(successfulCloseData);
                  updateTrunkTask.run();
                } else {
                  updateTrunkTask.setFinished(true);
                }
              });
      thread.setDaemon(true);
      thread.start();
    }

    // 等待全部结束
    boolean allFinish = false;
    while (!allFinish) {
      boolean finish = true;
      for (UpdateTask updateTask : updateTasks) {
        if (!updateTask.isFinished()) {
          finish = false;
          break;
        }
      }
      if (finish) {
        allFinish = true;
      } else {
        try {
          Thread.sleep(1000);
        } catch (InterruptedException exp) {
          logger.addLog("Sleep is interrupted!");
        }
      }
    }
    endUpdate("升级完成！");
  }

  /** . */
  public void updateMatImpl(List<TreeItem<UpdateData>> updateSlotGroup) {
    CaesarSwitchDataModel model = deviceController.getDataModel();
    if (filePath.isEmpty()) {
      logger.addLog(
          String.format("[%s] update file: %s is empty!", deviceController.getLoginUser(), filePath));
      return;
    }

    beginUpdate();

    for (TreeItem<UpdateData> slotItem : updateSlotGroup) {
      try {
        CaesarSwitchDataModel matModel =
            Utilities.getExternalModel(model, slotItem.getValue().getAddress());
        String matAddress =
            IpUtil.getAddressString(
                matModel
                    .getConfigData()
                    .getSystemConfigData()
                    .getNetworkDataCurrent1()
                    .getAddress());

        UpdateMatTask updateHostTask = new UpdateMatTask(matModel);
        updateTasks.add(updateHostTask);
        Platform.runLater(
            () -> {
              for (TreeItem<UpdateData> typeItem : slotItem.getChildren()) {
                if (typeItem.getValue().getSelected()
                    && typeItem.getValue().getAddress().equals(matAddress)) {
                  typeItem
                      .getValue()
                      .getProgressBar()
                      .progressProperty()
                      .bind(updateHostTask.progressProperty());
                }
              }
            });

        new Thread(updateHostTask).start();

      } catch (BusyException | ConfigException ex) {
        log.warn("Fail to update mat!", ex);
      }
    }

    // 等待全部结束
    boolean allFinish = false;
    while (!allFinish) {
      boolean finish = true;
      for (UpdateTask updateTask : updateTasks) {
        if (!updateTask.isFinished()) {
          finish = false;
          break;
        }
      }
      if (finish) {
        allFinish = true;
      } else {
        try {
          Thread.sleep(1000);
        } catch (InterruptedException exp) {
          logger.addLog("Sleep is interrupted!");
        }
      }
    }
    endUpdate("升级完成！");
  }

  /** . */
  public void updateCancel() {
    for (UpdateTask updateTask : updateTasks) {
      updateTask.setCancelled(true);
      updateTask.setFinished(true);
    }
    updateCancelled = true;
  }

  protected void beginUpdate() {
    // 记录开始日志
    for (TreeItem<UpdateData> slotItem : updateSlotGroup) {
      for (TreeItem<UpdateData> typeItem : slotItem.getChildren()) {
        if (typeItem.getValue().getSelected()) {
          logger.addLog(
              String.format("[%s] %s %s %s update start.",
                  deviceController.getLoginUser(),
                  slotItem.getParent().getValue().getName(),
                  slotItem.getValue().getName(),
                  typeItem.getValue().getType()));
        }
      }
    }
    hostPanel.setUpdating(true);
    hostPanel.setCancelled(false);
    updateCancelled = false;

    updateTasks.clear();
  }

  protected void endUpdate(String prompt) {
    Platform.runLater(
        () -> {
          if (!updateCancelled) {
            // 记录结束日志及解绑进度条
            for (TreeItem<UpdateData> slotItem : updateSlotGroup) {
              for (TreeItem<UpdateData> typeItem : slotItem.getChildren()) {
                if (typeItem.getValue().getSelected()) {
                  typeItem.getValue().getProgressBar().progressProperty().unbind();
                  boolean result = checkUpdate(slotItem.getValue(), typeItem.getValue());
                  if (result) {
                    logger.addLog(String.format("[%s] %s %s %s update success.",
                        deviceController.getLoginUser(),
                        slotItem.getParent().getValue().getName(),
                        slotItem.getValue().getName(),
                        typeItem.getValue().getType()));
                  } else {
                    logger.addLog(String.format("[%s] %s %s %s update fail.",
                        deviceController.getLoginUser(),
                        slotItem.getParent().getValue().getName(),
                        slotItem.getValue().getName(),
                        typeItem.getValue().getType()));
                  }
                }
                typeItem.getValue().setUpdateDate("");
                typeItem.getValue().setUpdateVersion(null);
                typeItem.getValue().getProgressBar().setProgress(0);
              }
            }
            hostPanel.updateNodes();

            UndecoratedAlert alert = new UndecoratedAlert(AlertExType.INFORMATION);
            if (tableView != null && tableView.getScene() != null) {
              alert.initOwner(tableView.getScene().getWindow());
            }
            alert.setTitle("升级");
            alert.setHeaderText(null);
            alert.setContentText(prompt);
            alert.showAndWait();
          } else {
            hostPanel.setCancelled(false);
            for (TreeItem<UpdateData> typeItem : availableGroup) {
              typeItem.getValue().setDisabled(false);
            }
            logger.addLog(String.format("[%s] update cancelled!", deviceController.getLoginUser()));
          }
          updateSlotGroup.clear();
          updateTasks.clear();

          hostPanel.setCancelled(false);
          hostPanel.setUpdating(false);
        });
  }

  private class UpdateTrunkTask extends UpdateIoTask {

    public UpdateTrunkTask(
        CaesarSwitchDataModel model,
        IoUpdateInfo ioUpdateInfo,
        int startAddress,
        String updateFile) {
      super(model, ioUpdateInfo, startAddress, updateFile);
    }

    @Override
    protected void updateOpen() throws DeviceConnectionException, ConfigException, BusyException {
      model.getController().setUpdateOpen((byte) 0, (byte) 0xff, (byte) 0);
    }

    @Override
    protected void updateWrite(int offset, byte[] dataToWrite)
        throws DeviceConnectionException, ConfigException, BusyException {
      model
          .getController()
          .setUpdateWrite((byte) 0, (byte) 0xff, (byte) 0, offset, dataToWrite, true);
    }

    @Override
    protected void updateClose(boolean successful)
        throws DeviceConnectionException, ConfigException, BusyException {
      model
          .getController()
          .setUpdateClose(
              (byte) 0,
              (byte) 0xff,
              (byte) (successful ? successfulCloseData : IO_TRUNK_UPDATE_FAIL_CLOSE_DATA));
    }
  }

  private class UpdateIoTask extends UpdateTask {
    protected final IoUpdateInfo ioUpdateInfo;
    protected final CaesarSwitchDataModel model;
    protected final int startAddress;
    protected final String updateFileName;

    @Setter protected int successfulCloseData = 0;

    public UpdateIoTask(
        CaesarSwitchDataModel model,
        IoUpdateInfo ioUpdateInfo,
        int startAddress,
        String updateFile) {
      this.model = model;
      this.ioUpdateInfo = ioUpdateInfo;
      this.startAddress = startAddress;
      this.updateFileName = updateFile;
      cancelled = false;
      finished = false;
      updateProgress(0, 0);
    }

    @Override
    protected Object call() {
      byte[] tmpFileBtye = null;
      CpioArchiveInputStream cpioIn = null;
      try {
        cpioIn = new CpioArchiveInputStream(Files.newInputStream(Paths.get(filePath)));
        CpioArchiveEntry cpioEntry;
        while ((cpioEntry = (CpioArchiveEntry) cpioIn.getNextEntry()) != null) {
          if (cpioEntry.getName().equals(updateFileName)) {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            int count;
            byte[] data = new byte[IO_PAGE_COUNT];
            while ((count = cpioIn.read(data, 0, IO_PAGE_COUNT)) != -1) {
              baos.write(data, 0, count);
            }
            tmpFileBtye = baos.toByteArray();
            baos.close();
          }
        }
      } catch (IOException ex) {
        logger.addLog(
            String.format("[%s] %s decompress fail.", deviceController.getLoginUser(), filePath),
            ex);
      } finally {
        if (cpioIn != null) {
          try {
            cpioIn.close();
          } catch (IOException ex) {
            log.warn("Fail to close cpioin!", ex);
          }
        }
      }

      if (null != tmpFileBtye) {
        try {
          finished = false;
          updateImpl(tmpFileBtye, IO_PAGE_COUNT, startAddress, logger);
          if (!cancelled) {
            readNewIoVersion();
          }
          finished = true;
        } catch (Exception ex) {
          logger.addLog(String.format("[%s] Update fail", deviceController.getLoginUser()), ex);
          updateCancel();
        } finally {
          if (finished && !cancelled) {
            updateProgress(1, 1);
          }
        }
      }
      return null;
    }

    @Override
    protected void updateOpen() throws DeviceConnectionException, ConfigException, BusyException {
      model.getController().setIoParallelUpdateOpen(ioUpdateInfo.getUpdateParams());
    }

    @Override
    protected void updateWrite(int offset, byte[] dataToWrite)
        throws DeviceConnectionException, ConfigException, BusyException {
      model.getController().setIoParallelUpdateWrite(offset, dataToWrite, true);
    }

    @Override
    protected void updateClose(boolean successful)
        throws DeviceConnectionException, ConfigException, BusyException {
      model
          .getController()
          .setUpdateClose(
              (byte) 0xff,
              (byte) 0,
              (byte) (successful ? successfulCloseData : IO_TRUNK_UPDATE_FAIL_CLOSE_DATA));
    }
  }

  private class UpdateMatTask extends UpdateTask {
    CaesarSwitchDataModel model;

    public UpdateMatTask(CaesarSwitchDataModel model) {
      super();
      this.model = model;
      cancelled = false;
      finished = false;
    }

    @Override
    protected Object call() {
      try {
        int byteHasRead = 0;

        finished = false;
        model.getController().setUpdateOpen((byte) 0, (byte) 0, (byte) 0);
        int readOnce;
        File file = new File(filePath);
        long fileSize = file.length();
        int packageCount = (int) (fileSize / MAT_PAGE_COUNT);
        log.debug("Host update package count : {}.", packageCount);
        updateProgress(0, fileSize);
        byte[] tmp = new byte[MAT_PAGE_COUNT];
        boolean successful = false;
        try (FileInputStream fis = new FileInputStream(file)) {
          while ((readOnce = fis.read(tmp)) != -1) {
            if (!cancelled) {
              if (readOnce < MAT_PAGE_COUNT) {
                byte[] lastTmp = new byte[readOnce];
                System.arraycopy(tmp, 0, lastTmp, 0, readOnce);
                model
                    .getController()
                    .setUpdateWrite((byte) 0, (byte) 0, (byte) 0, byteHasRead, lastTmp, true);
              } else {
                model
                    .getController()
                    .setUpdateWrite((byte) 0, (byte) 0, (byte) 0, byteHasRead, tmp, true);
              }

              byteHasRead += readOnce;
              updateProgress(byteHasRead, fileSize);
            } else {
              break;
            }
          }
          if (!cancelled) {
            successful = true;
          }
        } catch (FileNotFoundException ex) {
          log.warn("Fail to find file!", ex);
        } finally {
          model.getController().setUpdateClose((byte) 0, (byte) 0, (byte) (successful ? 0 : 1));
        }

        if (!cancelled) {
          waitForReboot();
          Thread.sleep(5 * 1000);
          model.getSwitchModuleData().reloadModules();
        }
        finished = true;
      } catch (RuntimeException ex) {
        logger.addLog(String.format("[%s] Update fail: ", deviceController.getLoginUser()), ex);
        updateCancel();
      } catch (Exception ex) {
        if (ex instanceof ReconnectException) {
          logger.addLog(String.format("[%s] Try to refresh: ", deviceController.getLoginUser()), ex);
        } else {
          logger.addLog(String.format("[%s] Update fail: ", deviceController.getLoginUser()), ex);
        }
        updateCancel();
      } finally {
        if (finished && !cancelled) {
          updateProgress(1, 1);
        }
      }
      return null;
    }

    @Override
    protected void updateOpen() throws DeviceConnectionException, ConfigException, BusyException {
      // TODO Auto-generated method stub

    }

    @Override
    protected void updateWrite(int offset, byte[] dataToWrite)
        throws DeviceConnectionException, ConfigException, BusyException {
      // TODO Auto-generated method stub

    }

    @Override
    protected void updateClose(boolean successful)
        throws DeviceConnectionException, ConfigException, BusyException {
      // TODO Auto-generated method stub

    }
  }

  protected void waitForReboot() throws Exception {
    logger.addLog(
        String.format("[%s] waiting for the DKM(s) restart....", deviceController.getLoginUser()));
    // 等待断连
    int waitStopCount = 0;
    while (deviceController.getDataModel().isConnected() && waitStopCount < 60) {
      try {
        Thread.sleep(2000L);
      } catch (InterruptedException ex) {
        log.error("thread sleep error");
      }
      waitStopCount++;
    }
    logger.addLog(String.format("[%s] Matrix is disconnected.", deviceController.getLoginUser()));
    int loopCounter = 0;
    // 等待重新连接
    while (!deviceController.getDataModel().isConnected() && loopCounter < 600) {
      try {
        Thread.sleep(1000L);
      } catch (InterruptedException ex) {
        log.error("thread sleep error");
      }
      loopCounter++;
      log.info("waiting for reconnect ...");
    }
    if (loopCounter == 600) {
      logger.addLog(
          String.format("[%s] Fail to reconnect matrix!", deviceController.getLoginUser()));
      throw new ReconnectException("Reconnect failed");
    }
    logger.addLog(String.format("[%s] Matrix is reconnected.", deviceController.getLoginUser()));
  }

  protected void readNewIoVersion() {
    logger.addLog(String.format("[%s] Waiting for reboot!", deviceController.getLoginUser()));
    int count = 0;
    int maxCount = 90;
    while (count < maxCount) {
      try {
        deviceController
            .submit(
                () -> {
                  try {
                    deviceController.getDataModel().getSwitchModuleData().reloadModules();
                  } catch (ConfigException | BusyException exception) {
                    log.warn("Fail to reload module data!", exception);
                  }
                })
            .get();
      } catch (ExecutionException | InterruptedException exception) {
        log.warn("Fail to reload module data!", exception);
      }
      boolean pass = true;
      for (TreeItem<UpdateData> slotItem : updateSlotGroup) {
        for (TreeItem<UpdateData> typeItem : slotItem.getChildren()) {
          pass = checkUpdate(slotItem.getValue(), typeItem.getValue());
          if (!pass) {
            break;
          }
        }
        if (!pass) {
          break;
        }
      }

      if (pass) {
        break;
      } else {
        try {
          Thread.sleep(1000);
        } catch (InterruptedException exception) {
          log.warn("Sleep is intterupted!", exception);
        }
        count++;
      }
    }
    if (count >= maxCount) {
      log.warn("Maybe fail to update some io!");
    }
  }

  protected boolean checkUpdate(UpdateData slotItem, UpdateData typeItem) {
    if (!typeItem.getSelected() || typeItem.getUpdateVersion() == null) {
      return true;
    }
    if (slotItem.getType().equals(UpdateConstant.UPDATE_IO_TYPE)) {
      if (typeItem.getType().equals(UpdateConstant.UPDATE_FPGA_TYPE)) {
        ModuleData data =
            deviceController
                .getDataModel()
                .getSwitchModuleData()
                .getModuleData(slotItem.getLevel1());
        VersionDef def = typeItem.getUpdateVersion().copy();
        def.setYear(def.getYear() - 2000);
        if (slotItem.getLevel1() == 0) {
          return data.getVersion().getOtherVersion().equals(def);
        } else {
          return data.getVersion().getFpgaVersion().equals(def);
        }
      } else {
        return true;
      }
    } else if (slotItem.getType().equals(UpdateConstant.UPDATE_CPU_TYPE)) {
      VersionDef versionDef = typeItem.getUpdateVersion().copy();
      versionDef.setYear(versionDef.getYear() - 2000);
      ModuleData data = deviceController.getDataModel().getSwitchModuleData().getModuleData(0);
      if (typeItem.getType().equals(UpdateConstant.UPDATE_APP_TYPE)) {
        return data.getVersion().getAppVersion().equals(versionDef);
      } else if (typeItem.getType().equals(UpdateConstant.UPDATE_FPGA_TYPE)) {
        return data.getVersion().getFpgaVersion().equals(versionDef);
      } else if (typeItem.getType().equals(UpdateConstant.UPDATE_SYS_TYPE)) {
        return data.getVersion().getSystemVersion().equals(versionDef);
      } else {
        return true;
      }
    }
    return true;
  }

  /**
   * 获取io升级的参数与升级用到的model.
   *
   * @param model 当前的datamodel
   * @param updateDatas 要升级的板卡
   */
  public static Collection<IoUpdateInfo> getIoUpdateInfos(
      CaesarSwitchDataModel model, UpdatePackageInfo updatePackageInfo, UpdateData... updateDatas) {
    Collection<MatrixDefinitionData> matrices = Utilities.getActiveMatrices(model);
    int index = -1;

    List<IoUpdateInfo> result = new ArrayList<>();
    for (MatrixDefinitionData mat : matrices) {
      List<ModuleData> modules = new ArrayList<>();
      boolean updateTrunk = false;
      index++;
      IoUpdateInfo info;
      CaesarSwitchDataModel externalModel;
      try {
        externalModel = Utilities.getExternalModel(model, mat.getAddress());
        info = new IoUpdateInfo(externalModel, null);
      } catch (BusyException | ConfigException exception) {
        log.warn("Fail to get io update info!", exception);
        continue;
      }
      // 获取基本信息
      ModuleData cpuModule = externalModel.getSwitchModuleData().getModuleData(0);
      String updateFileName = UpdateConstant.UPDATE_FILE_SINGLE;
      CaesarCpuTypeProperty property = CaesarCpuTypeProperty.getProperty(cpuModule.getType());
      if (property != null) {
        if (property.hasTrunk()) {
          updateFileName = updatePackageInfo.getBranchFilePath(property.getType());
        }
        info.setIoStartAddress(property.getIoUpdateStartAddress(cpuModule.getPorts()));
        info.setTrunkStartAddress(property.getTrunkUpdateStartAddress());
        info.setUpdateTrunkFileName(updatePackageInfo.getTrunkFilePath(property.getType()));
      }
      info.setUpdateIoFileName(updateFileName);
      // 获取要升级的io
      List<Integer> updateDataLevel = new ArrayList<>();
      for (UpdateData updateData : updateDatas) {
        if (!updateData.getAddress().equalsIgnoreCase(mat.getAddress())) {
          continue;
        }
        if (updateData.getLevel1() == 0) {
          updateTrunk = true;
        } else {
          modules.add(externalModel.getSwitchModuleData().getModuleData(updateData.getLevel1()));
          updateDataLevel.add(updateData.getLevel1() - mat.getFirstModule());
        }
      }

      int matrixType = cpuModule.getType();
      byte[] bytes = UpdateUtilities.calculateUpdateParams(index, matrixType, updateDataLevel);
      info.setUpdateParams(bytes);
      info.setUpdateTrunk(updateTrunk);
      result.add(info);
    }
    return result;
  }
}
