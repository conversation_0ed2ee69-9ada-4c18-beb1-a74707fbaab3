package com.mc.tool.caesar.vpm.pages.extenderupdate;

import com.google.common.base.Strings;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.VersionDef;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.utils.ArrayIterable;
import com.mc.tool.caesar.api.utils.ExtenderUpdateInfo;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.update.UpdateData;
import com.mc.tool.caesar.vpm.pages.update.UpdateLogger;
import com.mc.tool.caesar.vpm.pages.update.UpdateTask;
import com.mc.tool.caesar.vpm.util.update.UpdateConstant;
import com.mc.tool.framework.utility.UndecoratedAlert;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import javafx.application.Platform;
import javafx.scene.control.AlertEx;
import javafx.scene.control.TreeItem;
import javafx.scene.control.TreeTableView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.cpio.CpioArchiveEntry;
import org.apache.commons.compress.archivers.cpio.CpioArchiveInputStream;

/**
 * .
 */
@Slf4j
public class UpdateMultiExtenderUpdater {
  private static final String THREAD_NAME = "caesar_multi_extender_update_thread_";
  private static final String THREAD_UPDATE_TASK = "caesar_extender_update_task_thread_";
  private static final String EXT_VP6_FILE_NAME = "EXT_VP6.puw";
  private static final int COMMON_EXT_UPDATE_PACK_SIZE = 512;
  private static final int VP6_EXT_UPDATE_PACK_SIZE = 100;

  private TreeTableView<UpdateData> tableView;
  private ExtenderUpdatePageController extenderPanel;
  private final CaesarDeviceController deviceController;
  private final UpdateLogger logger;
  private List<TreeItem<UpdateData>> availableGroup = new ArrayList<>();
  private final List<UpdateTask> updateTasks = new ArrayList<>();
  private boolean updateCancelled = false;
  private boolean allOk = true;
  private Map<String, Set<Integer>> availableExtIdMap = new HashMap<>();
  private final Set<CaesarSwitchDataModel> dataModels = new HashSet<>();

  private final ThreadPoolExecutor executor =
      new ThreadPoolExecutor(
          1,
          1,
          0L,
          TimeUnit.MILLISECONDS,
          new LinkedBlockingQueue<>(100),
          new ThreadFactoryBuilder().setDaemon(true).setNameFormat(THREAD_NAME + "%d").build());
  private final ThreadPoolExecutor updateTasksExecutor =
      new ThreadPoolExecutor(
          1000,
          1000,
          0L,
          TimeUnit.MILLISECONDS,
          new LinkedBlockingQueue<>(),
          new ThreadFactoryBuilder()
              .setDaemon(true)
              .setNameFormat(THREAD_UPDATE_TASK + "%d")
              .build());

  /** Constructor. */
  public UpdateMultiExtenderUpdater(
      CaesarDeviceController deviceController,
      UpdateLogger logger,
      ExtenderUpdatePageController extenderPanel) {
    this.logger = logger;
    this.extenderPanel = extenderPanel;
    this.deviceController = deviceController;
    this.tableView = extenderPanel.getTreeView();
  }

  public void setAvailableGroup(List<TreeItem<UpdateData>> group) {
    this.availableGroup = group;
  }

  public void setAvailableExtIdMap(Map<String, Set<Integer>> availableExtIdMap) {
    this.availableExtIdMap = availableExtIdMap;
  }

  /** update. */
  public void update() {
    dataModels.clear();
    updateCancelled = false;
    List<Runnable> runnableList = new ArrayList<>();
    List<TreeItem<UpdateData>> allUpdateExtGroup = new ArrayList<>();
    Map<String, List<TreeItem<UpdateData>>> availableSelectedGroupMap =
        new HashMap<>(availableExtIdMap.size());
    for (Map.Entry<String, Set<Integer>> entry : availableExtIdMap.entrySet()) {
      List<TreeItem<UpdateData>> updateExtGroup = new ArrayList<>();
      for (TreeItem<UpdateData> dkmItem : tableView.getRoot().getChildren()) {
        for (TreeItem<UpdateData> slotItem : dkmItem.getChildren()) {
          if (slotItem.getChildren().stream()
              .anyMatch(
                  item ->
                      item.getValue().getSelected()
                          && entry.getValue().contains(slotItem.getValue().getId()))) {
            allUpdateExtGroup.add(slotItem);
            updateExtGroup.add(slotItem);
          }
        }
      }
      if (updateExtGroup.isEmpty()) {
        log.info("{} has no ext to update.", entry.getKey());
        continue;
      }
      availableSelectedGroupMap.put(entry.getKey(), updateExtGroup);
    }
    AtomicInteger count = new AtomicInteger();
    int updatePackagesSize = availableSelectedGroupMap.size();
    logger.addLog("Total selected update packages size:" + updatePackagesSize);
    for (Map.Entry<String, List<TreeItem<UpdateData>>> entry :
        availableSelectedGroupMap.entrySet()) {
      runnableList.add(
          () ->
              updateImpl(
                  entry.getKey(), entry.getValue(), count.incrementAndGet() == updatePackagesSize));
    }
    updateCancelled = false;
    if (!runnableList.isEmpty()) {
      runnableList.add(() -> doAfterUpdate(allUpdateExtGroup));
      allOk = true;
      for (Runnable runnable : runnableList) {
        executor.execute(runnable);
      }
    }
  }

  /** . */
  public void updateImpl(
      String filePath, List<TreeItem<UpdateData>> updateExtGroup, boolean isLastUpdate) {
    if (updateCancelled) {
      return;
    }
    if (isLastUpdate) {
      logger.addLog("start last update file: " + filePath);
    } else {
      logger.addLog("start update file: " + filePath);
    }
    CaesarSwitchDataModel model = deviceController.getDataModel();
    if (Strings.isNullOrEmpty(filePath)) {
      logger.addLog("update file: " + filePath + " is empty!");
      return;
    }
    ArrayList<ExtenderData> extenderDatas = new ArrayList<>();
    for (TreeItem<UpdateData> extItem : updateExtGroup) {
      ExtenderData extenderData =
          model.getConfigDataManager().getExtenderDataById(extItem.getValue().getId());
      if (null != extenderData) {
        extenderDatas.add(extenderData);
      }
    }

    updateTasks.clear();
    Collection<ExtenderUpdateInfo> extenderUpdateInfos =
        Utilities.getExtenderUpdateInfos(model, extenderDatas.toArray(new ExtenderData[0]));
    for (ExtenderUpdateInfo extenderUpdateInfo : extenderUpdateInfos) {
      if (null == extenderUpdateInfo) {
        continue;
      }
      String matAddress = extenderUpdateInfo.getAddress();
      UpdateExtenderTask updateExtenderTask =
          new UpdateExtenderTask(filePath, extenderUpdateInfo, updateExtGroup, isLastUpdate);
      updateTasks.add(updateExtenderTask);

      Platform.runLater(
          () -> {
            for (TreeItem<UpdateData> slotItem : updateExtGroup) {
              for (TreeItem<UpdateData> typeItem : slotItem.getChildren()) {
                if (typeItem.getValue().getSelected()
                    && typeItem.getValue().getAddress().equals(matAddress)) {
                  log.info(
                      matAddress
                          + " "
                          + slotItem.getValue().getName()
                          + " "
                          + typeItem.getValue().getType()
                          + " bind");
                  typeItem
                      .getValue()
                      .getProgressBar()
                      .progressProperty()
                      .bind(updateExtenderTask.progressProperty());
                }
              }
            }
          });
      updateTasksExecutor.execute(updateExtenderTask);
    }
    // 等待所有升级任务完成
    while (true) {
      boolean anyMatch = updateTasks.stream().allMatch(UpdateTask::isFinished);
      if (anyMatch) {
        break;
      }
      try {
        Thread.sleep(1000);
      } catch (InterruptedException exp) {
        logger.addLog("Sleep is interrupted!");
      }
    }
    updateTasks.clear();
  }

  private void doAfterUpdate(List<TreeItem<UpdateData>> allUpdateExtGroup) {
    if (!updateCancelled) {
      // 等待版本更新
      readNewVersion(allUpdateExtGroup);
      PlatformUtility.runInFxThread(
          () -> {
            // 记录结束日志及解绑进度条
            for (TreeItem<UpdateData> slotItem : allUpdateExtGroup) {
              for (TreeItem<UpdateData> typeItem : slotItem.getChildren()) {
                if (typeItem.getValue().getSelected()) {
                  if (checkUpdate(slotItem.getValue(), typeItem.getValue())) {
                    logger.addLog(
                        String.format(
                            "%s %s %s update success.",
                            slotItem.getParent().getValue().getName(),
                            slotItem.getValue().getName(),
                            typeItem.getValue().getType()));
                  } else {
                    logger.addLog(
                        String.format(
                            "%s %s %s update fail.",
                            slotItem.getParent().getValue().getName(),
                            slotItem.getValue().getName(),
                            typeItem.getValue().getType()));
                    allOk = false;
                  }
                  typeItem.getValue().getProgressBar().progressProperty().unbind();
                  typeItem.getValue().getProgressBar().setProgress(0);
                  typeItem.getValue().setUpdateDate("");
                  typeItem.getValue().setUpdateVersion(null);
                }
              }
            }
            UndecoratedAlert alert = new UndecoratedAlert(AlertEx.AlertExType.INFORMATION);
            if (tableView != null && tableView.getScene() != null) {
              alert.initOwner(tableView.getScene().getWindow());
            }
            alert.setTitle(
                Bundle.NbBundle.getMessage(
                    CaesarExtenderUpdatePage.class, "ExtenderUpdate.update"));
            alert.setHeaderText(null);
            if (allOk) {
              alert.setContentText(
                  Bundle.NbBundle.getMessage(
                      CaesarExtenderUpdatePage.class, "ExtenderUpdate.update_all_ok"));
            } else {
              alert.setContentText(
                  Bundle.NbBundle.getMessage(
                      CaesarExtenderUpdatePage.class, "ExtenderUpdate.update_some_fail"));
            }
            alert.showAndWait();
          });
      try {
        deviceController
            .submit(
                () -> {
                  try {
                    deviceController.getDataModel().getSwitchModuleData().requestPorts();
                  } catch (ConfigException | BusyException exception) {
                    log.warn("Fail to update ports!", exception);
                  }
                })
            .get();
      } catch (ExecutionException | InterruptedException exception) {
        log.warn("Fail to request ports.", exception);
      }
      extenderPanel.updateNodes();
    } else {
      extenderPanel.setCancelled(false);
      for (TreeItem<UpdateData> typeItem : availableGroup) {
        typeItem.getValue().setDisabled(false);
      }
      logger.addLog("update cancelled!");
    }
    extenderPanel.setCancelled(false);
    extenderPanel.setUpdating(false);
  }

  private class UpdateExtenderTask extends UpdateTask {
    private final String filePath;
    private final ExtenderUpdateInfo extenderUpdateInfo;
    private final CaesarSwitchDataModel model;
    private final String address;
    private final List<TreeItem<UpdateData>> updateExtGroup;
    private final boolean isLastUpdate;

    public UpdateExtenderTask(
        String filePath,
        ExtenderUpdateInfo extenderUpdateInfo,
        List<TreeItem<UpdateData>> updateExtGroup,
        boolean isLastUpdate) {
      super();
      this.filePath = filePath;
      this.extenderUpdateInfo = extenderUpdateInfo;
      this.model = extenderUpdateInfo.getModel();
      this.address = extenderUpdateInfo.getAddress();
      this.updateExtGroup = updateExtGroup;
      this.isLastUpdate = isLastUpdate;
      cancelled = false;
      finished = false;
    }

    @Override
    protected Object call() {
      try {
        int byteHasRead = 0;
        int threshold = 4;
        boolean isVpExt = false;
        int packageSize = COMMON_EXT_UPDATE_PACK_SIZE;
        // 记录开始日志及绑定进度条
        for (TreeItem<UpdateData> slotItem : updateExtGroup) {
          if (slotItem.getValue().getType().equals(UpdateConstant.EXTENDER_VPCON_TYPE)) {
            isVpExt = true;
          }
          for (TreeItem<UpdateData> typeItem : slotItem.getChildren()) {
            if (typeItem.getValue().getSelected()
                && typeItem.getValue().getAddress().equals(address)) {
              String msg =
                  String.format(
                      "%s %s %s update start.",
                      slotItem.getParent().getValue().getName(),
                      slotItem.getValue().getName(),
                      typeItem.getValue().getType());
              logger.addLog(msg);
            }
          }
        }
        // 开始升级
        log.info("Start to update {}.", address);
        logger.addLog("Parallel Update Open");
        model
            .getController()
            .setExtenderMultiPacketParallelUpdateOpen(extenderUpdateInfo.getUpdateParams());

        extenderPanel.setUpdating(true);
        extenderPanel.setCancelled(false);
        finished = false;
        File file = new File(filePath);
        long dataSize = file.length();
        updateProgress(0, dataSize);
        CpioArchiveInputStream cpioIn = null;
        boolean successful = false;
        try (FileInputStream fis = new FileInputStream(file)) {
          InputStream inputStream = fis;
          if (isVpExt) {
            ByteArrayInputStream bais = null;
            try {
              cpioIn = new CpioArchiveInputStream(fis);
              CpioArchiveEntry cpioEntry;
              while ((cpioEntry = (CpioArchiveEntry) cpioIn.getNextEntry()) != null) {
                if (cpioEntry.getName().equals(EXT_VP6_FILE_NAME)) {
                  ByteArrayOutputStream baos = new ByteArrayOutputStream();
                  int count;
                  byte[] data = new byte[512];
                  while ((count = cpioIn.read(data, 0, 512)) != -1) {
                    baos.write(data, 0, count);
                  }
                  byte[] allData = baos.toByteArray();
                  dataSize = allData.length;
                  bais = new ByteArrayInputStream(allData);
                  baos.close();
                  break;
                }
              }
            } finally {
              if (cpioIn != null) {
                cpioIn.close();
              }
            }
            if (bais == null) {
              logger.addLog("Fail to find EXT_VP6.puw");
              return null;
            } else {
              inputStream = bais;
            }
            packageSize = VP6_EXT_UPDATE_PACK_SIZE;
          }
          byte[] tmp = new byte[packageSize];
          int packageCount = (int) (dataSize / packageSize);
          log.debug("Extender update package count : {}.", packageCount);
          logger.addLog("Parallel Update Write:" + packageCount);
          int readOnce;
          while ((readOnce = inputStream.read(tmp)) != -1) {
            int time = 0;
            if (!cancelled) {
              if (readOnce < packageSize) {
                byte[] lastTmp = new byte[readOnce];
                System.arraycopy(tmp, 0, lastTmp, 0, readOnce);
                while (time < threshold) {
                  try {
                    model
                        .getController()
                        .setExtenderParallelUpdateWrite(byteHasRead, lastTmp, true);
                    break;
                  } catch (Exception ex) {
                    time++;
                    logger.addLog("Send timeout" + ex.getMessage(), ex);
                    if (time == threshold) {
                      throw ex;
                    }
                  }
                }
              } else {
                while (time < threshold) {
                  try {
                    model.getController().setExtenderParallelUpdateWrite(byteHasRead, tmp, true);
                    break;
                  } catch (Exception ex) {
                    time++;
                    logger.addLog("Send timeout: " + ex.getMessage(), ex);
                    if (time == threshold) {
                      throw ex;
                    }
                  }
                }
              }
              byteHasRead += readOnce;
              updateProgress(byteHasRead, dataSize);
            } else {
              break;
            }
          }
          if (!cancelled) {
            successful = true;
          }
        } finally {
          if (isLastUpdate) {
            logger.addLog("Parallel Update finally Close.");
            model.getController().setExtenderMultiPacketParallelUpdateClose();
          } else {
            logger.addLog("Parallel Update Close.");
            model.getController().setExtenderParallelUpdateClose(successful);
          }
        }
        if (!cancelled) {
          // 添加不同的外设对应的主机dataModel,以备获取主机外设信息之用
          dataModels.add(model);
        }
      } catch (DeviceConnectionException ex) {
        logger.addLog(" DeviceConnect fail: " + ex.getMessage(), ex);
        clearTasks();
      } catch (ConfigException ex) {
        logger.addLog("Config fail: " + ex.getMessage(), ex);
        clearTasks();
      } catch (BusyException ex) {
        logger.addLog("Busy: " + ex.getMessage(), ex);
        clearTasks();
      } catch (FileNotFoundException ex) {
        logger.addLog("File not found : " + ex.getMessage(), ex);
        clearTasks();
      } catch (IOException ex) {
        logger.addLog("IO fail: " + ex.getMessage(), ex);
        clearTasks();
      } finally {
        finished = true;
      }
      return null;
    }

    @Override
    protected void updateOpen() {}

    @Override
    protected void updateWrite(int offset, byte[] dataToWrite) {}

    @Override
    protected void updateClose(boolean successful) {}
  }

  protected boolean checkUpdate(UpdateData slotItem, UpdateData typeItem) {
    ExtenderData extenderData = null;
    try {
      extenderData =
          deviceController
              .getDataModel()
              .getConfigDataManager()
              .getExtenderDataById(slotItem.getId());
    } catch (RuntimeException exception) {
      log.warn("", exception);
    }
    if (extenderData == null) {
      log.warn("Fail to find extender({})", slotItem.getId());
      return false;
    }
    boolean result = true;
    if (typeItem.getSelected() && typeItem.getUpdateVersion() != null) {
      VersionDef versionDef = typeItem.getUpdateVersion().copy();
      versionDef.setYear(versionDef.getYear() - 2000);
      switch (typeItem.getType()) {
        case UpdateConstant.UPDATE_APP_TYPE:
          result = extenderData.getVersion().getAppVersion().equals(versionDef);
          break;
        case UpdateConstant.UPDATE_FPGA_TYPE:
          result = extenderData.getVersion().getFpgaVersion().equals(versionDef);
          break;
        case UpdateConstant.UPDATE_SYS_TYPE:
          result = extenderData.getVersion().getSystemVersion().equals(versionDef);
          break;
        default:
          break;
      }
    }
    return result;
  }

  private void readNewVersion(List<TreeItem<UpdateData>> allUpdateExtGroup) {
    logger.addLog("Waiting for reboot!");
    ExtenderData[] validExtenderData =
        allUpdateExtGroup.stream()
            .map(
                item ->
                    deviceController
                        .getDataModel()
                        .getConfigDataManager()
                        .getExtenderDataById(item.getValue().getId()))
            .toArray(ExtenderData[]::new);
    ArrayIterable<ExtenderData> extenderIterable = new ArrayIterable<>(validExtenderData);
    //外设升级等待时间，按外设数量计算，100个外设5分钟，200个外设10分钟
    int times = (int) Math.ceil((double) validExtenderData.length / 100);
    int count = 0;
    int maxCount = 150 * times;
    while (count < maxCount) {
      try {
        deviceController.submit(() -> {
          try {
            for (CaesarSwitchDataModel model : dataModels) {
              model.reloadExtenderVersion(false);
              model.reloadExtenderData(extenderIterable);
            }
          } catch (ConfigException | BusyException exception) {
            log.warn("Fail to reload extender data!", exception);
          }
        }).get();
      } catch (ExecutionException | InterruptedException exception) {
        log.warn("Fail to load extenders!", exception);
      }
      boolean pass = true;
      for (TreeItem<UpdateData> slotItem : allUpdateExtGroup) {
        for (TreeItem<UpdateData> typeItem : slotItem.getChildren()) {
          pass = checkUpdate(slotItem.getValue(), typeItem.getValue());
          if (!pass) {
            break;
          }
        }
        if (!pass) {
          break;
        }
      }
      if (pass) {
        break;
      } else {
        try {
          Thread.sleep(2000);
        } catch (InterruptedException exception) {
          log.warn("Sleep is intterupted!", exception);
        }
        count++;
      }
    }
    if (count >= maxCount) {
      logger.addLog("Fail to update some extenders!");
    }
  }

  /** updateCancel. */
  public void updateCancel() {
    for (UpdateTask updateTask : updateTasks) {
      updateTask.setCancelled(true);
      updateTask.setFinished(true);
    }
    updateCancelled = true;
  }

  /** updateCancel. */
  public void clearTasks() {
    for (UpdateTask updateTask : updateTasks) {
      updateTask.setCancelled(true);
      updateTask.setFinished(true);
    }
  }
}
