<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<fx:root id="main-container" stylesheets="@activateconfig_view.css" type="javafx.scene.layout.VBox"
  xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
  <children>
    <GridPane>
      <columnConstraints>
        <ColumnConstraints hgrow="SOMETIMES" maxWidth="203.0" minWidth="10.0" prefWidth="190.0"/>
        <ColumnConstraints hgrow="SOMETIMES" maxWidth="210.0" minWidth="10.0" prefWidth="99.0"/>
        <ColumnConstraints hgrow="SOMETIMES" maxWidth="390.0" minWidth="10.0" prefWidth="364.0"/>
      </columnConstraints>
      <rowConstraints>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      </rowConstraints>
      <children>
        <Label fx:id="configInfo" text="Label"/>
        <Label fx:id="nameLabel" text="Label" GridPane.columnIndex="1"/>
        <Label fx:id="infoLabel" text="Label" GridPane.columnIndex="1" GridPane.rowIndex="1"/>
        <Label fx:id="nameText" text="Label" GridPane.columnIndex="2"/>
        <Label fx:id="infoText" text="Label" GridPane.columnIndex="2" GridPane.rowIndex="1"/>
      </children>
    </GridPane>
    <TableView fx:id="tableView" VBox.vgrow="ALWAYS">
      <columns>
        <TableColumn fx:id="indexCol" prefWidth="30.0" text="C1"/>
        <TableColumn fx:id="filenameCol" prefWidth="161.0" text="C2"/>
        <TableColumn fx:id="nameCol" prefWidth="125.0" text="C3"/>
        <TableColumn fx:id="infoCol" prefWidth="116.0" text="C4"/>
        <TableColumn fx:id="addressCol" prefWidth="105.0" text="C5"/>
        <TableColumn fx:id="versionCol" prefWidth="114.0" text="C6"/>
      </columns>
    </TableView>
    <HBox id="tool-box">
      <children>
        <Button fx:id="activateButton" mnemonicParsing="false" styleClass="common-button"
          text="Button"/>
      </children>
    </HBox>
  </children>
</fx:root>
