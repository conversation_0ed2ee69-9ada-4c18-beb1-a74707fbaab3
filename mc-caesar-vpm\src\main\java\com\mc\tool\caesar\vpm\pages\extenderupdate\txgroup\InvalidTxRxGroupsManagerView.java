package com.mc.tool.caesar.vpm.pages.extenderupdate.txgroup;

import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.utility.UndecoratedDialog;
import java.lang.ref.WeakReference;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.Node;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.control.SelectionMode;
import javafx.scene.input.MouseEvent;
import javafx.stage.Window;
import org.controlsfx.control.CheckListView;

/**
 * .
 */
public class InvalidTxRxGroupsManagerView extends UndecoratedDialog<ButtonType> {

  private final WeakReference<CaesarDeviceController> controllerRef;
  private final CheckListView<TxRxGroupData> checkListView = new CheckListView<>();
  private final ObservableList<TxRxGroupData> txRxGroups = FXCollections.observableArrayList();
  private boolean checkAll = false;

  /** Construct. */
  public InvalidTxRxGroupsManagerView(Window owner, CaesarDeviceController deviceController) {
    this.controllerRef = new WeakReference<>(deviceController);
    initOwner(owner);
    setTitle(CaesarI18nCommonResource.getString("txGroupsManager.title"));
    checkListView.getSelectionModel().setSelectionMode(SelectionMode.MULTIPLE);
    checkListView.setItems(txRxGroups);
    getDialogPane().setContent(checkListView);
    updateData();
    getDialogPane().getButtonTypes().addAll(ButtonType.CLOSE, ButtonType.FINISH, ButtonType.APPLY);
    Node closeNode = getDialogPane().lookupButton(ButtonType.CLOSE);
    if (closeNode instanceof Button) {
      Button button = (Button) closeNode;
      button.addEventFilter(
          MouseEvent.MOUSE_PRESSED,
          event -> {
            destroy();
            event.consume();
          });
    }
    Node applyNode = getDialogPane().lookupButton(ButtonType.APPLY);
    if (applyNode instanceof Button) {
      Button button = (Button) applyNode;
      button.setText(CaesarI18nCommonResource.getString("offline_manager.delete_btn"));
      button.addEventFilter(
          MouseEvent.MOUSE_PRESSED,
          event -> {
            if (!checkListView.getCheckModel().getCheckedItems().isEmpty()) {
              deleteTxRxGroup();
              updateData();
            }
            event.consume();
          });
    }
    Node finishNode = getDialogPane().lookupButton(ButtonType.FINISH);
    if (finishNode instanceof Button) {
      Button button = (Button) finishNode;
      button.setText(CaesarI18nCommonResource.getString("txGroupsManager.selectAll"));
      button.addEventFilter(
          MouseEvent.MOUSE_PRESSED,
          event -> {
            if (checkAll) {
              checkListView.getCheckModel().clearChecks();
              checkAll = false;
            } else {
              checkListView.getCheckModel().checkAll();
              checkAll = true;
            }
            event.consume();
          });
    }
  }

  private void updateData() {
    CaesarDeviceController controller = controllerRef.get();
    if (controller != null) {
      Collection<CpuData> allCpus = controller.getDataModel().getConfigDataManager().getCpus();

      Set<Integer> existingGroups = allCpus.stream()
          .filter(CpuData::isOnline)
          .map(CpuData::getTxGroupIndex)
          .filter(txGroupIndex -> txGroupIndex > 0)
          .map(item -> item - 1).collect(Collectors.toSet());
      Collection<ConsoleData> consolesWithoutVpcon =
          controller.getDataModel().getConfigDataManager().getConsolesWithoutVpcon();
      consolesWithoutVpcon.stream()
          .filter(ConsoleData::isOnline)
          .map(ConsoleData::getGroupIndex)
          .filter(txRxGroupIndex -> txRxGroupIndex > 0)
          .map(item -> item - 1).forEach(existingGroups::add);
      Collection<TxRxGroupData> activeTxGroups =
          controller.getDataModel().getConfigDataManager().getActiveTxRxGroups();
      List<TxRxGroupData> txRxGroupDataList =
          activeTxGroups.stream()
              .filter(item -> !existingGroups.contains(item.getOid()))
              .sorted()
              .collect(Collectors.toList());
      txRxGroups.setAll(txRxGroupDataList);
    }
  }

  private void deleteTxRxGroup() {
    CaesarDeviceController controller = controllerRef.get();
    if (controller != null) {
      controller.execute(() -> controller.deleteTxRxGroupsDirectly(
          checkListView.getCheckModel().getCheckedItems()));
    }
  }

  public void destroy() {
    txRxGroups.clear();
  }
}
