package com.mc.tool.caesar.vpm.pages.systemedit.topological.data;

/**
 * .
 */
public class GridMatrixLink {

  public GridMatrixLink(int localPort, int remotePort) {
    this.localPort = localPort;
    this.remotePort = remotePort;
  }

  /** 级联线连接本主机的端口. 端口是在整个级联系统中的端口，从1开始. */
  public int localPort;
  /** 级联线连接远端主机的端口，端口是在整个级联系统中的端口，从1开始. */
  public int remotePort;

  @Override
  public int hashCode() {
    return localPort ^ remotePort;
  }

  @Override
  public boolean equals(Object obj) {
    if (obj instanceof GridMatrixLink) {
      GridMatrixLink input = (GridMatrixLink) obj;
      return input.localPort == localPort && input.remotePort == remotePort;
    } else {
      return false;
    }
  }
}
