<?xml version="1.0" encoding="UTF-8"?>

<?import com.dooapp.fxform.view.control.ConstraintLabel?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<VBox stylesheets="@networkForm.css" xmlns="http://javafx.com/javafx/8"
      xmlns:fx="http://javafx.com/fxml/1">
  <GridPane prefWidth="700.0">
    <columnConstraints>
      <ColumnConstraints hgrow="SOMETIMES" maxWidth="387.0" minWidth="10.0"
                         prefWidth="177.0"/>
      <ColumnConstraints hgrow="SOMETIMES" maxWidth="387.0" minWidth="60.0"
                         prefWidth="146.0"/>
      <ColumnConstraints hgrow="SOMETIMES" maxWidth="761.0" minWidth="294.0"
                         prefWidth="377.0"/>
    </columnConstraints>
    <rowConstraints>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
    </rowConstraints>
    <Label text="%network.configuration"/>

    <Label style="-fx-font-weight: bold" text="%network.main" GridPane.columnIndex="1"
           GridPane.rowIndex="0"/>
    <Label id="ipAddress1-form-label" text="Label" GridPane.columnIndex="1"
           GridPane.rowIndex="1"/>
    <Label id="netMask1-form-label" text="Label" GridPane.columnIndex="1"
           GridPane.rowIndex="2"/>
    <Label id="gateway1-form-label" text="Label" GridPane.columnIndex="1"
           GridPane.rowIndex="3"/>
    <Label id="macAddress1-form-label" text="Label" GridPane.columnIndex="1"
           GridPane.rowIndex="4"/>

    <Label fx:id="backupLabel" style="-fx-font-weight: bold" text="%network.backup" GridPane.columnIndex="1"
           GridPane.rowIndex="5"/>
    <Label fx:id="ipAddress2Label" id="ipAddress2-form-label" text="Label" GridPane.columnIndex="1"
           GridPane.rowIndex="6"/>
    <Label fx:id="netMask2Label" id="netMask2-form-label" text="Label" GridPane.columnIndex="1"
           GridPane.rowIndex="7"/>
    <Label fx:id="gateway2Label" id="gateway2-form-label" text="Label" GridPane.columnIndex="1"
           GridPane.rowIndex="8"/>
    <Label fx:id="dualMasterVirtualIpLabel" id="dualMasterVirtualIp-form-label" text="Label" GridPane.columnIndex="1"
           GridPane.rowIndex="10"/>
    <Label fx:id="dualMasterVirtualRouteIdLabel" id="dualMasterVirtualRouteId-form-label" text="Label" GridPane.columnIndex="1"
           GridPane.rowIndex="11"/>

    <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="2"
          GridPane.rowIndex="1">
      <TextField id="ipAddress1-form-editor" maxWidth="-Infinity" prefWidth="130.0"/>
      <Label id="ipAddress1-form-tooltip">
        <HBox.margin>
          <Insets left="10.0"/>
        </HBox.margin>
      </Label>
      <ConstraintLabel id="ipAddress1-form-constraint"/>
    </HBox>
    <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="2"
          GridPane.rowIndex="2">
      <TextField id="netMask1-form-editor" maxWidth="-Infinity" prefWidth="130.0"/>
      <ConstraintLabel id="netMask1-form-constraint"/>
    </HBox>
    <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="2"
          GridPane.rowIndex="3">
      <TextField id="gateway1-form-editor" maxWidth="-Infinity" prefWidth="130.0"/>
      <ConstraintLabel id="gateway1-form-constraint"/>
    </HBox>
    <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="2"
          GridPane.rowIndex="4">
      <TextField id="macAddress1-form-editor" maxWidth="-Infinity" prefWidth="130.0"/>
    </HBox>

    <HBox fx:id="ipAddress2Box" id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="2"
          GridPane.rowIndex="6">
      <TextField id="ipAddress2-form-editor" maxWidth="-Infinity" prefWidth="130.0"/>
      <Label id="ipAddress2-form-tooltip" text="Label">
        <HBox.margin>
          <Insets left="10.0"/>
        </HBox.margin>
      </Label>
      <ConstraintLabel id="ipAddress2-form-constraint"/>
    </HBox>
    <HBox fx:id="netMask2Box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="2"
          GridPane.rowIndex="7">
      <TextField id="netMask2-form-editor" maxWidth="-Infinity" prefWidth="130.0"/>
      <ConstraintLabel id="netMask2-form-constraint"/>
    </HBox>
    <HBox fx:id="gateway2Box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="2"
          GridPane.rowIndex="8">
      <TextField id="gateway2-form-editor" maxWidth="-Infinity" prefWidth="130.0"/>
      <ConstraintLabel id="gateway2-form-constraint"/>
    </HBox>

    <Button fx:id="rebootBtn" alignment="CENTER_RIGHT" styleClass="common-button"
            text="%network.reboot" GridPane.columnIndex="1" GridPane.rowIndex="9"/>
    <Label fx:id="rebootLabel" alignment="BOTTOM_LEFT" text="%network.reboot.info" GridPane.columnIndex="2"
           GridPane.rowIndex="9"/>

    <HBox fx:id="virtualIpBox" id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="2"
          GridPane.rowIndex="10">
      <children>
        <TextField id="dualMasterVirtualIp-form-editor" maxWidth="-Infinity" prefWidth="130.0"/>
        <Label id="dualMasterVirtualIp-form-tooltip" text="Label">
          <HBox.margin>
            <Insets left="10.0"/>
          </HBox.margin>
        </Label>
        <ConstraintLabel id="dualMasterVirtualIp-form-constraint"/>
      </children>
    </HBox>
    <HBox fx:id="routeIdBox" id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="2"
          GridPane.rowIndex="11">
      <children>
        <TextField id="dualMasterVirtualRouteId-form-editor" maxWidth="-Infinity" prefWidth="130.0"/>
        <ConstraintLabel id="dualMasterVirtualRouteId-form-constraint"/>
      </children>
    </HBox>
  </GridPane>
</VBox>
