package com.mc.tool.caesar.vpm.util.control;

import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import java.util.function.Predicate;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.ReadOnlyObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.control.TextField;
import javafx.scene.layout.HBox;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class SearchField<T extends DataObject> extends HBox implements Initializable {
  @FXML private TextField searchText;

  private ObjectProperty<Predicate<T>> filterPredicateProperty = new SimpleObjectProperty<>();

  /** Constructor. */
  public SearchField() {
    URL location =
        Thread.currentThread()
            .getContextClassLoader()
            .getResource("com/mc/tool/caesar/vpm/util/search_field.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    loader.setRoot(this);
    loader.setController(this);
    loader.setResources(CaesarI18nCommonResource.getResourceBundle());
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load search_field.fxml", exception);
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    searchText.textProperty().addListener((change) -> updatePredicate());
  }

  public Predicate<T> getFilterPredicate() {
    return filterPredicateProperty.get();
  }

  public ReadOnlyObjectProperty<Predicate<T>> filterPredicateProperty() {
    return filterPredicateProperty;
  }

  public String getSearchText() {
    return searchText.getText();
  }

  public void setSearchText(String text) {
    searchText.setText(text);
  }

  protected void updatePredicate() {
    filterPredicateProperty.set(
        (item) -> {
          if (item == null) {
            return false;
          }
          String text = searchText.getText();
          String idString = item.getId() + "";
          return idString.contains(text) || item.getName().contains(text);
        });
  }
}
