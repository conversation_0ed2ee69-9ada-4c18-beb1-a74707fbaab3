package com.mc.tool.caesar.api.utils;

/**
 * BitFieldEntry.
 */
public final class BitFieldEntry {

  private final String propertyName;
  private int bitField;

  /**
   * .
   *
   * @param propertyName propertyname
   * @param bits         bits
   */
  public BitFieldEntry(String propertyName, int... bits) {
    this.propertyName = propertyName;
    this.bitField = Utilities.getBitComposite(bits);
  }

  public int getBitField() {
    return this.bitField;
  }

  public String getPropertyName() {
    return this.propertyName;
  }

  @Override
  public boolean equals(Object obj) {
    return obj != null && getClass().equals(obj.getClass())
        && this.propertyName.equals(((BitFieldEntry) obj).propertyName);
  }

  @Override
  public int hashCode() {
    int hash = 3;
    hash = 89 * hash + (this.propertyName != null ? this.propertyName.hashCode() : 0);
    return hash;
  }
}

