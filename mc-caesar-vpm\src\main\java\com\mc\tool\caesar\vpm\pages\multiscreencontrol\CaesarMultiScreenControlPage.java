package com.mc.tool.caesar.vpm.pages.multiscreencontrol;

import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.framework.interfaces.Page;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.scene.layout.Pane;

/**
 * .
 */
public class CaesarMultiScreenControlPage implements Page {

  private final MultiScreenControlPageView view;
  private final BooleanProperty visibility = new SimpleBooleanProperty(true);
  public static final String NAME = "screencontrol";

  public CaesarMultiScreenControlPage(CaesarEntity entity) {
    view = new MultiScreenControlPageView();
    view.getController().setDeviceController(entity.getController());
  }

  @Override
  public String getTitle() {
    return NAME;
  }

  @Override
  public String getName() {
    return NAME;
  }

  @Override
  public Pane getView() {
    return view;
  }

  @Override
  public String getStyleClass() {
    return null;
  }

  @Override
  public BooleanProperty getVisibleProperty() {
    return visibility;
  }

  @Override
  public void showObject(Object object) {}
}
