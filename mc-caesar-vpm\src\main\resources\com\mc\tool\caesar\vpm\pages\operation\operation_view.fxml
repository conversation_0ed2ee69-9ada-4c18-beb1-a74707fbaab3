<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ListView?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import org.controlsfx.control.MasterDetailPane?>
<?import org.controlsfx.control.PropertySheet?>
<fx:root id="root" prefHeight="720" prefWidth="1280" stylesheets="@operation_view.css"
  type="javafx.scene.layout.VBox" xmlns="http://javafx.com/javafx/8"
  xmlns:fx="http://javafx.com/fxml/1">
  <HBox id="header-pane" alignment="CENTER" minHeight="40" prefHeight="40">
    <ComboBox fx:id="functionCombobox" prefWidth="150.0" styleClass="image-button"/>
    <Region HBox.Hgrow="ALWAYS"/>
    <HBox id="header-toolbox" alignment="CENTER_RIGHT">
      <Button id="menu-btn" fx:id="saveScenarioBtn" onAction="#onSaveScenario"
        styleClass="image-button" text="%toolbar.save_scenario"/>
      <Button id="config-btn" fx:id="configBtn" onAction="#onConfig" styleClass="image-button"
        text="%toolbar.config"/>
      <Button id="switch-btn" onAction="#onSwitch" styleClass="image-button"
        text="%toolbar.meeting_room"/>
      <Button id="screen-btn" mnemonicParsing="false" onAction="#onSceenMode"
        styleClass="image-button" text="%toolbar.videowall"/>
    </HBox>
  </HBox>
  <Region id="seperator" minHeight="1"/>
  <MasterDetailPane fx:id="masterDetailPane" VBox.vgrow="ALWAYS">
    <masterNode>
      <HBox id="body-pane" VBox.Vgrow="ALWAYS">
        <VBox id="function-source" fx:id="txVbox" minWidth="200" prefWidth="200">
          <HBox id="source-list-title-container" alignment="CENTER_LEFT" prefHeight="23.0"
            prefWidth="198.0">
            <Label id="source-list-title" text="%source_list_title"/>
          </HBox>
          <ListView fx:id="sourceList" VBox.vgrow="ALWAYS"/>
          <VBox fx:id="videoPreviewContainer" minHeight="139"/>
          <Region minHeight="1"/>
        </VBox>
        <VBox fx:id="rxVbox" minWidth="200.0" prefWidth="200.0">
          <children>
            <HBox id="source-list-title-container" alignment="CENTER_LEFT" prefHeight="23.0"
              prefWidth="198.0">
              <children>
                <Label id="source-list-title" text="显示单元"/>
              </children>
            </HBox>
            <TableView fx:id="tableView" prefHeight="200.0" prefWidth="200.0" VBox.vgrow="ALWAYS">
              <columns>
                <TableColumn fx:id="cellIndex" prefWidth="43.0" text="序号"/>
                <TableColumn fx:id="cellName" minWidth="1.0" prefWidth="73.0" text="名称"/>
                <TableColumn fx:id="cellDpi" minWidth="9.0" prefWidth="83.0" text="分辨率"/>
              </columns>
            </TableView>
            <Button mnemonicParsing="false" onAction="#autoArrange" text="自动单元排序"/>
          </children>
        </VBox>
        <VBox id="function-content" HBox.hgrow="ALWAYS">
          <HBox VBox.Vgrow="ALWAYS">
            <StackPane fx:id="functionView" HBox.Hgrow="ALWAYS"/>
          </HBox>
        </VBox>
        <VBox id="show-btn-container" fx:id="showBtnContainer" maxWidth="26" prefWidth="26">
          <VBox id="show-btn-sub-container">
            <HBox alignment="CENTER_RIGHT" minHeight="12" prefHeight="12">
              <Button id="show-property-btn" onAction="#onShowProperty" styleClass="image-button"/>
            </HBox>
            <Region minHeight="1" styleClass="seperator"/>
            <Button id="property-btn" styleClass="image-button"/>
            <Region minHeight="1" styleClass="seperator"/>
            <Button id="icon-btn" styleClass="image-button"/>
            <Region minHeight="1" styleClass="seperator"/>
          </VBox>
          <Region VBox.vgrow="ALWAYS"/>
        </VBox>
      </HBox>
    </masterNode>
    <detailNode>
      <VBox id="right-panel" minWidth="240" prefWidth="240" styleClass="right-panel">
        <HBox id="hide-btn-container" minHeight="12" prefHeight="12" styleClass="right-title">
          <Button id="hide-property-btn" onAction="#onHideProperty" styleClass="image-button"/>
        </HBox>
        <HBox fx:id="videoWindowContainer" minHeight="20" prefHeight="20" styleClass="right-title">
          <children>
            <Label text="创建视频窗口"/>
          </children>
        </HBox>
        <PropertySheet fx:id="videoWindowPropertySheet"/>
        <Region minHeight="1" styleClass="seperator"/>
        <HBox fx:id="propertyContainer" minHeight="20" prefHeight="20" styleClass="right-title">
          <Label text="属性"/>
        </HBox>
        <Region minHeight="1" styleClass="seperator"/>
        <PropertySheet fx:id="propertySheet"/>
        <HBox fx:id="videoLayoutContainer" minHeight="20" prefHeight="20" styleClass="right-title">
          <children>
            <Label text="视频位置"/>
          </children>
        </HBox>
        <PropertySheet fx:id="videoLayoutPropertySheet"/>
        <Region minHeight="1" styleClass="seperator"/>
        <HBox fx:id="layoutLabelContainer" minHeight="20" prefHeight="20" styleClass="right-title">
          <Label text=""/>
          <Label text="布局属性"/>
        </HBox>
        <Region minHeight="1" styleClass="seperator"/>
        <GridPane id="icon-grid-pane" fx:id="iconGridPane"/>
        <PropertySheet fx:id="layoutPropertySheet"/>
      </VBox>
    </detailNode>
  </MasterDetailPane>


</fx:root>
