package com.mc.tool.caesar.vpm.pages.splicingscreen;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.kaito.model.CreateVideoWallGroupInfo;
import com.mc.tool.caesar.vpm.kaito.model.DecodeCard;
import com.mc.tool.caesar.vpm.kaito.model.DecodeCardUpdateInfo;
import com.mc.tool.caesar.vpm.kaito.model.DecodeGroup;
import com.mc.tool.caesar.vpm.kaito.model.DecodeGroupUpdateInfo;
import com.mc.tool.caesar.vpm.kaito.model.InputCard;
import com.mc.tool.caesar.vpm.kaito.model.LayerSource;
import com.mc.tool.caesar.vpm.kaito.model.VideoWall;
import com.mc.tool.caesar.vpm.kaito.model.VideoWallGroup;
import com.mc.tool.caesar.vpm.kaito.model.VideoWallLayer;
import com.mc.tool.caesar.vpm.kaito.model.VideoWallScreen;
import com.mc.tool.caesar.vpm.util.splicingscreen.Utils;
import com.mc.tool.caesar.vpm.view.IdCell;
import feign.FeignException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;
import javafx.beans.binding.Bindings;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.Group;
import javafx.scene.canvas.Canvas;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.control.Button;
import javafx.scene.control.ChoiceBox;
import javafx.scene.control.ComboBox;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.control.MenuItem;
import javafx.scene.control.ScrollPane;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextField;
import javafx.scene.control.TreeItem;
import javafx.scene.control.TreeView;
import javafx.scene.control.cell.TextFieldTreeCell;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import javafx.scene.text.Font;
import javafx.stage.Modality;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.MasterDetailPane;
import org.controlsfx.control.table.TableRowExpanderColumn;

/**
 * CaesarSplicingScreenController.
 */
@Slf4j
public class CaesarSplicingScreenController implements Initializable {

  public static final double MIN_SCALE = 0.5;
  public static final double MAX_SCALE = 2;
  public static final double SCALE_FACTOR = 1.1;
  @FXML
  private TextField ipTextField;
  @FXML
  private TextField nameTextField;
  @FXML
  private TextField typeTextField;
  @FXML
  private Button decoderGroupConfigBtn;
  @FXML
  private Button deleteGroupBtn;
  @FXML
  private TableView<InputCardListItem> allOpticalList;
  @FXML
  private TableColumn<InputCardListItem, Integer> allOpticalIndexColumn;
  @FXML
  private TableColumn<InputCardListItem, String> caesarCardNameColumn;
  @FXML
  private TableColumn<InputCardListItem, Integer> caesarCardIdColumn;
  @FXML
  private TableColumn<InputCardListItem, Integer> caesarCardPortColumn;
  @FXML
  private TableColumn<InputCardListItem, String> nowaCardSnColumn;
  @FXML
  private TableColumn<InputCardListItem, Integer> nowaCardIdColumn;
  @FXML
  private TableColumn<InputCardListItem, Integer> nowaCardPortColumn;
  @FXML
  private TableView<InputCardListItem> rxList;
  @FXML
  private TableColumn<InputCardListItem, Integer> rxListIndexColumn;
  @FXML
  private TableColumn<InputCardListItem, String> rxListNameColumn;
  @FXML
  private TableColumn<InputCardListItem, Integer> rxListIdColumn;
  @FXML
  private TableColumn<InputCardListItem, Integer> rxListPortColumn;
  @FXML
  private TableColumn<InputCardListItem, Integer> rxListInputIdColumn;
  @FXML
  private TableColumn<InputCardListItem, Integer> rxListSlotIdColumn;
  @FXML
  private TreeView<TreeViewItem> sourceTree;
  @FXML
  private VBox functionContainer;
  @FXML
  private StackPane screenStack;
  @FXML
  private ScrollPane screenScroller;
  @FXML
  private Canvas canvas;
  @FXML
  private Group screenGroup;
  @FXML
  private Label zoominBtn;
  @FXML
  private Label zoomoutBtn;
  @FXML
  private VBox showBtnContainer;
  @FXML
  private StackPane functionViewContainer;
  @FXML
  private HBox mainContainer;
  @FXML
  private ComboBox<SplicingScreenVideoWall> videoWallGroupComboBox;
  @FXML
  private MasterDetailPane masterDetailPane;
  @Setter
  private CaesarDeviceController deviceController;
  private final ObjectProperty<SplicingScreenVideoWall> selectedVwGroup =
      new SimpleObjectProperty<>();

  @Override
  public void initialize(URL url, ResourceBundle resourceBundle) {
    sourceTree.setShowRoot(false);
    sourceTree.setCellFactory(item -> new TreeCellWithMenu());

    allOpticalIndexColumn.setCellFactory(new IdCell<>());
    caesarCardNameColumn.setCellValueFactory(
        param -> new SimpleStringProperty(param.getValue().getName()));
    caesarCardIdColumn.setCellValueFactory(
        param -> new SimpleObjectProperty<>(param.getValue().getRxId()));
    caesarCardPortColumn.setCellValueFactory(
        param -> new SimpleObjectProperty<>(param.getValue().getPort()));
    nowaCardSnColumn.setCellValueFactory(
        param -> new SimpleStringProperty(param.getValue().getSn()));
    nowaCardIdColumn.setCellValueFactory(
        param -> new SimpleObjectProperty<>(param.getValue().getInputId()));
    nowaCardPortColumn.setCellValueFactory(
        param -> new SimpleObjectProperty<>(param.getValue().getSlotId()));
    MenuItem deleteDecoderCardMenu = new MenuItem("删除解码卡");
    deleteDecoderCardMenu.disableProperty()
        .bind(allOpticalList.getSelectionModel().selectedItemProperty().isNull());
    deleteDecoderCardMenu.setOnAction(event -> {
      InputCardListItem item = allOpticalList.getSelectionModel().getSelectedItem();
      deviceController.execute(() -> {
        List<DecodeCard> availableCard =
            deviceController.getKaitoApi().kaitoDecodeCardAvailableVideowallGroupIdGet(
                item.getVideowallGroupId());
        if (availableCard.stream().anyMatch(card -> card.getRxId() == item.getRxId())) {
          try {
            deviceController.getKaitoApi()
                .kaitoDecodeCardVideowallGroupIdDecodeCardIdDelete(item.getVideowallGroupId(),
                    item.getRxId());
          } catch (FeignException e) {
            Utils.handleFeignException(e, allOpticalList);
          }
          refreshAllDecoderCards(item.getVideowallGroupId());
        } else {
          log.warn("{}解码卡已分配，不能删除", item.getRxId());
        }
      });
    });
    ContextMenu menu = new ContextMenu();
    menu.getItems().add(deleteDecoderCardMenu);
    allOpticalList.setContextMenu(menu);

    rxListIndexColumn.setCellFactory(new IdCell<>());
    rxListNameColumn.setCellValueFactory(
        param -> new SimpleStringProperty(param.getValue().getName()));
    rxListIdColumn.setCellValueFactory(
        param -> new SimpleObjectProperty<>(param.getValue().getRxId()));
    rxListPortColumn.setCellValueFactory(
        param -> new SimpleObjectProperty<>(param.getValue().getPort()));
    rxListInputIdColumn.setCellValueFactory(
        param -> new SimpleObjectProperty<>(param.getValue().getInputId()));
    rxListSlotIdColumn.setCellValueFactory(
        param -> new SimpleObjectProperty<>(param.getValue().getSlotId()));
    TableRowExpanderColumn<InputCardListItem> expander =
        new TableRowExpanderColumn<>(this::createEditor);
    expander.setStyle("-fx-alignment: top-left;");
    rxList.getColumns().add(0, expander);
    MenuItem deleteDecoderCard = new MenuItem("删除解码卡");
    deleteDecoderCard.disableProperty()
        .bind(rxList.getSelectionModel().selectedItemProperty().isNull());
    deleteDecoderCard.setOnAction(event -> {
      InputCardListItem item = rxList.getSelectionModel().getSelectedItem();
      deviceController.execute(() -> {
        try {
          deviceController.getKaitoApi()
              .kaitoDecodeCardVideowallGroupIdDecodeCardIdDelete(item.getVideowallGroupId(),
                  item.getRxId());
        } catch (FeignException e) {
          Utils.handleFeignException(e, rxList);
        }
        refreshAllDecoderCards(item.getVideowallGroupId());
      });
    });
    ContextMenu rxMenu = new ContextMenu();
    rxMenu.getItems().add(deleteDecoderCard);
    rxList.setContextMenu(rxMenu);

    showBtnContainer.managedProperty().bind(showBtnContainer.visibleProperty());
    showBtnContainer.visibleProperty().bind(masterDetailPane.showDetailNodeProperty().not());
    zoominBtn.disableProperty().bind(screenGroup.scaleXProperty().greaterThanOrEqualTo(MAX_SCALE));
    zoomoutBtn.disableProperty().bind(screenGroup.scaleXProperty().lessThanOrEqualTo(MIN_SCALE));
    videoWallGroupComboBox.setCellFactory(cell -> new VideoWallGroupListCell());
    videoWallGroupComboBox.setButtonCell(new VideoWallGroupListCell());
    videoWallGroupComboBox.getSelectionModel().selectedItemProperty()
        .addListener((observable, oldValue, newValue) -> {
          if (newValue != null && !newValue.equals(oldValue)) {
            selectedVwGroup.set(newValue);
            refreshProperties();
            refreshDecodeGroup(newValue.getId());
            refreshAllDecoderCards(newValue.getId());
            VideoWall videoWall = deviceController.getKaitoApi()
                .kaitoVideowallGroupIdWallIdGet(newValue.getId(), newValue.getVwId());
            if (videoWall != null) {
              drawVw(videoWall);
            }
          }
        });
    decoderGroupConfigBtn.disableProperty().bind(Bindings.createBooleanBinding(
        () -> sourceTree.getSelectionModel().getSelectedItem() != null
            && sourceTree.getSelectionModel().getSelectedItem().getValue() instanceof DecodeGroup,
        sourceTree.getSelectionModel().selectedItemProperty()).not());
    deleteGroupBtn.disableProperty().bind(Bindings.createBooleanBinding(
        () -> sourceTree.getSelectionModel().getSelectedItem() != null
            && sourceTree.getSelectionModel().getSelectedItem().getValue() instanceof DecodeGroup,
        sourceTree.getSelectionModel().selectedItemProperty()).not());
  }

  private GridPane createEditor(
      TableRowExpanderColumn.TableRowDataFeatures<InputCardListItem> param) {
    GridPane editor = new GridPane();
    editor.setPadding(new Insets(10));
    editor.setHgap(10);
    editor.setVgap(5);
    List<InputCard> inputCards = deviceController.getKaitoApi()
        .kaitoInputCardListVideowallGroupIdGet(param.getValue().getVideowallGroupId());
    ChoiceBox<InputCard> inputCardChoiceBox = new ChoiceBox<>();
    inputCardChoiceBox.setConverter(new InputCardStringConverter());
    inputCardChoiceBox.getItems().setAll(inputCards);
    editor.addRow(0, new Label("Name"), inputCardChoiceBox);
    Button save = new Button("保存");
    InputCardListItem value = param.getValue();
    save.setOnAction(event -> {
      InputCard selectedItem = inputCardChoiceBox.getSelectionModel().getSelectedItem();
      value.setInputId(selectedItem.getInputId());
      value.setSlotId(selectedItem.getSlotId());
      value.setSn(selectedItem.getSn());
      deviceController.execute(() -> save(value));
      param.toggleExpanded();
    });
    Button cancelButton = new Button("取消");
    cancelButton.setOnAction(event -> param.toggleExpanded());
    editor.addRow(1, save, cancelButton);
    return editor;
  }

  private void save(InputCardListItem value) {
    DecodeCardUpdateInfo info = new DecodeCardUpdateInfo();
    info.setInputId(value.getInputId());
    info.setSlotId(value.getSlotId());
    info.setSn(value.getSn());
    try {
      deviceController.getKaitoApi()
          .kaitoDecodeCardVideowallGroupIdDecodeCardIdPost(value.getVideowallGroupId(),
              value.getRxId(), info);
    } catch (FeignException e) {
      Utils.handleFeignException(e, rxList);
    }
  }

  private void drawVw(VideoWall videoWall) {
    double maxWidth = 0;
    double maxHeight = 0;
    if (videoWall.getScreens() != null && !videoWall.getScreens().isEmpty()) {
      for (VideoWallScreen screen : videoWall.getScreens()) {
        if (screen != null) {
          maxWidth = Math.max(maxWidth, screen.getXpos() + screen.getWidth());
          maxHeight = Math.max(maxHeight, screen.getYpos() + screen.getHeight());
        }
      }
    }
    if (maxWidth == 0 || maxHeight == 0) {
      log.warn("SplicingScreen data does not meet requirements");
      return;
    }
    double defaultWidth = functionContainer.getWidth() - 20;
    double defaultHeight = functionContainer.getHeight() - 20;
    if (defaultWidth / defaultHeight <= maxWidth / maxHeight) {
      canvas.setWidth(defaultWidth);
      canvas.setHeight(maxHeight * defaultWidth / maxWidth);
    } else {
      canvas.setHeight(defaultHeight);
      canvas.setWidth(maxWidth * defaultHeight / maxHeight);
    }

    GraphicsContext gc = canvas.getGraphicsContext2D();
    double width = canvas.getWidth();
    double height = canvas.getHeight();
    double widthScale = canvas.getWidth() / maxWidth;
    double heightScale = canvas.getHeight() / maxHeight;
    gc.clearRect(0, 0, width, height);
    gc.setFill(Color.CADETBLUE);
    gc.setStroke(Color.LIGHTGRAY);
    gc.strokeRect(0, 0, width, height);
    gc.setFont(new Font(10));
    gc.beginPath();
    if (videoWall.getScreens() != null && !videoWall.getScreens().isEmpty()) {
      for (VideoWallScreen screen : videoWall.getScreens()) {
        if (screen != null) {
          gc.strokeRect(screen.getXpos() * widthScale, screen.getYpos() * heightScale,
              screen.getWidth() * widthScale, screen.getHeight() * heightScale);
        }
      }
    }
    if (videoWall.getLayers() != null && !videoWall.getLayers().isEmpty()) {
      for (VideoWallLayer layer : videoWall.getLayers()) {
        if (layer != null && layer.getWindow() != null) {
          gc.setFill(Color.LIGHTGRAY);
          gc.fillRect(layer.getWindow().getXpos() * widthScale,
              layer.getWindow().getYpos() * heightScale,
              layer.getWindow().getWidth() * widthScale,
              layer.getWindow().getHeight() * heightScale);
          gc.setStroke(Color.DARKGRAY);
          gc.strokeRect(layer.getWindow().getXpos() * widthScale,
              layer.getWindow().getYpos() * heightScale,
              layer.getWindow().getWidth() * widthScale,
              layer.getWindow().getHeight() * heightScale);
          if (!layer.getSource().isEmpty()) {
            gc.setFill(Color.CADETBLUE);
            LayerSource layerSource = layer.getSource().get(0);
            String text = "NO SIGNAL";
            if (layerSource.getSourceType() == 1 || layerSource.getSourceType() == 2) {
              text = layer.getName();
            } else if (layerSource.getSourceType() == 3) {
              Integer sourceId = layerSource.getSourceId();
              CpuData cpuData =
                  deviceController.getDataModel().getConfigDataManager().getCpuData4Id(sourceId);
              if (cpuData != null) {
                text = "【" + cpuData.getName() + "】";
              }
            }
            gc.fillText(text,
                layer.getWindow().getXpos() * widthScale + 5,
                layer.getWindow().getYpos() * heightScale + 15);
          }
        }
      }
    }
    gc.stroke();
    gc.closePath();
  }

  /**
   * refresh.
   */
  public void refresh() {
    refreshVideowall();
  }

  @FXML
  public void onShowProperty(ActionEvent actionEvent) {
    masterDetailPane.setShowDetailNode(true);
  }

  @FXML
  public void onHideProperty(ActionEvent actionEvent) {
    masterDetailPane.setShowDetailNode(false);
  }

  /**
   * onNewSplicingScreen.
   */
  @FXML
  public void onNewSplicingScreen() {
    SplicingScreenInputDialog splicingScreenInputDialog = new SplicingScreenInputDialog();
    splicingScreenInputDialog.initOwner(mainContainer.getScene().getWindow());
    splicingScreenInputDialog.initModality(Modality.APPLICATION_MODAL);
    splicingScreenInputDialog.showAndWait();
    CreateVideoWallGroupInfo result = splicingScreenInputDialog.getResult();
    if (result != null) {
      deviceController.execute(() -> {
        try {
          deviceController.getKaitoApi().kaitoVideowallGroupPut(result);
        } catch (FeignException e) {
          Utils.handleFeignException(e, mainContainer);
        } catch (Exception ex) {
          log.error("kaitoVideowallGroupPut error", ex);
        }
      });
    }
  }

  /**
   * onDeleteSplicingScreen.
   */
  @FXML
  public void onDeleteSplicingScreen() {
    SplicingScreenDeleteDialog splicingScreenDeleteDialog = new SplicingScreenDeleteDialog();
    splicingScreenDeleteDialog.initOwner(mainContainer.getScene().getWindow());
    splicingScreenDeleteDialog.initModality(Modality.APPLICATION_MODAL);
    deviceController.execute(() -> {
      List<VideoWallGroup> videoWallGroups =
          deviceController.getKaitoApi().kaitoVideowallGroupListGet();
      List<DeleteVideoWallGroupInfo> deleteVideoWallGroupInfos = videoWallGroups.stream()
          .map(item -> new DeleteVideoWallGroupInfo(item.getId(), item.getName())).collect(
              Collectors.toList());
      PlatformUtility.runInFxThread(
          () -> splicingScreenDeleteDialog.setVideoWallGroups(deleteVideoWallGroupInfos));
    });
    splicingScreenDeleteDialog.showAndWait();
    List<DeleteVideoWallGroupInfo> result = splicingScreenDeleteDialog.getResult();
    if (result != null) {
      deviceController.submitAsync(() -> {
        for (DeleteVideoWallGroupInfo info : result) {
          try {
            deviceController.getKaitoApi().kaitoVideowallGroupIdDelete(info.getId());
          } catch (FeignException e) {
            Utils.handleFeignException(e, mainContainer);
          } catch (Exception ex) {
            log.error("kaitoVideowallGroupIdDelete error", ex);
          }
        }
      }).whenComplete((avoid, throwable) -> {
        if (throwable != null) {
          log.warn("kaitoVideowallGroupPut error", throwable);
        }
        refresh();
      });
    }
  }

  @FXML
  public void onZoomin() {
    screenGroup.setScaleX(screenGroup.getScaleX() * SCALE_FACTOR);
    screenGroup.setScaleY(screenGroup.getScaleY() * SCALE_FACTOR);
  }

  @FXML
  public void onZoomout() {
    screenGroup.setScaleX(screenGroup.getScaleX() / SCALE_FACTOR);
    screenGroup.setScaleY(screenGroup.getScaleY() / SCALE_FACTOR);
  }

  @FXML
  public void onRestore() {
    screenGroup.setScaleX(1);
    screenGroup.setScaleY(1);
  }

  private void refreshVideowall() {
    List<VideoWallGroup> videoWallGroups =
        deviceController.getKaitoApi().kaitoVideowallGroupListGet();
    List<SplicingScreenVideoWall> allVideowall = new ArrayList<>();
    for (VideoWallGroup videoWallGroup : videoWallGroups) {
      if (videoWallGroup.getVideowalls() != null && !videoWallGroup.getVideowalls().isEmpty()) {
        List<SplicingScreenVideoWall> screenVideoWalls =
            videoWallGroup.getVideowalls().stream().map(
                item -> new SplicingScreenVideoWall(videoWallGroup.getId(),
                    videoWallGroup.getName(),
                    videoWallGroup.getIp(), videoWallGroup.getPort(), videoWallGroup.getPid(),
                    item.getId(), item.getName())).collect(Collectors.toList());
        allVideowall.addAll(screenVideoWalls);
      }
    }
    PlatformUtility.runInFxThread(() -> {
      videoWallGroupComboBox.getItems().clear();
      videoWallGroupComboBox.getItems().setAll(allVideowall);
      videoWallGroupComboBox.getSelectionModel().clearSelection();
      videoWallGroupComboBox.getSelectionModel().selectFirst();
    });
  }

  private void refreshAllDecoderCards(int videowallGroupId) {
    List<DecodeCard> decodeCards = deviceController.getKaitoApi()
        .kaitoDecodeCardListVideowallGroupIdGet(videowallGroupId);
    List<InputCardListItem> rxDecodeList =
        decodeCards.stream().filter(item -> item.getType() == 0 || item.getType() == 1)
            .map(item -> new InputCardListItem(item, videowallGroupId))
            .collect(Collectors.toList());
    List<InputCardListItem> alOpticalDecodeList =
        decodeCards.stream().filter(item -> item.getType() == 2 || item.getType() == 3)
            .map(item -> new InputCardListItem(item, videowallGroupId))
            .collect(Collectors.toList());
    PlatformUtility.runInFxThread(() -> {
      this.rxList.getItems().setAll(rxDecodeList);
      this.allOpticalList.getItems().setAll(alOpticalDecodeList);
    });
  }

  private void refreshDecodeGroup(int videowallGroupId) {
    List<DecodeGroup> decodeGroups =
        deviceController.getKaitoApi().kaitoDecodeGroupListVideowallGroupIdGet(videowallGroupId);
    TreeItem<TreeViewItem> sourceTreeRoot = new TreeItem<>();
    for (DecodeGroup decodeGroup : decodeGroups) {
      TreeItem<TreeViewItem> decodeGroupTreeItem = new TreeItem<>(decodeGroup);
      sourceTreeRoot.getChildren().add(decodeGroupTreeItem);
      if (decodeGroup.getCards() != null && !decodeGroup.getCards().isEmpty()) {
        for (DecodeCard decodeCard : decodeGroup.getCards()) {
          TreeItem<TreeViewItem> cardTreeItem = new TreeItem<>(decodeCard);
          decodeGroupTreeItem.getChildren().add(cardTreeItem);
        }
      }
    }
    sourceTree.setRoot(sourceTreeRoot);
  }

  private void refreshProperties() {
    if (selectedVwGroup.get() != null) {
      try {
        VideoWallGroup videoWallGroups =
            deviceController.getKaitoApi().kaitoVideowallGroupIdGet(selectedVwGroup.get().getId());
        ipTextField.setText(videoWallGroups.getIp());
        nameTextField.setText(videoWallGroups.getName());
        typeTextField.setText(videoWallGroups.getType());
      } catch (FeignException e) {
        Utils.handleFeignException(e, mainContainer);
      }
    }
  }

  /**
   * 新建解码卡.
   */
  @FXML
  public void onNewDecoder() {
    if (selectedVwGroup.get() != null) {
      DecodeCardListDialog cardListDialog =
          new DecodeCardListDialog(selectedVwGroup.get().getId(), deviceController,
              mainContainer.getScene().getWindow());
      cardListDialog.showAndWait().ifPresent(itemList -> {
        if (itemList.isEmpty()) {
          return;
        }
        itemList.parallelStream().forEach(item -> {
          DecodeCardUpdateInfo info = new DecodeCardUpdateInfo();
          info.setInputId(item.getInputId());
          info.setSlotId(item.getSlotId());
          info.setSn(item.getSn());
          deviceController.getKaitoApi()
              .kaitoDecodeCardVideowallGroupIdDecodeCardIdPost(selectedVwGroup.get().getId(),
                  item.getDecodeCardId(), info);
        });
        refreshAllDecoderCards(selectedVwGroup.get().getId());
      });
    }
  }

  /**
   * 创建解码组.
   */
  @FXML
  public void onCreateDecoderGroup() {
    if (selectedVwGroup.get() != null) {
      EditDecoderGroupDialog groupDialog = new EditDecoderGroupDialog(selectedVwGroup.get()
          .getId(), deviceController);
      groupDialog.showAndWait().ifPresent(item -> {
        DecodeGroupUpdateInfo info = new DecodeGroupUpdateInfo();
        info.setName(item.getName());
        info.setRights(item.getRights());
        info.setCroppable(item.isCroppable());
        info.setCards(item.getRxList());
        try {
          deviceController.getKaitoApi()
              .kaitoDecodeGroupVideowallGroupIdPut(selectedVwGroup.get().getId(), info);
        } catch (FeignException e) {
          Utils.handleFeignException(e, mainContainer);
        }
        refreshDecodeGroup(selectedVwGroup.get().getId());
        refreshAllDecoderCards(selectedVwGroup.get().getId());
      });
    }
  }

  /**
   * 删除解码组.
   */
  @FXML
  public void onDeleteDecoderGroup() {
    TreeViewItem viewItem = sourceTree.getSelectionModel().getSelectedItem().getValue();
    if (viewItem instanceof DecodeGroup) {
      DecodeGroup decodeGroup = (DecodeGroup) viewItem;
      try {
        deviceController.getKaitoApi()
            .kaitoDecodeGroupVideowallGroupIdDecodeGroupIdDelete(selectedVwGroup.get().getId(),
                decodeGroup.getId());
      } catch (FeignException e) {
        Utils.handleFeignException(e, sourceTree);
      }
    }
    refreshDecodeGroup(selectedVwGroup.get().getId());
    refreshAllDecoderCards(selectedVwGroup.get().getId());
  }

  /**
   * 解码组配置.
   */
  @FXML
  public void onDecoderGroupConfig() {
    TreeViewItem viewItem = sourceTree.getSelectionModel().getSelectedItem().getValue();
    if (viewItem instanceof DecodeGroup) {
      DecodeGroup decodeGroup = (DecodeGroup) viewItem;
      AssignPermissionsDialog apDialog =
          new AssignPermissionsDialog(selectedVwGroup.get().getId(), decodeGroup,
              deviceController);
      apDialog.showAndWait().ifPresent(item -> {
        DecodeGroupUpdateInfo info = new DecodeGroupUpdateInfo();
        info.setName(item.getName());
        info.setRights(item.getRights());
        if (item.getCards() != null) {
          info.setCards(
              item.getCards().stream().map(DecodeCard::getRxId).collect(Collectors.toList()));
        }
        deviceController.execute(() -> deviceController.getKaitoApi()
            .kaitoDecodeGroupVideowallGroupIdDecodeGroupIdPost(selectedVwGroup.get().getId(),
                item.getId().toString(), info));
      });
    }

  }

  static class TreeCellWithMenu extends TextFieldTreeCell<TreeViewItem> {

    public TreeCellWithMenu() {
      this.setConverter(new TreeViewItemStringConverter());
    }

    @Override
    public void updateItem(TreeViewItem t, boolean bln) {
      super.updateItem(t, bln);
    }

  }

  static class VideoWallGroupListCell extends ListCell<SplicingScreenVideoWall> {
    @Override
    protected void updateItem(SplicingScreenVideoWall item, boolean empty) {
      super.updateItem(item, empty);
      if (item == null || empty) {
        setText("");
      } else {
        setText(item.getName() + "-" + item.getVwName());
      }
    }
  }

}
