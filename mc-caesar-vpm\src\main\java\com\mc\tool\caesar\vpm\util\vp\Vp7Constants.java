package com.mc.tool.caesar.vpm.util.vp;

import com.mc.tool.caesar.api.datamodel.vp.Vp7ConfigData;

/**
 * Vp7常量.
 */
public class Vp7Constants {
  public static final int MAX_SCREEN_COUNT = 4;
  public static final int MAX_VIDEO_INPUT = 8;
  public static final int HALF_CHANNEL_COUNT = 8; // 通道数的一半
  public static final int MAX_TOTAL_LAYER_COUNT = 16;
  public static final int MAX_LAYER_COUNT_PER_SINGLE_SCREEN = 16;
  public static final int MAX_LAYER_COUNT_PER_DOUBLE_SCREEN = 8;
  public static final int MAX_LAYER_COUNT_PER_QUADRUPLE_SCREEN = 4;
  public static final int MAX_PRIORITY_COUNT = 16;

  public static final byte CLIP_L_PIECE = Vp7ConfigData.CLIP_L_PIECE;
  public static final byte CLIP_R_PIECE = Vp7ConfigData.CLIP_R_PIECE;
  public static final byte CLIP_T_PIECE = Vp7ConfigData.CLIP_T_PIECE;
  public static final byte CLIP_B_PIECE = Vp7ConfigData.CLIP_B_PIECE;
}
