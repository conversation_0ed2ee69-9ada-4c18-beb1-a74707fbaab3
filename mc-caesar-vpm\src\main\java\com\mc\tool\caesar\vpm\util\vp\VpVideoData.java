package com.mc.tool.caesar.vpm.util.vp;

/**
 * .
 */
public interface VpVideoData {
  /**
   * 裁剪类型枚举.
   */
  enum VpCropTpe {
    NONE,
    CONFIG,
    GRID
  }

  String getName();

  int getTxId();

  int getSourceIndex();

  int getConfigCropIndex();

  int getGridCropIndex();

  VpCropTpe getCropType();

  boolean isVisible();

  int getLeft();

  int getTop();

  int getWidth();

  int getHeight();

  int getAlpha();
}
