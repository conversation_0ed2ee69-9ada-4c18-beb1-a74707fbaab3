<?xml version="1.0" encoding="UTF-8"?>

<?import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridWizardIndicator?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<VBox stylesheets="@gridsystem_view.css" xmlns="http://javafx.com/javafx/8"
  xmlns:fx="http://javafx.com/fxml/1"
  prefWidth="700" prefHeight="464" id="main-container">
  <children>
    <MatrixGridWizardIndicator currentIndex="3"/>
    <VBox id="sub-container">
      <Region prefHeight="45"/>
      <Label text="%gridsystem.info1"/>
      <Label text="%gridsystem.info2"/>
      <Label text="%gridsystem.info3"/>
      <Label text="%gridsystem.info4"/>
      <Region prefHeight="10"/>
      <TableView prefHeight="149" fx:id="tableView">
        <columns>
          <TableColumn fx:id="categoryCol" text="%gridsystem.table.category"/>
          <TableColumn fx:id="nameCol" text="%gridsystem.table.name"/>
          <TableColumn fx:id="ipCol" text="%gridsystem.table.ip"/>
          <TableColumn fx:id="portCol" text="%gridsystem.table.port"/>
          <TableColumn fx:id="validateCol" text="%gridsystem.table.validate"/>
          <TableColumn fx:id="validCol" text="%gridsystem.table.valid"/>
        </columns>
      </TableView>
    </VBox>

  </children>
</VBox>
