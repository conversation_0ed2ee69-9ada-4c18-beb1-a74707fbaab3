package com.mc.tool.caesar.vpm.util;

import com.mc.tool.caesar.api.CaesarConstants;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.function.Predicate;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * .
 */
@NoArgsConstructor
@AllArgsConstructor
public class CaesarNamePredicate implements Predicate<String> {

  private List<String> existingNames;

  @Override
  public boolean test(String item) {
    int len = item.getBytes(StandardCharsets.UTF_8).length;
    if (len > CaesarConstants.NAME_LEN || len == 0) {
      return false;
    }
    if (existingNames != null && !existingNames.isEmpty()) {
      return !existingNames.contains(item);
    }
    return true;
  }
}
