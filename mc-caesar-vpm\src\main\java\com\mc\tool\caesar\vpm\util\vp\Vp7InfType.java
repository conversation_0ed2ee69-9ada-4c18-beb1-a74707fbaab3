package com.mc.tool.caesar.vpm.util.vp;

/**
 * VP7接口类型.
 */
public enum Vp7InfType {
  TYPE_SINGLE(0, 1),
  TYPE_DOUBLE(1, 2),
  TYPE_QUADRUPLE(2, 4);

  private final int value;
  private final int outputCount;

  // Constructor for enum
  Vp7InfType(int value, int outputCount) {
    this.value = value;
    this.outputCount = outputCount;
  }

  // Getter for the value
  public int getValue() {
    return this.value;
  }

  public int getOutputCount() {
    return this.outputCount;
  }
}
