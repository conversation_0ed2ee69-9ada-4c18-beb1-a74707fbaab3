<?xml version="1.0" encoding="UTF-8"?>

<?import com.dooapp.fxform.view.control.ConstraintLabel?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.control.CheckBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<?import tornadofx.control.DateTimePicker?>
<?import javafx.scene.control.ChoiceBox?>
<VBox stylesheets="@generalForm.css" xmlns="http://javafx.com/javafx/8"
      xmlns:fx="http://javafx.com/fxml/1">
  <GridPane>
    <columnConstraints>
      <ColumnConstraints hgrow="SOMETIMES" maxWidth="173.0" minWidth="10.0" prefWidth="173.0"/>
      <ColumnConstraints hgrow="ALWAYS"/>
    </columnConstraints>
    <rowConstraints>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="35.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
    </rowConstraints>
    <Label id="basic-information" text="%general.basic_info"/>
    <HBox id="tool-box" styleClass="tool-box" GridPane.columnIndex="1">
      <Label id="hostName-form-label" text="Label"/>
      <TextField id="hostName-form-editor" prefWidth="200.0"/>
      <ConstraintLabel id="hostName-form-constraint"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="1">
      <Label id="configurationName-form-label" text="Label"/>
      <TextField id="configurationName-form-editor" prefWidth="200.0"/>
      <ConstraintLabel id="configurationName-form-constraint"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="2">
      <Label id="notes-form-label" text="Label"/>
      <TextField id="notes-form-editor" prefWidth="200.0"/>
    </HBox>

    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="3">
      <Label id="time-form-label" text="获取本地时间"/>
      <DateTimePicker id="time-form-editor"/>
      <Label id="time-form-tooltip" prefHeight="20.0" text="Label"/>
    </HBox>

    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="4">
      <CheckBox id="isLogin-form-editor" mnemonicParsing="false"/>
      <Label id="isLogin-form-label" text="Label"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="5">
      <CheckBox id="isUserLock-form-editor" mnemonicParsing="false"/>
      <Label id="isUserLock-form-label" text="Label"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="6">
      <CheckBox id="isConLock-form-editor" mnemonicParsing="false"/>
      <Label id="isConLock-form-label" text="Label"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="7">
      <CheckBox id="isUserAccess-form-editor" mnemonicParsing="false"/>
      <Label id="isUserAccess-form-label" text="Label"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="8">
      <CheckBox id="isConAccess-form-editor" mnemonicParsing="false"/>
      <Label id="isConAccess-form-label" text="Label"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="9">
      <CheckBox id="isCpuDisconnect-form-editor" mnemonicParsing="false"/>
      <Label id="isCpuDisconnect-form-label" text="Label"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="10">
      <CheckBox disable="true" mnemonicParsing="false" selected="true"/>
      <Label id="timeoutDisplay-form-label" text="Label"/>
      <TextField id="timeoutDisplay-form-editor" maxWidth="-Infinity" prefWidth="50.0">
        <HBox.margin>
          <Insets left="10.0" right="10.0"/>
        </HBox.margin>
      </TextField>
      <Label id="timeoutDisplay-form-tooltip" text="Label"/>
      <ConstraintLabel id="timeoutDisplay-form-constraint"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="11">
      <CheckBox disable="true" mnemonicParsing="false" selected="true"/>
      <Label id="timeoutLogout-form-label" text="Label"/>
      <TextField id="timeoutLogout-form-editor" maxWidth="-Infinity" prefWidth="50.0">
        <HBox.margin>
          <Insets left="10.0" right="10.0"/>
        </HBox.margin>
      </TextField>
      <Label id="timeoutLogout-form-tooltip" text="Label"/>
      <ConstraintLabel id="timeoutLogout-form-constraint"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="12">
      <CheckBox id="isCpuWatch-form-editor" mnemonicParsing="false"/>
      <Label id="isCpuWatch-form-label" text="Label"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="13">
      <CheckBox id="isCpuConnect-form-editor" mnemonicParsing="false"/>
      <Label id="isCpuConnect-form-label" text="Label"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="14">
      <CheckBox id="isConDisconnect-form-editor" mnemonicParsing="false"/>
      <Label id="isConDisconnect-form-label" text="Label"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="15">
      <CheckBox disable="true" mnemonicParsing="false" selected="true"/>
      <Label id="timeoutDisconnect-form-label" text="Label"/>
      <TextField id="timeoutDisconnect-form-editor" prefWidth="50.0">
        <HBox.margin>
          <Insets left="10.0" right="10.0"/>
        </HBox.margin>
      </TextField>
      <Label id="timeoutDisconnect-form-tooltip" text="Label"/>
      <ConstraintLabel id="timeoutDisconnect-form-constraint"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="16">
      <CheckBox id="isKeyboardMouseConnect-form-editor" mnemonicParsing="false"/>
      <Label id="timeoutShare-form-label" text="Label"/>
      <TextField id="timeoutShare-form-editor" prefWidth="50.0">
        <HBox.margin>
          <Insets left="10.0" right="10.0"/>
        </HBox.margin>
      </TextField>
      <Label id="timeoutShare-form-tooltip" text="Label"/>
      <ConstraintLabel id="timeoutShare-form-constraint"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="17">
      <CheckBox id="isForcePushGet-form-editor" mnemonicParsing="false"/>
      <Label id="isForcePushGet-form-label" text="Label"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="18">
      <CheckBox disable="true" mnemonicParsing="false" selected="true"/>
      <Label id="autoRejectTime-form-label" text="Label"/>
      <TextField id="autoRejectTime-form-editor" prefWidth="50.0">
        <HBox.margin>
          <Insets left="10.0" right="10.0"/>
        </HBox.margin>
      </TextField>
      <Label id="autoRejectTime-form-tooltip" text="Label"/>
      <ConstraintLabel id="autoRejectTime-form-constraint"/>
    </HBox>
    <Label text="%general.control" GridPane.rowIndex="19"/>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="19">
      <CheckBox id="isUart-form-editor" mnemonicParsing="false"/>
      <Label id="isUart-form-label" text="Label"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="20">
      <CheckBox id="isComEcho-form-editor" mnemonicParsing="false"/>
      <Label id="isComEcho-form-label" text="Label"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="21">
      <CheckBox id="isLanEcho-form-editor" mnemonicParsing="false"/>
      <Label id="isLanEcho-form-label" text="Label"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="22">
      <Label id="connectMode-form-label" text="Label"/>
      <ChoiceBox id="connectMode-form-editor" fx:id="connectModeFormEditor" />
    </HBox>

    <Label text="%general.log" GridPane.rowIndex="23"/>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="23">
      <CheckBox id="isDebug-form-editor" mnemonicParsing="false"/>
      <Label id="isDebug-form-label" text="Label"/>
      <CheckBox id="isInfo-form-editor" mnemonicParsing="false">
        <HBox.margin>
          <Insets left="40.0"/>
        </HBox.margin>
      </CheckBox>
      <Label id="isInfo-form-label" text="Label"/>
      <CheckBox id="isNotice-form-editor" mnemonicParsing="false">
        <HBox.margin>
          <Insets left="40.0"/>
        </HBox.margin>
      </CheckBox>
      <Label id="isNotice-form-label" text="Label"/>
      <CheckBox id="isWarning-form-editor" mnemonicParsing="false">
        <HBox.margin>
          <Insets left="40.0"/>
        </HBox.margin>
      </CheckBox>
      <Label id="isWarning-form-label" text="Label"/>
      <CheckBox id="isError-form-editor" mnemonicParsing="false">
        <HBox.margin>
          <Insets left="40.0"/>
        </HBox.margin>
      </CheckBox>
      <Label id="isError-form-label" text="Label"/>
    </HBox>
    <Label text="%general.remotelog" GridPane.rowIndex="24"/>
    <HBox id="tool-box" prefHeight="30.0" GridPane.columnIndex="1" GridPane.rowIndex="24">
      <CheckBox id="isEnableRemoteLog-form-editor" mnemonicParsing="false"/>
      <Label id="isEnableRemoteLog-form-label" text="Label"/>
      <CheckBox id="isEnableLocalLog-form-editor" mnemonicParsing="false"/>
      <Label id="isEnableLocalLog-form-label" text="Label"/>
    </HBox>
    <HBox id="tool-box" prefHeight="30.0" GridPane.columnIndex="1" GridPane.rowIndex="25">
      <Label id="logServerIp-form-label" text="Label"/>
      <TextField id="logServerIp-form-editor" prefWidth="130.0">
        <HBox.margin>
          <Insets left="10.0" right="10.0"/>
        </HBox.margin>
      </TextField>
      <ConstraintLabel id="logServerIp-form-constraint"/>
    </HBox>
    <Label text="%general.third_party_login" GridPane.rowIndex="26"/>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="26">
      <CheckBox id="isFaceLogin-form-editor" mnemonicParsing="false">
      </CheckBox>
      <Label id="isFaceLogin-form-label" text="Label"/>
    </HBox>
  </GridPane>
</VBox>
