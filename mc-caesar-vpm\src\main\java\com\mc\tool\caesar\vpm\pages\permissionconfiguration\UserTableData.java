package com.mc.tool.caesar.vpm.pages.permissionconfiguration;

import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.datamodel.UserGroupData;
import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.vpm.pages.permissionconfiguration.Bundle.NbBundle;
import java.text.DecimalFormat;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import lombok.Getter;

/**
 * .
 */
public class UserTableData implements DataObject {

  @Getter private UserData userData;

  private SimpleIntegerProperty id = new SimpleIntegerProperty();
  private SimpleStringProperty name = new SimpleStringProperty();
  private SimpleStringProperty permission = new SimpleStringProperty();
  private SimpleStringProperty usergroup = new SimpleStringProperty();
  private SimpleStringProperty edit = new SimpleStringProperty();

  private String isAdmin = NbBundle.getMessage("PermissionConfiguration.user_table_data.admin");

  /** . */
  public UserTableData(UserData userData) {
    this.userData = userData;
    this.id.set(userData.getId());
    this.name.set(userData.getName());
    StringBuilder pstring = new StringBuilder();
    if (userData.hasRightAdmin()) {
      pstring.append(isAdmin);
    } else {
      pstring.append(userData.getRights());
    }
    this.permission.set(pstring.toString());
    StringBuilder ustring = new StringBuilder();
    for (UserGroupData data : userData.getUserGroupDatas()) {
      ustring.append(data.getName()).append(" ");
    }
    this.usergroup.set(ustring.toString());
  }

  @Override
  public int getId() {
    return id.get();
  }

  public void setId(Integer id) {
    this.id.set(id);
  }

  public StringProperty getIdProperty() {
    return new SimpleStringProperty(new DecimalFormat("0000").format(id.get()));
  }

  @Override
  public String getName() {
    return this.name.get();
  }

  public void setName(String name) {
    this.name.set(name);
  }

  public StringProperty getNameProperty() {
    return this.name;
  }

  public String getPermission() {
    return this.permission.get();
  }

  public void setPermission(String permission) {
    this.permission.set(permission);
  }

  public StringProperty getPermissionProperty() {
    return this.permission;
  }

  public String getUsergroup() {
    return this.usergroup.get();
  }

  public void setUsergroup(String usergroup) {
    this.usergroup.set(usergroup);
  }

  public StringProperty getUsergroupProperty() {
    return this.usergroup;
  }

  public String getEdit() {
    return edit.get();
  }

  @Override
  public void delete() {
    // TODO Auto-generated method stub

  }

  @Override
  public void delete(boolean paramBoolean) {
    // TODO Auto-generated method stub

  }
}
