package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Setter;

/**
 * 全光拼接屏详细信息.
 */
@Setter
@JsonPropertyOrder({
    VideoWall.JSON_PROPERTY_ID,
    VideoWall.JSON_PROPERTY_NAME,
    VideoWall.JSON_PROPERTY_SCREENS,
    VideoWall.JSON_PROPERTY_LAYERS
})
public class VideoWall {
  public static final String JSON_PROPERTY_ID = "id";
  private Integer id;

  public static final String JSON_PROPERTY_NAME = "name";
  private String name;

  public static final String JSON_PROPERTY_SCREENS = "screens";
  private List<VideoWallScreen> screens = null;

  public static final String JSON_PROPERTY_LAYERS = "layers";
  private List<VideoWallLayer> layers = null;

  /**
   * .
   */
  public VideoWall id(Integer id) {
    this.id = id;
    return this;
  }

  /**
   * 全光拼接屏ID，与诺瓦/open/screen/readlList接口的screenId匹配.
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getId() {
    return id;
  }

  /**
   * .
   */
  public VideoWall name(String name) {
    this.name = name;
    return this;
  }

  /**
   * 全光拼接屏名称，与诺瓦/open/screen/readlList接口的name匹配.
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public String getName() {
    return name;
  }

  /**
   * .
   */
  public VideoWall screens(List<VideoWallScreen> screens) {
    this.screens = screens;
    return this;
  }

  /**
   * .
   */
  public VideoWall addScreensItem(VideoWallScreen screensItem) {
    if (this.screens == null) {
      this.screens = new ArrayList<>();
    }
    this.screens.add(screensItem);
    return this;
  }

  /**
   * Get screens.
   **/
  @Nullable
  @Valid
  @JsonProperty(JSON_PROPERTY_SCREENS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public List<VideoWallScreen> getScreens() {
    return screens;
  }

  /**
   * .
   */
  public VideoWall layers(List<VideoWallLayer> layers) {
    this.layers = layers;
    return this;
  }

  /**
   * .
   */
  public VideoWall addLayersItem(VideoWallLayer layersItem) {
    if (this.layers == null) {
      this.layers = new ArrayList<>();
    }
    this.layers.add(layersItem);
    return this;
  }

  /**
   * Get layers.
   **/
  @Nullable
  @Valid
  @JsonProperty(JSON_PROPERTY_LAYERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public List<VideoWallLayer> getLayers() {
    return layers;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    VideoWall videoWall = (VideoWall) o;
    return Objects.equals(this.id, videoWall.id)
        && Objects.equals(this.name, videoWall.name)
        && Objects.equals(this.screens, videoWall.screens)
        && Objects.equals(this.layers, videoWall.layers);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, name, screens, layers);
  }


  @Override
  public String toString() {
    return "VideoWall {\n"
        + "    id: " + toIndentedString(id) + "\n"
        + "    name: " + toIndentedString(name) + "\n"
        + "    screens: " + toIndentedString(screens) + "\n"
        + "    layers: " + toIndentedString(layers) + "\n" + "}";
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
