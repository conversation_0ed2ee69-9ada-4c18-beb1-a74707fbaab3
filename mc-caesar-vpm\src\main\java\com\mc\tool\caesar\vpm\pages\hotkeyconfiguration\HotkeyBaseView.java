package com.mc.tool.caesar.vpm.pages.hotkeyconfiguration;

import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.FavoriteObject;
import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.control.SearchField;
import com.mc.tool.caesar.vpm.util.hotkey.TempFavoriteObject;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.ViewControllable;
import java.beans.PropertyChangeListener;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;
import java.util.function.Predicate;
import javafx.beans.binding.Bindings;
import javafx.beans.binding.BooleanBinding;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.beans.value.ChangeListener;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredList;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.control.SelectionMode;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableSelectionModel;
import javafx.scene.control.TableView;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public abstract class HotkeyBaseView<T extends DataObject & FavoriteObject> extends VBox
    implements Initializable, ViewControllable {

  @FXML protected Label sourceListTitleLabel;
  @FXML protected Label hotkeyBlockTitleLabel;
  @FXML protected HBox hbox;

  @Getter @FXML protected TableView<T> sourceList;
  @FXML protected TableColumn<T, Number> sourceIdColumn;
  @FXML protected TableColumn<T, String> sourceNameColumn;

  @FXML protected Button applyBtn;
  @FXML protected Button cancelBtn;
  @FXML protected Button copyBtn;

  @FXML protected VBox searchFieldBox;
  protected SearchField<T> searchField;

  protected CopyListSelectionView<CpuData> listSelectionView =
      new CopyListSelectionView<>(CaesarConstants.Cpu.FAVORITE);

  protected WeakAdapter weakAdapter = new WeakAdapter();

  protected CaesarDeviceController deviceController = null;
  protected CustomPropertyChangeSupport pcs = new CustomPropertyChangeSupport(this);
  protected ObjectProperty<FavoriteObject> selectedItem = new SimpleObjectProperty<>();

  protected StringProperty sourceListTitle = new SimpleStringProperty();
  protected StringProperty hotkeyBlockTitle = new SimpleStringProperty();

  protected ObservableList<CpuData> cpuDataSourceList = FXCollections.observableArrayList();
  protected ObservableList<CpuData> cpuDataTargetList = FXCollections.observableArrayList();

  protected Button upButton = new Button();
  protected Button downButton = new Button();

  protected ObservableList<T> sourceRawList = FXCollections.observableArrayList();
  protected FilteredList<T> sourceFilteredList = new FilteredList<>(sourceRawList);

  protected BooleanProperty updatingProperty = new SimpleBooleanProperty(false);

  /** Constructor. */
  public HotkeyBaseView() {
    URL location =
        Thread.currentThread()
            .getContextClassLoader()
            .getResource("com/mc/tool/caesar/vpm/util/hotkey/hotkey_view.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    loader.setRoot(this);
    loader.setController(this);
    loader.setResources(ResourceBundle.getBundle("com.mc.tool.caesar.vpm.i18n.common"));
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load hotkey_view.fxml", exception);
    }
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
      // 监听数据的变化
      this.deviceController
          .getDataModel()
          .addPropertyChangeListener(
              new String[] {
                FavoriteObject.PROPERTY_FAVORITE,
                CpuData.PROPERTY_NAME,
                CpuData.PROPERTY_ID,
                CpuData.PROPERTY_STATUS
              },
              weakAdapter.wrap(
                  (PropertyChangeListener)
                      evt -> PlatformUtility.runInFxThread(this::updateSelection)));
    }
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceController;
  }

  /**
   * 选择源.
   *
   * @param item 源
   */
  public void selectSourceItem(T item) {
    searchField.setSearchText("");
    getSourceList().requestFocus();
    getSourceList().scrollTo(item);
    getSourceList().getSelectionModel().select(item);
  }

  @FXML
  protected void onApply(ActionEvent event) {
    FavoriteObject object = selectedItem.get();
    if (object instanceof TempFavoriteObject) {
      TempFavoriteObject tempFavoriteObject = (TempFavoriteObject) object;
      updatingProperty.set(true);
      deviceController
          .submitAsync(
              () -> {
                tempFavoriteObject.commitData();
                onSendNewFavorite(tempFavoriteObject.getWrappedObeject());
              })
          .whenComplete(
              (result, throwable) -> {
                if (throwable != null) {
                  log.warn("Fail to submit favorite object", throwable);
                }
                PlatformUtility.runInFxThread(
                    () -> {
                      tempFavoriteObject.commitStatus();
                      updateSelection();
                      updatingProperty.set(false);
                    });
              });
    }
  }

  @FXML
  protected void onCancel(ActionEvent event) {
    FavoriteObject object = selectedItem.get();
    if (object instanceof TempFavoriteObject) {
      TempFavoriteObject tempFavoriteObject = (TempFavoriteObject) object;
      tempFavoriteObject.cancel();
      updateSelection();
    }
  }

  protected abstract void onSendNewFavorite(FavoriteObject item);

  protected TableSelectionModel<T> getTableSelectionModel() {
    return sourceList.getSelectionModel();
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {

    // 检索框
    searchField = new SearchField<>();
    searchFieldBox.getChildren().add(1, searchField);

    Label sourceHeaderLabel =
        new Label(CaesarI18nCommonResource.getString("hotkey_selection_view.source_header"));
    HBox sourceHeaderBox = new HBox(sourceHeaderLabel);
    sourceHeaderBox.getStyleClass().add("title-box");
    sourceHeaderBox.setStyle(
        "-fx-background-color:#dbf1ef;-fx-border-width:1px;-fx-border-color:#e5e5e5;");
    listSelectionView.setSourceHeader(sourceHeaderBox);
    Label targetHeaderLabel =
        new Label(CaesarI18nCommonResource.getString("hotkey_selection_view.target_header"));
    HBox targetHeaderBox = new HBox(targetHeaderLabel);
    targetHeaderBox.getStyleClass().add("title-box");
    targetHeaderBox.setStyle("-fx-border-width:1px;-fx-border-color:#e5e5e5;");
    listSelectionView.setTargetHeader(targetHeaderBox);

    applyBtn.setDisable(true);
    cancelBtn.setDisable(true);
    copyBtn.setDisable(true);

    listSelectionView.setSourceItems(cpuDataSourceList);
    listSelectionView.setTargetItems(cpuDataTargetList);
    listSelectionView.setCellFactory(col -> new SelectionViewCell());
    // 设置标题
    sourceListTitleLabel.textProperty().bind(sourceListTitle);
    hotkeyBlockTitleLabel.textProperty().bind(hotkeyBlockTitle);
    // 监听变化
    getTableSelectionModel().setSelectionMode(SelectionMode.SINGLE);
    getTableSelectionModel()
        .selectedItemProperty()
        .addListener(
            weakAdapter.wrap(
                (ChangeListener<FavoriteObject>)
                    (observable, oldValue, newValue) -> {
                      if (newValue != null) {
                        updateSelection();
                        BooleanBinding isEmpty =
                            Bindings.isEmpty(
                                ((CopyListSelectionViewSkin<CpuData>) listSelectionView.getSkin())
                                    .getTargetListView()
                                    .getSelectionModel()
                                    .getSelectedItems());
                        upButton.disableProperty().bind(isEmpty);
                        downButton.disableProperty().bind(isEmpty);
                      }
                    }));

    // 添加上下移动按钮
    upButton.setId("upButton");
    downButton.setId("downButton");
    upButton.getStyleClass().add("common-button");
    downButton.getStyleClass().add("common-button");
    upButton.setDisable(true);
    downButton.setDisable(true);
    VBox buttonBox = new VBox();
    buttonBox.setStyle("-fx-alignment:center;-fx-padding:10px;-fx-spacing:10px;");
    buttonBox.getChildren().addAll(upButton, downButton);
    HBox.setHgrow(listSelectionView, Priority.ALWAYS);
    hbox.getChildren().addAll(listSelectionView, buttonBox);
    upButton.setOnAction(
        (event) -> {
          CopyListSelectionViewSkin<CpuData> skin =
              (CopyListSelectionViewSkin<CpuData>) listSelectionView.getSkin();
          int select = skin.getTargetListView().getSelectionModel().getSelectedIndex();
          ObservableList<CpuData> items = listSelectionView.getTargetItems();
          if (select != 0) {
            final CpuData back = items.get(select);
            final CpuData front = items.get(select - 1);
            items.set(select, front);
            items.set(select - 1, back);
            skin.getTargetListView().getSelectionModel().clearAndSelect(select - 1);
          }
        });
    downButton.setOnAction(
        (event) -> {
          CopyListSelectionViewSkin<CpuData> skin =
              (CopyListSelectionViewSkin<CpuData>) listSelectionView.getSkin();
          int select = skin.getTargetListView().getSelectionModel().getSelectedIndex();
          ObservableList<CpuData> items = listSelectionView.getTargetItems();
          if (select != items.size() - 1) {
            final CpuData front = items.get(select);
            final CpuData back = items.get(select + 1);
            items.set(select, back);
            items.set(select + 1, front);
            skin.getTargetListView().getSelectionModel().clearAndSelect(select + 1);
          }
        });

    // 应用与取消
    selectedItem.addListener(
        weakAdapter.wrap(
            (obs, oldVal, newval) -> {
              if (newval == null) {
                applyBtn.disableProperty().unbind();
                applyBtn.setDisable(true);
                cancelBtn.disableProperty().unbind();
                cancelBtn.setDisable(true);
                copyBtn.setDisable(true);
              } else if (newval instanceof TempFavoriteObject) {
                applyBtn
                    .disableProperty()
                    .bind(
                        ((TempFavoriteObject) selectedItem.get())
                            .isChangeProperty()
                            .not()
                            .or(updatingProperty));
                cancelBtn
                    .disableProperty()
                    .bind(
                        ((TempFavoriteObject) selectedItem.get())
                            .isChangeProperty()
                            .not()
                            .or(updatingProperty));
                copyBtn.setDisable(false);
              }
            }));
    // 监听Favorite数据变化
    cpuDataTargetList.addListener(
        weakAdapter.wrap(
            (ListChangeListener<CpuData>)
                change -> {
                  FavoriteObject object = selectedItem.get();
                  if (object instanceof TempFavoriteObject) {
                    TempFavoriteObject tempFavoriteObject = (TempFavoriteObject) object;
                    int idx = 0;
                    for (CpuData cpuData : cpuDataTargetList) {
                      tempFavoriteObject.setFavoriteData(idx, cpuData);
                      idx++;
                    }
                    while (idx < CaesarConstants.Cpu.FAVORITE) {
                      tempFavoriteObject.setFavorite(idx, 0);
                      idx++;
                    }
                  }
                }));

    sourceList.setItems(sourceFilteredList);

    searchField
        .filterPredicateProperty()
        .addListener(
            weakAdapter.wrap(
                (obs, oldVal, newVal) ->
                    sourceFilteredList.setPredicate(
                        searchField.getFilterPredicate().and(getSourcePrediate()))));
    sourceFilteredList.setPredicate(getSourcePrediate());
  }

  protected void updateSelection() {
    if (getTableSelectionModel().getSelectedItem() == null) {
      selectedItem.set(null);
    } else if (selectedItem.get() == null
        || selectedItem.get() instanceof TempFavoriteObject
            && getTableSelectionModel().getSelectedItem()
                != ((TempFavoriteObject) selectedItem.get()).getWrappedObeject()) {
      TempFavoriteObject temp =
          new TempFavoriteObject(
              pcs,
              deviceController.getDataModel().getConfigDataManager(),
              getTableSelectionModel().getSelectedItem());
      selectedItem.set(temp);
    }

    if (selectedItem.get() == null) {
      cpuDataSourceList.clear();
      cpuDataTargetList.clear();
    } else {
      FavoriteObject object = selectedItem.get();
      List<CpuData> assignedCpu = new ArrayList<>(object.getFavoriteDatas());
      cpuDataTargetList.setAll(assignedCpu);
      List<CpuData> availableCpu =
          new ArrayList<>(deviceController.getDataModel().getConfigDataManager().getActiveCpus());
      availableCpu.removeAll(assignedCpu);
      if (getTableSelectionModel().getSelectedItem() instanceof ConsoleData) {
        availableCpu.removeAll(
            ((ConsoleData) getTableSelectionModel().getSelectedItem()).getNoAccessCpuDatas());
      }
      cpuDataSourceList.setAll(availableCpu);
    }
  }

  protected Predicate<T> getSourcePrediate() {
    return (item) -> true;
  }

  private static class SelectionViewCell extends ListCell<CpuData> {

    @Override
    protected void updateItem(CpuData item, boolean empty) {
      super.updateItem(item, empty);
      if (empty || item == null) {
        setText("");
      } else {
        String indexString = String.format("[%02d]", getIndex() + 1);
        setText(indexString + item.getName());
      }
    }
  }
}
