package com.mc.tool.caesar.vpm.userright;

import com.mc.tool.caesar.vpm.devices.CaesarUserRightGetter;
import com.mc.tool.caesar.vpm.pages.extenderupdate.CaesarExtenderUpdatePage;
import com.mc.tool.caesar.vpm.pages.hostupdate.CaesarHostUpdatePage;

/**
 * .
 */
public class CaesarConfigFileUserRightDecorator extends CaesarUserRightDecoratorBase {

  public CaesarConfigFileUserRightDecorator(CaesarUserRightGetter userRight) {
    super(userRight);
  }

  @Override
  public boolean isPageVisible(String pageName) {
    if (pageName.equals(CaesarHostUpdatePage.NAME)) {
      return false;
    }
    if (pageName.equals(CaesarExtenderUpdatePage.NAME)) {
      return false;
    }
    return super.isPageVisible(pageName);
  }

  @Override
  public boolean isSystemLogVisible() {
    return false;
  }

  @Override
  public boolean isActivateConfigVisible() {
    return false;
  }
}
