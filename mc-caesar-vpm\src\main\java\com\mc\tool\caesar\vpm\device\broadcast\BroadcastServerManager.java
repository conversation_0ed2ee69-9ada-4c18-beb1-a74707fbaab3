package com.mc.tool.caesar.vpm.device.broadcast;

import java.util.HashMap;
import java.util.Map;

/**
 * .
 */
public class BroadcastServerManager {
  private static Map<BroadcastServer, Thread> map = new HashMap<>();

  /**
   * 创建server线程.
   *
   * @param server server
   * @return 线程.
   */
  public static synchronized Thread createServer(BroadcastServer server) {
    if (server == null) {
      return null;
    }
    Thread old = map.get(server);
    if (old != null && old.isAlive()) {
      return old;
    } else {
      Thread thread = new Thread(server::run);
      thread.setDaemon(true);
      thread.start();
      map.put(server, thread);
      return thread;
    }
  }
}
