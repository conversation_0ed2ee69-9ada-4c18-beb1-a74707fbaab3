.scroll-pane > .viewport {
  -fx-background-color: transparent;
}

.label {
  -fx-background-repeat: no-repeat;
}

#zoomin-btn {
  -fx-background-image: url("zoomin_normal.png");
  -fx-background-repeat: no-repeat;
  -fx-background-position: center;
}

#zoomin-btn:hover {
  -fx-background-image: url("zoomin_hover.png");
}

#zoomin-btn:disable {
  -fx-background-image: url("zoomin_disable.png");
}

#zoomout-btn {
  -fx-background-image: url("zoomout_normal.png");
  -fx-background-repeat: no-repeat;
  -fx-background-position: center;
}

#zoomout-btn:hover {
  -fx-background-image: url("zoomout_hover.png");
}

#zoomout-btn:disable {
  -fx-background-image: url("zoomout_disable.png");
}

#zoomin-btn:disable {
  -fx-background-image: url("zoomin_disable.png");
}

#restore-btn {
  -fx-background-image: url("restore_normal.png");
  -fx-background-repeat: no-repeat;
  -fx-background-position: center;
}

#restore-btn:hover {
  -fx-background-image: url("restore_hover.png");
}

#restore-btn:disable {
  -fx-background-image: url("restore_disable.png");
}

#preview-btn {
  -fx-background-image: url("preview_normal.png");
  -fx-background-repeat: no-repeat;
  -fx-background-position: center;
}

#preview-btn:hover {
  -fx-background-image: url("preview_hover.png");
}

#preview-btn:disable {
  -fx-background-image: url("preview_disable.png");
}


#graph-tool-bar {
  -fx-spacing: 40;
  -fx-background-color: #f7f7f7;
}

#scenario-list-container {
  -fx-padding: 7;
  -fx-background-color: #f7f7f7;
}

.seperator {
  -fx-background-color: #f0f0f0;
}

#scenario-left-btn {
  -fx-graphic: url("scenario_left_normal.png");
}

#scenario-left-btn:hover {
  -fx-graphic: url("scenario_left_hover.png");
}

#scenario-right-btn {
  -fx-graphic: url("scenario_right_normal.png");
}

#scenario-right-btn:hover {
  -fx-graphic: url("scenario_right_hover.png");
}

#scenario-list {
  -fx-background-color: transparent;
}

#scenario-list .scroll-bar:horizontal .increment-arrow,
#scenario-list .scroll-bar:horizontal .decrement-arrow,
#scenario-list .scroll-bar:horizontal .increment-button,
#scenario-list .scroll-bar:horizontal .decrement-button {
  -fx-padding: 0;
}

#scenario-list .list-cell {
  -fx-background-color: transparent;
}



