package com.mc.tool.caesar.api;

import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;

/**
 * .
 */
public final class DemoSwitchModuleData extends CaesarSwitchModuleData {

  public DemoSwitchModuleData(CustomPropertyChangeSupport pcs,
      DemoSwitchDataModel switchDataModel) {
    super(pcs, switchDataModel);
  }

  @Override
  public void reloadModules(Iterable<ModuleData> moduleDatas) throws ConfigException {

  }

  @Override
  public void requestPorts(Iterable<PortData> portDatas) throws ConfigException {

  }
}

