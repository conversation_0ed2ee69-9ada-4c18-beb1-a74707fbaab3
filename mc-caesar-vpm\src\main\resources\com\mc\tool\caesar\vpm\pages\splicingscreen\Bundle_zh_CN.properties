config_decoder_group.btn=\u7EC4\u914D\u7F6E
edit_decoder_group_dialog.name_label=\u89E3\u7801\u7EC4\u540D\u79F0\uFF1A
edit_decoder_group_dialog.croppable_check=\u89E3\u7801\u5361\u662F\u5426\u53EF\u88C1\u526A
assign_permissions_dialog.croppable_check=\u662F\u5426\u88C1\u526A
assign_permissions_dialog.yes=\u662F
assign_permissions_dialog.no=\u5426
edit_decoder_group_dialog.select_rx=\u8BF7\u9009\u62E9RX
assign_permissions_selection_view.source_header=\u672A\u5206\u914D\u6743\u9650\u7684\u4FE1\u53F7\u6E90
assign_permissions_selection_view.target_header=\u53EF\u4E0A\u5C4F\u7684\u4FE1\u53F7\u6E90
new_decode_card_dialog.title=\u6DFB\u52A0\u89E3\u7801\u5361
new_decode_card_dialog.header_text=\u9009\u62E9\u89E3\u7801\u5361\u7ED1\u5B9A\u5173\u7CFB
new_decode_card_dialog.delete=\u5220\u9664
new_decode_card_dialog.add_input_card=\u589E\u52A0\u89E3\u7801\u5361
new_decode_card_dialog.delete_input_card=\u5220\u9664\u89E3\u7801\u5361
new_decode_card_dialog.input_card=\u8F93\u5165\u5361
create_decoder_group.btn=\u521B\u5EFA\u89E3\u7801\u7EC4
decoder_group=\u89E3\u7801\u7EC4
host_label=\u5168\u5149\u62FC\u63A5\u4E3B\u673A
delete_decoder_group.btn=\u5220\u9664\u89E3\u7801\u7EC4
splicing_screen_delete_dialog.header=\u8BF7\u9009\u62E9\u5168\u5149\u62FC\u63A5\u5C4F
splicing_screen_delete_dialog.title=\u5220\u9664\u5168\u5149\u62FC\u63A5\u5C4F\u7EC4
edit_decoder_group_dialog.title=\u521B\u5EFA\u89E3\u7801\u7EC4
edit_decoder_group_dialog.type_requirement=\u7EC4\u5185\u7684RX\u5206\u8FA8\u7387\u7C7B\u578B\u5FC5\u987B\u4E00\u81F4
assign_permissions_dialog.decoder_group_configuration=\u89E3\u7801\u7EC4\u914D\u7F6E
assign_permissions_dialog.name_label=\u540D\u79F0\uFF1A
splicing_screen_input_dialog.header=\u8BF7\u8F93\u5165\u5168\u5149\u62FC\u63A5\u5C4F\u4FE1\u606F
splicing_screen_input_dialog.ip_address=IP\u5730\u5740
splicing_screen_input_dialog.ip_format_error=IP\u683C\u5F0F\u9519\u8BEF
splicing_screen_input_dialog.name=\u540D\u79F0
name_label=\u540D\u79F0
host_type_label=\u4E3B\u673A\u7C7B\u578B
kaito_input_list=\u5168\u5149\u8F93\u5165\u5361\u5217\u8868
index_column_text=\u5E8F\u53F7
caesar_card_name_column_text=C\u5361\u540D\u79F0
caesar_card_id_column_text=C\u5361ID
caesar_card_port_column_text=C\u5361\u7AEF\u53E3
nowa_card_sn_column_text=N\u5361SN
nowa_card_id_column_text=N\u5361ID
nowa_card_port_column_text=N\u5361\u7AEF\u53E3
splicing_screen_input_dialog.pid=\u8BFA\u74E6\u63A5\u5165\u65B9ID
splicing_screen_input_dialog.port=\u7AEF\u53E3
splicing_screen_input_dialog.port_format_error=\u7AEF\u53E3\u7684\u683C\u5F0F\u9519\u8BEF\uFF0C\u8BF7\u8F93\u5165\u4E00\u4E2A\u57281~65536\u8303\u56F4\u5185\u7684\u6570\u5B57\u3002
splicing_screen_input_dialog.secret_key=\u8BFA\u74E6secretkKey
splicing_screen_input_dialog.title=\u521B\u5EFA\u5168\u5149\u62FC\u63A5\u5C4F
splicing_screen_input_dialog.type=\u8BFA\u74E6\u4E3B\u673A\u7C7B\u578B
splicingscreen_page.title=\u62FC\u63A5\u5C4F
toolbar.delete_splicing_screen=\u5220\u9664\u5168\u5149\u62FC\u63A5\u5C4F\u7EC4
toolbar.new_decoder=\u6DFB\u52A0\u89E3\u7801\u5361
toolbar.new_splicing_screen=\u521B\u5EFA\u5168\u5149\u62FC\u63A5\u5C4F
rx_list.label=RX\u5217\u8868
rx_list.index=\u5E8F\u53F7
rx_list.name=RX\u540D\u79F0
rx_list.port=RX\u7AEF\u53E3
rx_list.input_id=\u62FC\u63A5\u5668\u8F93\u5165ID
rx_list.slot_id=\u62FC\u63A5\u5668\u69FD\u4F4DID