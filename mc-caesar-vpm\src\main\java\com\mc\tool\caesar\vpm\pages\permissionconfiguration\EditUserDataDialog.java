package com.mc.tool.caesar.vpm.pages.permissionconfiguration;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarConstants.User;
import com.mc.tool.caesar.api.CaesarValidatorFactory;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.datamodel.UserGroupData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData.VideoWallData;
import com.mc.tool.caesar.api.interfaces.Nameable;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.utility.UndecoratedDialog;
import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.ResourceBundle;
import java.util.concurrent.ExecutionException;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.control.ButtonBar.ButtonData;
import javafx.scene.control.ButtonType;
import javafx.scene.control.CheckBox;
import javafx.scene.control.ComboBox;
import javafx.scene.control.DialogPaneEx;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.control.PasswordField;
import javafx.scene.control.Spinner;
import javafx.scene.control.SpinnerValueFactory.IntegerSpinnerValueFactory;
import javafx.scene.control.TextField;
import javafx.scene.layout.HBox;
import javafx.util.StringConverter;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.ListSelectionViewEx;
import org.controlsfx.validation.ValidationSupport;
import org.controlsfx.validation.Validator;

/**
 * .
 */
@Slf4j
public final class EditUserDataDialog extends UndecoratedDialog<UserDataInfo>
    implements Initializable, ViewControllable {

  @FXML private TextField idField;
  @FXML private TextField usernameField;
  @FXML private Label msgLabel;
  @FXML private PasswordField passwordField;
  @FXML private PasswordField rePasswordField;
  @FXML private ComboBox<String> rightsComboBox;
  @FXML private HBox container;
  @FXML private Spinner<Integer> mouseSpeedSpinner;
  @FXML private CheckBox autoConnectCheckBox;
  @FXML private ListSelectionViewEx<VideoWallData> videoWallRightsSelectionView;
  @FXML private HBox selectionBox;
  @FXML
  private Label warningMsg;

  private UserGroupCheckComboBox userGroupCheckComboBox = new UserGroupCheckComboBox();

  private UserData userData;

  private Boolean isEditMode;
  private CaesarDeviceController deviceController = null;
  private Collection<UserData> users;
  private final ObjectProperty<Collection<? extends Nameable>> userDataProperty =
      new SimpleObjectProperty<>();

  private final ObservableList<UserGroupData> items = FXCollections.observableArrayList();
  private final ObservableList<UserGroupData> unselectItems = FXCollections.observableArrayList();
  private final ObservableList<VideoWallData> unselectedWalls = FXCollections.observableArrayList();
  private final ObservableList<VideoWallData> selectedWalls = FXCollections.observableArrayList();
  private VideoWallData[] videoWalls;

  private final ObservableList<String> roles = FXCollections.observableArrayList();

  /** 编辑用户数据对话框. */
  public EditUserDataDialog(
      UserData userData, Collection<VideoWallData> videoWalls, Boolean isEditMode) {
    this.userData = userData;
    this.videoWalls = videoWalls.toArray(new VideoWallData[0]);
    this.isEditMode = isEditMode;

    URL location =
        getClass()
            .getResource(
                "/com/mc/tool/caesar/vpm/pages/permissionconfiguration/"
                    + "editUserDataDialog_view.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    loader.setController(this);
    loader.setResources(
        ResourceBundle.getBundle("com.mc.tool.caesar.vpm.pages.permissionconfiguration.Bundle"));
    try {
      getDialogPane().setContent(loader.load());
    } catch (IOException ex) {
      log.warn("Can not load editUserDataDialog_view.fxml", ex);
    }

    idField.setText(String.valueOf(userData.getId()));
    usernameField.setText(String.format("NewUser%d", userData.getId()));
    if (isEditMode) {
      usernameField.setText(userData.getName());
      usernameField.setDisable(userData.getName().equals(User.ADMIN_NAME));
    }
    passwordField.setText(userData.getPassword());
    rePasswordField.setText(userData.getPassword());
    rightsComboBox.getSelectionModel().select(userData.getRights() - 1);
    mouseSpeedSpinner.getValueFactory().setValue(userData.getMouseSpeed());
    autoConnectCheckBox.setSelected(userData.isAutoConnect());

    for (int i = 0; i < this.videoWalls.length; i++) {
      if (userData.hasVideoWallAccess(i)) {
        selectedWalls.add(this.videoWalls[i]);
      } else {
        unselectedWalls.add(this.videoWalls[i]);
      }
    }

    initImpl();
    getDialogPane()
        .lookupButton(ButtonType.OK)
        .addEventFilter(
            ActionEvent.ACTION,
            ae -> {
              if (this.deviceController != null) {
                String usernameText = usernameField.getText();
                int len = usernameText.getBytes(StandardCharsets.UTF_8).length;
                if (len > CaesarConstants.NAME_LEN) {
                  msgLabel.setText(
                      Bundle.NbBundle.getMessage(
                          "PermissionConfiguration.edit_user_dialog.invalidMsg"));
                  ae.consume();
                } else {
                  try {
                    Boolean validity =
                        deviceController
                            .submit(() -> deviceController.checkUsernameValidity(usernameText))
                            .get();
                    if (!validity) {
                      msgLabel.setText(
                          Bundle.NbBundle.getMessage(
                              "PermissionConfiguration.edit_user_dialog.invalidMsg"));
                      ae.consume();
                    }
                  } catch (InterruptedException | ExecutionException ex) {
                    log.error("checkUsernameValidity failed", ex);
                    ae.consume();
                  }
                }
              }
            });

    setResultConverter(
        (dialogButton) -> {
          ButtonData data = dialogButton == null ? null : dialogButton.getButtonData();
          if (data == ButtonData.OK_DONE) {
            UserDataInfo info = new UserDataInfo();
            info.setUsername(usernameField.getText());
            info.setPassword(passwordField.getText());
            info.setRePassword(rePasswordField.getText());
            try {
              info.setRights(rightsComboBox.getSelectionModel().getSelectedIndex() + 1);
            } catch (RuntimeException exception) {
              info.setRights(100);
            }
            info.setUserGroupDatas(userGroupCheckComboBox.getCheckModel().getCheckedItems());
            unselectItems.addAll(items);
            unselectItems.removeAll(userGroupCheckComboBox.getCheckModel().getCheckedItems());
            info.setUnSelectUserGroupDatas(unselectItems);
            info.setMouseSpeed(mouseSpeedSpinner.getValue());
            info.setAutoConnect(autoConnectCheckBox.isSelected());

            int videoWallRights = 0;
            for (VideoWallData item : selectedWalls) {
              int index = new ArrayList<>(videoWalls).indexOf(item);
              videoWallRights |= 1 << index;
            }
            info.setVideoWallRights(videoWallRights);
            return info;
          } else {
            return null;
          }
        });
  }

  private void initImpl() {
    final DialogPaneEx dialogPane = getDialogPane();
    dialogPane.getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);

    ValidationSupport validationSupport = new ValidationSupport();
    validationSupport.setValidationDecorator(null);
    Validator<String> nameLengthValidator =
        CaesarValidatorFactory.nameLengthValidator(
            Bundle.NbBundle.getMessage("PermissionConfiguration.name_length_illegal"));
    validationSupport.registerValidator(usernameField, nameLengthValidator);

    Validator<String> rePasswordValidator = CaesarValidatorFactory
        .passwordValidator(passwordField.textProperty(),
            Bundle.NbBundle.getMessage("PermissionConfiguration.password_illegal"),
            Bundle.NbBundle.getMessage("PermissionConfiguration.password_not_match"));
    validationSupport.registerValidator(rePasswordField, rePasswordValidator);

    passwordField.textProperty().addListener((observable, oldValue, newValue) -> {
      if (newValue != null) {
        rePasswordField.setText("");
      }
    });

    validationSupport.validationResultProperty().addListener((observable, oldValue, newValue) -> {
      if (newValue.getErrors().size() == 0) {
        warningMsg.setText("");
      } else if (newValue.getErrors().size() > 0) {
        newValue.getErrors().stream().findFirst()
            .ifPresent(item -> warningMsg.setText(item.getText()));
      }
    });
    dialogPane.lookupButton(ButtonType.OK).disableProperty()
        .bind(validationSupport.invalidProperty());
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    userGroupCheckComboBox.setPrefWidth(200);
    container.getChildren().add(userGroupCheckComboBox);

    for (int i = 0; i < 100; i++) {
      roles.add("Role " + (i + 1));
    }
    rightsComboBox.setItems(roles);

    userGroupCheckComboBox.setConverter(new ComboBoxStringConverter());

    mouseSpeedSpinner.setValueFactory(
        new IntegerSpinnerValueFactory(
            CaesarConstants.MIN_MOUSE_SPEED,
            CaesarConstants.MAX_MOUSE_SPEED,
            userData.getMouseSpeed()));

    Label sourceHeaderLabel =
        new Label(CaesarI18nCommonResource.getString("user.videowallright.unselected"));
    HBox sourceHeaderBox = new HBox(sourceHeaderLabel);
    sourceHeaderBox.getStyleClass().add("title-box");
    sourceHeaderBox.setStyle(
        "-fx-background-color:#cccccc;-fx-border-width:1px;-fx-border-color:#e5e5e5;");
    videoWallRightsSelectionView.setSourceHeader(sourceHeaderBox);
    Label targetHeaderLabel =
        new Label(CaesarI18nCommonResource.getString("user.videowallright.selected"));
    HBox targetHeaderBox = new HBox(targetHeaderLabel);
    targetHeaderBox.getStyleClass().add("title-box");
    targetHeaderBox.setStyle(
        "-fx-background-color:#cccccc;-fx-border-width:1px;-fx-border-color:#e5e5e5;");
    videoWallRightsSelectionView.setTargetHeader(targetHeaderBox);
    videoWallRightsSelectionView.setSourceItems(unselectedWalls);
    videoWallRightsSelectionView.setTargetItems(selectedWalls);
    videoWallRightsSelectionView.setCellFactory((list) -> new VideoWallCell());

    selectionBox.managedProperty().bind(selectionBox.visibleProperty());
    selectionBox.setVisible(!userData.getName().equals(User.ADMIN_NAME));
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
    } else {
      log.warn("Error device controller type : {} !", deviceController.getClass().getName());
      return;
    }
    items.addAll(this.deviceController.getDataModel().getConfigDataManager().getActiveUserGroups());
    userGroupCheckComboBox.getItems().setAll(items);
    users = this.deviceController.getDataModel().getConfigDataManager().getActiveUsers();
    if (isEditMode) {
      users.remove(userData);
      for (UserGroupData userGroupData : userData.getUserGroupDatas()) {
        userGroupCheckComboBox.getCheckModel().check(userGroupData);
      }
    }
    userDataProperty.set(users);
  }

  @Override
  public DeviceControllable getDeviceController() {
    return null;
  }

  private static class ComboBoxStringConverter extends StringConverter<UserGroupData> {

    @Override
    public String toString(UserGroupData object) {
      return object.getName();
    }

    @Override
    public UserGroupData fromString(String string) {
      return null;
    }
  }

  private class VideoWallCell extends ListCell<VideoWallData> {

    @Override
    protected void updateItem(VideoWallData item, boolean empty) {
      super.updateItem(item, empty);
      if (empty || item == null) {
        setText("");
      } else {
        int index = Arrays.asList(videoWalls).indexOf(item);
        String name = item.getName();
        if (!item.isDataValid()) {
          name = "(" + CaesarI18nCommonResource.getString("videowall.not_created") + ")";
        }
        String text = String.format("[%d]%s", index, name);
        setText(text);
      }
    }
  }
}
