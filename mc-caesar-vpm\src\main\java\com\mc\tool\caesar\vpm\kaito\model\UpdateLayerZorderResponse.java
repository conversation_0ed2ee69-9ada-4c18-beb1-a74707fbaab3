package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Nullable;
import javax.validation.Valid;
import lombok.Setter;

/**
 * UpdateLayerZorderResponse.
 */
@Setter
@JsonPropertyOrder({
    UpdateLayerZorderResponse.JSON_PROPERTY_SEQ,
    UpdateLayerZorderResponse.JSON_PROPERTY_ZORDERS
})
public class UpdateLayerZorderResponse {
  public static final String JSON_PROPERTY_SEQ = "seq";
  private Integer seq;

  public static final String JSON_PROPERTY_ZORDERS = "zorders";
  private List<UpdateLayerZorderResponseZorders> zorders = null;


  /**
   * seq.
   */
  public UpdateLayerZorderResponse seq(Integer seq) {

    this.seq = seq;
    return this;
  }

  /**
   * 大屏修改序号.
   *
   * @return seq
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_SEQ)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getSeq() {
    return seq;
  }

  /**
   * .
   */
  public UpdateLayerZorderResponse zorders(List<UpdateLayerZorderResponseZorders> zorders) {
    this.zorders = zorders;
    return this;
  }

  /**
   * .
   */
  public UpdateLayerZorderResponse addZordersItem(UpdateLayerZorderResponseZorders zordersItem) {
    if (this.zorders == null) {
      this.zorders = new ArrayList<>();
    }
    this.zorders.add(zordersItem);
    return this;
  }

  /**
   * 所有图层的order.
   *
   * @return zorders
   **/
  @Nullable
  @Valid
  @JsonProperty(JSON_PROPERTY_ZORDERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public List<UpdateLayerZorderResponseZorders> getZorders() {
    return zorders;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    UpdateLayerZorderResponse updateLayerZorderResponse = (UpdateLayerZorderResponse) o;
    return Objects.equals(this.seq, updateLayerZorderResponse.seq)
        && Objects.equals(this.zorders, updateLayerZorderResponse.zorders);
  }

  @Override
  public int hashCode() {
    return Objects.hash(seq, zorders);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("UpdateLayerZorderResponse {\n");
    sb.append("    seq: ").append(toIndentedString(seq)).append("\n");
    sb.append("    zorders: ").append(toIndentedString(zorders)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
