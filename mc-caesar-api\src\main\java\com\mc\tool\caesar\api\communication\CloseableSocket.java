package com.mc.tool.caesar.api.communication;

import java.io.IOException;
import java.net.InetAddress;
import java.net.Proxy;
import java.net.Socket;
import java.net.SocketException;
import java.net.SocketImpl;
import java.net.UnknownHostException;

/**
 * .
 */
public class CloseableSocket extends Socket {

  public CloseableSocket(InetAddress address, int port, InetAddress localAddr, int localPort)
      throws IOException {
    super(address, port, localAddr, localPort);
  }

  public CloseableSocket(String host, int port, InetAddress localAddr, int localPort)
      throws IOException {
    super(host, port, localAddr, localPort);
  }

  public CloseableSocket(InetAddress address, int port) throws IOException {
    super(address, port);
  }

  public CloseableSocket(String host, int port) throws UnknownHostException, IOException {
    super(host, port);
  }

  public CloseableSocket(SocketImpl impl) throws SocketException {
    super(impl);
  }

  public CloseableSocket(Proxy proxy) {
    super(proxy);
  }

  public CloseableSocket() {

  }
}

