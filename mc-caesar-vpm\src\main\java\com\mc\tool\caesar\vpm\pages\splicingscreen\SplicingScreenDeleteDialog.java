package com.mc.tool.caesar.vpm.pages.splicingscreen;

import com.mc.tool.framework.utility.UndecoratedHeavyWeightDialog;
import java.util.List;
import javafx.scene.control.ButtonBar;
import javafx.scene.control.ButtonType;
import javafx.scene.control.DialogEx;
import javafx.scene.control.DialogPaneEx;
import javafx.scene.control.FxDialogEx;
import javafx.scene.control.SelectionMode;
import org.controlsfx.control.CheckListView;

/**
 * SplicingScreenDeleteDialog.
 */
public class SplicingScreenDeleteDialog extends DialogEx<List<DeleteVideoWallGroupInfo>> {

  private final CheckListView<DeleteVideoWallGroupInfo> checkListView = new CheckListView<>();

  /**
   * Constructor.
   */
  public SplicingScreenDeleteDialog() {
    initImpl();
    checkListView.getSelectionModel().setSelectionMode(SelectionMode.MULTIPLE);
    getDialogPane().setContent(checkListView);
    setResultConverter(dialogButton -> {
      ButtonBar.ButtonData data = dialogButton == null ? null : dialogButton.getButtonData();
      if (data == ButtonBar.ButtonData.OK_DONE) {
        return checkListView.getCheckModel().getCheckedItems();
      } else {
        return null;
      }
    });
  }

  public void setVideoWallGroups(List<DeleteVideoWallGroupInfo> videoWallGroups) {
    checkListView.getItems().setAll(videoWallGroups);
  }

  @Override
  protected FxDialogEx createDialog() {
    return new UndecoratedHeavyWeightDialog(this);
  }

  private void initImpl() {
    final DialogPaneEx dialogPane = getDialogPane();
    dialogPane.setGraphic(null);
    setTitle(Bundle.getMessage("splicing_screen_delete_dialog.title"));
    dialogPane.setHeaderText(Bundle.getMessage("splicing_screen_delete_dialog.header"));
    dialogPane.getStyleClass().add("text-input-dialog");
    dialogPane.getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);
  }
}
