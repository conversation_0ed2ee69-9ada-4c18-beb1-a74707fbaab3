package com.mc.tool.caesar.api.utils;

import com.mc.tool.caesar.api.interfaces.Extension;
import javax.swing.filechooser.FileFilter;
import javax.swing.filechooser.FileNameExtensionFilter;

/**
 * .
 */
public enum CaesarExtension implements Extension {
  VPD {
    @Override
    public String getDescription() {
      return "(*.vpd)";
    }

    @Override
    public String getExtension() {
      return "vpd";
    }
  };

  CaesarExtension() {

  }

  @Override
  public String getDescription() {
    return null;
  }

  @Override
  public String getExtension() {
    return null;
  }

  @Override
  public FileFilter createFileFilter() {
    return new FileNameExtensionFilter(this.getDescription(), this.getExtension());
  }

}
