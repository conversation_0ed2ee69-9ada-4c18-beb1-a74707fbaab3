tw:3840,th:2160,im:0,aas:1,aav:0
vpss[4]:vid,pre(x,y,w,h),piece(l,r,t,b),down(w,h),up(w,h),post(x,y,w,h),pos(x,y,w,h)
	[0]:1,(0,0,960,540),(0,1,0,0),(960,540),(1920,1080),(0,0,0,0),(0,0,1920,1080)
	[4]:1,(960,0,960,540),(1,0,0,0),(960,540),(1920,1080),(0,0,0,0),(0,0,1920,1080)
	[8]:1,(0,540,960,540),(0,1,0,0),(960,540),(1920,1080),(0,0,0,0),(0,0,1920,1080)
	[12]:1,(960,540,960,540),(1,0,0,0),(960,540),(1920,1080),(0,0,0,0),(0,0,1920,1080)
port[4]:en,f_en,pos(x,y,w,h)
	[0]:1,1,(0,0,1920,1080)
	[1]:1,1,(1920,0,1920,1080)
	[2]:1,1,(0,1080,1920,1080)
	[3]:1,1,(1920,1080,1920,1080)
prio[16]
	13,0,0,0,14,0,0,0,15,0,0,0,16,0,0,0
output_res:
	clock     ,horz(sync ,back ,disp ,total,polar),vert(sync ,back ,disp ,total,polar)
	148500000 ,    (44   ,148  ,1920 ,2200 ,0    ),    (5    ,36   ,1080 ,1125 ,0    )
logo&tpg:
	color   ,bg      ,oid1,oid2,oid3,oid4,left ,top  ,width,height,tpg(mode,color   ,speed)
	0000a4ff,00000000,0   ,1   ,2   ,3   ,200  ,200  ,80   ,400   ,   (0   ,ffffffff,0    )
overlap:
	bgimg_en,bgimg_a,layer_en,layer_a,cap_en,cap_a,logo_en,logo_a,tpg_en,tpg_a,cap_bg_en
	1       ,0      ,1       ,0      ,1     ,0    ,1      ,192   ,0     ,0    ,1        
