package com.mc.tool.caesar.vpm.pages.systemlog;

import com.mc.tool.framework.utility.InjectorProvider;
import java.io.IOException;
import java.net.URL;
import javafx.fxml.FXMLLoader;
import javafx.scene.layout.VBox;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class SystemsLogPageView extends VBox {

  @Getter private SystemLogPageController controller;

  /** Contructor. */
  public SystemsLogPageView() {
    try {
      controller = InjectorProvider.getInjector().getInstance(SystemLogPageController.class);
      URL location =
          getClass().getResource("/com/mc/tool/caesar/vpm/pages/systemlog/syslog_view.fxml");
      FXMLLoader loader = new FXMLLoader(location);
      loader.setController(controller);
      loader.setRoot(this);
      loader.load();
    } catch (IOException exc) {
      log.warn("Can not load systemlog/syslog_view.fxml", exc);
    }
  }
}
