package com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.CaesarSwitchDataModelManager;
import com.mc.tool.caesar.api.datamodel.LoginResponse;
import com.mc.tool.caesar.api.datamodel.MatrixData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridConfigUtility.GridStatus;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridConfigUtility.MatrixData4HostConfig;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridConfigUtility.ValidateStatus;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridConfigUtility.ValidateTableCell;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.VpmVersionUtility;
import com.mc.tool.framework.utility.UndecoratedWizard;
import com.mc.tool.framework.utility.UndecoratedWizardPane;
import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;
import javafx.beans.binding.Bindings;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.value.ChangeListener;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.control.Button;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.cell.PasswordFieldTableCell;
import javafx.scene.control.cell.TextFieldTableCell;
import javafx.util.StringConverter;
import javafx.util.converter.DefaultStringConverter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class WizardHostConfig extends UndecoratedWizardPane implements Initializable {

  private final CaesarDeviceController deviceController;

  @FXML private TableView<MatrixData4HostConfig> tableView;

  @FXML private TableColumn<MatrixData4HostConfig, String> categoryCol;
  @FXML private TableColumn<MatrixData4HostConfig, String> ipCol;
  @FXML private TableColumn<MatrixData4HostConfig, String> userCol;
  @FXML private TableColumn<MatrixData4HostConfig, String> pwdCol;
  @FXML private TableColumn<MatrixData4HostConfig, String> validateCol;
  @FXML private TableColumn<MatrixData4HostConfig, String> deleteCol;
  @FXML private TableColumn<MatrixData4HostConfig, ValidateStatus> validCol;
  @FXML private Button addMatrixBtn;

  private ObservableList<MatrixData4HostConfig> items = FXCollections.observableArrayList();
  private List<MatrixData4HostConfig> deleteItems = new ArrayList<>();

  private final ChangeListener<ValidateStatus> statusChangeListener;
  private final ListChangeListener<MatrixData4HostConfig> listChangeListener;

  private final BooleanProperty pass = new SimpleBooleanProperty();

  /** . */
  public WizardHostConfig(CaesarDeviceController deviceController) {
    this.deviceController = deviceController;
    FXMLLoader loader = new FXMLLoader();
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    loader.setLocation(
        classLoader.getResource(
            "com/mc/tool/caesar/vpm/pages/matrixgrid/configwizard/hostconfig_view.fxml"));
    loader.setResources(
        ResourceBundle.getBundle("com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.Bundle"));
    loader.setController(this);
    try {
      Node root = loader.load();
      setContent(root);
    } catch (IOException exception) {
      log.warn("Fail to load prepare_view.fxml!", exception);
    }

    statusChangeListener = (obs, oldVal, newVal) -> checkStatus();

    listChangeListener =
        (change) -> {
          while (change.next()) {
            for (MatrixData4HostConfig item : change.getAddedSubList()) {
              item.hostConfigValid.addListener(statusChangeListener);
            }
            for (MatrixData4HostConfig item : change.getRemoved()) {
              item.hostConfigValid.removeListener(statusChangeListener);
            }
          }
          checkStatus();
        };
  }

  private void checkStatus() {
    boolean ok = true;
    for (MatrixData4HostConfig item : items) {
      ok &= item.hostConfigValid.get() == ValidateStatus.VALID;
    }
    pass.set(ok);
  }

  @Override
  public void onEnteringPage(UndecoratedWizard wizard) {
    super.onEnteringPage(wizard);
    items.setAll(MatrixGridConfigUtility.getMatrixGridConfigFromWizard(wizard));
    if (items.isEmpty()) {
      initItems();
    }

    deleteItems.clear();
    deleteItems.addAll(MatrixGridConfigUtility.getMatrixGridDeleteItemsFromWizard(wizard));

    wizard.invalidProperty().bind(pass.not());

    for (MatrixData4HostConfig item : items) {
      item.hostConfigValid.addListener(statusChangeListener);
    }
    items.addListener(listChangeListener);
    checkStatus();
  }

  private void initItems() {
    CaesarSwitchDataModel model = deviceController.getDataModel();
    String localHostName =
        IpUtil.getAddressString(
            model.getConfigData().getSystemConfigData().getNetworkDataCurrent1().getAddress());
    int localMatrixIndex = -1;
    List<MatrixData4HostConfig> configs = new ArrayList<>();
    for (int i = 0; i < model.getConfigMetaData().getMatrixCount(); i++) {
      MatrixData matrixData = model.getConfigData().getMatrixData(i);
      if (matrixData.getDevice() != null && !matrixData.getDevice().isEmpty()) {
        MatrixData4HostConfig config = new MatrixData4HostConfig();
        String hostName = IpUtil.getAddressString(matrixData.getHostAddress());
        config.ip.set(hostName);
        config.name.set(matrixData.getDevice());
        config.user.set(deviceController.getLoginUser());
        config.pwd.set(deviceController.getLoginPwd());
        configs.add(config);
        if (hostName.equals(localHostName)) {
          localMatrixIndex = i;
        }
      }
    }

    // 如果没有matrixdata,添加一个
    if (localMatrixIndex < 0) {
      configs.clear();
      MatrixData4HostConfig config = new MatrixData4HostConfig();
      config.ip.set(localHostName);
      config.name.set(model.getConfigData().getSystemConfigData().getSystemData().getDevice());
      config.user.set(deviceController.getLoginUser());
      config.pwd.set(deviceController.getLoginPwd());
      configs.add(config);
    }

    configs.get(0).keepId.set(true);
    for (MatrixData4HostConfig config : configs) {
      config.local = true;
      config.hostConfigValid.set(ValidateStatus.VALID);
    }

    items.setAll(configs);
  }

  @Override
  public void onExitingPage(UndecoratedWizard wizard) {
    super.onExitingPage(wizard);
    for (MatrixData4HostConfig item : items) {
      item.hostConfigValid.removeListener(statusChangeListener);
    }
    items.removeListener(listChangeListener);

    wizard.getSettings().put(MatrixGridConfigUtility.KEY_HOST_CONFIG, new ArrayList<>(items));
    wizard
        .getSettings()
        .put(MatrixGridConfigUtility.KEY_DELETE_CONFIG, new ArrayList<>(deleteItems));
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    categoryCol.setCellFactory((col) -> new MatrixGridConfigUtility.CategoryTableCell());
    categoryCol.setPrefWidth(50);
    categoryCol.setMaxWidth(50);
    categoryCol.setMinWidth(50);

    ipCol.setCellFactory(view -> new TextFieldTableCellEx<>(new DefaultStringConverter()));
    ipCol.setCellValueFactory((cell) -> cell.getValue().ip);
    ipCol.setPrefWidth(110);
    ipCol.setMinWidth(110);
    ipCol.setMaxWidth(110);

    userCol.setCellFactory(view -> new TextFieldTableCellEx<>(new DefaultStringConverter()));
    userCol.setCellValueFactory(cell -> cell.getValue().user);
    userCol.setPrefWidth(Integer.MAX_VALUE);

    pwdCol.setCellFactory(view -> new PasswordFieldTableCellEx<>(new DefaultStringConverter()));
    pwdCol.setCellValueFactory(cell -> cell.getValue().pwd);
    pwdCol.setPrefWidth(Integer.MAX_VALUE);

    validateCol.setCellFactory(col -> new ValidateTableCell(this::validate, true));
    validateCol.setPrefWidth(Integer.MAX_VALUE);

    deleteCol.setCellFactory(col -> new DeleteTableCell());
    deleteCol.setPrefWidth(Integer.MAX_VALUE);

    validCol.setCellFactory(col -> new MatrixGridConfigUtility.ValidTableCell());
    validCol.setCellValueFactory(cell -> cell.getValue().hostConfigValid);
    validCol.setPrefWidth(Integer.MAX_VALUE);

    tableView.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);
    tableView.setItems(items);
    tableView.setEditable(true);

    addMatrixBtn
        .disableProperty()
        .bind(Bindings.size(items).greaterThanOrEqualTo(CaesarConstants.MATRIX_GRID_COUNT));
  }

  @FXML
  protected void onAddMatrix(ActionEvent event) {
    MatrixData4HostConfig config = new MatrixData4HostConfig();
    config.gridStatus.set(GridStatus.NEW);
    items.add(config);
  }

  protected void validate(ValidateTableCell cell, MatrixData4HostConfig config) {
    cell.setDisable(true);

    deviceController
        .submitAsync(() -> validateImpl(config))
        .whenComplete(
            (status, throwable) -> {
              if (throwable != null) {
                log.warn("Fail to get validate data!", throwable);
                return;
              }
              PlatformUtility.runInFxThread(
                  () -> {
                    config.hostConfigValid.set(status);
                    cell.setDisable(false);
                  });
            });
  }

  protected ValidateStatus validateImpl(MatrixData4HostConfig config) {
    // 检查是否完整
    if (config.ip.get().trim().isEmpty()
        || config.user.get().trim().isEmpty()
        || config.pwd.get().trim().isEmpty()) {
      return ValidateStatus.UNCOMPLETE;
    }
    // 检查有没有相同的项
    for (MatrixData4HostConfig item : items) {
      if (item.hostConfigValid.get() != ValidateStatus.VALID) {
        continue;
      }
      if (item == config) {
        continue;
      }
      if (item.ip.get().trim().equals(config.ip.get().trim())) {
        return ValidateStatus.DUPLICATE;
      }
    }
    // 检查是否能连接
    try {
      CaesarSwitchDataModel model =
          CaesarSwitchDataModelManager.getInstance()
              .getModel(config.ip.get(), deviceController.isDemo());
      LoginResponse response =
          model.login(
              config.user.get().getBytes(StandardCharsets.UTF_8),
              config.pwd.get().getBytes(StandardCharsets.UTF_8), VpmVersionUtility.makeVpmVersion());
      if (response.getStatus() == LoginResponse.LoginStatus.SUCCESS) {
        return ValidateStatus.VALID;
      } else {
        return ValidateStatus.UNACCESSABLE;
      }
    } catch (ConfigException | BusyException exception) {
      log.warn("Fail to connect {}!", config.ip.get(), exception);
      return ValidateStatus.UNACCESSABLE;
    }
  }

  private class DeleteTableCell extends TableCell<MatrixData4HostConfig, String> {

    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);

      if (!empty
          && getTableRow() != null
          && getTableRow().getItem() instanceof MatrixData4HostConfig) {
        Button button = new Button();
        button.setPrefSize(70, 25);
        button.setText(CaesarI18nCommonResource.getString("matrixgrid.wizard.hostconfig.delete"));
        button.getStyleClass().add("common-button");
        this.setGraphic(button);
        MatrixData4HostConfig config = (MatrixData4HostConfig) getTableRow().getItem();
        button.setOnAction(
            event -> {
              if (config.gridStatus.get() == GridStatus.NORMAL) {
                config.gridStatus.set(GridStatus.DELETE);
                deleteItems.add(config);
              }
              items.remove(config);
            });
      }
    }
  }

  static class TextFieldTableCellEx<T> extends TextFieldTableCell<MatrixData4HostConfig, T> {

    public TextFieldTableCellEx(StringConverter<T> converter) {
      super(converter);
    }

    @Override
    public void updateItem(T item, boolean empty) {
      super.updateItem(item, empty);
      boolean disable = false;
      if (!empty) {
        Object rowItem = getTableRow().getItem();
        if (rowItem instanceof MatrixData4HostConfig) {
          disable = ((MatrixData4HostConfig) rowItem).local;
        }
      }
      setDisable(disable);
    }
  }

  static class PasswordFieldTableCellEx<T>
      extends PasswordFieldTableCell<MatrixData4HostConfig, T> {

    public PasswordFieldTableCellEx(StringConverter<T> converter) {
      super(converter);
    }

    @Override
    public void updateItem(T item, boolean empty) {
      super.updateItem(item, empty);
      boolean disable = false;
      if (!empty) {
        Object rowItem = getTableRow().getItem();
        if (rowItem instanceof MatrixData4HostConfig) {
          disable = ((MatrixData4HostConfig) rowItem).local;
        }
      }
      setDisable(disable);
    }
  }
}
