package com.mc.tool.caesar.vpm.pages.operation.videowall.vp;

import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarScreenData;
import com.mc.tool.caesar.vpm.util.vp.VpResolution;
import com.mc.tool.caesar.vpm.util.vp.VpScreenData;

/**
 * .
 */
public class CaesarVpScreenData implements VpScreenData {

  private CaesarScreenData screenData;
  private VpResolution resolution;

  private int xpos;
  private int ypos;

  /**
   * .
   */
  public CaesarVpScreenData(CaesarScreenData screenData, VpResolution resolution, int xpos,
                            int ypos) {
    this.screenData = screenData;
    this.resolution = resolution;
    this.xpos = xpos;
    this.ypos = ypos;
  }

  @Override
  public int getRxId() {
    return screenData.getVpScreen() == null ? 0 : screenData.getVpScreen().getId();
  }

  @Override
  public VpResolution getResolution() {
    return resolution;
  }

  @Override
  public int getXpos() {
    return xpos;
  }

  @Override
  public int getYpos() {
    return ypos;
  }
}
