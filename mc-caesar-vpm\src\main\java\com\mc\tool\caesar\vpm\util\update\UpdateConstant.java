package com.mc.tool.caesar.vpm.util.update;

/**
 * .
 */
public class UpdateConstant {
  public static final String UPDATE_HOST_TYPE = "host";
  public static final String UPDATE_IO_TYPE = "io";
  public static final String UPDATE_CPU_TYPE = "CPU";
  public static final String UPDATE_APP_TYPE = "app";
  public static final String UPDATE_SYS_TYPE = "sys";
  public static final String UPDATE_FPGA_TYPE = "fpga";
  public static final String UPDATE_TRUNK_TYPE = "trunk";

  public static final String EXTENDER_CON_TYPE = "RX UNIT";
  public static final String EXTENDER_CPU_TYPE = "TX UNIT";
  public static final String EXTENDER_VPCON_TYPE = "VPCON UNIT";
  public static final String EXTENDER_VP7_TYPE = "VP7 UNIT";

  public static final String VERSION_HEADER_TYPE = "type";
  public static final String VERSION_HEADER_PRODUCT = "product";
  public static final String VERSION_HEADER_SYS = "sys";
  public static final String VERSION_HEADER_APP = "app";
  public static final String VERSION_HEADER_FPGA = "fpga";
  public static final String VERSION_HEADER_LOGO = "logo";
  public static final String VERSION_HEADER_INTERFACE = "interface";
  public static final String VERSION_HEADER_PORTS = "ports";
  public static final String VERSION_HEADER_TRUNK = "trunk";
  public static final String VERSION_HEADER_TRUNK_144 = "trunk_144-plugin";
  public static final String VERSION_HEADER_TRUNK_384 = "trunk_384-plugin";
  public static final String VERSION_HEADER_TRUNK_816 = "trunk_816-plugin";
  public static final String VERSION_HEADER_BRANCH = "branch";
  public static final String VERSION_HEADER_IO = "io";
  public static final String VERSION_HEADER_FPGA_HDMI_TX = "fpga_hdmi_tx";
  public static final String VERSION_HEADER_FPGA_HDMI_RX = "fpga_hdmi_rx";
  public static final String VERSION_HEADER_FPGA_HDMI_4RX = "fpga_hdmi_4rx";
  public static final String VERSION_HEADER_FPGA_HDMI_4RX_HALF_1G = "fpga_hdmi_4rx_half_1g";
  public static final String VERSION_HEADER_FPGA_HDMI_4RX_HALF_2_5G = "fpga_hdmi_4rx_half_2_5g";
  public static final String VERSION_HEADER_FPGA_CAESAR_PREVIEW = "fpga_caesar_preview";

  public static final String VERSION_INTERFACE_SFP = "sfp";
  public static final String VERSION_INTERFACE_CAT = "cat";

  public static final String VERSION_PRODUCT_MAT = "dkm_mat";

  public static final String VERSION_PRODUCT_CON_CPU = "dkm_cpu_con";
  public static final String VERSION_PRODUCT_CON = "dkm_con";
  public static final String VERSION_PRODUCT_CPU = "dkm_cpu";
  public static final String VERSION_PRODUCT_VP6 = "ext_vp6";
  public static final String VERSION_PRODUCT_VP7 = "dkm_vp7";

  public static final String UPDATE_FILE_SINGLE = "io.rbf";
  public static final String UPDATE_FILE_TRUNK = "trunk.rbf";
  public static final String UPDATE_FILE_TRUNK_144 = "trunk_144-Plugin.rbf";
  public static final String UPDATE_FILE_TRUNK_384 = "trunk_384-Plugin.rbf";
  public static final String UPDATE_FILE_TRUNK_816 = "trunk_816-Plugin.rbf";
  public static final String UPDATE_FILE_BRANCH = "branch.rbf";

  /**
   * .
   */
  public enum ProductType {
    MAT,
    CON_CPU,
    CPU,
    CON,
    VP6,
    VP7
  }

  /**
   * .
   */
  public enum IoInterfaceType {
    SFP,
    CAT
  }
}
