package com.mc.tool.caesar.api.communication;

import java.lang.ref.WeakReference;

/**
 * .
 */
public class WeakConnectionListener implements ConnectionListener {

  private final WeakReference<ConnectionListener> weakRef;

  public WeakConnectionListener(ConnectionListener listener) {
    weakRef = new WeakReference<>(listener);
  }

  @Override
  public void connected() {
    ConnectionListener listener = weakRef.get();
    if (listener != null) {
      listener.connected();
    }
  }
}
