package com.mc.tool.caesar.vpm.pages.hostconfiguration;

import java.net.URL;
import java.util.ResourceBundle;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.ChoiceBox;

/**
 * GeneralFormController.
 */
public class GeneralFormController implements Initializable {

  @FXML
  private ChoiceBox<ConnectMode> connectModeFormEditor;

  @Override
  public void initialize(URL url, ResourceBundle resourceBundle) {
    connectModeFormEditor.getItems().setAll(ConnectMode.values());
  }
}
