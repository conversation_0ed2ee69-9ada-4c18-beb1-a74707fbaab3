package com.mc.tool.caesar.api.version.v4x;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.AbstractData;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.extargs.DhdmiSelection;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.version.ApiDataConverter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class ConsoleDataConverter4x implements ApiDataConverter<ConsoleData> {

  private static final int RESERVED_COUNT = 21;

  @Override
  public void readData(ConsoleData data, CfgReader cfgReader) throws ConfigException {
    String name = cfgReader.readString(CaesarConstants.NAME_LEN);
    data.setName(name);
    int status = cfgReader.readInteger();
    data.setStatus(status);
    int cpu = cfgReader.read2ByteValue();
    data.setCpu(cpu);
    int rdCpu = cfgReader.read2ByteValue();
    data.setRdCpu(rdCpu);
    int consoleVirtual = cfgReader.read2ByteValue();
    data.setConsoleVirtual(consoleVirtual);
    int scanTime = cfgReader.read2ByteValue();
    data.setScanTime(scanTime);
    int id = cfgReader.read2ByteValue();
    data.setId(id);
    int ports = cfgReader.read2ByteValue();
    data.setPorts(ports);
    for (int idx = 0; idx < CaesarConstants.Extender.CPUCON; idx++) {
      int extender = cfgReader.read2ByteValue();
      data.setExtender(idx, extender & 0xFFFF);
      data.setCpuIndex(idx, extender >> 16);
    }
    int user = cfgReader.read2ByteValue();
    data.setUser(user);
    int multiScreenIndex = cfgReader.read2ByteValue();
    data.setMultiScreenIndex(multiScreenIndex);
    int osdOpacity = cfgReader.readInteger();
    data.setOsdTransparency(osdOpacity);
    int suspendPosition = cfgReader.read2ByteValue();
    data.setSuspendPosition(CaesarConstants.Console.SuspendPosition.valueOf(suspendPosition));
    int suspendTime = cfgReader.read2ByteValue();
    data.setSuspendTime(suspendTime);
    int mouseSpeed = cfgReader.readByteValue();
    data.setMouseSpeed(mouseSpeed);
    int osdStyle = cfgReader.readByteValue();
    data.setOsdStyle(osdStyle);
    int whichCpuHdmi = cfgReader.readByteValue();
    if (whichCpuHdmi >= 0 && whichCpuHdmi < 3) {
      data.setCpuHdmi(DhdmiSelection.fromValue(whichCpuHdmi));
    } else {
      log.warn("Error which_cpu_hdmi {} in CON {} which is {}", whichCpuHdmi, name,
          data.isStatusActive() ? "active" : "inactive");
    }

    cfgReader.readByteArray(RESERVED_COUNT);

    data.setInitMode(true);
    data.getVideoAccessBitSet().clear();
    data.getNoAccessBitSet().clear();

    short[] lckControl = AbstractData
        .createArrayShort(data.getConfigDataManager().getConfigMetaData().getCpuCount() / 16);
    for (int idx = 0; idx < lckControl.length; idx++) {
      lckControl[idx] = cfgReader.readShort();
    }
    data.getVideoAccessBitSet().setFromShorts(lckControl);

    short[] lckVideo = AbstractData
        .createArrayShort(data.getConfigDataManager().getConfigMetaData().getCpuCount() / 16);
    for (int idx = 0; idx < lckVideo.length; idx++) {
      lckVideo[idx] = cfgReader.readShort();
    }
    data.getNoAccessBitSet().setFromShorts(lckVideo);
    data.setInitMode(false);
    for (int idx = 0; idx < CaesarConstants.Cpu.FAVORITE; idx++) {
      int favorite = cfgReader.readInteger();
      data.setFavorite(idx, favorite);
    }
  }

  @Override
  public void writeData(ConsoleData data, CfgWriter cfgWriter) throws ConfigException {
    cfgWriter.writeString(data.getName(), CaesarConstants.NAME_LEN);
    cfgWriter.writeInteger(data.getStatus());
    cfgWriter.write2ByteSmallEndian(data.getCpu());
    cfgWriter.write2ByteSmallEndian(data.getRdCpu());
    cfgWriter.write2ByteSmallEndian(data.getConsoleVirtual());
    cfgWriter.write2ByteSmallEndian(data.getScanTime());
    cfgWriter.write2ByteSmallEndian(data.getId());
    cfgWriter.write2ByteSmallEndian(data.getPorts());
    for (int idx = 0; idx < CaesarConstants.Extender.CPUCON; idx++) {
      int value = data.getCpuIndex(idx);
      value |= data.getExtender(idx);
      cfgWriter.write2ByteSmallEndian(value);
    }
    cfgWriter.write2ByteSmallEndian(data.getUser());

    cfgWriter.write2ByteSmallEndian(data.getMultiScreenIndex());

    cfgWriter.writeInteger(data.getOsdTransparency());
    cfgWriter.write2ByteSmallEndian(data.getSuspendPosition().getId());
    cfgWriter.write2ByteSmallEndian(data.getSuspendTime());
    cfgWriter.writeByte((byte) data.getMouseSpeed());
    cfgWriter.writeByte((byte) data.getOsdStyle());
    cfgWriter.writeByte((byte) data.getCpuHdmi().getValue());

    cfgWriter.writeByteArray(new byte[RESERVED_COUNT]);
    short[] lckControl = data.getVideoAccessBitSet()
        .getAsShortArray(data.getConfigDataManager().getConfigMetaData().getCpuCount() / 16);
    for (short value : lckControl) {
      cfgWriter.writeShort(value);
    }
    short[] lckVideo = data.getNoAccessBitSet()
        .getAsShortArray(data.getConfigDataManager().getConfigMetaData().getCpuCount() / 16);
    for (short i : lckVideo) {
      cfgWriter.writeShort(i);
    }
    for (int idx = 0; idx < CaesarConstants.Cpu.FAVORITE; idx++) {
      cfgWriter.writeInteger(data.getFavorite(idx));
    }
  }
}