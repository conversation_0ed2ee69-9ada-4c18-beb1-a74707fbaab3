<?xml version="1.0" encoding="UTF-8"?>

<?import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridWizardIndicator?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<VBox stylesheets="@gridname_view.css" xmlns="http://javafx.com/javafx/8"
  xmlns:fx="http://javafx.com/fxml/1"
  prefWidth="700" prefHeight="400" id="main-container">
  <children>
    <MatrixGridWizardIndicator currentIndex="2"/>
    <VBox id="sub-container">
      <Region prefHeight="45"/>
      <Label text="%gridname.info1"/>
      <Region prefHeight="10"/>
      <Label text="%gridname.info2"/>
      <Region prefHeight="6"/>
      <Label text="%gridname.info3"/>
      <Region prefHeight="15"/>
      <HBox spacing="10" style="-fx-alignment:CENTER_LEFT">
        <Label text="%gridname.gridname"/>
        <TextField fx:id="gridName"/>
      </HBox>
    </VBox>

  </children>
</VBox>
