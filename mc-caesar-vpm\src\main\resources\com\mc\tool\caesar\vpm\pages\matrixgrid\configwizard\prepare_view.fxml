<?xml version="1.0" encoding="UTF-8"?>

<?import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridWizardIndicator?>
<?import javafx.scene.control.CheckBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<VBox stylesheets="@prepare_view.css" xmlns="http://javafx.com/javafx/8"
  xmlns:fx="http://javafx.com/fxml/1"
  prefWidth="700" prefHeight="400" id="main-container">
  <children>
    <MatrixGridWizardIndicator currentIndex="0"/>
    <VBox id="sub-container">
      <Region prefHeight="45"/>
      <Label text="%prepare.info1"/>
      <Label text="%prepare.info2"/>
      <Region prefHeight="20"/>
      <Label text="%prepare.info3"/>
      <Region prefHeight="15"/>
      <VBox spacing="11" id="check-container">
        <CheckBox text="%prepare.check1" fx:id="check1"/>
        <CheckBox text="%prepare.check2" fx:id="check2"/>
        <CheckBox text="%prepare.check3" fx:id="check3"/>
      </VBox>
    </VBox>

  </children>
</VBox>
