package com.mc.tool.caesar.vpm.validation.constraints.impl;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.SystemConfigData;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.vpm.validation.constraints.UniqueStandbyIp;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 验证IP不能与其他网络配置的IP地址相同的实现类.
 * 此验证器需要通过自定义的ConstraintValidatorFactory注入SystemConfigData依赖.
 */
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UniqueStandbyIpImpl implements ConstraintValidator<UniqueStandbyIp, String> {

  /**
   * 系统配置数据.
   */
  private SystemConfigData systemConfigData;

  @Override
  public void initialize(UniqueStandbyIp constraintAnnotation) {
    // 初始化方法，暂时不需要特殊处理
  }

  @Override
  public boolean isValid(String value, ConstraintValidatorContext context) {
    if (value == null || value.trim().isEmpty()) {
      return true; // 空值由其他验证器处理
    }
    if (value.equals(CaesarConstants.IP_0)) {
      return true; // 特殊处理，允许0.0.0.0
    }
    if (systemConfigData == null) {
      return true; // 无法获取systemConfigData，跳过验证
    }
    // 获取主用板卡的IP地址
    if (systemConfigData.getNetworkDataPreset1() != null) {
      String networkIp1 =
          IpUtil.getAddressString(systemConfigData.getNetworkDataPreset1().getAddress());
      if (value.equals(networkIp1)) {
        return false;
      }
    }
    // 获取虚拟IP地址
    if (systemConfigData.getNetworkDataPreset2() != null) {
      String networkIp2 =
          IpUtil.getAddressString(systemConfigData.getNetworkDataPreset2().getAddress());
      if (value.equals(networkIp2)) {
        return false;
      }
    }
    // 获取控制板卡数据同步的IP地址
    if (systemConfigData.getNetworkDataPreset4() != null) {
      String networkIp4 =
          IpUtil.getAddressString(systemConfigData.getNetworkDataPreset4().getAddress());
      if (value.equals(networkIp4)) {
        return false;
      }
    }
    // 获取双机冗余的主用IP地址
    if (systemConfigData.getSystemData() != null) {
      String masterIp = IpUtil.getAddressString(systemConfigData.getSystemData().getMasterIp());
      return !value.equals(masterIp);
    }
    return true;
  }
}
