package com.mc.tool.caesar.vpm.pages.systemdata;

import com.dooapp.fxform.FXForm;
import com.dooapp.fxform.builder.FXFormBuilder;
import com.mc.tool.caesar.api.datamodel.SystemData;
import com.mc.tool.caesar.api.datamodel.SystemDataBean;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.utility.InjectorProvider;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.fxml.FXMLLoader;
import javafx.scene.layout.BorderPane;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class SystemDataPageView extends BorderPane {

  @Getter private SystemDataPageController controller;

  private SystemData systemdata = null;

  /** Contructor. */
  public SystemDataPageView(DeviceControllable deviceController) {
    controller = InjectorProvider.getInjector().getInstance(SystemDataPageController.class);
    URL url =
        getClass()
            .getResource("/com/mc/tool/caesar/vpm/pages/" + "systemdata/SystemDataForm_View.fxml");
    if (deviceController instanceof CaesarDeviceController) {
      this.systemdata =
          ((CaesarDeviceController) deviceController)
              .getDataModel()
              .getConfigData()
              .getSystemConfigData()
              .getSystemData();
    } else {
      log.warn("Error device controller type : {} !", deviceController.getClass().getName());
      return;
    }
    SystemDataBean bean = new SystemDataBean(this.systemdata);
    ResourceBundle resourceBundle =
        ResourceBundle.getBundle("com.mc.tool.caesar.vpm.pages.systemdata.systemdatabean");
    FXForm<SystemDataBean> fxform =
        new FXFormBuilder()
            .source(bean)
            .buffered(true, false)
            .resourceBundle(resourceBundle)
            .fxml(url)
            .build();
    controller.setFxform(fxform);

    URL location =
        getClass()
            .getResource("/com/mc/tool/caesar/vpm/pages/" + "systemdata/SystemData_View.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    loader.setController(controller);
    loader.setRoot(this);
    try {
      loader.load();
    } catch (IOException ex) {
      log.warn("Can not load SystemData_View.fxml", ex);
    }
  }
}
