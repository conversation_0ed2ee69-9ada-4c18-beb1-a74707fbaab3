package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import javax.annotation.Nullable;
import lombok.Setter;

/**
 * UpdateLayerZorderResponseZorders.
 */
@Setter
@JsonPropertyOrder({
    UpdateLayerZorderResponseZorders.JSON_PROPERTY_LAYER_ID,
    UpdateLayerZorderResponseZorders.JSON_PROPERTY_ZORDER
})
public class UpdateLayerZorderResponseZorders {
  public static final String JSON_PROPERTY_LAYER_ID = "layerId";
  private Integer layerId;

  public static final String JSON_PROPERTY_ZORDER = "zorder";
  private Integer zorder;


  public UpdateLayerZorderResponseZorders layerId(Integer layerId) {
    this.layerId = layerId;
    return this;
  }

  /**
   * 图层ID.
   *
   * @return layerId
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_LAYER_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getLayerId() {
    return layerId;
  }


  public UpdateLayerZorderResponseZorders zorder(Integer zorder) {
    this.zorder = zorder;
    return this;
  }

  /**
   * z序.
   *
   * @return zorder
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_ZORDER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getZorder() {
    return zorder;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    UpdateLayerZorderResponseZorders
        updateLayerZorderResponseZorders = (UpdateLayerZorderResponseZorders) o;
    return Objects.equals(this.layerId, updateLayerZorderResponseZorders.layerId)
        && Objects.equals(this.zorder, updateLayerZorderResponseZorders.zorder);
  }

  @Override
  public int hashCode() {
    return Objects.hash(layerId, zorder);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("UpdateLayerZorderResponseZorders {\n");
    sb.append("    layerId: ").append(toIndentedString(layerId)).append("\n");
    sb.append("    zorder: ").append(toIndentedString(zorder)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
