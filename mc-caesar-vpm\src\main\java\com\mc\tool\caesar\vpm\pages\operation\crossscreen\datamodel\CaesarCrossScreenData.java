package com.mc.tool.caesar.vpm.pages.operation.crossscreen.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.framework.operation.crossscreen.datamodel.CrossScreenData;
import com.mc.tool.framework.operation.crossscreen.datamodel.CrossScreenObject;
import com.mc.tool.framework.operation.seat.datamodel.SeatData.SeatConnection;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.util.Collection;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.ObservableMap;
import lombok.Getter;

/**
 * .
 */
public class CaesarCrossScreenData implements CrossScreenObject {
  @Expose private CrossScreenData innerData = new CrossScreenData();
  @Expose @Getter private IntegerProperty displayTime = new SimpleIntegerProperty();

  @Getter @Expose
  private ObjectProperty<CrossScreenMode> mode = new SimpleObjectProperty<>(CrossScreenMode.AUTO);

  @Override
  public StringProperty getName() {
    return innerData.getName();
  }

  @Override
  public IntegerProperty getRows() {
    return innerData.getRows();
  }

  @Override
  public IntegerProperty getColumns() {
    return innerData.getColumns();
  }

  @Override
  public ObjectProperty<VisualEditTerminal> getControlSource() {
    return innerData.getControlSource();
  }

  @Override
  public Collection<ObjectProperty<VisualEditTerminal>> getTargets() {
    return innerData.getTargets();
  }

  @Override
  public ObjectProperty<VisualEditTerminal> getTarget(int index) {
    return innerData.getTarget(index);
  }

  @Override
  public ObservableMap<VisualEditTerminal, SeatConnection> getConnections() {
    return innerData.getConnections();
  }

  @Override
  public int indexOfTarget(VisualEditTerminal target) {
    return innerData.indexOfTarget(target);
  }

  /**
   * 插入一个target.
   *
   * @param target target
   * @param index 插入的索引
   */
  public void insertTarget(VisualEditTerminal target, int index) {
    innerData.insertTarget(target, index);
  }

  /**
   * 重置screen的容量.
   *
   * @param size 容量的大小
   */
  public void resetCapacity(int size) {
    innerData.resetCapacity(size);
  }

  /**
   * .
   */
  public enum CrossScreenMode {
    AUTO,
    MANUAL,
    IRREGULAR
  }
}
