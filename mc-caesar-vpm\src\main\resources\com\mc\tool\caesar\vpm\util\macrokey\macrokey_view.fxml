<?xml version="1.0" encoding="UTF-8"?>

<?import com.mc.tool.caesar.vpm.util.control.SearchField?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ListView?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<fx:root stylesheets="@macrokey_view.css" type="javafx.scene.layout.VBox"
  xmlns:fx="http://javafx.com/fxml/1" id="main-container">
  <children>
    <HBox id="main-panel" VBox.vgrow="ALWAYS">
      <VBox styleClass="border-panel">
        <HBox styleClass="title-box">
          <Label styleClass="title" fx:id="sourceListTitleLabel"/>
        </HBox>
        <SearchField fx:id="searchField"/>
        <TableView styleClass="right-table" fx:id="sourceList"
          VBox.vgrow="ALWAYS">
          <columns>
            <TableColumn fx:id="sourceIdColumn" text="ID"/>
            <TableColumn fx:id="sourceNameColumn" text="%macro_key.name"/>
          </columns>
          <columnResizePolicy>
            <TableView fx:constant="CONSTRAINED_RESIZE_POLICY"/>
          </columnResizePolicy>
        </TableView>
      </VBox>
      <VBox styleClass="border-panel" HBox.hgrow="ALWAYS">
        <HBox styleClass="title-box">
          <Label styleClass="title" fx:id="accessBlockTitleLabel"/>
        </HBox>
        <HBox id="right-table-box" VBox.vgrow="ALWAYS">
          <VBox prefWidth="60" fx:id="keyListViewVbox">
            <HBox styleClass="title-box" id="macro-title-box">
              <Label styleClass="title" text="%macro_key.macro"/>
            </HBox>
            <ListView fx:id="keyListView" VBox.vgrow="ALWAYS"/>
          </VBox>
          <TableView prefWidth="145" fx:id="keyTable">
            <columns>
              <TableColumn prefWidth="40" resizable="false" sortable="false" fx:id="keyColumn"
                text="%macro_key.macro"/>
              <TableColumn prefWidth="90" sortable="false" fx:id="nameColumn"
                text="%macro_key.name"/>
            </columns>
            <!--						<columnResizePolicy>-->
            <!--							<TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />-->
            <!--						</columnResizePolicy>-->
          </TableView>
          <Region prefWidth="1" styleClass="seperator"/>
          <TableView HBox.hgrow="ALWAYS" fx:id="macroTable">
            <columns>
              <TableColumn sortable="false" fx:id="indexColumn" text="%macro_key.index"/>
              <TableColumn sortable="false" fx:id="cmdColumn" text="%macro_key.function"/>
              <TableColumn sortable="false" fx:id="param1Column" text="X"/>
              <TableColumn sortable="false" fx:id="param2Column" text="Y"/>
            </columns>
            <columnResizePolicy>
              <TableView fx:constant="CONSTRAINED_RESIZE_POLICY"/>
            </columnResizePolicy>
          </TableView>
        </HBox>
      </VBox>
    </HBox>
    <HBox id="tool-box">
      <Button fx:id="copyToRxBtn" styleClass="common-button" onAction="#onCopyToRx"
        text="%macro_key.copy_to_other_rx"/>
      <Button fx:id="copyMacroListBtn" styleClass="common-button" onAction="#onCopyMacroList"
        text="%macro_key.copy_macro_list"/>
      <Button fx:id="pasteMacroListBtn" styleClass="common-button" onAction="#onPasteMacroList"
        text="%macro_key.paste_macro_list"/>
      <Button fx:id="deleteMacroListBtn" styleClass="common-button" onAction="#onDeleteMacroList"
        text="%macro_key.delete_macro_list"/>
      <Region prefWidth="0"/>
      <Button fx:id="applyBtn" onAction="#onApply" styleClass="common-button"
        text="%macro_key.apply"/>
      <Button fx:id="cancelBtn" onAction="#onCancel" styleClass="common-button"
        text="%macro_key.cancel"/>
    </HBox>
  </children>
</fx:root>
