package com.mc.tool.caesar.api;

import com.mc.tool.caesar.api.datamodel.ScenarioData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData.VideoWallData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.CaesarExtension;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Comparator;
import java.util.concurrent.locks.Lock;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * .
 */
public class DemoVpDataModel extends VpDataModel {

  private static final Logger LOG = Logger.getLogger(DemoVpDataModel.class.getName());

  public DemoVpDataModel(CustomPropertyChangeSupport pcs, CaesarSwitchDataModel model, Lock lock,
      Runnable requireSaveRunnable) {
    super(pcs, model, lock, requireSaveRunnable);
  }

  @Override
  public void reloadVideoWallGroup() {
    model.getConfigData().commit(Threshold.ALL);
  }

  @Override
  public void sendVideoWallData(int index, VideoWallData videoWallData) throws BusyException {
    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    try {
      videoWallData.write(baos);
      ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
      videoWallGroupData.getData(index).read(bais);
    } catch (IOException ex) {
      LOG.log(Level.WARNING, "Fail to send videowall data!", ex);
    }
  }

  @Override
  public void sendVideoWallGlobalData() {
    System.out.println("sendVideoWallGlobalData");
  }

  @Override
  public void reloadScenarioDatas() {
    File file = new File(System.getProperty("default.dtp.dir", System.getProperty("user.home")));
    // File[] dtpFiles = file.listFiles(new FileFilter() {
    // @Override
    // public boolean accept(File pathname) {
    // return pathname.getName().endsWith(TeraExtension.VPD.getExtension());
    // }
    // });
    File[] dtpFiles = file.listFiles(new Filter());
    if (dtpFiles == null) {
      return;
    }
    // Arrays.sort(dtpFiles, new Comparator<File>() {
    // @Override
    // public int compare(File f1, File f2) {
    // return f1.getName().compareTo(f2.getName());
    // }
    // });
    Arrays.sort(dtpFiles, new FilesComparator());

    for (int i = 0; i < CaesarConstants.SCENARIO_SIZE; i++) {
      scenarioDatas[i] = null;
    }
    for (File scenFile : dtpFiles) {
      ScenarioData scenarioData = new ScenarioData();
      scenarioData.setFileName(scenFile.getAbsoluteFile().getPath());
      if (scenarioData.load() && scenarioData.getIndex() >= 0
          && scenarioData.getIndex() < CaesarConstants.SCENARIO_SIZE) {
        scenarioDatas[scenarioData.getIndex()] = scenarioData;
      }
    }

    getChangeSupport().firePropertyChange(ScenarioData.PROPERTY_DATA_CHANGED, 0, 1);
  }

  @Override
  public void sendScenarioData(ScenarioData scenarioData) {
    if (scenarioData == null || scenarioData.getIndex() < 0
        || scenarioData.getIndex() >= scenarioDatas.length) {
      return;
    }

    scenarioDatas[scenarioData.getIndex()] = scenarioData;

    getChangeSupport().firePropertyChange(ScenarioData.PROPERTY_DATA_CHANGED, 0, 1);
  }

  @Override
  public void removeScenarioData(ScenarioData data) {
    if (data == null || scenarioDatas[data.getIndex()] != data) {
      return;
    }

    File file = new File(data.getFileName());
    if (file.isFile() && file.exists() && !file.delete()) {
      LOG.log(Level.WARNING, "file delete fail!");
    }
    scenarioDatas[data.getIndex()] = null;
    getChangeSupport().firePropertyChange(ScenarioData.PROPERTY_DATA_CHANGED, 0, 1);
  }

  private static class Filter implements FileFilter {

    @Override
    public boolean accept(File pathname) {
      return pathname.getName().endsWith(CaesarExtension.VPD.getExtension());
    }
  }

  private static class FilesComparator implements Comparator<File>, Serializable {

    /**
     * .
     */
    private static final long serialVersionUID = 8048442168067574224L;

    @Override
    public int compare(File f1, File f2) {
      return f1.getName().compareTo(f2.getName());
    }
  }
}
