package com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard;

import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridConfigUtility.MatrixData4HostConfig;
import com.mc.tool.caesar.vpm.util.CaesarNamePredicate;
import com.mc.tool.framework.utility.UndecoratedWizard;
import com.mc.tool.framework.utility.UndecoratedWizardPane;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.beans.binding.Bindings;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.control.TextField;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class WizardGridName extends UndecoratedWizardPane implements Initializable {

  @FXML private TextField gridName;

  private BooleanProperty invalid = new SimpleBooleanProperty(true);

  private final CaesarDeviceController deviceController;

  private ObservableList<MatrixData4HostConfig> items = FXCollections.observableArrayList();

  /** . */
  public WizardGridName(CaesarDeviceController controller) {
    this.deviceController = controller;
    FXMLLoader loader = new FXMLLoader();
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    loader.setLocation(
        classLoader.getResource(
            "com/mc/tool/caesar/vpm/pages/matrixgrid/configwizard/gridname_view.fxml"));
    loader.setResources(
        ResourceBundle.getBundle("com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.Bundle"));
    loader.setController(this);
    try {
      Node root = loader.load();
      setContent(root);
    } catch (IOException exception) {
      log.warn("Fail to load gridname_view.fxml!", exception);
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    gridName.textProperty().addListener((obs, oldVal, newVal) -> checkValid());
    gridName.disableProperty().bind(Bindings.isEmpty(items));
  }

  @Override
  public void onEnteringPage(UndecoratedWizard wizard) {
    super.onEnteringPage(wizard);
    if (MatrixGridConfigUtility.isWizardHasGridName(wizard)) {
      gridName.setText(MatrixGridConfigUtility.getMatrixGridNameFromWizard(wizard));
    } else {
      gridName.setText(
          deviceController
              .getDataModel()
              .getConfigData()
              .getSystemConfigData()
              .getSystemData()
              .getName());
    }
    items.setAll(MatrixGridConfigUtility.getMatrixGridConfigFromWizard(wizard));
    wizard.invalidProperty().bind(invalid);
    checkValid();
  }

  @Override
  public void onExitingPage(UndecoratedWizard wizard) {
    super.onExitingPage(wizard);
    wizard.getSettings().put(MatrixGridConfigUtility.KEY_GRID_NAME, gridName.getText());
  }

  private void checkValid() {
    CaesarNamePredicate predicate = new CaesarNamePredicate();
    invalid.set(!predicate.test(gridName.getText()));
  }
}
