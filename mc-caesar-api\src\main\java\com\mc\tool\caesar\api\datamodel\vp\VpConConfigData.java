package com.mc.tool.caesar.api.datamodel.vp;

import com.mc.tool.caesar.api.io.NormalStruct;

/**
 * .
 */
public abstract class VpConConfigData extends NormalStruct {

  /**
   * 打印配置数据到console.
   */
  public abstract void print();

  /**
   * .
   */
  public static int generateOid(int inOid) {
    int oidValue = 0;
    long oidRawValue = inOid;
    for (int i = 100; i >= 1; i /= 10) {
      oidValue += (oidRawValue / i) << (4 * (int) (Math.log10(i)));
      oidRawValue %= i;
    }
    return oidValue;
  }
}
