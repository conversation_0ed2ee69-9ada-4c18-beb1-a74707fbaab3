package com.mc.tool.caesar.vpm.pages.systemedit.datamodel;

import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ReadOnlyStringWrapper;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.StringProperty;

/**
 * .
 */
public class TerminalPropertyConverter {

  /**
   * 获取id属性.
   *
   * @param terminal terminal
   * @return id属性
   */
  public static IntegerProperty getIdProperty(VisualEditTerminal terminal) {
    if (terminal instanceof CaesarCpuTerminal) {
      CaesarCpuTerminal cpuTerminal = (CaesarCpuTerminal) terminal;
      if (cpuTerminal.getCpuData() != null) {
        return cpuTerminal.getCpuData().getIdProperty();
      }
    } else if (terminal instanceof CaesarConTerminal) {
      CaesarConTerminal conTerminal = (CaesarConTerminal) terminal;
      if (conTerminal.getConsoleData() != null) {
        return conTerminal.getConsoleData().getIdProperty();
      }
    }
    return new SimpleIntegerProperty();
  }

  /**
   * 获取名称属性.
   *
   * @param terminal terminal
   * @return 名称属性
   */
  public static StringProperty getNameProperty(VisualEditTerminal terminal) {
    return terminal.nameProperty();
  }

  /**
   * 获取端口属性.
   *
   * @param terminal terminal
   * @return 端口属性
   */
  public static StringProperty getPortProperty(VisualEditTerminal terminal) {
    if (terminal instanceof CaesarTerminalBase) {
      CaesarTerminalBase terminalBase = (CaesarTerminalBase) terminal;
      if (terminalBase.getExtenderData() != null) {
        return terminalBase.getExtenderData().getPortProperty();
      }
    }
    return new ReadOnlyStringWrapper("");
  }

  /**
   * 获取序列号属性.
   *
   * @param terminal terminal
   * @return 序列号属性
   */
  public static StringProperty getSerialProperty(VisualEditTerminal terminal) {
    if (terminal instanceof CaesarTerminalBase) {
      CaesarTerminalBase terminalBase = (CaesarTerminalBase) terminal;
      if (terminalBase.getExtenderData() != null) {
        return terminalBase.getExtenderData().getSerialProperty();
      }
    }
    return new ReadOnlyStringWrapper("");
  }

  /**
   * 获取类型属性.
   *
   * @param terminal terminal
   * @return 类型属性
   */
  public static StringProperty getTypeProperty(VisualEditTerminal terminal) {
    if (terminal instanceof CaesarCpuTerminal) {
      return new ReadOnlyStringWrapper("TX");
    } else if (terminal instanceof CaesarUsbTxTerminal || terminal instanceof CaesarUsbRxTerminal) {
      return new ReadOnlyStringWrapper("USB");
    } else if (terminal instanceof CaesarConTerminal) {
      CaesarConTerminal conTerminal = (CaesarConTerminal) terminal;
      if (conTerminal.isVp()) {
        return new ReadOnlyStringWrapper("VP");
      } else {
        return new ReadOnlyStringWrapper("RX");
      }
    }
    return new ReadOnlyStringWrapper("");
  }
}
