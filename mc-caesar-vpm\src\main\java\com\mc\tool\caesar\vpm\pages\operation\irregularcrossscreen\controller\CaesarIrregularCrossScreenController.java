package com.mc.tool.caesar.vpm.pages.operation.irregularcrossscreen.controller;

import com.google.common.collect.Lists;
import com.google.common.eventbus.Subscribe;
import com.mc.common.util.WeakAdapter;
import com.mc.graph.canvas.PageCanvas.PageText;
import com.mc.graph.canvas.PageCanvasUtility;
import com.mc.graph.interfaces.CellObject;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.extargs.DhdmiSelection;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.utils.CpuDataStateWrapper;
import com.mc.tool.caesar.api.utils.ExtendedSwitchUtility;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.crossscreen.view.CaesarScreenItem;
import com.mc.tool.caesar.vpm.pages.operation.irregularcrossscreen.graph.IrregularCrossScreenGraph;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminalWrapper;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarIrregularCrossScreenFunc;
import com.mc.tool.caesar.vpm.util.TerminalUtility;
import com.mc.tool.framework.event.VisualEditFuncBeginUpdateEvent;
import com.mc.tool.framework.event.VisualEditFuncEndUpdateEvent;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.operation.controller.DefaultSnapshotGetter;
import com.mc.tool.framework.operation.crossscreen.controller.ScreenItem;
import com.mc.tool.framework.operation.videowall.controller.VideoWallConstants;
import com.mc.tool.framework.operation.videowall.datamodel.VideoData;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.EventBusProvider;
import com.mc.tool.framework.utility.TypeWrapper;
import java.net.URL;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.ResourceBundle;
import java.util.concurrent.atomic.AtomicInteger;
import javafx.application.Platform;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.Group;
import javafx.scene.control.Label;
import javafx.scene.control.ScrollPane;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.ColumnConstraints;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.RowConstraints;
import javafx.scene.layout.StackPane;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarIrregularCrossScreenController
    implements IrregularCrossScreenControllable, Initializable {
  private CaesarDeviceController deviceController;
  protected VisualEditModel model = null;
  protected CaesarIrregularCrossScreenFunc func = null;
  protected IrregularCrossScreenGraph graph = null;
  protected ObservableList<PageText> pageTexts = FXCollections.observableArrayList();
  protected WeakAdapter weakAdapter = new WeakAdapter();
  // private final BooleanProperty editableProperty = new SimpleBooleanProperty();

  private final BooleanProperty statusNewProperty = new SimpleBooleanProperty();
  private final AtomicInteger updateCount = new AtomicInteger(0);
  private boolean needToUpdate = false;
  private List<VideoData> prevVideoDatas;

  @FXML protected HBox graphBox;

  @FXML protected ScrollPane screenScroller;
  @FXML protected StackPane screenStack;
  @FXML protected Group screenGroup;
  @FXML protected GridPane screenList;

  @FXML protected Label zoominBtn;
  @FXML protected Label zoomoutBtn;

  @Override
  public void beginUpdate() {
    updateCount.incrementAndGet();
  }

  @Override
  public void endUpdate() {
    int count = updateCount.decrementAndGet();
    if (count <= 0) {
      updateCount.set(0);
      onIrregularCrossScreenChange();
    }
  }

  @Override
  public Collection<TypeWrapper> getMultiviewChannel(VisualEditTerminal rx) {
    return Collections.emptyList();
  }

  @Override
  public void connectMultiview(VisualEditTerminal tx, VisualEditTerminal rx, int mode,
                               int channel) {
    // Not supported
  }

  protected void endUpdateClear() {
    updateCount.decrementAndGet();
    if (updateCount.get() != 0) {
      log.warn("update count is not zero but {}", updateCount.get());
      updateCount.set(0);
    }
    onIrregularCrossScreenChange();
  }

  public boolean isUpdating() {
    return updateCount.get() != 0;
  }

  private void onIrregularCrossScreenChange() {
    if (needToUpdate) {
      double len = 100;
      int wholeWidth = func.getMaxColumn() * 100;
      int wholeHeight = func.getMaxRow() * 100;
      List<VideoData> videoDatas = func.getCaesarCrossScreenData().getVideos();
      for (int i = 0; i < videoDatas.size(); i++) {
        VideoData video = videoDatas.get(i);
        if (video == null) {
          continue;
        }
        VideoData prevVideo = prevVideoDatas.get(i);
        int xpos1 = video.getXpos().get();
        int ypos1 = video.getYpos().get();
        int xpos2 = video.getXpos().get() + video.getWidth().get();
        int ypos2 = video.getYpos().get() + video.getHeight().get();
        if (isVideoDataEqual(prevVideo, video)) {
          continue;
        }
        int newXpos1 = (int) len * (int) Math.round(xpos1 / len);
        int newYpos1 = (int) len * (int) Math.round(ypos1 / len);
        int newXpos2 = (int) len * (int) Math.round(xpos2 / len);
        int newYpos2 = (int) len * (int) Math.round(ypos2 / len);
        if (newXpos1 == newXpos2) {
          if (xpos1 % 100 != 0) {
            newXpos1 = xpos1 > xpos2 ? xpos2 + 100 : xpos2 - 100;
          } else if (xpos2 % 100 != 0) {
            newXpos2 = xpos2 > xpos1 ? xpos1 + 100 : xpos1 - 100;
          }
        }
        if (newYpos1 == newYpos2) {
          if (ypos1 % 100 != 0) {
            newYpos1 = ypos1 > ypos2 ? ypos2 + 100 : ypos2 - 100;
          } else if (ypos2 % 100 != 0) {
            newYpos2 = ypos2 > ypos1 ? ypos1 + 100 : ypos1 - 100;
          }
        }
        if (xpos1 != newXpos1) {
          video.getXpos().set(newXpos1);
        }
        if (video.getWidth().get() != newXpos2 - newXpos1) {
          video.getWidth().set(newXpos2 - newXpos1);
        }
        if (ypos1 != newYpos1) {
          video.getYpos().set(newYpos1);
        }
        if (video.getHeight().get() != newYpos2 - newYpos1) {
          video.getHeight().set(newYpos2 - newYpos1);
        }
        // 不允许cell超出网格
        if (newXpos1 < 0 || newYpos1 < 0 || newXpos2 > wholeWidth || newYpos2 > wholeHeight) {
          prevVideo.copyTo(video);
          continue;
        }
        if (isVideoDataEqual(prevVideo, video)) {
          continue;
        }
        // 不允许cell重叠
        if (checkCellOverlap(video, i)) {
          prevVideo.copyTo(video);
          continue;
        }
        video.copyTo(prevVideoDatas.get(i));
      }
      needToUpdate = false;
    }
  }

  private boolean isVideoDataEqual(VideoData video1, VideoData video) {
    return video1.getXpos().get() == video.getXpos().get()
        && video1.getYpos().get() == video.getYpos().get()
        && video1.getWidth().get() == video.getWidth().get()
        && video1.getHeight().get() == video.getHeight().get();
  }

  private boolean checkCellOverlap(VideoData video, int videoIndex) {
    for (int j = 0; j < prevVideoDatas.size(); j++) {
      if (videoIndex == j) {
        continue;
      }
      if (prevVideoDatas.get(j) == null) {
        continue;
      }
      if (isVideoDataOverlap(video, prevVideoDatas.get(j))) {
        return true;
      }
    }
    return false;
  }

  private boolean isVideoDataOverlap(VideoData video1, VideoData video2) {
    return video2.getXpos().get() < video1.getXpos().get() + video1.getWidth().get()
        && video1.getXpos().get() < video2.getXpos().get() + video2.getWidth().get()
        && video2.getYpos().get() < video1.getYpos().get() + video1.getHeight().get()
        && video1.getYpos().get() < video2.getYpos().get() + video2.getHeight().get();
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    graphBox.managedProperty().bind(statusNewProperty);
    graphBox.visibleProperty().bind(statusNewProperty);
    screenScroller.managedProperty().bind(statusNewProperty.not());
    screenScroller.visibleProperty().bind(statusNewProperty.not());

    screenScroller
        .viewportBoundsProperty()
        .addListener(
            (observable, oldValue, newValue) ->
                screenStack.setMinSize(newValue.getWidth(), newValue.getHeight()));

    screenList.setGridLinesVisible(true);

    if (func.getDeviceData() != null) {
      resetScreen();
    } else {
      func.getResetObservable().addListener((change) -> resetScreen());
    }
  }

  private void resetScreen() {
    screenList.getColumnConstraints().clear();
    for (int i = 0; i < func.getMaxColumn(); i++) {
      ColumnConstraints columnConstraints = new ColumnConstraints(200);
      screenList.getColumnConstraints().add(columnConstraints);
    }
    screenList.getRowConstraints().clear();
    for (int i = 0; i < func.getMaxColumn(); i++) {
      RowConstraints rowConstraints = new RowConstraints(160);
      screenList.getRowConstraints().add(rowConstraints);
    }
    statusNewProperty.set(func.getDeviceData().isStatusNew());
    if (func.getDeviceData().isStatusNew()) {
      initializeGraph();
      statusNewProperty.addListener(
          (observable, oldValue, newValue) -> {
            if (oldValue.booleanValue() != newValue.booleanValue() && !newValue) {
              Platform.runLater(this::resetScreenItems);
            }
          });
    } else {
      resetScreenItems();
    }
  }

  private void resetScreenItems() {
    zoominBtn.disableProperty().unbind();
    zoominBtn
        .disableProperty()
        .bind(screenGroup.scaleXProperty().greaterThanOrEqualTo(VideoWallConstants.MAX_SCALE));
    zoomoutBtn.disableProperty().unbind();
    zoomoutBtn
        .disableProperty()
        .bind(screenGroup.scaleXProperty().lessThanOrEqualTo(VideoWallConstants.MIN_SCALE));
    for (int i = 0; i < func.getCaesarCrossScreenData().getTargets().size(); i++) {
      ObjectProperty<VisualEditTerminal> target = func.getCaesarCrossScreenData().getTarget(i);
      if (target.get() != null) {
        VideoData video = func.getCaesarCrossScreenData().getVideo(i);
        int xpos = video.getXpos().get() / 100;
        int ypos = video.getYpos().get() / 100;
        int width = video.getWidth().get() / 100;
        int height = video.getHeight().get() / 100;
        ScreenItem item = createScreenItem(xpos, ypos, target);
        screenList.add(item, xpos, ypos, width, height);
      }
    }
  }

  protected ScreenItem createScreenItem(
      int row, int column, ObjectProperty<VisualEditTerminal> target) {
    return new CaesarScreenItem(row, column, target, model, func, this);
  }

  private void initializeGraph() {
    graph = new IrregularCrossScreenGraph(new DefaultSnapshotGetter(model));
    graph.init();
    graphBox.getChildren().add(graph.getCanvas().getNode());
    HBox.setHgrow(graph.getCanvas().getNode(), Priority.ALWAYS);

    List<Integer> widths = Lists.newArrayList();
    for (int i = 0; i < func.getCrossScreenData().getColumns().get(); i++) {
      widths.add(100);
    }
    List<Integer> heights = Lists.newArrayList();
    for (int i = 0; i < func.getCrossScreenData().getRows().get(); i++) {
      heights.add(100);
    }
    graph.getPageCanvas().setAllPageAreas(PageCanvasUtility.createGridRectangles(widths, heights));
    // 当鼠标释放时才算完成一个动作
    graph
        .getCanvas()
        .getContainer()
        .addEventFilter(
            MouseEvent.MOUSE_PRESSED,
            weakAdapter.wrap((EventHandler<MouseEvent>) (event) -> beginUpdate()));
    graph
        .getCanvas()
        .getContainer()
        .addEventFilter(
            MouseEvent.MOUSE_RELEASED,
            weakAdapter.wrap((EventHandler<MouseEvent>) (event) -> endUpdateClear()));

    zoominBtn.disableProperty().unbind();
    zoominBtn
        .disableProperty()
        .bind(
            graph
                .getCanvas()
                .getScaleProperty()
                .greaterThanOrEqualTo(VideoWallConstants.MAX_SCALE));
    zoomoutBtn.disableProperty().unbind();
    zoomoutBtn
        .disableProperty()
        .bind(graph.getCanvas().getScaleProperty().lessThanOrEqualTo(VideoWallConstants.MIN_SCALE));

    // 监听文本变化
    pageTexts.addListener(
        weakAdapter.wrap(
            (ListChangeListener<PageText>)
                change -> graph.getPageCanvas().setPageTexts(pageTexts)));
    // initVideoGraphBehavior();

    Platform.runLater(
        () -> {
          graph.getPageCanvas().fitToView();

          // 在这里添加cell避免显示异常
          prevVideoDatas = Lists.newArrayList();
          func.getCaesarCrossScreenData().getVideos().clear();
          for (int i = 0; i < func.getMaxRow(); i++) {
            for (int j = 0; j < func.getMaxColumn(); j++) {
              int index = i * func.getMaxColumn() + j;
              if (index < func.getCaesarCrossScreenData().getTargets().size()) {
                if (func.getCaesarCrossScreenData().getTarget(index).get() == null) {
                  func.getCaesarCrossScreenData().getVideos().add(null);
                  prevVideoDatas.add(null);
                } else {
                  VideoData videoData = new VideoData();
                  func.getCaesarCrossScreenData().getVideos().add(videoData);
                  videoData.getSource().set(func.getCaesarCrossScreenData().getTarget(index).get());
                  videoData.getXpos().set(j * 100);
                  videoData.getYpos().set(i * 100);
                  videoData.getWidth().set(100);
                  videoData.getHeight().set(100);
                  // backup videoData
                  VideoData prevVideoData = new VideoData();
                  videoData.copyTo(prevVideoData);
                  prevVideoDatas.add(prevVideoData);
                  videoData.getXpos().addListener((observable, oldVal, newVal) -> {
                    if (!oldVal.equals(newVal)) {
                      needToUpdate = true;
                    }
                  });
                  videoData.getYpos().addListener((observable, oldVal, newVal) -> {
                    if (!oldVal.equals(newVal)) {
                      needToUpdate = true;
                    }
                  });
                  videoData.getWidth().addListener((observable, oldVal, newVal) -> {
                    if (!oldVal.equals(newVal)) {
                      needToUpdate = true;
                    }
                  });
                  videoData.getHeight().addListener((observable, oldVal, newVal) -> {
                    if (!oldVal.equals(newVal)) {
                      needToUpdate = true;
                    }
                  });
                  addVideoCell(videoData);
                }
              }
            }
          }
        });
  }

  //  private void initVideoGraphBehavior() {
  //    graph.cellDeletable().bind(editableProperty);
  //    graph.cellMovable().bind(editableProperty);
  //    graph.cellOrderable().bind(editableProperty);
  //    graph.cellResizable().bind(editableProperty);
  //    graph.cellRotatable().bind(editableProperty);
  //  }

  protected void addVideoCell(VideoObject data) {
    CellObject cellObject = graph.insertCell(data.getName().get(), "", 0, 0, 0, 0, "video", data);
    cellObject.getXProperty().bindBidirectional(data.getXpos());
    cellObject.getYProperty().bindBidirectional(data.getYpos());
    cellObject.getWidthProperty().bindBidirectional(data.getWidth());
    cellObject.getHeightProperty().bindBidirectional(data.getHeight());
  }

  @Override
  public void onConfig() {}

  @Override
  public void saveScenario() {}

  @Override
  public void saveAsScenario() {}

  @Override
  public boolean isConfigable() {
    return false;
  }

  @Override
  public boolean canSaveScenario() {
    return false;
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
      // editableProperty.set(true);
    }
  }

  @Override
  public DeviceControllable getDeviceController() {
    return null;
  }

  @Override
  public void init(VisualEditModel model, VisualEditFunc currentFunction) {
    this.model = model;
    EventBusProvider.getEventBus().register(this);
    if (currentFunction instanceof CaesarIrregularCrossScreenFunc) {
      this.func = (CaesarIrregularCrossScreenFunc) currentFunction;
    }
  }

  @Override
  public boolean isConnetable(VisualEditTerminal tx, VisualEditTerminal rx) {
    if (deviceController == null) {
      return false;
    }
    if (!deviceController.getCaesarUserRight().isCrossScreenConnectable()) {
      return false;
    }

    if (tx != null && rx != null) {
      return !TerminalUtility.getConnectableType(deviceController, rx, tx).isEmpty();
    }
    return true;
  }

  @FXML
  protected void onZoomin(MouseEvent event) {
    if (statusNewProperty.get()) {
      double scale = graph.getCanvas().getScaleProperty().get();
      scale = scale * VideoWallConstants.SCALE_FACTOR;
      if (scale > VideoWallConstants.MAX_SCALE) {
        scale = VideoWallConstants.MAX_SCALE;
      }
      graph.getCanvas().getScaleProperty().set(scale);
    } else {
      screenGroup.setScaleX(screenGroup.getScaleX() * VideoWallConstants.SCALE_FACTOR);
      screenGroup.setScaleY(screenGroup.getScaleY() * VideoWallConstants.SCALE_FACTOR);
    }
  }

  @FXML
  protected void onZoomout(MouseEvent event) {
    if (statusNewProperty.get()) {
      double scale = graph.getCanvas().getScaleProperty().get();
      scale = scale / VideoWallConstants.SCALE_FACTOR;
      if (scale < VideoWallConstants.MIN_SCALE) {
        scale = VideoWallConstants.MIN_SCALE;
      }
      graph.getCanvas().getScaleProperty().set(scale);
    } else {
      screenGroup.setScaleX(screenGroup.getScaleX() / VideoWallConstants.SCALE_FACTOR);
      screenGroup.setScaleY(screenGroup.getScaleY() / VideoWallConstants.SCALE_FACTOR);
    }
  }

  @FXML
  protected void onRestore(MouseEvent event) {
    if (statusNewProperty.get()) {
      graph.getCanvas().getScaleProperty().set(1);
      graph.getPageCanvas().fitToView();
    } else {
      screenGroup.setScaleX(1);
      screenGroup.setScaleY(1);
    }
  }

  @Override
  public void connectForCrossScreen(VisualEditTerminal tx, VisualEditTerminal rx) {
    int sourceIndex = -1;
    VisualEditTerminal realTx = tx;
    if (tx instanceof CaesarCpuTerminalWrapper) {
      realTx = ((CaesarCpuTerminalWrapper) tx).getTerminal();
      sourceIndex = ((CaesarCpuTerminalWrapper) tx).getIndex();
    }
    if (realTx instanceof CaesarCpuTerminal && rx instanceof CaesarConTerminal) {
      CaesarCpuTerminal cpu = (CaesarCpuTerminal) realTx;
      CaesarConTerminal con = (CaesarConTerminal) rx;
      CpuData cpuData = cpu.getCpuData();
      ConsoleData consoleData = con.getConsoleData();
      if (cpuData == null) {
        log.warn("Cpu data is null!");
        return;
      }
      if (consoleData == null) {
        log.warn("Console data is null!");
        return;
      }
      final int sourceIndexFinal = sourceIndex;
      deviceController.execute(
          () -> {
            ExtendedSwitchUtility.fullAccess(
                deviceController.getDataModel(),
                consoleData,
                new CpuDataStateWrapper(cpuData, CpuDataStateWrapper.Access.FULL),
                true);
            if (sourceIndexFinal >= 0) {
              consoleData.setCpuHdmi(
                  sourceIndexFinal == 0 ? DhdmiSelection.HDMI1 : DhdmiSelection.HDMI2);
              try {
                deviceController.getDataModel()
                    .sendConsoleData(Collections.singletonList(consoleData));
              } catch (DeviceConnectionException exc) {
                log.warn("Fail to send console data.", exc);
              } catch (BusyException exc) {
                log.warn("Fail to send console data.", exc);
              }
            }
          });
    } else if (realTx == null && rx instanceof CaesarConTerminal) {
      CaesarConTerminal con = (CaesarConTerminal) rx;
      ConsoleData consoleData = con.getConsoleData();
      deviceController.execute(
          () ->
              ExtendedSwitchUtility.disconnect(deviceController.getDataModel(), consoleData, true));
    }
  }

  @Subscribe
  protected void onFuncBeginUpdate(VisualEditFuncBeginUpdateEvent event) {
    if (event.getFunc() == func) {
      beginUpdate();
    }
  }

  @Subscribe
  protected void onFuncEndUpdate(VisualEditFuncEndUpdateEvent event) {
    if (event.getFunc() == func) {
      endUpdate();
    }
  }

  @Override
  public void close() {
    if (graph != null) {
      graph.destroy();
    }
    EventBusProvider.getEventBus().unregister(this);
  }

  @Override
  public void setStatusNew(boolean status) {
    statusNewProperty.set(status);
  }

  @Override
  public boolean isStatusNew() {
    return statusNewProperty.get();
  }
}
