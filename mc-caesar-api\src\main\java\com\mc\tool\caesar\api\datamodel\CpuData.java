package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.BitFieldEntry;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.api.version.ApiDataConverter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import lombok.Getter;

/**
 * CpuData.
 *
 * @brief cpu的配置数据.
 */
public final class CpuData extends AbstractData implements DataObject, CaesarCommunicatable {

  private static final Logger LOG = Logger.getLogger(CpuData.class.getName());
  public static final String PROPERTY_BASE = "CpuData.";
  public static final String PROPERTY_NAME = "CpuData.Name";
  public static final String PROPERTY_STATUS = "CpuData.Status";
  public static final String PROPERTY_STATUS_ACTIVE = "CpuData.Status.Active";
  public static final String PROPERTY_STATUS_ALLOWPRIVATE = "CpuData.Status.AllowPrivate";
  public static final String PROPERTY_STATUS_FORCEPRIVATE = "CpuData.Status.ForcePrivate";
  public static final String PROPERTY_STATUS_VIRTUAL_OUT = "CpuData.Status.VirtualOut";
  public static final String PROPERTY_STATUS_FIXFRAME = "CpuData.Status.FixFrame";
  public static final String PROPERTY_STATUS_DELETE = "CpuData.Status.Delete";
  public static final String PROPERTY_STATUS_NEW_DATA = "CpuData.Status.NewData";
  public static final String PROPERTY_STATUS_ONLINE = "CpuData.Status.Online";
  public static final String PROPERTY_STATUS_PRIVATE = "CpuData.Status.Private";
  public static final String PROPERTY_STATUS_VIRTUAL = "CpuData.Status.Virtual";
  public static final String PROPERTY_CONSOLE = "CpuData.Console";
  public static final String PROPERTY_REAL_CPU = "CpuData.RealCpu";
  public static final String PROPERTY_ID = "CpuData.ID";
  public static final String PROPERTY_CTRL_USER_INDEX = PROPERTY_BASE + "CtrlUserIndex";
  public static final String PROPERTY_PORTS = "CpuData.Ports";
  public static final String PROPERTY_EXTENDER = "CpuData.Extender";
  public static final String PROPERTY_CPUINDEX = "CpuData.CpuIndex";
  public static final String PROPERTY_CONNECT = "CpuData.Connect";
  public static final String PROPERTY_GROUP_INDEX = "CpuData.GroupIndex";
  public static final String PROPERTY_PDU_OPEN_MODE = "CpuData.PduOpenMode";
  public static final String PROPERTY_PDU_PORT = "CpuData.PduPort";
  public static final String PROPERTY_PDU_IP = "CpuData.PduIp";
  public static final String PROPERTY_DECODER_IP = "CpuData.DecoderIp";
  public static final String PROPERTY_DECODER_PORT = "CpuData.DecoderPort";
  public static final String PROPERTY_DECODER_DEFAULT_GRID_TYPE = "CpuData.DecoderDefaultGridType";

  public static final String PROPERTY_TYPE = "CpuData.Type";

  public static final Map<String, Collection<BitFieldEntry>> BIT_FIELD_MAP;

  /**
   * .
   */
  public enum CpuType {
    DVD, CAMERA, CABLETV, RASPBERRY_PI, COMPUTER, WEB_TV_BOX, WEB_DECODER
  }

  static {
    Collection<BitFieldEntry> bitMapStatus = new HashSet<>();

    bitMapStatus
        .add(new BitFieldEntry(CpuData.PROPERTY_STATUS_ACTIVE, CaesarConstants.Cpu.Status.ACTIVE));
    bitMapStatus.add(new BitFieldEntry(CpuData.PROPERTY_STATUS_ALLOWPRIVATE,
        CaesarConstants.Cpu.Status.ALLOWPRIVATE));
    bitMapStatus.add(new BitFieldEntry(CpuData.PROPERTY_STATUS_FORCEPRIVATE,
        CaesarConstants.Cpu.Status.FORCEPRIVATE));
    bitMapStatus.add(
        new BitFieldEntry(CpuData.PROPERTY_STATUS_FIXFRAME, CaesarConstants.Cpu.Status.FIXFRAME));
    bitMapStatus
        .add(new BitFieldEntry(CpuData.PROPERTY_STATUS_DELETE, CaesarConstants.Cpu.Status.DELETE));
    bitMapStatus
        .add(new BitFieldEntry(CpuData.PROPERTY_STATUS_NEW_DATA, CaesarConstants.Cpu.Status.NEW));
    bitMapStatus.add(
        new BitFieldEntry(CpuData.PROPERTY_STATUS_PRIVATE, CaesarConstants.Cpu.Status.PRIVATE));
    bitMapStatus.add(
        new BitFieldEntry(CpuData.PROPERTY_STATUS_VIRTUAL, CaesarConstants.Cpu.Status.VIRTUAL));

    Map<String, Collection<BitFieldEntry>> bitMaps = new HashMap<>();

    bitMaps.put(CpuData.PROPERTY_STATUS, Collections.unmodifiableCollection(bitMapStatus));

    BIT_FIELD_MAP = Collections.unmodifiableMap(bitMaps);
  }

  @Expose
  private StringProperty nameProperty = new SimpleStringProperty("");
  @Expose
  private String name;
  @Expose
  private BooleanProperty allowPrivateProperty = new SimpleBooleanProperty(false);
  @Expose
  private BooleanProperty forcePrivateProperty = new SimpleBooleanProperty(false);
  @Expose
  private BooleanProperty virtualProperty = new SimpleBooleanProperty(false);
  @Expose
  private BooleanProperty virtualOutProperty = new SimpleBooleanProperty(false);
  @Expose
  private int status = 0;
  @Expose
  private int console = 0; // 连接的获取full access 或者 private access 的con的oid + 1
  @Expose
  private int realCpu = 0; // 绑定的real cpu的oid + 1，
  @Expose
  private IntegerProperty idProperty = new SimpleIntegerProperty(0); //int id = 0; // cpu的id
  @Expose
  private int id = 0;
  @Expose
  @Getter
  private int ctrlUserIndex = 0; // 强制用户登录后做连接之后保存的用户索引，表明该cpu的归属用户
  @Expose
  private int ports = 0; // 关联的extender的个数
  @Expose
  private final int[] extender = createArrayInt(CaesarConstants.Extender.CPUCON); // @unknown
  @Expose
  @Getter
  private ScreenOffsetConfig screenOffsetConfig = new ScreenOffsetConfig();
  @Expose
  @Getter
  private int txGroupIndex; // TX分组的index,0表示没有,组index+1
  @Expose
  @Getter
  private IntegerProperty openModeProperty = new SimpleIntegerProperty(0);
  @Expose
  @Getter
  private int openMode; // 0：禁用   1：网卡唤醒  2：控制PC电源
  @Expose
  @Getter
  private int pduPort; // 电源PDU的端口号,从1开始有效，0表示没绑定PDU
  @Expose
  private byte[] pduIp = new byte[] {0, 0, 0, 0}; // 电源PDU的IP地址

  /**
   * Constructor.
   */
  public CpuData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager, int oid,
                 String fqn) {
    super(pcs, configDataManager, oid, fqn);
    initCommitRollback();
  }

  /**
   * 设置属性.
   *
   * @param property 属性名称.
   * @param value    属性值
   */
  public void setProperty(String property, Object value) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (property.equals(PROPERTY_NAME) && value instanceof String) {
      setName((String) value);
    } else if (property.equals(PROPERTY_STATUS_ALLOWPRIVATE) && value instanceof Boolean) {
      setStatusAllowPrivate((Boolean) value);
    } else if (property.equals(PROPERTY_STATUS_FORCEPRIVATE) && value instanceof Boolean) {
      setStatusForcePrivate((Boolean) value);
    } else if (property.equals(ExtenderData.PROPERTY_NAME) && value instanceof String) {
      if (getExtenderData(0) != null) {
        getExtenderData(0).setName((String) value);
      }
    } else if (property.equals(PROPERTY_STATUS_VIRTUAL) && value instanceof Boolean) {
      setStatusVirtual((Boolean) value);
    } else if (property.equals(PROPERTY_STATUS_VIRTUAL_OUT) && value instanceof Boolean) {
      setStatusVirtualOut((Boolean) value);
    } else if (property.equals(PROPERTY_ID) && value instanceof Number) {
      setId(((Number) value).intValue());
    } else if (CpuData.PROPERTY_GROUP_INDEX.equals(property)) {
      setTxGroupIndex((Integer) value);
    }
  }

  @Override
  public void initDefaults() {
    super.initDefaults();
    setConsoleData(null);
    for (int i = 0; i < this.extender.length; i++) {
      setExtenderData(i, null);
    }
    setStatus(0);
    setId(0);
    setName("");
    setPorts(0);
    setRealCpuData(null);
  }

  public int getConsole() {
    return this.console;
  }

  /**
   * .
   */
  public void setConsole(int console) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.console;
    this.console = console;
    firePropertyChange(CpuData.PROPERTY_CONSOLE, oldValue, this.console);
  }

  public int getCpuIndex(int idx) {
    return this.extender[idx] >> 16;
  }

  /**
   * .
   */
  public void setCpuIndex(int idx, int cpuIndex) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.extender[idx] & 0xFFFF0000;
    this.extender[idx] &= 0xFFFF;
    this.extender[idx] |= cpuIndex << 16;
    firePropertyChange(CpuData.PROPERTY_CPUINDEX, oldValue, cpuIndex, idx);
  }

  public int getExtender(int idx) {
    return this.extender[idx] & 0xFFFF;
  }

  /**
   * .
   */
  public void setExtender(int idx, int extender) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.extender[idx] & 0xFFFF;
    this.extender[idx] &= 0xFFFF0000;
    this.extender[idx] |= extender;
    firePropertyChange(CpuData.PROPERTY_EXTENDER, oldValue, extender, idx);
  }

  @Override
  public int getId() {
    return this.id;
  }

  /**
   * .
   */
  public void setId(int id) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldId = this.id;
    this.id = id;
    PlatformUtility.runInFxThread(() -> idProperty.set(id));
    firePropertyChange(CpuData.PROPERTY_ID, oldId, id);
  }

  public IntegerProperty getIdProperty() {
    return this.idProperty;
  }

  /**
   * .
   */
  public void setCtrlUserIndex(int ctrlUserIndex) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.ctrlUserIndex;
    this.ctrlUserIndex = ctrlUserIndex;
    firePropertyChange(CpuData.PROPERTY_CTRL_USER_INDEX, oldValue, this.ctrlUserIndex);
  }

  /**
   * .
   */
  public void setTxGroupIndex(int txGroupIndex) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.txGroupIndex;
    this.txGroupIndex = txGroupIndex;
    firePropertyChange(CpuData.PROPERTY_GROUP_INDEX, oldValue, this.txGroupIndex);
  }

  /**
   * .
   */
  public void setOpenMode(int openMode) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.openMode;
    this.openMode = openMode;
    if (openMode != openModeProperty.get()) {
      PlatformUtility.runInFxThread(() -> openModeProperty.set(openMode));
    }
    firePropertyChange(CpuData.PROPERTY_PDU_OPEN_MODE, oldValue, this.openMode);
  }

  /**
   * .
   */
  public void setPduPort(int pduPort) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.pduPort;
    this.pduPort = pduPort;
    firePropertyChange(CpuData.PROPERTY_PDU_PORT, oldValue, this.pduPort);
  }

  /**
   * 获取pduIp数组.
   */
  public byte[] getPduIp() {
    if (this.pduIp != null) {
      return this.pduIp.clone();
    }
    return new byte[0];
  }

  public String getPduIpString() {
    return IpUtil.getAddressString(getPduIp());
  }

  public String getMonitorDecoderIpString() {
    return IpUtil.getAddressString(getScreenOffsetConfig().getMonitorDecoderInfo().getIp());
  }

  public int getMonitorDecoderPort() {
    return getScreenOffsetConfig().getMonitorDecoderInfo().getPort();
  }

  public MonitorDecoderInfo.GridType getMonitorDecoderDefaultGridType() {
    return getScreenOffsetConfig().getMonitorDecoderInfo().getDefaultGridType();
  }


  /**
   * .
   */
  public void setPduIp(byte[] pduIp) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (pduIp != null) {
      byte[] oldValue = this.pduIp;
      this.pduIp = Arrays.copyOf(pduIp, pduIp.length);
      firePropertyChange(CpuData.PROPERTY_PDU_IP, oldValue, pduIp);
    }
  }

  /**
   * 设置监控解码器IP地址.
   */
  public void setMonitorDecoderIp(byte[] decoderIp) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (decoderIp != null) {
      byte[] oldValue = this.getScreenOffsetConfig().getMonitorDecoderInfo().getIp();
      getScreenOffsetConfig().getMonitorDecoderInfo().setIp(decoderIp);
      firePropertyChange(CpuData.PROPERTY_DECODER_IP, oldValue, decoderIp);
    }
  }

  /**
   * 设置监控解码器端口.
   */
  public void setMonitorDecoderPort(int decoderPort) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.getScreenOffsetConfig().getMonitorDecoderInfo().getPort();
    getScreenOffsetConfig().getMonitorDecoderInfo().setPort(decoderPort);
    firePropertyChange(CpuData.PROPERTY_DECODER_PORT, oldValue, this.pduPort);
  }

  /**
   * 设置监控解码器网格类型.
   */
  public void setMonitorDecoderDefaultGridType(MonitorDecoderInfo.GridType gridType) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    MonitorDecoderInfo monitorDecoderInfo = getScreenOffsetConfig().getMonitorDecoderInfo();
    MonitorDecoderInfo.GridType oldValue = monitorDecoderInfo.getDefaultGridType();
    monitorDecoderInfo.setDefaultGridType(gridType);
    firePropertyChange(CpuData.PROPERTY_DECODER_DEFAULT_GRID_TYPE, oldValue, gridType);
  }

  @Override
  public String getName() {
    return name;
  }

  /**
   * .
   */
  public void setName(String name) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    String oldName = this.name;
    this.name = name;
    PlatformUtility.runInFxThread(() -> nameProperty.set(name));
    firePropertyChange(CpuData.PROPERTY_NAME, oldName, name);
  }

  public StringProperty getNameProperty() {
    return this.nameProperty;
  }

  public BooleanProperty getAllowPrivateProperty() {
    return this.allowPrivateProperty;
  }

  public BooleanProperty getForcePrivateProperty() {
    return this.forcePrivateProperty;
  }

  public BooleanProperty getVirtualProperty() {
    return this.virtualProperty;
  }

  public BooleanProperty getVirtualOutProperty() {
    return this.virtualOutProperty;
  }

  public int getPorts() {
    return this.ports;
  }

  /**
   * .
   */
  public void setPorts(int ports) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.ports;
    this.ports = ports;
    firePropertyChange(CpuData.PROPERTY_PORTS, oldValue, this.ports);
  }

  public boolean isExtenderFull() {
    return getPorts() >= CaesarConstants.Extender.CPUCON;
  }

  /**
   * 是否绑定了指定的外设.
   *
   * @param extenderData 外设数据.
   * @return 如果绑定了，返回true
   */
  public boolean hasExtender(ExtenderData extenderData) {
    for (int i = 0; i < CaesarConstants.Extender.CPUCON; i++) {
      if (getExtenderData(i) == extenderData) {
        return true;
      }
    }
    return false;
  }

  /**
   * 添加绑定的外设.
   *
   * @param extenderData 外设数据
   */
  public void addExtender(ExtenderData extenderData) {
    if (extenderData == null) {
      return;
    }
    if (hasExtender(extenderData)) {
      return;
    }
    for (int i = 0; i < CaesarConstants.Extender.CPUCON; i++) {
      if (getExtenderData(i) == null) {
        setExtenderData(i, extenderData);
        setPorts(getPorts() + 1);
        break;
      }
    }
  }

  /**
   * 删除外设绑定.
   *
   * @param extenderData 要删除的外设
   */
  public void removeExtender(ExtenderData extenderData) {
    if (extenderData == null) {
      return;
    }
    for (int i = 0; i < CaesarConstants.Extender.CPUCON; i++) {
      if (getExtenderData(i) == extenderData) {
        setExtenderData(i, null);
        setPorts(getPorts() - 1);
      }
    }
  }

  public int getRealCpu() {
    return this.realCpu;
  }

  /**
   * .
   */
  public void setRealCpu(int realCpu) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.realCpu;
    this.realCpu = realCpu;
    firePropertyChange(CpuData.PROPERTY_REAL_CPU, oldValue, this.realCpu);
  }

  public int getStatus() {
    return this.status;
  }

  /**
   * .
   */
  public void setStatus(int status) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    final int oldValue = this.status;
    this.status = status;
    PlatformUtility.runInFxThread(() -> {
      allowPrivateProperty.set(isStatusAllowPrivate());
      forcePrivateProperty.set(isStatusForcePrivate());
      virtualProperty.set(isStatusVirtual());
      virtualOutProperty.set(isStatusVirtualOut());
    });
    firePropertyChange(CpuData.PROPERTY_STATUS, oldValue, this.status);
  }


  /**
   * 获取端口.
   *
   * @return 端口
   */
  public int getPort() {
    if (getExtenderData(0) != null) {
      return getExtenderData(0).getPort();
    } else {
      return 0;
    }
  }

  public boolean isStatusActive() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Cpu.Status.ACTIVE);
  }

  public boolean isStatusAllowPrivate() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Cpu.Status.ALLOWPRIVATE);
  }

  public boolean isStatusForcePrivate() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Cpu.Status.FORCEPRIVATE);
  }

  public boolean isStatusVirtualOut() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Cpu.Status.VIRTUAL_OUT);
  }

  public boolean isStatusFixFrame() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Cpu.Status.FIXFRAME);
  }

  public boolean isStatusDelete() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Cpu.Status.DELETE);
  }

  public boolean isStatusNewData() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Cpu.Status.NEW);
  }

  public boolean isStatusPrivate() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Cpu.Status.PRIVATE);
  }

  public boolean isStatusVirtual() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Cpu.Status.VIRTUAL);
  }

  public void setStatusActive(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Cpu.Status.ACTIVE));
  }

  public void setStatusAllowPrivate(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Cpu.Status.ALLOWPRIVATE));
  }

  public void setStatusForcePrivate(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Cpu.Status.FORCEPRIVATE));
  }

  public void setStatusVirtualOut(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Cpu.Status.VIRTUAL_OUT));
  }

  public void setStatusFixFrame(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Cpu.Status.FIXFRAME));
  }

  public void setStatusDelete(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Cpu.Status.DELETE));
  }

  public void setStatusNewData(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Cpu.Status.NEW));
  }

  public void setStatusPrivate(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Cpu.Status.PRIVATE));
  }

  public void setStatusVirtual(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.Cpu.Status.VIRTUAL));
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (CpuData.PROPERTY_NAME.equals(propertyName)) {
      setName((String) value);
    } else if (CpuData.PROPERTY_STATUS.equals(propertyName)) {
      setStatus((Integer) value);
    } else if (CpuData.PROPERTY_CONSOLE.equals(propertyName)) {
      setConsole((Integer) value);
    } else if (CpuData.PROPERTY_REAL_CPU.equals(propertyName)) {
      setRealCpu((Integer) value);
    } else if (CpuData.PROPERTY_ID.equals(propertyName)) {
      setId((Integer) value);
    } else if (CpuData.PROPERTY_PORTS.equals(propertyName)) {
      setPorts((Integer) value);
    } else if (CpuData.PROPERTY_EXTENDER.equals(propertyName)) {
      setExtender(indizes[0], (Integer) value);
    } else if (CpuData.PROPERTY_CPUINDEX.equals(propertyName)) {
      setCpuIndex(indizes[0], (Integer) value);
    } else if (CpuData.PROPERTY_GROUP_INDEX.equals(propertyName)) {
      setTxGroupIndex((Integer) value);
    }
  }

  @Override
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    ApiDataConverter<CpuData> converter =
        getConfigDataManager().getConfigMetaData().getUtilVersion().getDataConverter(CpuData.class);
    converter.writeData(this, cfgWriter);
  }

  @Override
  public void readData(CfgReader cfgReader) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    ApiDataConverter<CpuData> converter =
        getConfigDataManager().getConfigMetaData().getUtilVersion().getDataConverter(CpuData.class);
    converter.readData(this, cfgReader);
  }

  public boolean isAvailable() {
    return isStatusActive() && (isStatusVirtual() || 0 != getPorts() && 0 == getRealCpu());
  }

  public boolean isReal() {
    return !isStatusVirtual();
  }

  public boolean isVirtual() {
    return isStatusVirtual();
  }

  public ConsoleData getConsoleData() {
    return getConfigDataManager().getConsoleData(this.console - 1);
  }

  public void setConsoleData(ConsoleData consoleData) {
    setConsole(null == consoleData ? 0 : consoleData.getOid() + 1);
  }

  public ExtenderData getExtenderData(int idx) {
    return getConfigDataManager().getExtenderData(this.extender[idx] - 1);
  }

  /**
   * .
   */
  public Collection<ExtenderData> getExtenderDatas() {
    Collection<ExtenderData> extenderDatas = new ArrayList<>();
    for (int i = 0; i < this.extender.length; i++) {
      ExtenderData extenderData = getExtenderData(i);
      if (null != extenderData) {
        extenderDatas.add(extenderData);
      }
    }
    return extenderDatas;
  }

  public void setExtenderData(int idx, ExtenderData extenderData) {
    setExtender(idx, null == extenderData ? 0 : extenderData.getOid() + 1);
  }

  public CpuData getRealCpuData() {
    return getConfigDataManager().getCpuData(getRealCpu() - 1);
  }

  /**
   * .
   */
  public void setRealCpuData(CpuData cpuData) {
    setRealCpu(null == cpuData ? 0 : cpuData.getOid() + 1);
  }

  /**
   * .
   */
  public void removeFromAssociatedExtenderDatas(boolean dependencyCommit, Threshold threshold) {
    for (ExtenderData extenderData : getExtenderDatas()) {
      if (equals(extenderData.getCpuData())) {
        extenderData.setThreshold(threshold);
        extenderData.setCpuData(null);
        if (dependencyCommit) {
          extenderData.commit(threshold);
        }
        Threshold oldThreshold = extenderData.getThreshold();
        extenderData.setThreshold(oldThreshold);
      } else {
        LOG.log(Level.CONFIG,
            "{0} has {1} attached and it is not configured be attached to the CpuData.",
            new Object[] {getFqn(), extenderData.getFqn()});
      }
    }
  }

  /**
   * .
   */
  public void removeFromAssociatedConsoleDatas(boolean dependencyCommit, Threshold threshold) {
    for (ConsoleData consoleData : getConfigDataManager().getActiveConsoles()) {
      boolean hasChanged = false;

      consoleData.setThreshold(threshold);
      if (consoleData.isNoAccess(this)) {
        consoleData.setNoAccess(this, true);
        hasChanged = true;
      }
      if (consoleData.isVideoAccess(this)) {
        consoleData.setVideoAccess(this, false);
        hasChanged = true;
      }
      if (consoleData.isStatusPrivate()) {
        consoleData.setStatusPrivate(false);
        hasChanged = true;
      }
      for (int i = 0; i < CaesarConstants.Cpu.FAVORITE; i++) {
        if (equals(consoleData.getFavoriteData(i))) {
          consoleData.setFavoriteData(i, null);
          hasChanged = true;
        }
      }
      if (equals(consoleData.getCpuData())) {
        consoleData.setCpuData(null);
        consoleData.setStatusVideoOnly(false);
        hasChanged = true;
      }
      if (hasChanged && dependencyCommit) {
        consoleData.commit(threshold);
      }
      Threshold oldThreshold = consoleData.getThreshold();
      consoleData.setThreshold(oldThreshold);
    }
  }


  /**
   * .
   */
  public void removeFromAssociatedUserDatas(boolean commitDependencies, Threshold threshold) {
    for (UserData userData : getConfigDataManager().getActiveUsers()) {
      boolean hasChanged = false;

      userData.setThreshold(threshold);
      if (userData.isNoAccess(this)) {
        userData.setNoAccess(this, true);
        hasChanged = true;
      }
      if (userData.isVideoAccess(this)) {
        userData.setVideoAccess(this, false);
        hasChanged = true;
      }
      for (int i = 0; i < CaesarConstants.Cpu.FAVORITE; i++) {
        if (equals(userData.getFavoriteData(i))) {
          userData.setFavoriteData(i, null);
          hasChanged = true;
        }
      }
      if (hasChanged && commitDependencies) {
        userData.commit(threshold);
      }
      Threshold oldThreshold = userData.getThreshold();
      userData.setThreshold(oldThreshold);
    }
  }

  @Override
  public void delete(boolean internalCommit) {
    setStatusDelete(true);
    removeFromAssociatedConsoleDatas(internalCommit, getThreshold());
    removeFromAssociatedExtenderDatas(internalCommit, getThreshold());
    removeFromAssociatedUserDatas(internalCommit, getThreshold());
    if (null != getRealCpuData()) {
      getRealCpuData().setThreshold(getThreshold());
      getRealCpuData().setRealCpuData(null);
      if (internalCommit) {
        getRealCpuData().commit(getThreshold());
      }
      Threshold cpuOldThreshold = getRealCpuData().getThreshold();
      getRealCpuData().setThreshold(cpuOldThreshold);
    }
    initDefaults();
    if (internalCommit) {
      commit(getThreshold());
    }
  }

  @Override
  public void delete() {
    delete(true);
  }

  /**
   * 是否在线.
   */
  public boolean isOnline() {
    ExtenderData extenderData = getExtenderData(0);
    return isStatusActive() && extenderData != null
        && (extenderData.getPort() != 0 || extenderData.getRdPort() != 0);
  }
}

