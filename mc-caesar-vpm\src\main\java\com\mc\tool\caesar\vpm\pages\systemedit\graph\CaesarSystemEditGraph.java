package com.mc.tool.caesar.vpm.pages.systemedit.graph;

import com.mc.graph.interfaces.SkinFactory;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.graph.SystemEditGraph;

/**
 * .
 */
public class CaesarSystemEditGraph extends SystemEditGraph {

  public CaesarSystemEditGraph(SystemEditControllable controllable) {
    super(controllable);
  }

  @Override
  protected SkinFactory createSkinFactory() {
    return new CaesarSeSkinFactory();
  }
}
