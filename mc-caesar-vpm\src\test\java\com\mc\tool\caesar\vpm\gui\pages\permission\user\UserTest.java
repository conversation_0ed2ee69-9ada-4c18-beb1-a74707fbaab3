package com.mc.tool.caesar.vpm.gui.pages.permission.user;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.caesar.vpm.pages.permissionconfiguration.CaesarPermissionConfigurationPage;
import com.mc.tool.caesar.vpm.pages.permissionconfiguration.UserTableData;
import java.io.File;
import java.io.IOException;
import javafx.scene.control.Button;
import javafx.scene.control.Spinner;
import javafx.scene.control.TableView;
import javafx.scene.control.TextField;
import javafx.scene.input.KeyCode;
import org.junit.Assert;
import org.junit.Test;
import org.testfx.api.FxRobot;

/**
 * .
 */
public class UserTest extends AppGuiTestBase {

  @Override
  public void beforeAll() {}

  @Test
  public void testAdmin() {
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      clickOn("#" + CaesarPermissionConfigurationPage.NAME);
      clickOn(GuiTestConstants.TAB_USER);
      try {
        Thread.sleep(1000);
      } catch (InterruptedException exc) {
        Assert.fail("Expect no exception!");
      }
      // 编辑admin账户
      clickOn(
          lookupTableCell(
                  lookup(GuiTestConstants.USER_TABLE_VIEW).match((item) -> isShowing(item)),
                  0,
                  GuiTestConstants.USER_EDIT_COL)
              .lookup(".button"));
      // 用户名应该不能修改
      FxRobot window = targetWindow(GuiTestConstants.WINDOW_EDIT_USER);
      TextField nameField = window.lookup(GuiTestConstants.USER_USER_NAME_FIELD).query();
      Assert.assertTrue(nameField.isDisabled());
      //
      clickOn(GuiTestConstants.BUTTON_CANCEL);
      // 不能删除
      clickOn("admin");
      Button deleteButton = lookup(GuiTestConstants.USER_DELETE_BTN).query();
      Assert.assertTrue(deleteButton.isDisabled());

      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testAddAndRemove() {
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      clickOn("#" + CaesarPermissionConfigurationPage.NAME);
      clickOn(GuiTestConstants.TAB_USER);
      clickOn(GuiTestConstants.USER_NEW_BTN);

      FxRobot window = targetWindow(GuiTestConstants.WINDOW_NEW_USER);
      Button confirmButton = window.lookup(GuiTestConstants.BUTTON_CONFIRM).query();

      Assert.assertTrue(confirmButton.isDisabled());
      // 添加验证密码才能确定
      String pwd = "123";
      window.clickOn(GuiTestConstants.USER_PWD_FIELD);
      window.write(pwd);
      Assert.assertTrue(confirmButton.isDisabled());
      window.clickOn(GuiTestConstants.USER_RE_PWD_FIELD);
      window.write(pwd);
      Assert.assertFalse(confirmButton.isDisabled());
      // 不匹配不能确定
      window.eraseText(pwd.length());
      window.write(pwd + "1");
      Assert.assertTrue(confirmButton.isDisabled());
      window.eraseText(pwd.length() + 1);
      window.write(pwd);
      // 用户名不能为空
      window.clickOn(GuiTestConstants.USER_USER_NAME_FIELD);
      window.type(KeyCode.END);
      window.eraseText(50);
      Assert.assertTrue(confirmButton.isDisabled());
      // 不能为admin
      window.write("admin");
      Assert.assertTrue(confirmButton.isDisabled());
      window.eraseText(5);
      // 不能太长
      StringBuilder name = new StringBuilder();
      for (int i = 0; i < CaesarConstants.NAME_LEN; i++) {
        name.append("a");
        window.write("a");
      }
      Assert.assertFalse(confirmButton.isDisabled());
      window.write("b");
      Assert.assertTrue(confirmButton.isDisabled());
      window.eraseText(1);

      // 检查鼠标速度范围
      window.clickOn(GuiTestConstants.USER_MOUSE_SPEED);
      Spinner<Integer> mouseSpeed = window.lookup(GuiTestConstants.USER_MOUSE_SPEED).query();
      int value = mouseSpeed.getValue();
      Assert.assertTrue(
          value <= CaesarConstants.MAX_MOUSE_SPEED && value >= CaesarConstants.MIN_MOUSE_SPEED);
      for (int i = 0;
          i < CaesarConstants.MAX_MOUSE_SPEED - CaesarConstants.MIN_MOUSE_SPEED + 5;
          i++) {
        window.clickOn(mouseSpeed.lookup(GuiTestConstants.SPINNER_BUTTON_INCREMENT));
      }
      Assert.assertEquals(CaesarConstants.MAX_MOUSE_SPEED, mouseSpeed.getValue().intValue());
      for (int i = 0;
          i < CaesarConstants.MAX_MOUSE_SPEED - CaesarConstants.MIN_MOUSE_SPEED + 5;
          i++) {
        window.clickOn(mouseSpeed.lookup(GuiTestConstants.SPINNER_BUTTON_DECREMENT));
      }
      Assert.assertEquals(CaesarConstants.MIN_MOUSE_SPEED, mouseSpeed.getValue().intValue());

      clickOn(GuiTestConstants.BUTTON_CONFIRM);

      TableView<UserTableData> tableView =
          lookup(GuiTestConstants.USER_TABLE_VIEW).match((item) -> isShowing(item)).query();
      Assert.assertEquals(2, tableView.getItems().size());
      UserTableData userTableData = tableView.getItems().get(1);
      Assert.assertEquals(name.toString(), userTableData.getName());
      // 编辑
      clickOn(
          lookupTableCell(
                  lookup(GuiTestConstants.USER_TABLE_VIEW).match((item) -> isShowing(item)),
                  1,
                  GuiTestConstants.USER_EDIT_COL)
              .lookup(".button"));
      window = targetWindow(GuiTestConstants.WINDOW_EDIT_USER);
      window.clickOn(GuiTestConstants.BUTTON_CONFIRM);
      // 新建第二个
      clickOn(GuiTestConstants.USER_NEW_BTN);

      window = targetWindow(GuiTestConstants.WINDOW_NEW_USER);
      confirmButton = window.lookup(GuiTestConstants.BUTTON_CONFIRM).query();
      window.clickOn(GuiTestConstants.USER_PWD_FIELD);
      window.write(pwd);
      window.clickOn(GuiTestConstants.USER_RE_PWD_FIELD);
      window.write(pwd);
      window.clickOn(GuiTestConstants.USER_USER_NAME_FIELD);
      type(KeyCode.END);
      eraseText(50);
      write(name.toString());
      // 不能重名
      Assert.assertTrue(confirmButton.isDisabled());
      // 取消
      window.clickOn(GuiTestConstants.BUTTON_CANCEL);
      Assert.assertEquals(2, tableView.getItems().size());
      // 删除
      clickOn(name.toString());
      clickOn(GuiTestConstants.USER_DELETE_BTN);
      window = targetWindow(GuiTestConstants.WINDOW_DELETE_USER);
      window.clickOn(GuiTestConstants.BUTTON_CONFIRM);
      Assert.assertEquals(1, tableView.getItems().size());
      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }
}
