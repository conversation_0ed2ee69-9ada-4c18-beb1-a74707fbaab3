package com.mc.tool.caesar.vpm.util;

import com.google.common.collect.BiMap;
import com.google.gson.GsonBuilder;
import com.google.gson.graph.GraphAdapterBuilder;
import com.google.gson.typeadapters.BimapCreator;
import com.google.gson.typeadapters.RuntimeTypeAdapterFactory;
import com.mc.graph.connector.AnomymousConnectorObject;
import com.mc.graph.connector.IntegerConnectorIdentifier;
import com.mc.graph.connector.StringConnectorIdentifier;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCrossScreenFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarGridLineTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarIrregularCrossScreenFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMultiScreenFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbRxTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbTxTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarVideoWallFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.AudioGroup;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.CpuGroup;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.framework.systemedit.datamodel.DefaultSeatFunc;
import com.mc.tool.framework.systemedit.datamodel.InnerVisualEditGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditMatrix;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.datamodel.preview.VideoPreviewFunc;
import com.mc.tool.framework.systemedit.datamodel.preview.VideoThumbnailFunc;
import org.hildan.fxgson.FxGsonBuilder;


/**
 * .
 */
public class CaesarModelJsonBuilder {

  /**
   * 获取model序列化的builder.
   *
   * @return gsonbuilder
   */
  public static GsonBuilder getBuilder() {
    RuntimeTypeAdapterFactory<VisualEditNode> factory =
        RuntimeTypeAdapterFactory.of(VisualEditNode.class)
            .registerSubtype(VisualEditGroup.class)
            .registerSubtype(CaesarMatrix.class)
            .registerSubtype(CaesarConTerminal.class)
            .registerSubtype(CaesarUsbTxTerminal.class)
            .registerSubtype(CaesarUsbRxTerminal.class)
            .registerSubtype(CaesarGridLineTerminal.class)
            .registerSubtype(CaesarCpuTerminal.class)
            .registerSubtype(InnerVisualEditGroup.class)
            .registerSubtype(CaesarVideoWallFunc.class)
            .registerSubtype(VpGroup.class)
            .registerSubtype(VideoPreviewFunc.class)
            .registerSubtype(VideoThumbnailFunc.class)
            .registerSubtype(CaesarCrossScreenFunc.class)
            .registerSubtype(CaesarIrregularCrossScreenFunc.class)
            .registerSubtype(CaesarMultiScreenFunc.class)
            .registerSubtype(DefaultSeatFunc.class)
            .registerSubtype(CpuGroup.class)
            .registerSubtype(AudioGroup.class);
    RuntimeTypeAdapterFactory<ConnectorIdentifier> connectorFactory =
        RuntimeTypeAdapterFactory.of(ConnectorIdentifier.class)
            .registerSubtype(IntegerConnectorIdentifier.class)
            .registerSubtype(StringConnectorIdentifier.class)
            .registerSubtype(AnomymousConnectorObject.class);
    GsonBuilder builder =
        new FxGsonBuilder()
            .builder()
            .excludeFieldsWithoutExposeAnnotation()
            .setPrettyPrinting()
            .serializeSpecialFloatingPointValues()
            .enableComplexMapKeySerialization()
            .registerTypeAdapter(BiMap.class, new BimapCreator())
            .registerTypeAdapterFactory(connectorFactory);
    new GraphAdapterBuilder()
        .addDelegateAdapterFactory(factory)
        .addType(VisualEditNode.class)
        .addType(VisualEditTerminal.class)
        .addType(VisualEditMatrix.class)
        .addType(CaesarMatrix.class)
        .addType(CaesarConTerminal.class)
        .addType(CaesarUsbTxTerminal.class)
        .addType(CaesarUsbRxTerminal.class)
        .addType(CaesarGridLineTerminal.class)
        .addType(CaesarCpuTerminal.class)
        .addType(CaesarVideoWallFunc.class)
        .addType(VpGroup.class)
        .addType(CaesarCrossScreenFunc.class)
        .addType(CaesarIrregularCrossScreenFunc.class)
        .addType(CaesarMultiScreenFunc.class)
        .addType(DefaultSeatFunc.class)
        .addType(InnerVisualEditGroup.class)
        .addType(VideoThumbnailFunc.class)
        .addType(VideoPreviewFunc.class)
        .addType(VisualEditGroup.class)
        .addType(VisualEditModel.class)
        .addType(CpuGroup.class)
        .addType(AudioGroup.class)
        .registerOn(builder);
    return builder;
  }
}
