* {
  MC_COLOR: #f08519;
}

.tab-pane {
  -fx-tab-min-width: 90px;
  -fx-tab-min-height: 28px;
  -fx-background-color: white;
}

.tab-pane {
  -fx-tab-min-height: 40px;
}

.tab-pane > .tab-header-area {
  -fx-padding: 0;
}

.tab-pane .tab-header-background {
  -fx-background-color: #cccccc, white;
  -fx-background-insets: 0, 0 0 1 0;
}

.tab-pane .tab {
  -fx-background-color: white;
  -fx-background-image: null;
}

.tab-pane .tab .tab-label {
  -fx-font-size: 12;
  -fx-text-fill: black;
}

.tab-pane .tab:selected {
  -fx-background-color: MC_COLOR, white;
  -fx-background-insets: 0, 0 0 2 0;
  -fx-background-image: null;
}

.tab-pane .tab:selected .focus-indicator {
  -fx-border-width: 0;
}

.tab-pane .tab:selected .tab-label {
  -fx-text-fill: MC_COLOR;
}

.tab-pane .tab:hover .tab-label {
  -fx-text-fill: MC_COLOR;
}