package com.mc.tool.caesar.vpm.pages.systemedit.controller;

import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.TerminalPropertyConverter;
import com.mc.tool.framework.systemedit.controller.NodeSearchBase;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.util.Collection;
import javafx.util.StringConverter;

/**
 * .
 */
public class CaesarNodeSearcher extends NodeSearchBase {

  public CaesarNodeSearcher(Collection<VisualEditNode> roots) {
    super(roots);
  }

  @Override
  public StringConverter<VisualEditNode> createNodeStringConverter() {
    return new CaesarNodeStringConverter();
  }

  static class CaesarNodeStringConverter extends StringConverter<VisualEditNode> {
    @Override
    public String toString(VisualEditNode object) {
      if (object instanceof VisualEditTerminal) {
        VisualEditTerminal terminal = (VisualEditTerminal) object;
        return String.format(
            "%d|%s|%s|%s|%s",
            TerminalPropertyConverter.getIdProperty(terminal).get(),
            TerminalPropertyConverter.getNameProperty(terminal).get(),
            TerminalPropertyConverter.getPortProperty(terminal).get(),
            TerminalPropertyConverter.getTypeProperty(terminal).get(),
            TerminalPropertyConverter.getSerialProperty(terminal).get());
      } else {
        return object.getName();
      }
    }

    @Override
    public VisualEditNode fromString(String string) {
      return null;
    }
  }
}
