package com.mc.tool.caesar.vpm.pages.operation.irregularcrossscreen.view;

import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.view.CaesarOperationFuncPane;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarIrregularCrossScreenFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbTxTerminal;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.operation.view.VideoSourceTree;
import com.mc.tool.framework.operation.view.VideoSourceTree.SourceMode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.datamodel.preview.VideoPreviewFunc;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import java.beans.PropertyChangeListener;
import java.io.IOException;
import java.net.URL;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.function.UnaryOperator;
import javafx.application.Platform;
import javafx.beans.binding.Bindings;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.control.ComboBox;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextField;
import javafx.scene.control.TextFormatter;
import javafx.scene.control.TextFormatter.Change;
import javafx.scene.layout.Pane;
import javafx.scene.layout.VBox;
import javafx.util.Callback;
import javafx.util.StringConverter;
import javafx.util.converter.NumberStringConverter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarIrregularCrossScreenFuncPane extends CaesarOperationFuncPane
    implements Initializable, ViewControllable {

  @FXML private VideoSourceTree sourceTree;

  @FXML private TextField crossScreenName;

  @FXML private ComboBox<VisualEditTerminal> controlCombo;
  @FXML private TextField displayTimeText;
  @FXML private VBox videoPreviewContainer;

  @FXML private TableView<ObjectProperty<VisualEditTerminal>> rxList;
  @FXML private TableColumn<ObjectProperty<VisualEditTerminal>, Integer> rxListIndexColumn;
  @FXML private TableColumn<ObjectProperty<VisualEditTerminal>, String> rxListNameColumn;

  @FXML private Button commitButton;
  @FXML private Button cancelButton;

  private final CaesarIrregularCrossScreenFunc func;
  protected CaesarDeviceController deviceController;
  private IrregularCrossScreenOperationView operationView = null;

  /**
   * Constructor.
   *
   * @param func func
   * @param model model
   * @param deviceController device controller
   */
  public CaesarIrregularCrossScreenFuncPane(
      CaesarIrregularCrossScreenFunc func,
      VisualEditModel model,
      CaesarDeviceController deviceController) {
    super(model);
    this.func = func;
    this.deviceController = deviceController;

    URL location =
        Thread.currentThread()
            .getContextClassLoader()
            .getResource(
                "com/mc/tool/caesar/vpm/pages/operation/"
                    + "irregularcrossscreen/irregularcrossscreen_func_panel.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    loader.setRoot(this);
    loader.setController(this);
    loader.setResources(CaesarI18nCommonResource.getResourceBundle());
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load crossscreen_func_panel.xml", exception);
    }
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
      operationView.getOperationControllable().setDeviceController(getDeviceController());
    }
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceController;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    operationView = new IrregularCrossScreenOperationView(systemEditModel, func);
    operationView.getOperationControllable().setDeviceController(getDeviceController());
    getFunctionViewContent().set(operationView.getView());

    sourceTree.init(
        systemEditModel,
        new SimpleObjectProperty<>(func),
        SourceMode.ALL,
        (terminal) ->
            terminal.isOnline() && terminal.isTx() && !(terminal instanceof CaesarUsbTxTerminal));
    // video preview
    videoPreviewContainer.managedProperty().bind(videoPreviewContainer.visibleProperty());
    videoPreviewContainer
        .visibleProperty()
        .bind(
            Bindings.size(
                    systemEditModel
                        .getAllOnlineFuncs()
                        .filtered((func) -> func instanceof VideoPreviewFunc))
                .greaterThan(0));
    // 名称
    crossScreenName.textProperty().bindBidirectional(func.nameProperty());
    // 控制源
    controlCombo
        .valueProperty()
        .bindBidirectional(func.getCaesarCrossScreenData().getControlSource());
    controlCombo.setConverter(new VisualEditTerminalStringConverter());
    controlCombo.setItems(func.getAllTerminalChild());
    // 红框时间
    UnaryOperator<Change> integerFilter =
        change -> {
          String newText = change.getControlNewText();
          if (newText.matches("[0-9]{1,}") && Integer.parseInt(newText) <= 65535) {
            return change;
          }
          return null;
        };
    displayTimeText.setTextFormatter(new TextFormatter<>(integerFilter));
    Bindings.bindBidirectional(
        displayTimeText.textProperty(),
        func.getCaesarCrossScreenData().getDisplayTime(),
        new NumberStringConverter());
    // 管控端列表
    rxListIndexColumn.setCellFactory(new IndexCellFactory());
    rxListNameColumn.setCellValueFactory(
        (feat) -> {
          StringProperty nameProperty = new SimpleStringProperty();
          if (feat.getValue() != null) {
            ObjectProperty<VisualEditTerminal> value = feat.getValue();
            if (value.get() != null) {
              nameProperty.bind(value.get().nameProperty());
            }
            value.addListener(
                (obs, oldVal, newVal) -> {
                  if (newVal == null) {
                    nameProperty.unbind();
                    nameProperty.set("");
                  } else {
                    nameProperty.bind(newVal.nameProperty());
                  }
                });
          }
          return nameProperty;
        });
    rxList.getItems().setAll(func.getCaesarCrossScreenData().getTargets());
    //

    commitButton.managedProperty().bind(commitButton.visibleProperty());
    cancelButton.managedProperty().bind(cancelButton.visibleProperty());
    updateStatus();
    deviceController
        .getDataModel()
        .addPropertyChangeListener(
            MultiScreenData.PROPERTY_STATUS,
            weakAdapter.wrap(
                (PropertyChangeListener) evt -> Platform.runLater(this::updateStatus)));
    updateVideoSourceList(func);
  }

  protected void updateStatus() {
    boolean isStatusNew = func.getDeviceData() != null && func.getDeviceData().isStatusNew();
    commitButton.setVisible(isStatusNew);
    cancelButton.setVisible(isStatusNew);
    controlCombo.setDisable(!isStatusNew);
    displayTimeText.setDisable(!isStatusNew);
  }

  @FXML
  protected void onSwitch() {
    if (switchFunctional != null) {
      switchFunctional.onSwitch();
    }
  }

  @FXML
  protected void onSaveScenario() {}

  @FXML
  protected void onCommit() {
    MultiScreenData screenData = func.getDeviceData();
    // 新建时才commit
    if (screenData != null && screenData.isStatusNew()) {
      Optional<ButtonType> result =
          ViewUtility.getConfirmResultFromDialog(
              this.getScene().getWindow(),
              CaesarI18nCommonResource.getString("cross_screen.commit.warn_title"),
              CaesarI18nCommonResource.getString("cross_screen.commit.warn_content"));
      if (result.isPresent() && result.get() == ButtonType.YES) {
        deviceController.execute(
            () -> {
              func.commit();
              screenData.setStatusNew(false);
              operationView.getOperationControllable().setStatusNew(false);
              deviceController.addMultiScreenData(screenData);
            });
      }
    }
  }

  @FXML
  protected void onCancel() {
    MultiScreenData screenData = func.getDeviceData();
    // 新建时才取消
    if (screenData != null && screenData.isStatusNew()) {
      deviceController.execute(
          () -> {
            screenData.setStatusActive(false);
            screenData.setStatusNew(false);
          });
      systemEditModel.deleteGroup(func); // TODO 监听删除func后销毁func
    }
  }

  @Override
  public Pane getPreviewContainer() {
    return videoPreviewContainer;
  }

  static class IndexCellFactory
      implements Callback<
          TableColumn<ObjectProperty<VisualEditTerminal>, Integer>,
          TableCell<ObjectProperty<VisualEditTerminal>, Integer>> {

    @Override
    public TableCell<ObjectProperty<VisualEditTerminal>, Integer> call(
        TableColumn<ObjectProperty<VisualEditTerminal>, Integer> param) {
      TableCell<ObjectProperty<VisualEditTerminal>, Integer> cell = new TableCell<>();
      cell.textProperty()
          .bind(
              Bindings.createStringBinding(
                  () -> {
                    if (cell.isEmpty()) {
                      return null;
                    } else {
                      return (cell.getIndex() + 1) + "";
                    }
                  },
                  cell.indexProperty(),
                  cell.emptyProperty()));
      return cell;
    }
  }

  static class VisualEditTerminalStringConverter extends StringConverter<VisualEditTerminal> {

    @Override
    public String toString(VisualEditTerminal object) {
      return object.getName();
    }

    @Override
    public VisualEditTerminal fromString(String string) {
      // TODO Auto-generated method stub
      return null;
    }
  }

  @Override
  public void close() {
    super.close();
    sourceTree.destroy();
    operationView.getOperationControllable().close();
  }
}
