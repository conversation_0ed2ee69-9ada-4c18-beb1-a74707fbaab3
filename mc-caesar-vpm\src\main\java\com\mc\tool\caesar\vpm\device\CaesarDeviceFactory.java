package com.mc.tool.caesar.vpm.device;

import com.google.common.base.Charsets;
import com.google.common.io.CharStreams;
import com.mc.common.io.ResourceFile;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.DemoSwitchDataModel;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.vpm.devices.CaesarDemoDeviceController;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.util.SaveStatusUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.concurrent.ExecutionException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarDeviceFactory {

  /**
   * 创建设备控制器.
   *
   * @param ip 设备ip
   * @return 返回设备控制器
   */
  public static CaesarDeviceController createDevice(String ip, String user, String pwd)
      throws ConfigException, BusyException {
    CaesarSwitchDataModel model = new CaesarSwitchDataModel();
    model.setConnection(ip, true);
    return new CaesarDeviceController(model, user, pwd, ip);
  }

  /** 创建文件读取本地文件的设备控制器. */
  public static CaesarDeviceController createDevice(ResourceFile file, String user, String pwd) {
    if (!file.exists()) {
      log.warn(file.getAbsolutePath() + " not exist!");
      return null;
    }

    CaesarDemoDataModelFactory factory =
        InjectorProvider.getInjector().getInstance(CaesarDemoDataModelFactory.class);
    if (file.getName().endsWith(CaesarConstants.CFG_FILE_EXTENSION)) {
      DemoSwitchDataModel model = factory.create(true);
      CaesarDemoDeviceController deviceController =
          new CaesarDemoDeviceController(model, user, pwd, file.getName(), null);
      try {
        boolean result =
            deviceController
                .submit(
                    () -> {
                      model.initDefaults();
                      return model.readDemoData(file);
                    })
                .get();
        return result ? deviceController : null;
      } catch (ExecutionException | InterruptedException exception) {
        log.warn("Fail to read cfg file!", exception);
        return null;
      }
    } else if (file.getName().endsWith(CaesarConstants.CFGX_FILE_EXTENSION)) {
      DemoSwitchDataModel model = factory.create(true);
      CaesarDemoDeviceController deviceController =
          new CaesarDemoDeviceController(model, user, pwd, file.getName(), null);
      try {
        boolean result = deviceController.submit(() -> model.readCfgxFile(file)).get();
        return result ? deviceController : null;
      } catch (ExecutionException | InterruptedException exception) {
        log.warn("Fail to read cfgx file.", exception);
        return null;
      }
    } else if (file.getName().endsWith(".status")) {
      DemoSwitchDataModel model = factory.create(false);
      CaesarDemoDeviceController controller =
          new CaesarDemoDeviceController(
              model, user, pwd, file.getName(), getModelFromStatus(file));
      try {
        boolean result =
            controller
                .submit(
                    () -> {
                      model.initDefaults();
                      return model.readStatus(file);
                    })
                .get();
        return result ? controller : null;
      } catch (ExecutionException | InterruptedException exception) {
        log.warn("Fail to read status.", exception);
        return null;
      }
    } else {
      log.warn("Unsupported file : {}", file.getName());
      return null;
    }
  }

  protected static String getModelFromStatus(ResourceFile file) {
    InputStream fis = null;
    ZipInputStream zis;
    try {
      fis = file.createInputStream();
      zis = new ZipInputStream(new BufferedInputStream(fis));

      ZipEntry entry;
      String result = null;
      while ((entry = zis.getNextEntry()) != null) {
        if (entry.getName().equals(SaveStatusUtility.STATUS_MODEL_NAME)) {
          result = CharStreams.toString(new InputStreamReader(zis, Charsets.UTF_8));
          break;
        }
      }
      return result;
    } catch (IOException exception) {
      log.warn("Read demo error!", exception);
      return null;
    } finally {
      if (fis != null) {
        try {
          fis.close();
        } catch (IOException exception) {
          log.warn("Fail to close stream!");
        }
      }
    }
  }
}
