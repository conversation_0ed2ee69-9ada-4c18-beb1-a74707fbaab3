package com.mc.tool.caesar.api.utils;

import com.google.common.collect.Lists;
import com.mc.tool.caesar.api.CaesarConfigDataModel;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.AbstractData;
import com.mc.tool.caesar.api.datamodel.AccessData;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.MultiviewData;
import com.mc.tool.caesar.api.datamodel.MultiviewSource;
import com.mc.tool.caesar.api.datamodel.SwitchData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.interfaces.ConfigDataModel;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class ExtendedSwitchUtility {

  /**
   * .
   */
  public static void fullAccess(ConfigDataModel model, ConsoleData consoleData,
                                CpuDataStateWrapper cdsw, boolean immediately) {
    if (consoleData == null || cdsw == null || cdsw.getCpuData() == null || model == null) {
      return;
    }
    CpuData cpuData = cdsw.getCpuData();
    if (cpuData.isStatusPrivate() && !consoleData.equals(cpuData.getConsoleData())) {
      log.warn("fullAccess: CPU {} is in private access. Switching not allowed!",
          cpuData.getName());
      return;
    }
    if (model instanceof CaesarConfigDataModel) {
      SwitchData switchData =
          ((CaesarConfigDataModel) model).getConfigData().getSystemConfigData().getSwitchData();
      if (cpuData.getConsoleData() != null && !cpuData.getConsoleData().equals(consoleData)) {
        if (switchData.isCpuConnect() || switchData.isConDisconnect()) {
          if (switchData.isConDisconnect()) {
            Collection<ConsoleData> consoleDatas =
                ((CaesarConfigDataModel) model).getConfigDataManager().getActiveConsoles();
            for (final ConsoleData cd : consoleDatas) {
              if (cpuData.equals(cd.getCpuData())) {
                if (cd.isStatusVideoOnly()) {
                  cd.setStatusVideoOnly(false);
                }
                cd.setCpuData(null);
                cd.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
                if (model instanceof CaesarSwitchDataModel && immediately) {
                  try {
                    ((CaesarSwitchDataModel) model).sendCpuConsoleConnection(null, cd);
                    ((CaesarSwitchDataModel) model).commit();
                  } catch (ConfigException ex) {
                    log.warn("reset of connection failed");
                  } catch (BusyException ex) {
                    log.warn("matrix system is busy");
                  }
                } else {
                  ((CaesarConfigDataModel) model).commit(AbstractData.THRESHOLD_UI_LOCAL_CHANGES);
                }
              }
            }
          }
          fullAccess(model, consoleData, cpuData, immediately);
        } else {
          log.warn("CPU " + cpuData.getName() + " is in full access. Switching not allowed!");
        }
      } else {
        fullAccess(model, consoleData, cpuData, immediately);
      }
    }
  }

  private static void fullAccess(ConfigDataModel model, final ConsoleData consoleData,
                                 final CpuData cpuData, boolean immediately) {
    if (model == null) {
      return;
    }
    CpuData connectedCpuData = consoleData.getCpuData();
    if (connectedCpuData != null && connectedCpuData.getConsoleData() != null
        && connectedCpuData.getConsoleData().equals(consoleData)) {
      connectedCpuData.setStatusPrivate(false);
      connectedCpuData.setConsoleData(null);
      connectedCpuData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
    }
    cpuData.setConsoleData(consoleData);
    cpuData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
    consoleData.setStatusVideoOnly(false);
    consoleData.setStatusPrivate(false);
    consoleData.setCpuData(cpuData);
    consoleData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
    if (model instanceof CaesarSwitchDataModel && immediately) {
      try {
        if (consoleData.getMultiviewChannel() > 0) {
          ((CaesarSwitchDataModel) model).switchMultiviewTx(cpuData, consoleData,
              consoleData.getMultiviewChannel(), 0);
        } else {
          ((CaesarSwitchDataModel) model).sendCpuConsoleConnection(cpuData, consoleData);
        }
        ((CaesarSwitchDataModel) model).reloadCpuConsoleMatrix();
        ((CaesarSwitchDataModel) model).commit();
      } catch (ConfigException ex) {
        log.warn("", ex);
      } catch (BusyException ex) {
        log.warn("matrix system is busy");
      }
    } else {
      ((CaesarConfigDataModel) model).commit(AbstractData.THRESHOLD_UI_LOCAL_CHANGES);
    }
  }

  /**
   * 全连接.
   */
  public static void asyncFullAccessWithoutReload(ConfigDataModel model, ConsoleData consoleData,
                                                  CpuDataStateWrapper cdsw, boolean immediately) {
    if (consoleData == null || cdsw == null || cdsw.getCpuData() == null || model == null) {
      return;
    }
    CpuData cpuData = cdsw.getCpuData();
    if (cpuData.isStatusPrivate() && !consoleData.equals(cpuData.getConsoleData())) {
      log.warn("fullAccess: CPU {} is in private access. Switching not allowed!",
          cpuData.getName());
      return;
    }
    if (model instanceof CaesarConfigDataModel) {
      SwitchData switchData =
          ((CaesarConfigDataModel) model).getConfigData().getSystemConfigData().getSwitchData();
      if (cpuData.getConsoleData() != null && !cpuData.getConsoleData().equals(consoleData)) {
        if (switchData.isCpuConnect() || switchData.isConDisconnect()) {
          if (switchData.isConDisconnect()) {
            Collection<ConsoleData> consoleDatas =
                ((CaesarConfigDataModel) model).getConfigDataManager().getActiveConsoles();
            for (final ConsoleData cd : consoleDatas) {
              if (cpuData.equals(cd.getCpuData())) {
                if (cd.isStatusVideoOnly()) {
                  cd.setStatusVideoOnly(false);
                }
                cd.setCpuData(null);
                cd.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
                if (model instanceof CaesarSwitchDataModel && immediately) {
                  try {
                    ((CaesarSwitchDataModel) model).sendCpuConsoleConnection(null, cd);
                    ((CaesarSwitchDataModel) model).commit();
                  } catch (ConfigException ex) {
                    log.warn("reset of connection failed");
                  } catch (BusyException ex) {
                    log.warn("matrix system is busy");
                  }
                } else {
                  ((CaesarConfigDataModel) model).commit(AbstractData.THRESHOLD_UI_LOCAL_CHANGES);
                }
              }
            }
          }
          asyncFullAccessWithoutReload(model, consoleData, cpuData, immediately);
        } else {
          log.warn("CPU " + cpuData.getName() + " is in full access. Switching not allowed!");
        }
      } else {
        asyncFullAccessWithoutReload(model, consoleData, cpuData, immediately);
      }
    }
  }

  private static void asyncFullAccessWithoutReload(ConfigDataModel model,
                                                   final ConsoleData consoleData,
                                                   final CpuData cpuData, boolean immediately) {
    if (model == null) {
      return;
    }
    CpuData connectedCpuData = consoleData.getCpuData();
    if (connectedCpuData != null && connectedCpuData.getConsoleData() != null
        && connectedCpuData.getConsoleData().equals(consoleData)) {
      connectedCpuData.setStatusPrivate(false);
      connectedCpuData.setConsoleData(null);
      connectedCpuData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
    }
    cpuData.setConsoleData(consoleData);
    cpuData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
    consoleData.setStatusVideoOnly(false);
    consoleData.setStatusPrivate(false);
    consoleData.setCpuData(cpuData);
    consoleData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
    if (model instanceof CaesarSwitchDataModel && immediately) {
      try {
        ((CaesarSwitchDataModel) model).sendCpuConsoleConnection(cpuData, consoleData);
        ((CaesarSwitchDataModel) model).commit();
      } catch (ConfigException ex) {
        log.warn("", ex);
      } catch (BusyException ex) {
        log.warn("matrix system is busy");
      }
    } else {
      ((CaesarConfigDataModel) model).commit(AbstractData.THRESHOLD_UI_LOCAL_CHANGES);
    }
  }

  /**
   * 批量视频连接.
   *
   * @param model       model
   * @param connections 需要做的连接，如果CpuData为null，那么为断开.
   */
  public static void batchVideoAccessConnect(CaesarSwitchDataModel model,
                                             Map<ConsoleData, CpuData> connections) {
    Map<ConsoleData, CpuData> newConnections = new HashMap<>();
    // 筛选
    for (Map.Entry<ConsoleData, CpuData> connection : connections.entrySet()) {
      CpuData cpuData = connection.getValue();
      ConsoleData consoleData = connection.getKey();
      SwitchData switchData = model.getConfigData().getSystemConfigData().getSwitchData();
      if (cpuData != null && cpuData.getConsoleData() != null) {
        if (switchData.isCpuWatch() || cpuData.getConsoleData().equals(consoleData)) {
          if (cpuData.getConsoleData().equals(consoleData)) {
            cpuData.setConsoleData(null);
            cpuData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
          }
          newConnections.put(consoleData, cpuData);
        } else {
          log.warn(
              "videoAccess: CPU {} is in full access and 'enable video sharing' "
                  + "is not enabled. Switching not allowed!",
              cpuData.getName());
        }
      } else {
        newConnections.put(consoleData, cpuData);
      }
    }
    // 连接
    for (Map.Entry<ConsoleData, CpuData> connection : newConnections.entrySet()) {
      CpuData cpuData = connection.getValue();
      ConsoleData consoleData = connection.getKey();

      if (consoleData.getCpuData() != null && consoleData.getCpuData().isStatusPrivate()) {
        consoleData.getCpuData().setStatusPrivate(false);
        consoleData.getCpuData().commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
      }
      consoleData.setCpuData(cpuData);
      consoleData.setStatusPrivate(false);
      consoleData.setStatusVideoOnly(true);
      consoleData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
      model.commit();
    }
    try {
      model.getController().setCpuConsoleConnectionBlockByVideoMode(newConnections);
    } catch (DeviceConnectionException | BusyException | ConfigException exception) {
      log.warn("Fail to set connection block by video mode.", exception);
    }
  }

  /**
   * .
   */
  public static void asyncVideoAccessWidthoutReload(ConfigDataModel model, ConsoleData consoleData,
                                                    CpuDataStateWrapper cdsw) {
    if (consoleData == null || cdsw == null || cdsw.getCpuData() == null || model == null) {
      return;
    }
    final CpuData cpuData = cdsw.getCpuData();
    if (cpuData.isStatusPrivate() && !consoleData.equals(cpuData.getConsoleData())) {
      log.warn("videoAccess: CPU {} is in private access. Switching not allowed!",
          cpuData.getName());
      return;
    }
    if (model instanceof CaesarConfigDataModel) {
      SwitchData switchData =
          ((CaesarConfigDataModel) model).getConfigData().getSystemConfigData().getSwitchData();
      if (cpuData.getConsoleData() != null) {
        if (switchData.isCpuWatch() || cpuData.getConsoleData().equals(consoleData)) {
          if (cpuData.getConsoleData().equals(consoleData)) {
            cpuData.setConsoleData(null);
            cpuData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
          }
          asyncVideoAccessWidthoutReload(model, consoleData, cpuData);
        } else {
          log.warn(
              "videoAccess: CPU {} is in full access and 'enable video sharing' "
                  + "is not enabled. Switching not allowed!",
              cpuData.getName());
        }
      } else {
        asyncVideoAccessWidthoutReload(model, consoleData, cpuData);
      }
    }
  }

  private static void asyncVideoAccessWidthoutReload(ConfigDataModel model,
                                                     final ConsoleData consoleData, final CpuData cpuData) {
    if (model == null) {
      return;
    }
    if (consoleData.getCpuData() != null && consoleData.getCpuData().isStatusPrivate()) {
      consoleData.getCpuData().setStatusPrivate(false);
      consoleData.getCpuData().commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
    }
    consoleData.setCpuData(cpuData);
    consoleData.setStatusPrivate(false);
    consoleData.setStatusVideoOnly(true);
    consoleData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
    try {
      ((CaesarSwitchDataModel) model).sendCpuConsoleConnection(null, consoleData);
      ((CaesarSwitchDataModel) model).videoConnect(consoleData, cpuData);
      ((CaesarSwitchDataModel) model).commit();
    } catch (ConfigException ex) {
      log.warn("", ex);
    } catch (BusyException ex) {
      log.warn("matrix system is busy");
    }
  }

  /**
   * .
   */
  public static void videoAccess(ConfigDataModel model, ConsoleData consoleData,
                                 CpuDataStateWrapper cdsw, boolean immediately) {
    if (consoleData == null || cdsw == null || cdsw.getCpuData() == null || model == null) {
      return;
    }
    final CpuData cpuData = cdsw.getCpuData();
    if (cpuData.isStatusPrivate() && !consoleData.equals(cpuData.getConsoleData())) {
      log.warn("videoAccess: CPU {} is in private access. Switching not allowed!",
          cpuData.getName());
      return;
    }
    if (model instanceof CaesarConfigDataModel) {
      SwitchData switchData =
          ((CaesarConfigDataModel) model).getConfigData().getSystemConfigData().getSwitchData();
      if (cpuData.getConsoleData() != null) {
        if (switchData.isCpuWatch() || cpuData.getConsoleData().equals(consoleData)) {
          if (cpuData.getConsoleData().equals(consoleData)) {
            cpuData.setConsoleData(null);
            cpuData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
            if (!(model instanceof CaesarSwitchDataModel) || !immediately) {
              ((CaesarConfigDataModel) model).commit(AbstractData.THRESHOLD_UI_LOCAL_CHANGES);
            }
          }
          videoAccess(model, consoleData, cpuData, immediately);
        } else {
          log.warn("videoAccess: CPU " + cpuData.getName()
              + " is in full access and 'enable video sharing' "
              + "is not enabled. Switching not allowed!");
        }
      } else {
        videoAccess(model, consoleData, cpuData, immediately);
      }
    }
  }

  private static void videoAccess(ConfigDataModel model, final ConsoleData consoleData,
                                  final CpuData cpuData, boolean immediately) {
    if (model == null) {
      return;
    }
    if (consoleData.getCpuData() != null && consoleData.getCpuData().isStatusPrivate()) {
      consoleData.getCpuData().setStatusPrivate(false);
      consoleData.getCpuData().commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
    }
    consoleData.setCpuData(cpuData);
    consoleData.setStatusPrivate(false);
    consoleData.setStatusVideoOnly(true);
    consoleData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
    if (model instanceof CaesarSwitchDataModel && immediately) {
      try {
        ((CaesarSwitchDataModel) model).sendCpuConsoleConnection(null, consoleData);
        if (consoleData.getMultiviewChannel() > 0) {
          ((CaesarSwitchDataModel) model).switchMultiviewTx(cpuData, consoleData,
              consoleData.getMultiviewChannel(), 1);
        } else {
          ((CaesarSwitchDataModel) model).videoConnect(consoleData, cpuData);
        }
        ((CaesarSwitchDataModel) model).reloadCpuConsoleMatrix();
        ((CaesarSwitchDataModel) model).commit();
      } catch (BusyException ex) {
        log.warn("matrix system is busy", ex);
      } catch (Exception ex) {
        log.warn("", ex);
      }
    } else {
      ((CaesarConfigDataModel) model).commit(AbstractData.THRESHOLD_UI_LOCAL_CHANGES);
    }
  }

  /**
   * .
   */
  public static void privateMode(ConfigDataModel model, ConsoleData consoleData,
                                 CpuDataStateWrapper cdsw, boolean immediately) {
    if (consoleData == null || cdsw == null || cdsw.getCpuData() == null || model == null) {
      return;
    }
    CpuData cpuData = cdsw.getCpuData();
    if (cpuData.isStatusPrivate() && model instanceof CaesarSwitchDataModel) {
      log.warn("privateMode: CPU " + cpuData.getName()
          + " is in private access. Switching not allowed!");
      return;
    }
    if (model instanceof CaesarConfigDataModel) {
      SwitchData switchData =
          ((CaesarConfigDataModel) model).getConfigData().getSystemConfigData().getSwitchData();
      if (cpuData.getConsoleData() != null && !cpuData.getConsoleData().equals(consoleData)) {
        if (switchData.isCpuConnect() || switchData.isConDisconnect()) {
          if (switchData.isConDisconnect()) {
            Collection<ConsoleData> consoleDatas =
                ((CaesarConfigDataModel) model).getConfigDataManager().getActiveConsoles();
            for (final ConsoleData cd : consoleDatas) {
              if (cpuData.equals(cd.getCpuData())) {
                if (cd.isStatusVideoOnly()) {
                  cd.setStatusVideoOnly(false);
                }
                cd.setCpuData(null);
                cd.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
                if (model instanceof CaesarSwitchDataModel && immediately) {
                  try {
                    ((CaesarSwitchDataModel) model).sendCpuConsoleConnection(null, cd);
                    ((CaesarSwitchDataModel) model).commit();
                  } catch (ConfigException ex) {
                    log.warn("privateMode: reset of connection failed");
                  } catch (BusyException ex) {
                    log.warn("matrix system is busy");
                  }
                } else {
                  ((CaesarConfigDataModel) model).commit(AbstractData.THRESHOLD_UI_LOCAL_CHANGES);
                }
              }
            }
          }
          privateMode((CaesarConfigDataModel) model, consoleData, cpuData, immediately);
        } else {
          log.warn("privateMode: CPU " + cpuData.getName()
              + " is in full access. Switching not allowed!");
        }
      } else {
        privateMode((CaesarConfigDataModel) model, consoleData, cpuData, immediately);
      }
    }
  }

  private static void privateMode(CaesarConfigDataModel model, final ConsoleData consoleData,
                                  final CpuData cpuData, boolean immediately) {
    if (model == null) {
      return;
    }
    CpuData connectedCpuData = consoleData.getCpuData();
    if (connectedCpuData != null && connectedCpuData.getConsoleData() != null
        && connectedCpuData.getConsoleData().equals(consoleData)) {
      connectedCpuData.setStatusPrivate(false);
      connectedCpuData.setConsoleData(null);
      connectedCpuData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
    }
    Collection<ConsoleData> activeConsoles = model.getConfigDataManager().getActiveConsoles();
    for (ConsoleData activeConsoleData : activeConsoles) {
      if (cpuData.equals(activeConsoleData.getCpuData())) {
        activeConsoleData.setCpuData(null);
        activeConsoleData.setStatusVideoOnly(false);
        activeConsoleData.setStatusPrivate(false);
        activeConsoleData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
      }
    }
    cpuData.setStatusPrivate(true);
    cpuData.setConsoleData(consoleData);
    cpuData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
    CpuData realCpuData = cpuData.getRealCpuData();
    if (realCpuData != null) {
      realCpuData.setStatusPrivate(true);
      realCpuData.setConsoleData(consoleData);
    }
    consoleData.setStatusVideoOnly(false);
    consoleData.setStatusPrivate(true);
    consoleData.setCpuData(cpuData);
    consoleData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
    if (model instanceof CaesarSwitchDataModel && immediately) {
      CaesarSwitchDataModel dataModel = (CaesarSwitchDataModel) model;
      try {
        dataModel.sendCpuConsolePrivate(cpuData, consoleData);
        dataModel.reloadCpuConsoleMatrix();
        dataModel.commit();
      } catch (ConfigException ex) {
        log.warn("", ex);
      } catch (BusyException ex) {
        log.warn("matrix system is busy");
      }
    } else {
      model.commit(AbstractData.THRESHOLD_UI_LOCAL_CHANGES);
    }
  }

  /**
   * .
   */
  public static void disconnect(CaesarConfigDataModel model, final ConsoleData consoleData,
                                boolean immediately) {
    if (consoleData == null || model == null) {
      return;
    }
    CpuData cpuData = consoleData.getCpuData();
    if (cpuData != null && consoleData.equals(cpuData.getConsoleData())) {
      cpuData.setStatusPrivate(false);
      cpuData.setConsoleData(null);
      cpuData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
      CpuData realCpuData = cpuData.getRealCpuData();
      if (realCpuData != null) {
        realCpuData.setStatusPrivate(false);
        realCpuData.setConsoleData(null);
      }
    }
    consoleData.setStatusVideoOnly(false);
    consoleData.setStatusPrivate(false);
    consoleData.setCpuData(null);
    consoleData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
    if (model instanceof CaesarSwitchDataModel && immediately) {
      CaesarSwitchDataModel dataModel = (CaesarSwitchDataModel) model;
      try {
        dataModel.sendCpuConsoleConnection(null, consoleData);
        dataModel.reloadCpuConsoleMatrix();
        dataModel.commit();
      } catch (ConfigException ex) {
        log.warn("", ex);
      } catch (BusyException ex) {
        log.warn("matrix system is busy");
      }
    } else {
      model.commit(AbstractData.THRESHOLD_UI_LOCAL_CHANGES);
    }
  }

  /**
   * .
   */
  public static void disconnect(CaesarConfigDataModel model, CpuData cpuData, boolean immediately) {
    if (cpuData == null || model == null) {
      return;
    }
    cpuData.setStatusPrivate(false);
    cpuData.setConsoleData(null);
    cpuData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
    CpuData realCpuData = cpuData.getRealCpuData();
    if (realCpuData != null) {
      realCpuData.setStatusPrivate(false);
      realCpuData.setConsoleData(null);
    }
    final Map<ConsoleData, CpuData> disconnectedConsoles = new HashMap<>();
    for (ConsoleData consoleData : model.getConfigDataManager().getActiveConsoles()) {
      CpuData connectedCpuData = consoleData.getCpuData();
      if (cpuData.equals(connectedCpuData)) {
        consoleData.setStatusVideoOnly(false);
        consoleData.setStatusPrivate(false);
        consoleData.setCpuData(null);
        consoleData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
        disconnectedConsoles.put(consoleData, null);
      }
    }
    if (model instanceof CaesarSwitchDataModel && immediately) {
      CaesarSwitchDataModel dataModel = (CaesarSwitchDataModel) model;
      try {
        dataModel.sendCpuConsoleBlock(disconnectedConsoles,
            Collections.emptyMap(), Collections.emptyMap());
        dataModel.reloadCpuConsoleMatrix();
        dataModel.commit();
      } catch (ConfigException ex) {
        log.warn("", ex);
      } catch (BusyException ex) {
        log.warn("matrix system is busy");
      }
    } else {
      model.commit(AbstractData.THRESHOLD_UI_LOCAL_CHANGES);
    }
  }

  /**
   * 异步断开不重载.
   *
   * @param model       data model
   * @param consoleData console
   * @param cdsw        state
   */
  public static void asyncDisconnectWithoutReload(CaesarSwitchDataModel model,
                                                  final ConsoleData consoleData, DisconnectCpuDataStateWrapper cdsw) {
    if (consoleData == null || cdsw == null || model == null) {
      return;
    }
    CpuData cpuData = consoleData.getCpuData();
    if (cpuData != null && consoleData.equals(cpuData.getConsoleData())) {
      cpuData.setStatusPrivate(false);
      cpuData.setConsoleData(null);
      cpuData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
      CpuData realCpuData = cpuData.getRealCpuData();
      if (realCpuData != null) {
        realCpuData.setStatusPrivate(false);
        realCpuData.setConsoleData(null);
      }
    }
    consoleData.setStatusVideoOnly(false);
    consoleData.setStatusPrivate(false);
    consoleData.setCpuData(null);
    consoleData.commit(AbstractData.THRESHOLD_LOCAL_CHANGES);
    try {
      model.sendCpuConsoleConnection(null, consoleData);
      model.commit();
    } catch (ConfigException ex) {
      log.warn("", ex);
    } catch (BusyException ex) {
      log.warn("matrix system is busy");
    }
  }

  /**
   * 返回CON的所有切换类型.
   *
   * @return 如果是普通CON，返回一个元素的List，如果是多画面CON，返回多个元素的List
   */
  public static List<SwitchType> getAllSwitchType(ConsoleData consoleData, CaesarSwitchDataModel sdm) {
    List<SwitchType> switchTypes = new ArrayList<>();
    if (consoleData != null && sdm != null) {
      if (consoleData.isMultiview()) {
        MultiviewData multiviewData = consoleData.getMultiviewData();
        if (multiviewData != null) {
          for (int i = 0; i < multiviewData.getSourceCount(); i++) {
            MultiviewSource source = multiviewData.getSource(i);
            SwitchType type = SwitchType.NOT_SWITCHED;
            if (source != null && source.getCpuData() != null) {
              if (consoleData.equals(source.getCpuData().getConsoleData())) {
                if (hasFullSharedAccess(sdm, consoleData, source.getCpuData())) {
                  type = SwitchType.FULL_SHARED;
                } else if (consoleData.isStatusPrivate()) {
                  type = SwitchType.PRIVATE;
                } else {
                  type = SwitchType.FULL;
                }
              } else if (source.getCpuData() != null && !source.isStatusVideoOnly()) {
                type = SwitchType.SHARED;
              } else {
                type = SwitchType.VIDEO;
              }
            }
            switchTypes.add(type);
          }
        }
      } else {
        CpuData cpuData = consoleData.getCpuData();
        SwitchType type = SwitchType.NOT_SWITCHED;
        if (cpuData != null) {
          if (consoleData.equals(cpuData.getConsoleData())) {
            if (hasFullSharedAccess(sdm, consoleData, cpuData)) {
              type = SwitchType.FULL_SHARED;
            } else if (consoleData.isStatusPrivate()) {
              type = SwitchType.PRIVATE;
            } else {
              type = SwitchType.FULL;
            }
          } else if (consoleData.getCpuData() != null && !consoleData.isStatusVideoOnly()) {
            type = SwitchType.SHARED;
          } else {
            type = SwitchType.VIDEO;
          }
        }
        switchTypes.add(type);
      }
    }
    return switchTypes;
  }

  /**
   * .
   */
  public static List<SwitchType> getSwitchType(ExtenderData extenderData, CaesarSwitchDataModel sdm) {
    if (extenderData == null || sdm == null) {
      return Lists.newArrayList(SwitchType.NOT_SWITCHED);
    }

    if (extenderData.isConType() || extenderData.isCustConType() || extenderData.isUniConType()) {
      return getAllSwitchType(extenderData.getConsoleData(), sdm);
    }
    if (extenderData.isCpuType() || extenderData.isCustCpuType() || extenderData.isUniCpuType()) {
      return Lists.newArrayList(getSwitchType(extenderData.getCpuData(), sdm));
    }
    return Lists.newArrayList(SwitchType.NOT_SWITCHED);
  }

  private static SwitchType getSwitchType(CpuData realCpuData, CaesarSwitchDataModel sdm) {
    if (realCpuData != null && sdm != null) {
      if (realCpuData.isStatusPrivate()) {
        return SwitchType.PRIVATE;
      }
      if (realCpuData.getConsoleData() != null) {
        return SwitchType.FULL;
      }
      for (ConsoleData consoleData : sdm.getConfigDataManager().getActiveConsoles()) {
        if (isConConnectedToCpu(sdm, consoleData, realCpuData)) {
          return SwitchType.VIDEO;
        }
      }
    }
    return SwitchType.NOT_SWITCHED;
  }

  /**
   * .
   */
  public static boolean hasFullSharedAccess(CaesarConfigDataModel cdm, ConsoleData consoleData,
                                            CpuData cpuData) {
    if (consoleData != null && cpuData != null
        && consoleData.equals(cpuData.getConsoleData()) && cdm != null) {
      Collection<ConsoleData> consoles = cdm.getConfigDataManager().getActiveConsoles();
      for (ConsoleData activeConsole : consoles) {
        if (!activeConsole.equals(consoleData) && isConConnectedToCpu(cdm, activeConsole, cpuData)) {
          return true;
        }
      }
    }
    return false;
  }

  /**
   * .
   */
  public static boolean isConConnectedToCpu(CaesarConfigDataModel cdm, ConsoleData consoleData, CpuData cpuData) {
    if (consoleData.isMultiview()) {
      MultiviewData multiviewData = consoleData.getMultiviewData();
      if (multiviewData != null) {
        for (int i = 0; i < multiviewData.getSourceCount(); i++) {
          MultiviewSource source = multiviewData.getSource(i);
          if (source != null && source.getCpuData() != null && source.getCpuData().equals(cpuData)) {
            return true;
          }
        }
      }
      return false;
    } else {
      return consoleData.getCpuData() != null && consoleData.getCpuData().equals(cpuData);
    }
  }

  /**
   * 判断是否允许全连接访问.
   *
   * @param cdm 配置数据模型
   * @param consoleData 控制台数据
   * @param cpuData CPU数据
   * @param userData 用户数据
   * @return 是否允许全连接访问
   */
  public static boolean isFullAccessAllowed(CaesarConfigDataModel cdm, ConsoleData consoleData,
                                            CpuData cpuData, UserData userData) {
    if (cdm == null || consoleData == null || cpuData == null) {
      return false;
    }
    // 检查CPU私有状态
    if (cpuData.isStatusForcePrivate() || !cpuData.equals(consoleData.getCpuData()) && cpuData.isStatusPrivate()) {
      return false;
    }
    // 处理虚拟CPU
    CpuData localCpuData = getLocalCpuData(cpuData);
    SwitchData switchData = cdm.getConfigData().getSystemConfigData().getSwitchData();
    // 检查CPU连接状态
    if (isCpuConnectionBlocked(switchData, localCpuData, consoleData)) {
      return false;
    }
    // 管理员用户拥有全部权限
    if (userData != null && userData.hasRightAdmin()) {
      return true;
    }
    AccessData accessData = cdm.getConfigData().getSystemConfigData().getAccessData();
    // 检查用户和控制台访问权限
    return checkAccessPermissions(accessData, userData, consoleData, localCpuData);
  }

  /**
   * 判断是否允许视频访问.
   *
   * @param cdm 配置数据模型
   * @param consoleData 控制台数据
   * @param cpuData CPU数据
   * @param userData 用户数据
   * @param selectedItem 选中的CPU项
   * @return 是否允许视频访问
   */
  public static boolean isVideoAccessAllowed(CaesarConfigDataModel cdm, ConsoleData consoleData,
      CpuData cpuData, UserData userData, CpuData selectedItem) {
    if (cdm == null || consoleData == null || cpuData == null) {
      return false;
    }
    // 检查CPU私有状态
    if (cpuData.isStatusForcePrivate() || !cpuData.equals(consoleData.getCpuData()) && cpuData.isStatusPrivate()) {
      return false;
    }

    SwitchData switchData = cdm.getConfigData().getSystemConfigData().getSwitchData();
    // 检查CPU观看状态
    if (!switchData.isCpuWatch() && cpuData.getConsoleData() != null
        && !cpuData.getConsoleData().equals(consoleData) && !cpuData.equals(selectedItem)) {
      return false;
    }
    // 管理员用户拥有全部权限
    if (userData != null && userData.hasRightAdmin()) {
      return true;
    }

    AccessData accessData = cdm.getConfigData().getSystemConfigData().getAccessData();
    // 检查视频访问权限
    return checkVideoAccessPermissions(accessData, userData, consoleData, cpuData);
  }

  /**
   * 判断是否允许私有模式.
   *
   * @param cdm 配置数据模型
   * @param consoleData 控制台数据
   * @param cpuData CPU数据
   * @param userData 用户数据
   * @return 是否允许私有模式
   */
  public static boolean isPrivateModeAllowed(CaesarConfigDataModel cdm, ConsoleData consoleData,
      CpuData cpuData, UserData userData) {
    if (cdm == null || consoleData == null || cpuData == null) {
      return false;
    }
    // 检查控制台虚拟状态
    if (consoleData.isVirtual()) {
      return false;
    }
    // 检查CPU私有状态
    if (cpuData.isStatusPrivate()) {
      return false;
    }
    // 检查CPU是否允许私有模式
    if (!cpuData.isStatusAllowPrivate() && !cpuData.isStatusForcePrivate()) {
      return false;
    }
    SwitchData switchData = cdm.getConfigData().getSystemConfigData().getSwitchData();
    // 获取本地CPU数据
    CpuData localCpuData = getLocalCpuData(cpuData);
    // 检查CPU连接状态
    if (isCpuConnectionBlocked(switchData, localCpuData, consoleData)) {
      return false;
    }
    // 只有管理员用户可以使用私有模式
    return userData != null && userData.hasRightAdmin();
  }

  /**
   * 获取本地CPU数据（处理虚拟CPU）.
   *
   * @param cpuData CPU数据
   * @return 本地CPU数据
   */
  private static CpuData getLocalCpuData(CpuData cpuData) {
    return cpuData.isVirtual() && cpuData.getRealCpuData() != null ? cpuData.getRealCpuData() : cpuData;
  }

  /**
   * 检查CPU连接是否被阻止.
   *
   * @param switchData 切换数据
   * @param localCpuData 本地CPU数据
   * @param consoleData 控制台数据
   * @return 是否被阻止
   */
  private static boolean isCpuConnectionBlocked(SwitchData switchData, CpuData localCpuData,
      ConsoleData consoleData) {
    return !switchData.isCpuConnect() && !switchData.isConDisconnect()
        && localCpuData.getConsoleData() != null
        && !consoleData.equals(localCpuData.getConsoleData());
  }

  /**
   * 检查访问权限.
   *
   * @param accessData 访问数据
   * @param userData 用户数据
   * @param consoleData 控制台数据
   * @param cpuData CPU数据
   * @return 是否允许访问
   */
  private static boolean checkAccessPermissions(AccessData accessData, UserData userData,
      ConsoleData consoleData, CpuData cpuData) {
    // 检查用户访问权限
    boolean hasUserAccess =
        (!accessData.isUserLock() || userData == null || !userData.isVideoAccess(cpuData))
            && (userData == null || !userData.isNoAccess(cpuData));

    // 检查控制台访问权限
    boolean hasConAccess = (!accessData.isConLock() || accessData.isUserLock())
        && !accessData.isUserConOr()
        && (!accessData.isUserConAnd()
        || !consoleData.isVideoAccess(cpuData) && !consoleData.isNoAccess(cpuData));

    // 根据权限策略确定最终访问权限
    return accessData.isUserConOr() ? hasConAccess || hasUserAccess : hasConAccess && hasUserAccess;
  }

  /**
   * 检查视频访问权限.
   *
   * @param accessData 访问数据
   * @param userData 用户数据
   * @param consoleData 控制台数据
   * @param cpuData CPU数据
   * @return 是否允许视频访问
   */
  private static boolean checkVideoAccessPermissions(AccessData accessData, UserData userData,
      ConsoleData consoleData, CpuData cpuData) {
    boolean hasConAccess = true;
    boolean hasUserAccess = true;

    // 检查用户访问限制
    if (accessData.isUserLock() && userData != null && userData.isNoAccess(cpuData)) {
      hasConAccess = false;
    }

    // 检查控制台访问限制
    if (accessData.isConLock() && !accessData.isUserLock()
        || accessData.isUserConOr()
        || accessData.isUserConAnd() && consoleData.isNoAccess(cpuData)) {
      hasUserAccess = false;
    }

    // 根据权限策略确定最终访问权限
    return accessData.isUserConOr() ? hasConAccess || hasUserAccess : hasConAccess && hasUserAccess;
  }


  /**
   * TX连接多画面RX.
   */
  public static void connectMultiview(CaesarSwitchDataModel model, CpuData cpuData,
                                      ConsoleData consoleData, int channel, int mode) {
    try {
      model.switchMultiviewTx(cpuData, consoleData, channel, mode);
      //切换成功后,更新数据
      model.reloadMultiviewData();
    } catch (ConfigException | BusyException | DeviceConnectionException e) {
      log.warn("切换多画面失败:" + e.getMessage());
    }
  }
}
