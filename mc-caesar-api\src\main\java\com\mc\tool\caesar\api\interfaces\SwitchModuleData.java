package com.mc.tool.caesar.api.interfaces;

import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;

/**
 * .
 */
public interface SwitchModuleData extends CommitRollback {

  void initDefaults();

  ModuleData getModuleData(int paramInt);

  void reloadModules() throws ConfigException, BusyException;

  void reloadModules(Iterable<ModuleData> paramIterable)
      throws ConfigException, BusyException;

  void reloadModules(Iterable<ModuleData> paramIterable1,
      Iterable<ModuleData> paramIterable2) throws ConfigException, BusyException;

  /**
   * 加载端口数据.
   */
  void requestPorts() throws ConfigException, BusyException;

  void requestPorts(int... paramVarArgs) throws ConfigException, BusyException;

  void requestPorts(int paramInt1, int paramInt2)
      throws ConfigException, BusyException;

  void requestPorts(Iterable<PortData> paramIterable)
      throws ConfigException, BusyException;

  Iterable<ModuleData> getModuleDatas();
}

