package com.mc.tool.caesar.api;

import com.mc.tool.caesar.api.interfaces.Nameable;
import java.util.Collection;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.StringProperty;
import org.controlsfx.validation.ValidationResult;
import org.controlsfx.validation.Validator;

/**
 * .
 */
public class CaesarValidatorFactory {

  private static final String IP_REGX = "((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})"
      + "(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}";

  /**
   * 名字长度和重复验证器.
   *
   * @param userDataProperty 已存在的名字
   * @param sameNameError    名字已存在的错误信息
   * @param illegalLenError  名字长度不合法的错误信息
   */
  public static Validator<String> nameValidator(
      ObjectProperty<Collection<? extends Nameable>> userDataProperty, String sameNameError,
      String illegalLenError) {
    return (target, value) -> {
      Collection<? extends Nameable> nameableList = userDataProperty.get();
      String errorMsg;
      boolean anyMatch = nameableList.stream().anyMatch(item -> item.getName().equals(value));
      errorMsg = anyMatch ? sameNameError : "";
      boolean lengthCondition = value.length() > CaesarConstants.NAME_LEN || value.length() == 0;
      errorMsg = lengthCondition ? illegalLenError : errorMsg;
      return ValidationResult.fromErrorIf(target, errorMsg, anyMatch || lengthCondition);
    };
  }

  /**
   * 名字重复验证器.
   *
   * @param userDataProperty 已存在的名字
   * @param errorMsg         错误信息
   */
  public static Validator<String> notSameNameValidator(
      ObjectProperty<Collection<? extends Nameable>> userDataProperty, String errorMsg) {
    return (target, value) -> {
      Collection<? extends Nameable> nameableList = userDataProperty.get();
      boolean anyMatch = nameableList.stream().anyMatch(item -> item.getName().equals(value));
      return ValidationResult.fromErrorIf(target, errorMsg, anyMatch);
    };
  }

  /**
   * 名字长度验证器.
   *
   * @param errorMsg 错误信息
   */
  public static Validator<String> nameLengthValidator(String errorMsg) {
    return (target, value) -> {
      boolean condition = value.length() > CaesarConstants.NAME_LEN || value.length() == 0;
      return ValidationResult.fromErrorIf(target, errorMsg, condition);
    };
  }

  /**
   * 创建密码验证器,包括是否重复和是否符合复杂度.
   *
   * @param anotherPassword 另一个密码
   * @param illegalMsg 密码不符合复杂度的错误信息
   * @param samePasswordMsg 密码重复的错误信息
   */
  public static Validator<String> passwordValidator(StringProperty anotherPassword,
                                                    String illegalMsg, String samePasswordMsg) {
    return (target, value) -> {
      boolean condition = testPassword(value);
      if (!condition) {
        return ValidationResult.fromErrorIf(target, illegalMsg, true);
      }
      return ValidationResult
          .fromErrorIf(target, samePasswordMsg, !value.equals(anotherPassword.getValue()));
    };
  }

  /**
   * 创建密码重复验证器.
   *
   * @param anotherPassword 另一个密码
   * @param errorMsg        错误信息
   */
  public static Validator<String> notSamePasswordValidator(StringProperty anotherPassword,
      String errorMsg) {
    return (target, value) -> ValidationResult
        .fromErrorIf(target, errorMsg, !value.equals(anotherPassword.getValue()));
  }

  /**
   * testPassword.
   */
  public static boolean testPassword(String password) {
    if (password.length() < CaesarConstants.PASSWORD_LEN) {
      return false;
    }
    // 包含字母和数字
    boolean hasLetter = false;
    boolean hasNumber = false;
    for (char c : password.toCharArray()) {
      if (Character.isLetter(c)) {
        hasLetter = true;
      }
      if (Character.isDigit(c)) {
        hasNumber = true;
      }
    }
    if (!hasLetter || !hasNumber) {
      return false;
    }

    // 包含特殊字符
    String pattern = ".*[~!@#$%^&*.].*";
    Pattern p = Pattern.compile(pattern);
    Matcher m = p.matcher(password);
    return m.matches();
  }

  /**
   * 创建IP验证器.
   *
   * @param errorMsg 错误信息.
   */
  public static Validator<String> ipValidator(String errorMsg) {
    return (target, value) -> ValidationResult
        .fromErrorIf(target, errorMsg, !value.matches(IP_REGX));
  }
}
