package com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard;

import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridConfigUtility.CategoryTableCell;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridConfigUtility.MatrixData4HostConfig;
import com.mc.tool.framework.utility.UndecoratedWizard;
import com.mc.tool.framework.utility.UndecoratedWizardPane;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.ResourceBundle;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.cell.CheckBoxTableCell;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class WizardAutoId extends UndecoratedWizardPane implements Initializable {

  @FXML private TableView<MatrixData4HostConfig> tableView;

  @FXML private TableColumn<MatrixData4HostConfig, String> categoryCol;
  @FXML private TableColumn<MatrixData4HostConfig, String> nameCol;
  @FXML private TableColumn<MatrixData4HostConfig, String> ipCol;
  @FXML private TableColumn<MatrixData4HostConfig, Boolean> autoId;

  private ObservableList<MatrixData4HostConfig> items = FXCollections.observableArrayList();

  /** . */
  public WizardAutoId() {
    FXMLLoader loader = new FXMLLoader();
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    loader.setLocation(
        classLoader.getResource(
            "com/mc/tool/caesar/vpm/pages/matrixgrid/configwizard/autoid_view.fxml"));
    loader.setResources(
        ResourceBundle.getBundle("com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.Bundle"));
    loader.setController(this);
    try {
      Node root = loader.load();
      setContent(root);
    } catch (IOException exception) {
      log.warn("Fail to load autoid_view.fxml!", exception);
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    categoryCol.setCellFactory((col) -> new CategoryTableCell());
    categoryCol.setPrefWidth(40);

    nameCol.setCellValueFactory((cell) -> cell.getValue().name);
    nameCol.setPrefWidth(Integer.MAX_VALUE);

    ipCol.setCellValueFactory((cell) -> cell.getValue().ip);
    ipCol.setPrefWidth(Integer.MAX_VALUE);

    autoId.setCellValueFactory((cell) -> cell.getValue().keepId);
    autoId.setCellFactory(CheckBoxTableCell.forTableColumn(autoId));
    autoId.setPrefWidth(Integer.MAX_VALUE);

    tableView.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);
    tableView.setItems(items);
    tableView.setEditable(true);
  }

  @Override
  public void onEnteringPage(UndecoratedWizard wizard) {
    super.onEnteringPage(wizard);
    items.setAll(MatrixGridConfigUtility.getMatrixGridConfigFromWizard(wizard));
  }

  @Override
  public void onExitingPage(UndecoratedWizard wizard) {
    super.onExitingPage(wizard);
    wizard.getSettings().put(MatrixGridConfigUtility.KEY_HOST_CONFIG, new ArrayList<>(items));
  }
}
