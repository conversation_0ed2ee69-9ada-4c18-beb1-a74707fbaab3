package com.mc.tool.caesar.api.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

/**
 * .
 */
public final class CollectionUtil {

  public static <T> List<T> getList(T... objects) {
    return getList(new ArrayIterable<>(objects));
  }

  /**
   * .
   *
   * @param iterable iterable
   */
  public static <T> List<T> getList(Iterable<T> iterable) {
    if (null == iterable) {
      return Collections.emptyList();
    }
    ArrayList<T> list = new ArrayList<>();
    for (T object : iterable) {
      list.add(object);
    }
    list.trimToSize();

    return list;
  }

  /**
   * .
   *
   * @param iterator iterator
   */
  public static <T> List<T> getList(Iterator<T> iterator) {
    if (null == iterator) {
      return Collections.emptyList();
    }
    return getList(new IteratorIterable<>(iterator));
  }

  public static <T> List<T> getListWithoutNull(T... objects) {
    return getListWithoutNull(new ArrayIterable<>(objects));
  }

  /**
   * .
   *
   * @param iterable iterable
   */
  public static <T> List<T> getListWithoutNull(Iterable<T> iterable) {
    if (null == iterable) {
      return Collections.emptyList();
    }
    ArrayList<T> list = new ArrayList<>();
    for (T object : iterable) {
      if (null != object) {
        list.add(object);
      }
    }
    if (list.isEmpty()) {
      return Collections.emptyList();
    }
    list.trimToSize();
    return list;
  }

  /**
   * .
   *
   * @param iterator iterator
   */
  public static <T> List<T> getListWithoutNull(Iterator<T> iterator) {
    if (null == iterator) {
      return Collections.emptyList();
    }
    return getListWithoutNull(new IteratorIterable<>(iterator));
  }

  public static <T> Set<T> getSet(T... objects) {
    return getSet(new ArrayIterable<>(objects));
  }

  /**
   * .
   *
   * @param iterable iterable
   */
  public static <T> Set<T> getSet(Iterable<T> iterable) {
    if (null == iterable) {
      return Collections.emptySet();
    }
    Set<T> list = new HashSet<>();
    for (T object : iterable) {
      list.add(object);
    }
    return list;
  }

  /**
   * .
   *
   * @param iterator iterator
   */
  public static <T> Set<T> getSet(Iterator<T> iterator) {
    if (null == iterator) {
      return Collections.emptySet();
    }
    return getSet(new IteratorIterable<>(iterator));
  }

  public static <T> Set<T> getSetWithoutNull(T... objects) {
    return getSetWithoutNull(new ArrayIterable<>(objects));
  }

  /**
   * .
   *
   * @param iterable iterable
   */
  public static <T> Set<T> getSetWithoutNull(Iterable<T> iterable) {
    if (null == iterable) {
      return Collections.emptySet();
    }
    Set<T> list = new HashSet<>();
    for (T object : iterable) {
      if (null != object) {
        list.add(object);
      }
    }
    return list;
  }

  /**
   * .
   *
   * @param iterator iterator
   */
  public static <T> Set<T> getSetWithoutNull(Iterator<T> iterator) {
    if (null == iterator) {
      return Collections.emptySet();
    }
    return getSetWithoutNull(new IteratorIterable<>(iterator));
  }
}
