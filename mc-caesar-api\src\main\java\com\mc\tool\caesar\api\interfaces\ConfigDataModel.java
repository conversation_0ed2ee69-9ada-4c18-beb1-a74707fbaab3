package com.mc.tool.caesar.api.interfaces;

import com.mc.tool.caesar.api.exception.ConfigException;
import java.beans.PropertyChangeListener;
import java.util.Collection;

/**
 * .
 */
public interface ConfigDataModel {

  void addPropertyChangeListener(
      PropertyChangeListener paramPropertyChangeListener);

  void addPropertyChangeListener(String[] paramArrayOfString,
      PropertyChangeListener paramPropertyChangeListener);

  void addPropertyChangeListener(Collection<String> paramCollection,
      PropertyChangeListener paramPropertyChangeListener);

  void addPropertyChangeListener(String paramString,
      PropertyChangeListener paramPropertyChangeListener);

  void removePropertyChangeListener(
      PropertyChangeListener paramPropertyChangeListener);

  void removePropertyChangeListener(String[] paramArrayOfString,
      PropertyChangeListener paramPropertyChangeListener);

  void removePropertyChangeListener(Collection<String> paramCollection,
      PropertyChangeListener paramPropertyChangeListener);

  void removePropertyChangeListener(String paramString,
      PropertyChangeListener paramPropertyChangeListener);

  /**
   * 从文件中读取配置.
   *
   * @param paramString paramString
   */
  void readFile(String paramString) throws ConfigException;

  /**
   * 从ftp获取配置数据.
   *
   * @param paramString url
   */
  void readFtp(String paramString) throws ConfigException;

  /**
   * 把当前配置写到文件.
   *
   * @param paramString 文件路径
   */
  void writeFile(String paramString) throws ConfigException;

  /**
   * 把当前配置写到ftp去.
   *
   * @param paramString url
   */
  void writeFtp(String paramString) throws ConfigException;

  boolean checkUserRights(String paramString) throws ConfigException;

  Threshold getUiThreshold();

  void clear();
}

