package com.mc.tool.caesar.vpm.pages.systemedit.property;

import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarCpuVirtualOutInSender implements CaesarPropertySender<Boolean> {

  private final CaesarDeviceController controller;
  private final CpuData cpuData;
  private final String propertyName;

  /** . */
  public CaesarCpuVirtualOutInSender(
      CaesarDeviceController controller, CpuData cpuData, String propertyName) {
    this.controller = controller;
    this.cpuData = cpuData;
    this.propertyName = propertyName;
  }

  @Override
  public void sendValue(Boolean value) {
    controller.execute(
        () -> {
          cpuData.setProperty(propertyName, value);
          try {
            controller.getDataModel().setCpuVirtualOutIn(cpuData, !cpuData.isStatusVirtualOut());
          } catch (BusyException | ConfigException exception) {
            log.error("Fail to send value!", exception);
          }
        });
  }
}
