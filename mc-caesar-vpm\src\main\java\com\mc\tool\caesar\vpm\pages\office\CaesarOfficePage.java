package com.mc.tool.caesar.vpm.pages.office;

import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.interfaces.Page;
import com.mc.tool.framework.office.view.OfficePageView;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.scene.layout.Pane;

/**
 * .
 */
public class CaesarOfficePage implements Page {
  public static final String NAME = "office";
  private final OfficePageView view;
  private final BooleanProperty visibility = new SimpleBooleanProperty(true);

  public CaesarOfficePage(CaesarEntity entity) {
    view = new OfficePageView(entity.getVisualEditModel());
  }

  @Override
  public String getTitle() {
    return CaesarI18nCommonResource.getString("CaesarOfficePage.title");
  }

  @Override
  public String getName() {
    return NAME;
  }

  @Override
  public Pane getView() {
    return view;
  }

  @Override
  public String getStyleClass() {
    return "caesar-office-page";
  }

  @Override
  public BooleanProperty getVisibleProperty() {
    return visibility;
  }

  @Override
  public void showObject(Object object) {}
}
