package com.mc.tool.caesar.api.version;

import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;

/**
 * ApiDataConverter.
 */
public interface ApiDataConverter<T extends CaesarCommunicatable> {

  void readData(T data, CfgReader cfgReader) throws ConfigException;

  void writeData(T data, CfgWriter cfgWriter) throws ConfigException;
}
