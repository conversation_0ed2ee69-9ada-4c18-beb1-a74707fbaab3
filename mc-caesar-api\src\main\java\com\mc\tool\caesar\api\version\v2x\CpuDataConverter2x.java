package com.mc.tool.caesar.api.version.v2x;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.version.ApiDataConverter;

/**
 * .
 */
public class CpuDataConverter2x implements ApiDataConverter<CpuData> {

  private final int dataSize;

  public CpuDataConverter2x(int dataSize) {
    this.dataSize = dataSize;
  }

  @Override
  public void readData(CpuData data, CfgReader cfgReader) throws ConfigException {
    final int startAvailable = cfgReader.available();
    String name = cfgReader.readString(CaesarConstants.NAME_LEN);
    data.setName(name);
    int status = cfgReader.readInteger();
    data.setStatus(status);
    int console = cfgReader.read2ByteValue();
    data.setConsole(console);
    int realCpu = cfgReader.read2ByteValue();
    data.setRealCpu(realCpu);
    int id = cfgReader.read2ByteValue();
    data.setId(id);

    int ctrlUserIndex = cfgReader.read2ByteValue();
    data.setCtrlUserIndex(ctrlUserIndex);

    int ports = cfgReader.read2ByteValue();
    data.setPorts(ports);
    // extender size
    for (int idx = 0; idx < CaesarConstants.Extender.CPUCON; idx++) {
      int extender = cfgReader.read2ByteValue();
      data.setExtender(idx, extender & 0xFFFF);
      data.setCpuIndex(idx, extender & 0xFFFF0000);
    }
    int endAvailable = cfgReader.available();
    int readedSize = startAvailable - endAvailable;
    int reserveSize = dataSize - readedSize;
    cfgReader.readByteArray(reserveSize);
  }

  @Override
  public void writeData(CpuData data, CfgWriter cfgWriter) throws ConfigException {
    final long startSize = cfgWriter.getSize();
    cfgWriter.writeString(data.getName(), CaesarConstants.NAME_LEN);
    cfgWriter.writeInteger(data.getStatus());
    cfgWriter.write2ByteSmallEndian(data.getConsole());
    cfgWriter.write2ByteSmallEndian(data.getRealCpu());
    cfgWriter.write2ByteSmallEndian(data.getId());
    cfgWriter.write2ByteSmallEndian(data.getCtrlUserIndex());
    cfgWriter.write2ByteSmallEndian(data.getPorts());
    for (int idx = 0; idx < CaesarConstants.Extender.CPUCON; idx++) {
      int value = data.getCpuIndex(idx);
      value |= data.getExtender(idx);
      cfgWriter.write2ByteSmallEndian(value);
    }
    long endSize = cfgWriter.getSize();
    int writedSize = (int) (endSize - startSize);
    int reservedSize = dataSize - writedSize;
    cfgWriter.writeByteArray(new byte[reservedSize]);
  }
}
