package com.mc.tool.caesar.vpm.pages.matrixgrid;

import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.ConfigData;
import com.mc.tool.caesar.api.datamodel.MatrixData;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.caesar.vpm.CaesarEntityFactory;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.WizardActivate;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.WizardAutoId;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.WizardGridName;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.WizardGridSystem;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.WizardHostConfig;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.WizardPrepare;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.utility.EventBusProvider;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.TypeWrapper;
import com.mc.tool.framework.utility.UndecoratedWizard;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.beans.PropertyChangeListener;
import java.lang.ref.WeakReference;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.beans.property.ReadOnlyStringWrapper;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.cell.CheckBoxTableCell;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
@SuppressFBWarnings("SIC_INNER_SHOULD_BE_STATIC_ANON")
public class CaesarMatrixGridConfigController implements Initializable, ViewControllable {

  private CaesarDeviceController deviceController;
  private ObservableList<MatrixGridModel> tableModel = FXCollections.observableArrayList();
  private WeakReference<Entity> entityRef = new WeakReference<>(null);

  @FXML private TableView<MatrixGridModel> tableView;
  @FXML private TableColumn<MatrixGridModel, String> matrixCol;
  @FXML private TableColumn<MatrixGridModel, Boolean> activateCol;
  @FXML private TableColumn<MatrixGridModel, String> deviceCol;
  @FXML private TableColumn<MatrixGridModel, String> hostCol;
  @FXML private TableColumn<MatrixGridModel, Number> portCol;
  @FXML private TableColumn<MatrixGridModel, Boolean> masterCol;
  @FXML private TableColumn<MatrixGridModel, String> connectCol;

  private WeakAdapter weakAdapter = new WeakAdapter();

  public CaesarMatrixGridConfigController() {
    EventBusProvider.getEventBus().register(this);
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
      this.deviceController
          .getDataModel()
          .addPropertyChangeListener(
              new String[] {
                ConfigData.PROPERTY_GRID_INDEX,
                MatrixData.PROPERTY_DEVICE,
                MatrixData.PROPERTY_HOSTADDRESS,
                MatrixData.PROPERTY_PORTCOUNT,
                MatrixData.PROPERTY_STATUS
              },
              weakAdapter.wrap(
                  (PropertyChangeListener)
                      evt -> PlatformUtility.runInFxThread(this::updateModel)));
      updateModel();
    } else {
      log.error("Error device controller type!");
    }
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceController;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    for (int i = 0; i < CaesarConstants.MATRIX_GRID_COUNT; i++) {
      MatrixGridModel model = new MatrixGridModel(i);
      tableModel.add(model);
    }

    matrixCol.setCellValueFactory(
        (cell) ->
            new ReadOnlyStringWrapper(
                String.format(
                    "%s%02d",
                    CaesarI18nCommonResource.getString("matrixgrid.matrix"),
                    cell.getValue().getIndex() + 1)));
    matrixCol.setMaxWidth(Integer.MAX_VALUE);

    activateCol.setCellValueFactory((cell) -> cell.getValue().activate);
    activateCol.setCellFactory(CheckBoxTableCell.forTableColumn(activateCol));
    activateCol.setMaxWidth(Integer.MAX_VALUE);

    deviceCol.setCellValueFactory((cell) -> cell.getValue().device);
    deviceCol.setMaxWidth(Integer.MAX_VALUE);

    hostCol.setCellValueFactory((cell) -> cell.getValue().host);
    hostCol.setMaxWidth(Integer.MAX_VALUE);

    portCol.setCellValueFactory((cell) -> cell.getValue().port);
    portCol.setMaxWidth(Integer.MAX_VALUE);

    masterCol.setCellValueFactory((cell) -> cell.getValue().master);
    masterCol.setCellFactory(CheckBoxTableCell.forTableColumn(masterCol));
    masterCol.setMaxWidth(Integer.MAX_VALUE);

    connectCol.setCellFactory((column) -> new ConnectTableCell());
    connectCol.setMaxWidth(Integer.MAX_VALUE);

    tableView.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);
    tableView.setItems(tableModel);

    updateModel();
  }

  private void updateModel() {
    if (deviceController == null) {
      return;
    }
    for (int i = 0; i < CaesarConstants.MATRIX_GRID_COUNT; i++) {
      tableModel.get(i).reset();
      MatrixData matrixData = deviceController.getDataModel().getConfigData().getMatrixData(i);
      if (matrixData == null) {
        log.error("Matrix data is null at {}!", i);
      } else {
        MatrixGridModel model = tableModel.get(i);
        model.device.set(matrixData.getDevice());
        model.activate.set(matrixData.isStatusActive());
        model.host.set(IpUtil.getAddressString(matrixData.getHostAddress()));
        model.port.set(matrixData.getPortCount());
        model.master.set(
            model.getIndex() + 1 == deviceController.getDataModel().getConfigData().getGridIndex());
      }
    }
  }

  @FXML
  protected void onConfig(ActionEvent event) {
    UndecoratedWizard wizard = new UndecoratedWizard(tableView.getScene().getWindow());
    WizardActivate wizardActivate = new WizardActivate(deviceController);
    wizard.setFlow(
        new UndecoratedWizard.LinearFlow(
            new WizardPrepare(),
            new WizardHostConfig(deviceController),
            new WizardGridName(deviceController),
            new WizardGridSystem(deviceController),
            new WizardAutoId(),
            wizardActivate));
    wizard.showAndWait();
    if (wizardActivate.isHasActivated()) {
      // 刷新
      Entity entity = entityRef.get();
      if (entity != null) {
        entity.onMenu(new TypeWrapper("", CaesarEntity.MENU_REFRESH, ""));
      }
    }
  }

  @Override
  public void setEntity(Entity entity) {
    entityRef = new WeakReference<>(entity);
  }

  private static class ConnectTableCell extends TableCell<MatrixGridModel, String> {

    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);

      if (!empty && getTableRow() != null) {
        Object rowItem = this.getTableRow().getItem();
        MatrixGridModel data = null;
        if (rowItem instanceof MatrixGridModel) {
          data = (MatrixGridModel) rowItem;
        }
        String ip;
        if (data != null) {
          ip = data.host.get();
        } else {
          ip = "";
        }

        if (data == null || !data.activate.get()) {
          return;
        }
        Button button = new Button();
        button.setPrefSize(70, 25);
        button.setText(CaesarI18nCommonResource.getString("matrixgrid.connect"));
        button.getStyleClass().add("common-button");
        this.setGraphic(button);
        button.setOnAction(
            event -> {
              ApplicationBase app =
                  InjectorProvider.getInjector().getInstance(ApplicationBase.class);
              app.getEntityFactory()
                  .createEntity(
                      CaesarEntityFactory.CAESAR_CONNECT_WITH_IP_TYPE + " " + ip,
                      entity -> app.getEntityMananger().addEntity(entity));
            });
      }
    }
  }
}
