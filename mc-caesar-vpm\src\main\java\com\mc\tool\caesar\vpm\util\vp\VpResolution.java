package com.mc.tool.caesar.vpm.util.vp;

/**
 * .
 */
public class VpResolution {
  /**
   * Video resolution type.
   */
  public enum Type {
    VIDEO_2K(0),
    VIDEO_4K30(1),
    VIDEO_4K60(2),
    VIDEO_UNKNOWN(3);

    private final int value;

    Type(int value) {
      this.value = value;
    }

    public int getValue() {
      return value;
    }
  }

  public final int width;
  public final int height;
  public final Type type;

  /**
   * Constructor with parameters.
   */
  public VpResolution(int width, int height, Type type) {
    this.width = width;
    this.height = height;
    this.type = type;
  }

  /**
   * Constructor with default values.
   */
  public VpResolution() {
    width = 0;
    height = 0;
    type = Type.VIDEO_2K;
  }

  public boolean is4k() {
    return width > 1920;
  }

  public boolean isValid() {
    return width > 0 && height > 0;
  }

}
