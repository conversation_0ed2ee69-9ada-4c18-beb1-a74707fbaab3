package com.mc.tool.caesar.vpm.pages.update;

import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import javafx.concurrent.Task;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public abstract class UpdateTask extends Task<Object> {
  @Setter @Getter protected boolean cancelled = false;
  @Setter @Getter protected boolean finished = false;

  @Override
  protected Object call() throws Exception {
    return null;
  }

  protected abstract void updateOpen()
      throws DeviceConnectionException, ConfigException, BusyException;

  protected abstract void updateWrite(int offset, byte[] dataToWrite)
      throws DeviceConnectionException, ConfigException, BusyException;

  protected abstract void updateClose(boolean successful)
      throws DeviceConnectionException, ConfigException, BusyException;

  protected boolean updateImpl(
      byte[] packageData, int pageCount, int startAddress, UpdateLogger logger)
      throws DeviceConnectionException, ConfigException, BusyException, Exception, IOException {
    int byteHasRead = 0;
    int threshold = 4;
    updateOpen();
    int fileSize = packageData.length;
    byte[] tmp = new byte[pageCount];
    int readOnce;
    updateProgress(0, fileSize);
    ByteArrayInputStream bais = null;
    boolean successful = false;
    try {
      bais = new ByteArrayInputStream(packageData);
      while ((readOnce = bais.read(tmp)) != -1 && !cancelled) {
        int time = 0;
        byte[] toWriteData = tmp;
        if (readOnce < pageCount) {
          byte[] lastTmp = new byte[readOnce];
          System.arraycopy(tmp, 0, lastTmp, 0, readOnce);
          toWriteData = lastTmp;
        }
        while (time < threshold && !cancelled) {
          try {
            updateWrite(byteHasRead + startAddress, toWriteData);
            break;
          } catch (Exception ex) {
            time++;
            logger.addLog("Send timeout:" + ex.getMessage(), ex);
            if (time == threshold) {
              throw ex;
            }
          }
        }

        byteHasRead += readOnce;
        updateProgress(byteHasRead, fileSize);
      }
      if (!cancelled) {
        successful = true;
      }
    } catch (Exception ex) {
      throw ex;
    } finally {
      if (bais != null) {
        bais.close();
      }

      updateClose(successful);
    }
    return successful;
  }
}
