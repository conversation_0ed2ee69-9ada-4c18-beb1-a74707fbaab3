package com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel;

import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.scene.paint.Color;

/**
 * 测试画面配置数据.
 */
public class CaesarTestFrameData {
  private final ObjectProperty<CaesarTestFrameMode> mode = new SimpleObjectProperty<>(CaesarTestFrameMode.CLOSE);
  // 颜色R,G,B,Alpha
  private final ObjectProperty<Color> color = new SimpleObjectProperty<>(Color.WHITE);
  // 速度0-255
  private final IntegerProperty speed = new SimpleIntegerProperty(0);
  // 透明度0-255
  private final IntegerProperty alpha = new SimpleIntegerProperty(255);

  // Mode property methods
  public CaesarTestFrameMode getMode() {
    return mode.get();
  }

  public void setMode(CaesarTestFrameMode mode) {
    this.mode.set(mode);
  }

  public ObjectProperty<CaesarTestFrameMode> modeProperty() {
    return mode;
  }

  // Color property methods
  public Color getColor() {
    return color.get();
  }

  public void setColor(Color color) {
    this.color.set(color);
  }

  public ObjectProperty<Color> colorProperty() {
    return color;
  }

  // Speed property methods
  public int getSpeed() {
    return speed.get();
  }

  public void setSpeed(int speed) {
    this.speed.set(speed);
  }

  public IntegerProperty speedProperty() {
    return speed;
  }

  public IntegerProperty alphaProperty() {
    return alpha;
  }

  /**
   * RGB值(不含alpha).
   */
  public int getRgbWithoutAlpha() {
    Color fxColor = color.get();
    return ((int) (fxColor.getRed() * 255) << 16)
        | ((int) (fxColor.getGreen() * 255) << 8)
        | ((int) (fxColor.getBlue() * 255));
  }

  public void setRgbWithoutAlpha(int rgb) {
    color.set(Color.rgb((rgb >> 16) & 0xff, (rgb >> 8) & 0xff, rgb & 0xff));
  }

  public int getAlpha() {
    return alpha.get();
  }

  public void setAlpha(int alpha) {
    this.alpha.set(alpha);
  }

  /**
   * copy to VideoWallGroupData.VideoWallData.
   */
  public void copyTo(VideoWallGroupData.VideoWallData videoWallData) {
    videoWallData.setTestFrameMode(mode.get().getValue());
    videoWallData.setTestFrameColor(getRgbWithoutAlpha());
    videoWallData.setTestFrameSpeed(speed.get());
    videoWallData.setAlpha(alpha.get());
  }
}
