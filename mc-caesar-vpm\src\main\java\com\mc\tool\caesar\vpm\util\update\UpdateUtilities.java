package com.mc.tool.caesar.vpm.util.update;

import com.mc.tool.caesar.api.CaesarConstants.Module.Type;
import com.mc.tool.caesar.api.datamodel.MatrixDefinitionData;
import com.mc.tool.caesar.api.datamodel.VersionDef;
import com.mc.tool.caesar.vpm.pages.update.UpdateData;
import com.mc.tool.caesar.vpm.util.update.UpdateConstant.IoInterfaceType;
import com.mc.tool.caesar.vpm.util.update.UpdateConstant.ProductType;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javafx.scene.control.TreeItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.cpio.CpioArchiveEntry;
import org.apache.commons.compress.archivers.cpio.CpioArchiveInputStream;

/**
 * .
 */
@Slf4j
public class UpdateUtilities {

  /**
   * 更新升级项的信息.
   *
   * @param slotItem 升级项
   * @param updatePackageInfo 升级包信息
   * @param mdd 矩阵信息
   * @param isCpuSuitable 是否可用于cpu升级
   * @param isIoSuitable 是否可用于io升级
   * @param isIoVirtual io是否为虚拟的
   */
  public static void updateSlotVersion(
      TreeItem<UpdateData> slotItem,
      UpdatePackageInfo updatePackageInfo,
      MatrixDefinitionData mdd,
      boolean isCpuSuitable,
      boolean isIoSuitable,
      boolean isIoVirtual,
      int matrixType) {
    for (TreeItem<UpdateData> typeItem : slotItem.getChildren()) {
      UpdateData updateData = typeItem.getValue();
      boolean enable = false;
      boolean hasVersion = false;
      if (slotItem.getValue().getType() != null
          && updatePackageInfo.getTargetType().contains(slotItem.getValue().getType())
          && (updatePackageInfo.isCpuUpdate() && isCpuSuitable
              || updatePackageInfo.isIoUpdate() && isIoSuitable)) {

        enable = true;
        hasVersion = true;
        if (updateData.getType().equalsIgnoreCase(UpdateConstant.UPDATE_APP_TYPE)
            && updatePackageInfo.getApp() != null) {
          updateData.setUpdateVersion(updatePackageInfo.getApp().copy());
          updateData.setUpdateDate(updatePackageInfo.getApp().getDate());
        } else if (updateData.getType().equalsIgnoreCase(UpdateConstant.UPDATE_SYS_TYPE)
            && updatePackageInfo.getSys() != null) {
          updateData.setUpdateVersion(updatePackageInfo.getSys().copy());
          updateData.setUpdateDate(updatePackageInfo.getSys().getDate());
        } else if (updateData.getType().equalsIgnoreCase(UpdateConstant.UPDATE_FPGA_TYPE)
            && updatePackageInfo.getRightFpga() != null) {
          if (updateData.getLevel1() == 0 && updatePackageInfo.isIoUpdate()) {
            updateData.setUpdateVersion(
                updatePackageInfo.getTrunk(Type.valueOf(matrixType)).copy());
            updateData.setUpdateDate(
                updatePackageInfo.getTrunk(Type.valueOf(matrixType)).getDate());
          } else {
            updateData.setUpdateVersion(updatePackageInfo.getRightFpga().copy());
            updateData.setUpdateDate(updatePackageInfo.getRightFpga().getDate());
          }
        } else {
          enable = false;
          hasVersion = false;
        }
      }

      if (isIoVirtual && mdd != null && updateData.getLevel1() > mdd.getFirstModule()) {
        enable = false;
      }

      updateData.setDisabled(!enable);
      if (!hasVersion) {
        updateData.setUpdateDate("");
        updateData.setUpdateVersion(new VersionDef());
      }
    }
  }

  protected static VersionDef parseVersion(String version) {
    if (version == null || version.isEmpty()) {
      return null;
    }
    String[] splitResult = version.split("[-]");
    if (splitResult.length < 2) {
      return null;
    }

    String versionInfo = splitResult[0].substring(1);
    String dateInfo = splitResult[1];

    if (dateInfo.length() != 6) {
      return null;
    }

    String[] versionInfoSplitResult = versionInfo.split("[.]");
    if (versionInfoSplitResult.length < 2) {
      return null;
    }

    String masterVersion = versionInfoSplitResult[0];
    String subVersion = versionInfoSplitResult[1];
    String patchVersion = "";
    if (versionInfoSplitResult.length > 2) {
      patchVersion = versionInfoSplitResult[2];
    }


    String year = dateInfo.substring(0, 2);
    String month = dateInfo.substring(2, 4);
    String day = dateInfo.substring(4, 6);

    try {
      VersionDef versionDef = new VersionDef();
      versionDef.setMasterVersion(Integer.parseInt(masterVersion));
      if (patchVersion.isEmpty()) {
        versionDef.setSubVersion(Integer.parseInt(subVersion));
      } else {
        int subVersionInt = Integer.parseInt(subVersion);
        int patchVersionInt = Integer.parseInt(patchVersion);
        if (subVersionInt > 0xff) {
          log.warn("Subversion number %d is too large. Convert to %d", subVersionInt, subVersionInt & 0xff);
        }
        if (patchVersionInt > 0xff) {
          log.warn("Patch number %d is too large. Convert to %d", patchVersionInt, patchVersionInt & 0xff);
        }
        versionDef.setSubVersion(((subVersionInt & 0xff) << 8) + (patchVersionInt & 0xff));
        versionDef.setHasPatchVersion(true);
      }
      versionDef.setYear(Integer.parseInt(year) + 2000);
      versionDef.setMonth(Integer.parseInt(month));
      versionDef.setDay(Integer.parseInt(day));
      return versionDef;
    } catch (NumberFormatException exception) {
      return null;
    }
  }

  protected static VersionDef parseHardwareVersion(String version) {
    String[] splitResult = version.split("[-]");
    if (splitResult.length < 2) {
      return null;
    }

    String hardWareVersion = splitResult[1].substring(2);
    String[] versionSplitResult = hardWareVersion.split("[.]");
    if (versionSplitResult.length < 2) {
      return null;
    }

    try {
      VersionDef versionDef = new VersionDef();
      versionDef.setMasterVersion(Integer.parseInt(versionSplitResult[0]));
      versionDef.setSubVersion(Integer.parseInt(versionSplitResult[1]));
      return versionDef;
    } catch (NumberFormatException exception) {
      return null;
    }
  }

  /**
   * 获取升级包的信息.
   *
   * @param filePath 升级包文件路径.
   * @return 升级信息.
   * @throws IOException 获取失败
   */
  @SuppressFBWarnings(value = "NP_NULL_PARAM_DEREF")
  public static UpdatePackageInfo getUpdatePackageInfo(String filePath) throws IOException {
    InputStream newInputStream = Files.newInputStream(Paths.get(filePath));
    CpioArchiveInputStream cpioIn = new CpioArchiveInputStream(newInputStream);
    CpioArchiveEntry cpioEntry;

    List<Type> matrixTypes = null;
    IoInterfaceType ioInterfaceType = null;
    ProductType productType = null;
    Integer ports = null;
    VersionDef sys = null;
    VersionDef app = null;
    VersionDef fpga = null;
    VersionDef fpgaHdmiTx = null;
    VersionDef fpgaHdmiRx = null;
    VersionDef fpgaHdmi4Rx = null;
    VersionDef fpgaHdmi4RxHalf1G = null;
    VersionDef fpgaHdmi4RxHalf25G = null;
    VersionDef fpgaCaesarPreview = null;
    VersionDef io = null;
    VersionDef trunk = null;
    VersionDef branch = null;
    Map<Type, VersionDef> trunks = new HashMap<>();
    VersionDef hardWare = null;
    String logo = null;

    // 获取版本信息
    while ((cpioEntry = (CpioArchiveEntry) cpioIn.getNextEntry()) != null) {
      if ("version.txt".equals(cpioEntry.getName())) {
        BufferedReader buff = null;
        String tmp;
        try (InputStreamReader reader = new InputStreamReader(cpioIn, StandardCharsets.UTF_8)) {
          try {
            buff = new BufferedReader(reader);
            Map<String, String> versionInfoMap = new HashMap<>();
            // 读取所有版本信息项
            while ((tmp = buff.readLine()) != null) {
              log.info(tmp);
              if (!tmp.contains("=")) {
                continue;
              }
              String[] splitResult = tmp.split("[=]");
              if (splitResult.length != 2) {
                continue;
              }

              versionInfoMap.put(
                  splitResult[0].trim().toLowerCase(Locale.ENGLISH),
                  splitResult[1].trim().toLowerCase(Locale.ENGLISH));
            }
            // 读取硬件版本
            String productInfo = versionInfoMap.get(UpdateConstant.VERSION_HEADER_PRODUCT);
            hardWare = parseHardwareVersion(productInfo);
            if (productInfo.contains(UpdateConstant.VERSION_PRODUCT_MAT)) {
              productType = ProductType.MAT;
            } else if (productInfo.contains(UpdateConstant.VERSION_PRODUCT_CON_CPU)) {
              productType = ProductType.CON_CPU;
            } else if (productInfo.contains(UpdateConstant.VERSION_PRODUCT_CON)) {
              productType = ProductType.CON;
            } else if (productInfo.contains(UpdateConstant.VERSION_PRODUCT_CPU)) {
              productType = ProductType.CPU;
            } else if (productInfo.contains(UpdateConstant.VERSION_PRODUCT_VP6)) {
              productType = ProductType.VP6;
            } else if (productInfo.contains(UpdateConstant.VERSION_PRODUCT_VP7)) {
              productType = ProductType.VP7;
            }

            // 读取矩阵类型
            matrixTypes = new ArrayList<>();
            String matrixTypeInfo = versionInfoMap.get(UpdateConstant.VERSION_HEADER_TYPE);
            if (matrixTypeInfo != null) {
              String[] matrixTypeSplits = matrixTypeInfo.split(",");
              for (String type : matrixTypeSplits) {
                type = type.trim();
                Type matrixType = Type.valueOfUpdateName(type);
                if (matrixType != null && matrixType.isMatrix()) {
                  matrixTypes.add(matrixType);
                }
              }
            }

            // 读取接口类型
            String interfaceType = versionInfoMap.get(UpdateConstant.VERSION_HEADER_INTERFACE);
            if (interfaceType != null) {
              if (interfaceType.equalsIgnoreCase(UpdateConstant.VERSION_INTERFACE_SFP)) {
                ioInterfaceType = IoInterfaceType.SFP;
              } else if (interfaceType.equalsIgnoreCase(UpdateConstant.VERSION_INTERFACE_CAT)) {
                ioInterfaceType = IoInterfaceType.CAT;
              }
            }

            // 读取端口数
            String portsInfo = versionInfoMap.get(UpdateConstant.VERSION_HEADER_PORTS);
            if (portsInfo != null) {
              try {
                ports = Integer.parseInt(portsInfo);
              } catch (NumberFormatException exception) {
                ports = null;
              }
            }

            // 读取版本
            String version = versionInfoMap.get(UpdateConstant.VERSION_HEADER_SYS);
            if (version != null) {
              sys = parseVersion(version);
            }
            version = versionInfoMap.get(UpdateConstant.VERSION_HEADER_FPGA);
            if (version != null) {
              fpga = parseVersion(version);
            }
            version = versionInfoMap.get(UpdateConstant.VERSION_HEADER_FPGA_HDMI_TX);
            if (version != null) {
              fpgaHdmiTx = parseVersion(version);
            }
            version = versionInfoMap.get(UpdateConstant.VERSION_HEADER_FPGA_HDMI_RX);
            if (version != null) {
              fpgaHdmiRx = parseVersion(version);
            }
            version = versionInfoMap.get(UpdateConstant.VERSION_HEADER_FPGA_HDMI_4RX);
            if (version != null) {
              fpgaHdmi4Rx = parseVersion(version);
            }
            version = versionInfoMap.get(UpdateConstant.VERSION_HEADER_FPGA_HDMI_4RX_HALF_1G);
            if (version != null) {
              fpgaHdmi4RxHalf1G = parseVersion(version);
            }
            version = versionInfoMap.get(UpdateConstant.VERSION_HEADER_FPGA_HDMI_4RX_HALF_2_5G);
            if (version != null) {
              fpgaHdmi4RxHalf25G = parseVersion(version);
            }
            version = versionInfoMap.get(UpdateConstant.VERSION_HEADER_FPGA_CAESAR_PREVIEW);
            if (version != null) {
              fpgaCaesarPreview = parseVersion(version);
            }
            version = versionInfoMap.get(UpdateConstant.VERSION_HEADER_APP);
            if (version != null) {
              app = parseVersion(version);
            }
            version = versionInfoMap.get(UpdateConstant.VERSION_HEADER_IO);
            if (version != null) {
              io = parseVersion(version);
            }
            version = versionInfoMap.get(UpdateConstant.VERSION_HEADER_TRUNK);
            if (version != null) {
              trunk = parseVersion(version);
            }
            version = versionInfoMap.get(UpdateConstant.VERSION_HEADER_BRANCH);
            if (version != null) {
              branch = parseVersion(version);
            }
            version = versionInfoMap.get(UpdateConstant.VERSION_HEADER_TRUNK_144);
            if (version != null) {
              trunks.put(Type.PLUGIN_144, parseVersion(version));
            }
            version = versionInfoMap.get(UpdateConstant.VERSION_HEADER_TRUNK_384);
            if (version != null) {
              trunks.put(Type.PLUGIN_384, parseVersion(version));
            }
            version = versionInfoMap.get(UpdateConstant.VERSION_HEADER_TRUNK_816);
            if (version != null) {
              trunks.put(Type.PLUGIN_816, parseVersion(version));
            }

            logo = versionInfoMap.get(UpdateConstant.VERSION_HEADER_LOGO);
          } catch (RuntimeException ex) {
            log.warn("Fail to parse update package!", ex);
          } finally {
            if (buff != null) {
              buff.close();
            }
          }
        } catch (UnsupportedEncodingException ex) {
          log.warn("Unsupported encoding!", ex);
        }
        cpioIn.close();
        break;
      }
    }

    cpioIn.close();
    newInputStream.close();

    UpdatePackageInfo info = new UpdatePackageInfo();
    info.setApp(app);
    info.setBranch(branch);
    info.setFpga(fpga);
    info.setFpgaHdmiTx(fpgaHdmiTx);
    info.setFpgaHdmiRx(fpgaHdmiRx);
    info.setFpgaHdmi4Rx(fpgaHdmi4Rx);
    info.setFpgaHdmi4RxHalf1G(fpgaHdmi4RxHalf1G);
    info.setFpgaHdmi4RxHalf25G(fpgaHdmi4RxHalf25G);
    info.setFpgaCaesarPreview(fpgaCaesarPreview);
    info.setHardWare(hardWare);
    info.setIo(io);
    info.setIoInterfaceType(ioInterfaceType);
    info.setMatrixType(matrixTypes);
    info.setPorts(ports);
    info.setSys(sys);
    info.setTrunk(trunk);
    info.setProductType(productType);
    info.setLogo(logo);

    for (Map.Entry<Type, VersionDef> entry : trunks.entrySet()) {
      info.setTrunk(entry.getKey(), entry.getValue());
    }
    return info;
  }

  /**
   * 计算IO升级参数.
   *
   * @param matrixIndex 矩阵在级联中的索引
   * @param matrixType 矩阵类型
   * @param updateDataLevels 要升级的IO的索引的集合，索引从0开始
   * @return 升级参数
   */
  public static byte[] calculateUpdateParams(
      int matrixIndex, int matrixType, List<Integer> updateDataLevels) {
    long value = 0;
    for (Integer level : updateDataLevels) {
      value |= 1L << level;
    }

    // 计算参数
    byte[] bytes = null;
    if (value != 0) {
      if (matrixType == Type.PLUGIN_816.getValue()) {
        int matrixByteCount = 8;
        bytes = new byte[(matrixIndex + 1) * matrixByteCount];
        int byteIndex = matrixIndex * matrixByteCount;
        bytes[byteIndex] = (byte) (value & 0xff); // 8位
        bytes[byteIndex + 1] = (byte) ((value >> 8) & 0xff); // 8位
        bytes[byteIndex + 2] = (byte) ((value >> 16) & 0x01); // 1位
        value >>= 17;
        bytes[byteIndex + 4] = (byte) (value & 0xff); // 8位
        bytes[byteIndex + 5] = (byte) ((value >> 8) & 0xff); // 8位
        bytes[byteIndex + 6] = (byte) ((value >> 16) & 0x01); // 1位
      } else {
        // 每个矩阵占8个字节
        int matrixByteCount = 8;
        bytes = new byte[(matrixIndex + 1) * matrixByteCount];
        for (int i = matrixIndex * matrixByteCount; i < (matrixIndex + 1) * matrixByteCount; i++) {
          bytes[i] = (byte) (value >> (8 * (i - matrixIndex * matrixByteCount)) & 0xff);
        }
      }
    }
    return bytes;
  }
}
