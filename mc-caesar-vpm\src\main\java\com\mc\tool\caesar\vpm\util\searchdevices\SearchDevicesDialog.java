package com.mc.tool.caesar.vpm.util.searchdevices;

import com.google.common.eventbus.Subscribe;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarControllerConstants;
import com.mc.tool.caesar.api.datamodel.VersionSet;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.vpm.device.broadcast.BroadcastServerManager;
import com.mc.tool.caesar.vpm.device.broadcast.GridBroadcastServer;
import com.mc.tool.caesar.vpm.event.GridEvent;
import com.mc.tool.caesar.vpm.util.CaesarResources;
import com.mc.tool.caesar.vpm.util.ConnectDeviceUtility;
import com.mc.tool.caesar.vpm.util.searchdevices.Bundle.NbBundle;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.utility.EventBusProvider;
import com.mc.tool.framework.utility.UndecoratedAlert;
import com.mc.tool.framework.utility.UndecoratedDialog;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.SocketException;
import java.net.URL;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Locale;
import java.util.ResourceBundle;
import java.util.Timer;
import java.util.TimerTask;
import java.util.function.Consumer;
import javafx.application.Platform;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.control.DialogPaneEx;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.stage.Stage;
import javafx.stage.Window;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
@SuppressFBWarnings("SIC_INNER_SHOULD_BE_STATIC_ANON")
public final class SearchDevicesDialog extends UndecoratedDialog<Object> implements Initializable {

  @FXML private TableView<GridInfoData> tableView;
  @FXML private TableColumn<GridInfoData, String> indexCol;
  @FXML private TableColumn<GridInfoData, String> deviceCol;
  @FXML private TableColumn<GridInfoData, String> versionCol;
  @FXML private TableColumn<GridInfoData, String> statusCol;
  @FXML private TableColumn<GridInfoData, String> asCol;
  @FXML private TableColumn<GridInfoData, String> nameCol;
  @FXML private TableColumn<GridInfoData, String> addressCol;
  @FXML private TableColumn<GridInfoData, String> macCol;
  @FXML private TableColumn<GridInfoData, Boolean> connectCol;

  private volatile boolean shutdown = true;
  private ObservableList<GridInfoData> dataList = FXCollections.observableArrayList();
  private DatagramSocket datagramSocket = null;
  @Setter private static ApplicationBase app;
  @Setter private static Consumer<Entity> connectedAction;
  private static BooleanProperty isReloading = new SimpleBooleanProperty(false);

  /** Contructor. */
  public SearchDevicesDialog() {
    EventBusProvider.getEventBus().register(this);
    URL location =
        getClass()
            .getResource("/com/mc/tool/caesar/vpm/util/searchdevices/SearchDevices_View.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    loader.setController(this);
    try {
      getDialogPane().setContent(loader.load());
    } catch (IOException ex) {
      log.warn("Can not load SearchDevices_View.fxml", ex);
    }
    initButton();
  }

  public void destroy() {
    EventBusProvider.getEventBus().unregister(this);
  }

  private void initButton() {
    final DialogPaneEx dialogPane = getDialogPane();
    dialogPane.getButtonTypes().addAll(ButtonType.NEXT, ButtonType.CLOSE);
    dialogPane.lookupButton(ButtonType.NEXT).disableProperty().bind(isReloading);
    ((Button) dialogPane.lookupButton(ButtonType.NEXT))
        .setText(NbBundle.getMessage(SearchDevicesDialog.class, "reloadButton.text"));
    dialogPane
        .lookupButton(ButtonType.NEXT)
        .setOnMousePressed(
            (event) -> {
              event.consume();
              searchDevices();
            });
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    setTitle(CaesarResources.getString("searchForDevices.title"));

    indexCol.setText(null);
    deviceCol.setText(NbBundle.getMessage(SearchDevicesDialog.class, "deviceCol.text"));
    versionCol.setText(NbBundle.getMessage(SearchDevicesDialog.class, "versionCol.text"));
    statusCol.setText(NbBundle.getMessage(SearchDevicesDialog.class, "statusCol.text"));
    asCol.setText(NbBundle.getMessage(SearchDevicesDialog.class, "asCol.text"));
    nameCol.setText(NbBundle.getMessage(SearchDevicesDialog.class, "nameCol.text"));
    addressCol.setText(NbBundle.getMessage(SearchDevicesDialog.class, "addressCol.text"));
    macCol.setText(NbBundle.getMessage(SearchDevicesDialog.class, "macCol.text"));
    connectCol.setText(NbBundle.getMessage(SearchDevicesDialog.class, "connectCol.text"));

    indexCol.setCellFactory(col -> new IndexCell());
    deviceCol.setCellValueFactory(item -> item.getValue().getDeviceProperty());
    versionCol.setCellValueFactory(item -> item.getValue().getVersionProperty());
    statusCol.setCellFactory(col -> new StatusCell());
    asCol.setCellFactory(col -> new AsCell());
    nameCol.setCellValueFactory(item -> item.getValue().getNameProperty());
    addressCol.setCellValueFactory(item -> item.getValue().getAddressProperty());
    macCol.setCellValueFactory(item -> item.getValue().getMacProperty());

    connectCol.setCellFactory(col -> new ConnectTableCell());

    searchDevices();
    tableView.setItems(dataList);
  }

  private void searchDevices() {
    isReloading.set(true);
    dataList.clear();
    BroadcastServerManager.createServer(GridBroadcastServer.getInstance());
    try {
      Utilities.getGridInfo("s255.255.255.255".substring(1), "s255.255.255.255".substring(1));
    } catch (IOException ex) {
      log.warn("Fail to get get grid info!", ex);
    }
    // 1s后可以再次刷新
    new Timer(true)
        .schedule(
            new TimerTask() {

              @Override
              public void run() {
                PlatformUtility.runInFxThread(() -> isReloading.set(false));
              }
            },
            1000);
  }

  @Subscribe
  protected void onReceivedGridEvent(GridEvent event) {
    if (event.getType() == CaesarControllerConstants.BroadcastRequest.RET_GRIDINFO.getByteValue()) {
      GridInfoData data = new GridInfoData();
      data.setAddress(IpUtil.getAddressString(event.getGridData().getHostname()));
      data.setName(event.getGridData().getGridname());
      data.setVersion("v" + event.getGridData().getVersion().getAppVersion().toString());
      data.setDevice(event.getGridData().getDeviceName());
      data.setMac(getAddressString(event.getGridData().getMacAddress()));
      data.setHasMatrixInfo(event.getGridData().isHasMatrixInfo());
      data.setActive(event.getGridData().isActive());
      data.setBackup(event.getGridData().isBackup());

      PlatformUtility.runInFxThread(() -> addList(data));
    }
  }

  protected void broakcastServer() {
    try {
      datagramSocket = new DatagramSocket(CaesarConstants.GRID_PORT);
      datagramSocket.setSoTimeout(1000);
      setShutdown(false);
    } catch (SocketException ex) {
      setShutdown(true);
      Platform.runLater(
          () -> {
            UndecoratedAlert alert = new UndecoratedAlert(AlertExType.WARNING);
            alert.initOwner(getOwner());
            alert.setTitle(NbBundle.getMessage(SearchDevicesDialog.class, "alert.title.text"));
            alert.setHeaderText(null);
            alert.setContentText(
                NbBundle.getMessage(SearchDevicesDialog.class, "alert.content.text"));
            alert.showAndWait();
          });
    } catch (RuntimeException ex) {
      log.warn("Fail to send udp data!", ex);
    }
    byte[] receiveData = new byte[1024];
    if (!dataList.isEmpty()) {
      dataList.clear();
    }
    while (!isShutdown()) {
      try {
        Arrays.fill(receiveData, (byte) 0);
        DatagramPacket packet = new DatagramPacket(receiveData, receiveData.length);
        datagramSocket.receive(packet);
        InputStream is = new ByteArrayInputStream(receiveData);
        CfgReader reader = new CfgReader(is);
        try {
          if (reader.readByteValue() == CaesarControllerConstants.BIN_ESC) {
            int serverByte = reader.readByteValue();
            int requestByte = reader.readByteValue();
            if (serverByte
                    == CaesarControllerConstants.BroadcastRequest.RET_GRIDINFO
                        .getServerResponseByte()
                && requestByte
                    == CaesarControllerConstants.BroadcastRequest.RET_GRIDINFO.getByteValue()) {
              GridInfoData gmd = readGridInfoData(reader);
              addList(gmd);
            }
          }
        } catch (ConfigException ex) {
          log.warn(null, ex);
        }
      } catch (IOException ex) {
        setShutdown(true);
        datagramSocket.close();
      }
    }
    dataList.sort(new SearchComparator());
    isReloading.set(false);
  }

  private void addList(GridInfoData gmd) {
    String mac = gmd.getMac();
    int index = 0;
    int insertIndex = 0;
    for (GridInfoData data : dataList) {
      if (mac.equals(data.getMac())) {
        return;
      }
      // 查找应该插入的位置
      if (new SearchComparator().compare(data, gmd) > 0) {
        insertIndex = index;
      }
      index++;
    }
    dataList.add(insertIndex, gmd);
  }

  private void setShutdown(boolean shutdown) {
    this.shutdown = shutdown;
  }

  private boolean isShutdown() {
    return this.shutdown;
  }

  private GridInfoData readGridInfoData(CfgReader reader) throws ConfigException {
    GridInfoData gmd = new GridInfoData();
    reader.read2ByteValue();
    byte[] src = reader.readByteArray(4);
    gmd.setAddress(IpUtil.getAddressString(src));
    reader.readByteArray(4);
    reader.readByteArray(4);
    String gridname = reader.readString(CaesarConstants.NAME_LEN);
    gmd.setName(gridname);
    String device = reader.readString(CaesarConstants.MATRIX_DEVICE_LEN);
    gmd.setDevice(device);
    VersionSet version = new VersionSet();
    version.readData(reader);
    byte[] mac = reader.readByteArray(6);
    gmd.setMac(getAddressString(mac));
    return gmd;
  }

  private String getAddressString(byte[] address) {
    if (address.length == 6) {
      return String.format(
              "%02x:%02x:%02x:%02x:%02x:%02x",
              address[0], address[1], address[2], address[3], address[4], address[5])
          .toUpperCase(Locale.ENGLISH);
    }
    return "";
  }

  private static class IndexCell extends TableCell<GridInfoData, String> {
    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);

      if (!empty) {
        int rowIndex = this.getIndex() + 1;
        this.setText(String.valueOf(rowIndex));
      }
    }
  }

  private static class StatusCell extends TableCell<GridInfoData, String> {
    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);

      if (!empty) {
        Object rowItem = this.getTableRow().getItem();
        GridInfoData data;
        if (rowItem instanceof GridInfoData) {
          data = (GridInfoData) rowItem;
          if (data.isHasMatrixInfo()) {
            this.setText(data.isActive() ? "已激活" : "未激活");
          } else {
            this.setText("");
          }
        }
      }
    }
  }

  private static class AsCell extends TableCell<GridInfoData, String> {
    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);

      if (!empty) {
        Object rowItem = this.getTableRow().getItem();
        GridInfoData data;
        if (rowItem instanceof GridInfoData) {
          data = (GridInfoData) rowItem;
          if (data.isHasMatrixInfo()) {
            this.setText(data.isBackup() ? "备" : "主");
          } else {
            this.setText("");
          }
        }
      }
    }
  }

  private static class ConnectTableCell extends TableCell<GridInfoData, Boolean> {
    @Override
    public void updateItem(Boolean item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);

      if (!empty) {
        Object rowItem = this.getTableRow().getItem();
        GridInfoData data = null;
        if (rowItem instanceof GridInfoData) {
          data = (GridInfoData) rowItem;
        }
        String ip;
        if (data != null) {
          ip = data.getAddress();
        } else {
          ip = "";
        }
        Button button = new Button();
        button.setPrefSize(70, 25);
        button.setText(NbBundle.getMessage(SearchDevicesDialog.class, "connectCol.text"));
        button.disableProperty().bind(isReloading);
        this.setGraphic(button);
        button.setOnAction(
            event -> {
              Window window = getScene().getWindow();
              if (window instanceof Stage) {
                Stage stage = (Stage) window;
                stage.close();
              }
              Platform.runLater(
                  () -> ConnectDeviceUtility.connectToDevice(app, connectedAction, ip));
            });
      }
    }
  }

  private static class SearchComparator implements Comparator<GridInfoData>, Serializable {

    /** . */
    private static final long serialVersionUID = 1L;

    @Override
    public int compare(GridInfoData infoData1, GridInfoData infoData2) {
      if (infoData1.getDevice().compareTo(infoData2.getDevice()) == 0) {
        return infoData1.getName().compareTo(infoData2.getName());
      }
      return infoData1.getDevice().compareTo(infoData2.getDevice());
    }
  }
}
