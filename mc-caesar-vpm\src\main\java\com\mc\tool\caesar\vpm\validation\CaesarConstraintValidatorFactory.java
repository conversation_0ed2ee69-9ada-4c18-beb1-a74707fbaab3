package com.mc.tool.caesar.vpm.validation;

import com.mc.tool.caesar.api.datamodel.SystemConfigData;
import com.mc.tool.caesar.vpm.validation.constraints.impl.UniqueDualMasterVirtualIpImpl;
import com.mc.tool.caesar.vpm.validation.constraints.impl.UniqueDualMasterVirtualRouteIdImpl;
import com.mc.tool.caesar.vpm.validation.constraints.impl.UniqueMasterIpImpl;
import com.mc.tool.caesar.vpm.validation.constraints.impl.UniquePrimaryIpImpl;
import com.mc.tool.caesar.vpm.validation.constraints.impl.UniqueStandbyIpImpl;
import com.mc.tool.caesar.vpm.validation.constraints.impl.UniqueVirtualIpImpl;
import com.mc.tool.caesar.vpm.validation.constraints.impl.UniqueVirtualRouteIdImpl;
import java.util.HashMap;
import java.util.Map;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorFactory;

/**
 * 自定义的ConstraintValidatorFactory，支持依赖注入.
 * 用于为验证器注入SystemConfigData等依赖项.
 */
public class CaesarConstraintValidatorFactory implements ConstraintValidatorFactory {

  private final ConstraintValidatorFactory defaultFactory;
  private final Map<String, Object> dependencies;

  /**
   * 构造函数.
   *
   * @param defaultFactory 默认的验证器工厂
   */
  public CaesarConstraintValidatorFactory(ConstraintValidatorFactory defaultFactory) {
    this.defaultFactory = defaultFactory;
    this.dependencies = new HashMap<>();
  }

  /**
   * 添加依赖项.
   *
   * @param name       依赖项名称
   * @param dependency 依赖项对象
   */
  public void addDependency(String name, Object dependency) {
    dependencies.put(name, dependency);
  }

  /**
   * 设置SystemConfigData依赖.
   *
   * @param systemConfigData 系统配置数据
   */
  public void setSystemConfigData(SystemConfigData systemConfigData) {
    addDependency("systemConfigData", systemConfigData);
  }

  @SuppressWarnings("unchecked")
  @Override
  public <T extends ConstraintValidator<?, ?>> T getInstance(Class<T> key) {
    T instance;
    // 对于特定的验证器，使用自定义的创建逻辑
    SystemConfigData systemConfigData = (SystemConfigData) dependencies.get("systemConfigData");
    if (UniqueVirtualIpImpl.class.equals(key)) {
      instance = (T) new UniqueVirtualIpImpl(systemConfigData);
    } else if (UniquePrimaryIpImpl.class.equals(key)) {
      instance = (T) new UniquePrimaryIpImpl(systemConfigData);
    } else if (UniqueStandbyIpImpl.class.equals(key)) {
      instance = (T) new UniqueStandbyIpImpl(systemConfigData);
    } else if (UniqueDualMasterVirtualIpImpl.class.equals(key)) {
      instance = (T) new UniqueDualMasterVirtualIpImpl(systemConfigData);
    } else if (UniqueMasterIpImpl.class.equals(key)) {
      instance = (T) new UniqueMasterIpImpl(systemConfigData);
    } else if (UniqueDualMasterVirtualRouteIdImpl.class.equals(key)) {
      instance = (T) new UniqueDualMasterVirtualRouteIdImpl(systemConfigData);
    } else if (UniqueVirtualRouteIdImpl.class.equals(key)) {
      instance = (T) new UniqueVirtualRouteIdImpl(systemConfigData);
    } else {
      // 对于其他验证器，使用默认工厂
      instance = defaultFactory.getInstance(key);
    }
    return instance;
  }

  @Override
  public void releaseInstance(ConstraintValidator<?, ?> instance) {
    // 对于自定义创建的实例，不需要特殊的释放逻辑
    if (!(instance instanceof UniqueVirtualIpImpl)) {
      defaultFactory.releaseInstance(instance);
    }
  }
}
