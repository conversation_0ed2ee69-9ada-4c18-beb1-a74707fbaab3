package com.mc.tool.caesar.vpm.pages.systemedit.property;

import com.mc.common.util.PlatformUtility;
import java.util.Objects;
import java.util.function.Function;
import javafx.beans.property.Property;
import javafx.beans.property.SimpleObjectProperty;

/**
 * .
 */
public class CaesarProperty<T> extends SimpleObjectProperty<T> {
  private final Property<T> value;
  private final CaesarPropertySender<T> sender;
  private final Function<T, Boolean> callback;

  /**
   * 属性.
   *
   * @param value 实际的属性值
   * @param sender 属性值发送器
   */
  public CaesarProperty(Property<T> value, CaesarPropertySender<T> sender) {
    this.value = value;
    this.sender = sender;
    this.bind(value);
    this.callback = null;
  }

  /**
   * 属性.
   *
   * @param value 实际的属性值
   * @param sender 属性值发送器
   * @param callback 属性确认器
   */
  public CaesarProperty(
      Property<T> value, CaesarPropertySender<T> sender, Function<T, Bo<PERSON>an> callback) {
    this.value = value;
    this.sender = sender;
    this.bind(value);
    this.callback = callback;
  }

  @Override
  public void set(T newValue) {
    T currentValue = value.getValue();
    if (Objects.equals(currentValue, newValue)) {
      return;
    }
    if (currentValue instanceof Number
        && newValue instanceof Number
        && Math.abs(((Number) currentValue).doubleValue() - ((Number) newValue).doubleValue())
            < 1e-10) {
      return;
    }
    value.setValue(newValue);
    if (callback == null) {
      if (sender != null) {
        sender.sendValue(newValue);
      }
    } else {
      if (callback.apply(newValue)) {
        if (sender != null) {
          sender.sendValue(newValue);
        }
      } else {
        PlatformUtility.runInFxThreadLater(() -> value.setValue(currentValue));
      }
    }
  }
}
