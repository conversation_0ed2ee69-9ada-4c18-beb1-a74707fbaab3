package com.mc.tool.caesar.vpm.pages.operation.videowall.view;

import java.net.URL;
import java.util.ResourceBundle;
import java.util.function.UnaryOperator;
import javafx.beans.binding.Bindings;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ReadOnlyBooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.event.ActionEvent;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.TextField;
import javafx.scene.control.TextFormatter;
import javafx.scene.control.TextFormatter.Change;
import javafx.scene.layout.Pane;
import javafx.scene.layout.Region;
import javafx.util.converter.NumberStringConverter;
import org.controlsfx.validation.ValidationSupport;

/**
 * .
 *
 * <AUTHOR>
 */

public class SignalCuttingConfigViewController extends ValidationSupport implements Initializable {

  @FXML
  private TextField name;
  @FXML
  private TextField top;
  @FXML
  private TextField bottom;
  @FXML
  private TextField left;
  @FXML
  private TextField right;

  @FXML
  private TextField resWidth;

  @FXML
  private TextField resHeight;

  @FXML
  private Pane oldScreen;
  @FXML
  private Region newScreen;

  private StringProperty nameProperty = new SimpleStringProperty();

  private BooleanProperty validChange = new SimpleBooleanProperty();
  private IntegerProperty resWidthProperty = new SimpleIntegerProperty(1920);
  private IntegerProperty resHeightProperty = new SimpleIntegerProperty(1080);

  private IntegerProperty topProperty = new SimpleIntegerProperty(0);
  private IntegerProperty leftProperty = new SimpleIntegerProperty(0);
  private IntegerProperty rightProperty = new SimpleIntegerProperty(0);
  private IntegerProperty bottomProperty = new SimpleIntegerProperty(0);

  SignalCuttingConfigViewController(String name, int resWidth, int resHeight, int left, int top, int right,
                                    int bottom) {
    nameProperty.set(name);
    resWidthProperty.set(resWidth);
    resHeightProperty.set(resHeight);
    topProperty.set(top);
    leftProperty.set(left);
    rightProperty.set(right);
    bottomProperty.set(bottom);
  }

  public int getTop() {
    return topProperty.get();
  }

  public int getLeft() {
    return leftProperty.get();
  }

  public int getBottom() {
    return bottomProperty.get();
  }

  public int getRight() {
    return rightProperty.get();
  }

  public String getName() {
    return nameProperty.get();
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    setValidationDecorator(null);

    UnaryOperator<Change> integerFilter = change -> {
      String newText = change.getControlNewText();
      if (newText.matches("[0-9]+")) {
        return change;
      }
      return null;
    };

    name.textProperty().bindBidirectional(nameProperty);
    NumberStringConverter stringConverter = new NumberStringConverter("0");
    top.setTextFormatter(new TextFormatter<>(integerFilter));
    top.textProperty().bindBidirectional(topProperty, stringConverter);
    EventHandler<ActionEvent> handler = (event) -> {
    };
    top.setOnAction(handler);
    bottom.setTextFormatter(new TextFormatter<>(integerFilter));
    bottom.textProperty().bindBidirectional(bottomProperty, stringConverter);
    bottom.setOnAction(handler);
    left.setTextFormatter(new TextFormatter<>(integerFilter));
    left.textProperty().bindBidirectional(leftProperty, stringConverter);
    left.setOnAction(handler);
    right.setTextFormatter(new TextFormatter<>(integerFilter));
    right.textProperty().bindBidirectional(rightProperty, stringConverter);
    right.setOnAction(handler);

    resWidth.textProperty().bindBidirectional(resWidthProperty, stringConverter);
    resWidth.setOnAction(handler);
    resHeight.textProperty().bindBidirectional(resHeightProperty, new NumberStringConverter("0"));
    resHeight.setOnAction(handler);

    newScreen.translateXProperty().bind(Bindings.min(oldScreen.widthProperty(),
        leftProperty.multiply(
            oldScreen.widthProperty().divide(Bindings.max(1.0, resWidthProperty)))));
    newScreen.translateYProperty().bind(Bindings.min(oldScreen.heightProperty(),
        topProperty.multiply(
            oldScreen.heightProperty().divide(Bindings.max(1.0, resHeightProperty)))));
    newScreen.prefWidthProperty().bind(Bindings.min(oldScreen.widthProperty(), Bindings.max(0.0,
        resWidthProperty.subtract(leftProperty).subtract(rightProperty)
            .multiply(oldScreen.widthProperty()).divide(Bindings.max(1.0, resWidthProperty)))));
    newScreen.prefHeightProperty().bind(Bindings.min(oldScreen.heightProperty(), Bindings.max(0.0,
        resHeightProperty.subtract(topProperty).subtract(bottomProperty)
            .multiply(oldScreen.heightProperty()).divide(Bindings.max(1.0, resHeightProperty)))));

    validChange.bind(Bindings.createBooleanBinding(() -> {
      try {
        int topInt = topProperty.get();
        if (topInt < 0) {
          return false;
        }
        int bottomInt = bottomProperty.get();
        if (bottomInt < 0) {
          return false;
        }
        int leftInt = leftProperty.get();
        if (leftInt < 0) {
          return false;
        }
        int rightInt = rightProperty.get();
        if (rightInt < 0) {
          return false;
        }
        if (topInt + bottomInt >= resHeightProperty.get()) {
          return false;
        }
        if (leftInt + rightInt >= resWidthProperty.get()) {
          return false;
        }
      } catch (NumberFormatException e) {
        return false;
      }
      return true;
    }, topProperty, rightProperty, bottomProperty, leftProperty, resHeightProperty,
        resWidthProperty));
  }

  public ReadOnlyBooleanProperty validProperty() {
    return validChange;
  }
}
