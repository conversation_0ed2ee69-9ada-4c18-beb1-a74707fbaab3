package com.mc.tool.caesar.api.interfaces;

/**
 * .
 */
public interface CommitRollback {

  void commit();

  void commit(Threshold paramThreshold);

  void commitProperty(String paramString, int... paramVarArgs);

  void commitProperty(Threshold paramThreshold, String paramString,
      int... paramVarArgs);

  void rollback();

  void rollback(Threshold paramThreshold);

  void rollbackProperty(String paramString, int... paramVarArgs);

  void rollbackProperty(Threshold paramThreshold, String paramString,
      int... paramVarArgs);

  boolean isChanged();

  boolean isChanged(Threshold paramThreshold);

  boolean equals(Threshold paramThreshold);

  boolean isPropertyChanged(String paramString, int... paramVarArgs);

  boolean isPropertyChanged(Threshold paramThreshold, String paramString,
      int... paramVarArgs);

  boolean propertyEquals(Threshold paramThreshold, String paramString,
      int... paramVarArgs);

  void addChangedListener(ChangedListener paramChangedListener);

  void removeChangedListener(ChangedListener paramChangedListener);
}

