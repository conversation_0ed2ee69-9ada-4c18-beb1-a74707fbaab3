package com.mc.tool.caesar.vpm.util.searchdevices;

import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;

/**
 * .
 */
public class GridInfoData {

  private SimpleStringProperty device = new SimpleStringProperty();
  private SimpleStringProperty version = new SimpleStringProperty();
  private SimpleStringProperty name = new SimpleStringProperty();

  private BooleanProperty hasMatrixInfo = new SimpleBooleanProperty();
  private SimpleBooleanProperty active = new SimpleBooleanProperty();
  private SimpleBooleanProperty backup = new SimpleBooleanProperty();

  private SimpleStringProperty address = new SimpleStringProperty();
  private SimpleStringProperty mac = new SimpleStringProperty();
  private SimpleStringProperty category = new SimpleStringProperty();

  public GridInfoData() {}

  public String getDevice() {
    return this.device.get();
  }

  public StringProperty getDeviceProperty() {
    return this.device;
  }

  public String getVersion() {
    return this.version.get();
  }

  public StringProperty getVersionProperty() {
    return this.version;
  }

  public void setVersion(String version) {
    this.version.set(version);
  }

  public String getName() {
    return this.name.get();
  }

  public StringProperty getNameProperty() {
    return this.name;
  }

  public boolean isHasMatrixInfo() {
    return hasMatrixInfo.get();
  }

  public BooleanProperty getHasMatrixInfoProperty() {
    return hasMatrixInfo;
  }

  public boolean isActive() {
    return active.get();
  }

  public BooleanProperty getActiveProperty() {
    return active;
  }

  public boolean isBackup() {
    return backup.get();
  }

  public BooleanProperty getBackupProperty() {
    return backup;
  }

  public String getAddress() {
    return this.address.get();
  }

  public StringProperty getAddressProperty() {
    return this.address;
  }

  public String getMac() {
    return this.mac.get();
  }

  public StringProperty getMacProperty() {
    return this.mac;
  }

  public String getCategory() {
    return this.category.get();
  }

  public StringProperty getCategoryProperty() {
    return this.category;
  }

  public void setDevice(String device) {
    this.device.set(device);
  }

  public void setName(String name) {
    this.name.set(name);
  }

  public void setAddress(String address) {
    this.address.set(address);
  }

  public void setMac(String mac) {
    this.mac.set(mac);
  }

  public void setCategory(String category) {
    this.category.set(category);
  }

  public void setHasMatrixInfo(boolean hasMatrixInfo) {
    this.hasMatrixInfo.set(hasMatrixInfo);
  }

  public void setBackup(boolean backup) {
    this.backup.set(backup);
  }

  public void setActive(boolean active) {
    this.active.set(active);
  }
}
