package com.mc.tool.caesar.vpm.pages.extenderosdupdate;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.utils.ExtenderUpdateInfo;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.vpm.pages.update.UpdateLogger;
import com.mc.tool.caesar.vpm.pages.update.UpdateTask;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javafx.beans.property.BooleanProperty;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class OsdUpdater {
  private final CaesarSwitchDataModel model;
  private final List<OsdUpdateItem> needToUpdateItems = new ArrayList<>();
  private final File updateFile;
  private final UpdateLogger logger;
  private final BooleanProperty updating;
  private final String updateMd5;
  private boolean canceled = false;

  private List<OsdUpdateTask> updateTasks = new ArrayList<>();

  /**
   * 创建osd updater.
   *
   * @param model model
   * @param updateFile 更新文件
   * @param logger 日志
   * @param updating 当前是否在升级的属性
   * @param updateMd5 新的MD5信息
   */
  public OsdUpdater(
      CaesarSwitchDataModel model,
      File updateFile,
      UpdateLogger logger,
      BooleanProperty updating,
      String updateMd5) {
    this.updateFile = updateFile;
    this.updating = updating;
    this.model = model;
    this.logger = logger;
    this.updateMd5 = updateMd5;
  }

  public void setUpdateItems(Collection<OsdUpdateItem> needToUpdateItems) {
    this.needToUpdateItems.clear();
    this.needToUpdateItems.addAll(needToUpdateItems);
  }

  /** 取消升级. */
  public void cancel() {
    canceled = true;
    for (OsdUpdateTask task : updateTasks) {
      task.setCancelled(true);
    }
  }

  /** 开始升级. */
  public void update() {
    prepareUpdate();

    Map<ExtenderData, OsdUpdateItem> map = new HashMap<>();
    for (OsdUpdateItem extItem : needToUpdateItems) {
      ExtenderData extenderData = extItem.getExtenderData();
      if (null != extenderData) {
        map.put(extenderData, extItem);
      }
    }

    Collection<ExtenderUpdateInfo> extenderUpdateInfos =
        Utilities.getExtenderUpdateInfos(model, map.keySet().toArray(new ExtenderData[0]));

    for (ExtenderUpdateInfo extenderUpdateInfo : extenderUpdateInfos) {
      if (null == extenderUpdateInfo) {
        continue;
      }
      // 查找对应的升级项
      List<OsdUpdateItem> matrixUpdateItems = new ArrayList<>();
      for (ExtenderData extenderData : extenderUpdateInfo.getExtenders()) {
        OsdUpdateItem item = map.get(extenderData);
        if (item != null) {
          matrixUpdateItems.add(item);
          logger.addLog("Start to update " + item.getName().get() + "'s osd.");
        }
      }
      // 创建任务
      OsdUpdateTask task =
          new OsdUpdateTask(extenderUpdateInfo, matrixUpdateItems, updateFile, logger, updateMd5);
      updateTasks.add(task);
      // 绑定进度条
      for (OsdUpdateItem item : matrixUpdateItems) {
        item.getProgress().bind(task.progressProperty());
      }
    }

    for (OsdUpdateTask task : updateTasks) {
      new Thread(task).start();
    }
    new Thread(new CheckFinishTask()).start();
  }

  protected void prepareUpdate() {
    logger.addLog("Prepare to update.");
    updating.set(true);
    canceled = false;
    updateTasks.clear();
    for (OsdUpdateItem item : needToUpdateItems) {
      item.getProgress().unbind();
      item.getProgress().set(0);
    }
  }

  protected void updateFinish() {
    for (OsdUpdateItem item : needToUpdateItems) {
      item.getProgress().unbind();
    }
    updating.set(false);
    if (canceled) {
      logger.addLog("Update cancelled!");
    } else {
      logger.addLog("Update finish!");
    }
  }

  static class OsdUpdateTask extends UpdateTask {
    private final CaesarSwitchDataModel model;
    private final ExtenderUpdateInfo updateInfo;
    private final Collection<OsdUpdateItem> updateItems;
    private final File updateFile;
    private final UpdateLogger logger;
    private final String updateMd5;

    public OsdUpdateTask(
        ExtenderUpdateInfo updateInfo,
        Collection<OsdUpdateItem> updateItems,
        File updateFile,
        UpdateLogger logger,
        String updateMd5) {
      this.updateInfo = updateInfo;
      this.updateItems = updateItems;
      this.model = updateInfo.getModel();
      this.updateFile = updateFile;
      this.logger = logger;
      this.updateMd5 = updateMd5;
    }

    @Override
    protected Object call() throws Exception {
      ByteArrayOutputStream baos = new ByteArrayOutputStream();
      // 读取文件
      try (FileInputStream fis = new FileInputStream(updateFile)) {
        byte[] temp = new byte[512];
        int readCount;
        while ((readCount = fis.read(temp)) > 0) {
          baos.write(temp, 0, readCount);
        }
      } catch (IOException exception) {
        logger.addLog("Fail to read file " + updateFile.getPath() + "! ");
        return null;
      }
      try {
        boolean successful = updateImpl(baos.toByteArray(), 512, 0, logger);
        // 如果升级完成，检查是否升级成功
        if (successful) {
          waitForReboot();
          check();
        }
      } catch (Exception exce) {
        logger.addLog("Update fail! " + exce.getMessage(), exce);
      }
      finished = true;
      return null;
    }

    protected void waitForReboot() {
      logger.addLog("Waiting for reboot!");
      int count = 0;
      int maxCount = 60;
      while (count < maxCount && !cancelled) {
        boolean pass = true;
        for (OsdUpdateItem item : updateItems) {

          String md5 = "";
          try {
            md5 = model.getOsdMd5(item.getExtenderData());
          } catch (DeviceConnectionException | ConfigException | BusyException ex) {
            log.warn("Fail to get osd md5 for {}!", item.getExtenderData().getName(), ex);
          }
          item.setCurrentMd5(md5);
          if (!md5.equalsIgnoreCase(updateMd5)) {
            pass = false;
            break;
          }
        }
        if (pass) {
          break;
        }
        count++;
        try {
          Thread.sleep(1000);
        } catch (InterruptedException exception) {
          log.warn("Sleep is interrupted!", exception);
        }
      }
    }

    protected void check() {
      for (OsdUpdateItem item : updateItems) {
        if (item.getCurrentMd5().get().equalsIgnoreCase(updateMd5)) {
          logger.addLog(item.getName().get() + " update successfully.");
        } else {
          logger.addLog(item.getName().get() + " update fail!");
        }
      }
    }

    @Override
    protected void updateOpen() throws DeviceConnectionException, ConfigException, BusyException {
      model.getController().setExtenderParallelUpdateOpen(updateInfo.getUpdateParams());
    }

    @Override
    protected void updateWrite(int offset, byte[] dataToWrite)
        throws DeviceConnectionException, ConfigException, BusyException {
      model.getController().setExtenderParallelUpdateWrite(offset, dataToWrite, true);
    }

    @Override
    protected void updateClose(boolean successful)
        throws DeviceConnectionException, ConfigException, BusyException {
      model.getController().setExtenderParallelUpdateClose(successful);
    }
  }

  class CheckFinishTask implements Runnable {
    @Override
    public void run() {

      while (true) {
        // 检查是否全部task都执行完成.
        boolean finish = true;
        for (UpdateTask task : updateTasks) {
          if (!task.isDone()) {
            finish = false;
            break;
          }
        }
        if (finish) {
          break;
        }

        try {
          Thread.sleep(100);
        } catch (Exception exception) {
          log.warn("Sleep is interrupted!", exception);
        }
      }

      PlatformUtility.runInFxThread(OsdUpdater.this::updateFinish);
    }
  }
}
