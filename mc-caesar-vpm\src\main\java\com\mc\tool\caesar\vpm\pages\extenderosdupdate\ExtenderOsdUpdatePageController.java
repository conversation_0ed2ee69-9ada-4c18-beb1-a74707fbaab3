package com.mc.tool.caesar.vpm.pages.extenderosdupdate;

import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.update.UpdateLogger;
import com.mc.tool.caesar.vpm.util.update.UpdatePackageInfo;
import com.mc.tool.caesar.vpm.util.update.UpdateUtilities;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import java.beans.PropertyChangeListener;
import java.io.File;
import java.net.URL;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.beans.value.ChangeListener;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.Button;
import javafx.scene.control.ListView;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.cell.CheckBoxTableCell;
import javafx.scene.control.cell.ProgressBarTableCell;
import javafx.stage.FileChooser;
import javafx.stage.FileChooser.ExtensionFilter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class ExtenderOsdUpdatePageController implements Initializable, ViewControllable {

  @FXML private TableView<OsdUpdateItem> tableView;
  @FXML private Button updateButton;
  @FXML private Button loadButton;
  @FXML private Button cancelButton;
  @FXML private Button selectAllButton;
  @FXML private ListView<String> loglist;
  @FXML private TableColumn<OsdUpdateItem, String> deviceCol;
  @FXML private TableColumn<OsdUpdateItem, String> nameCol;
  @FXML private TableColumn<OsdUpdateItem, String> portCol;
  @FXML private TableColumn<OsdUpdateItem, String> serialCol;
  @FXML private TableColumn<OsdUpdateItem, String> currentMd5Col;
  @FXML private TableColumn<OsdUpdateItem, String> updateMd5Col;
  @FXML private TableColumn<OsdUpdateItem, Boolean> updateCol;
  @FXML private TableColumn<OsdUpdateItem, Double> progressCol;

  private ObservableList<OsdUpdateItem> items = FXCollections.observableArrayList();

  private CaesarDeviceController deviceController;

  private PropertyChangeListener propertyChangeListener;

  private Map<Integer, String> md5s = new HashMap<>();

  private StringProperty updateMd5 = new SimpleStringProperty();
  private File updateFile = null;

  private BooleanProperty updating = new SimpleBooleanProperty();

  private BooleanProperty selectedAll = new SimpleBooleanProperty();

  private WeakAdapter weakAdapter = new WeakAdapter();

  private ChangeListener<Boolean> itemSelectedChangeListener;

  private ObservableList<String> logs = FXCollections.observableArrayList();

  private UpdateLogger logger = new UpdateLogger(logs);

  private OsdUpdater osdUpdater = null;

  protected final String[] updateData = {
    ExtenderData.PROPERTY_STATUS, ExtenderData.PROPERTY_VERSION, ExtenderData.PROPERTY_PORT
  };

  /** . */
  public ExtenderOsdUpdatePageController() {
    propertyChangeListener = (evt) -> PlatformUtility.runInFxThread(this::updateItems);

    itemSelectedChangeListener =
        (obs, oldVal, newVal) -> {
          if (!newVal) {
            selectedAll.set(false);
          }
        };
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    // 清除之前的listener
    if (this.deviceController != null && this.deviceController.getDataModel() != null) {
      this.deviceController
          .getDataModel()
          .removePropertyChangeListener(updateData, propertyChangeListener);
    }
    //
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
    }

    CaesarSwitchDataModel model = this.deviceController.getDataModel();
    model.addPropertyChangeListener(updateData, propertyChangeListener);

    updateItems();
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceController;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    initTable();

    cancelButton.disableProperty().bind(updating.not());
    updateButton.disableProperty().bind(updating.or(updateMd5.isNull()));
    selectAllButton.disableProperty().bind(updating.or(updateMd5.isNull()));
    loadButton.disableProperty().bind(updating);

    selectedAll.addListener(
        weakAdapter.wrap(
            (observable, oldValue, newValue) -> {
              boolean newv = newValue;
              if (newv) {
                selectAllButton.setText(
                    Bundle.NbBundle.getMessage("ExtenderOsdUpdate.seleteAllButton.text.opposite"));
              } else {
                selectAllButton.setText(
                    Bundle.NbBundle.getMessage("ExtenderOsdUpdate.seleteAllButton.text"));
              }
            }));

    loglist.setItems(logs);
  }

  protected void initTable() {
    tableView.editableProperty().bind(updateMd5.isNotNull());
    deviceCol.setCellValueFactory((item) -> item.getValue().getDevice());
    nameCol.setCellValueFactory((item) -> item.getValue().getName());
    serialCol.setCellValueFactory((item) -> item.getValue().getSerial());
    portCol.setCellValueFactory((item) -> item.getValue().getPort());
    currentMd5Col.setCellValueFactory((item) -> item.getValue().getCurrentMd5());
    updateMd5Col.setCellValueFactory((item) -> updateMd5);
    updateCol.setCellFactory(CheckBoxTableCell.forTableColumn(updateCol));
    updateCol.setCellValueFactory((item) -> item.getValue().getSelected());

    progressCol.setCellFactory(ProgressBarTableCell.forTableColumn());
    progressCol.setCellValueFactory((item) -> item.getValue().getProgressBackup());

    tableView.setItems(items);
  }

  protected void updateItems() {
    if (deviceController == null || deviceController.getDataModel() == null) {
      return;
    }
    CaesarSwitchDataModel model = deviceController.getDataModel();
    List<OsdUpdateItem> retainItems = new ArrayList<>();
    for (ExtenderData extenderData : model.getConfigDataManager().getActiveExtenders()) {
      if (!isUpgradable(extenderData)) {
        continue;
      }
      OsdUpdateItem osdUpdateItem = null;
      for (OsdUpdateItem item : items) {
        if (item.getExtenderData() == extenderData) {
          osdUpdateItem = item;
          break;
        }
      }
      if (osdUpdateItem == null) {
        osdUpdateItem = new OsdUpdateItem();
        osdUpdateItem.getSelected().addListener(weakAdapter.wrap(itemSelectedChangeListener));
        items.add(osdUpdateItem);
      }
      osdUpdateItem.setExtenderData(extenderData);
      String md5 = getMd5(extenderData.getId());
      osdUpdateItem.setCurrentMd5(md5);

      retainItems.add(osdUpdateItem);
    }
    items.retainAll(retainItems);
    items.sort(Comparator.comparing(osdUpdateItem -> osdUpdateItem.getPort().get()));

    tableView.refresh();
  }

  protected boolean isUpgradable(ExtenderData extenderData) {
    // USB外设与VPCON外设不升级osd
    if (extenderData.isUsbConType() || extenderData.isUsbCpuType() || extenderData.isVpConType()) {
      return false;
    }

    // 离线设备不升级
    if (extenderData.getPort() == 0 && extenderData.getRdPort() == 0) {
      return false;
    }

    // 只有RX需要升级
    if (!extenderData.isConType()) {
      return false;
    }

    return true;
  }

  protected synchronized String getMd5(int id) {
    String md5 = md5s.get(id);
    return md5;
  }

  protected synchronized void putMd5(int id, String md5) {
    md5s.put(id, md5);
  }

  /** 刷新md5信息. */
  public void refresh() {
    CaesarSwitchDataModel model = deviceController.getDataModel();

    for (ExtenderData extenderData : model.getConfigDataManager().getActiveExtenders()) {
      if (!isUpgradable(extenderData)) {
        continue;
      }
      try {
        putMd5(extenderData.getId(), model.getOsdMd5(extenderData));
      } catch (DeviceConnectionException | ConfigException | BusyException exception) {
        log.warn("Fail to get md5 for {}.", extenderData.getName());
        break;
      }
    }

    PlatformUtility.runInFxThread(this::updateItems);
  }

  @FXML
  protected void onLoad(ActionEvent event) {
    FileChooser fileChooser = new FileChooser();
    fileChooser.getExtensionFilters().add(new ExtensionFilter("SWU", "*.swu"));

    File choosedfile = fileChooser.showOpenDialog(loadButton.getScene().getWindow());
    if (null == choosedfile) {
      return;
    }

    clearUpdateInfo();
    try {
      UpdatePackageInfo updatePackageInfo =
          UpdateUtilities.getUpdatePackageInfo(choosedfile.getPath());
      if (updatePackageInfo.getLogo() == null) {
        ViewUtility.showAlert(
            tableView.getScene().getWindow(),
            Bundle.NbBundle.getMessage("ExtenderOsdUpdate.alert.warning.packageError.text"),
            AlertExType.WARNING);
      } else {
        updateMd5.set(updatePackageInfo.getLogo());
        updateFile = choosedfile;
      }
    } catch (Exception exception) {
      log.warn("Fail to parse package!", exception);
    }
  }

  protected void clearUpdateInfo() {
    updateMd5.set("");
    for (OsdUpdateItem item : items) {
      item.getSelected().set(false);
      item.getProgress().set(0);
    }
  }

  @FXML
  protected void onSelectAll(ActionEvent event) {
    if (selectedAll.get()) {
      selectedAll.set(false);
      for (OsdUpdateItem item : items) {
        item.getSelected().set(false);
      }
    } else {
      selectedAll.set(true);
      for (OsdUpdateItem item : items) {
        item.getSelected().set(true);
      }
    }
  }

  @FXML
  protected void onUpdate(ActionEvent event) {
    osdUpdater =
        new OsdUpdater(
            deviceController.getDataModel(), updateFile, logger, updating, updateMd5.get());

    List<OsdUpdateItem> selectedItems = new ArrayList<>();
    for (OsdUpdateItem item : items) {
      if (item.getSelected().get()) {
        selectedItems.add(item);
      }
    }
    osdUpdater.setUpdateItems(selectedItems);
    osdUpdater.update();
  }

  @FXML
  protected void onCancel(ActionEvent event) {
    if (osdUpdater != null) {
      osdUpdater.cancel();
    }
  }
}
