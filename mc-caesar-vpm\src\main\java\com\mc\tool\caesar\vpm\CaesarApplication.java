package com.mc.tool.caesar.vpm;

import com.google.common.primitives.Ints;
import com.google.inject.Singleton;
import com.mc.tool.caesar.Versions;
import com.mc.tool.framework.DefaultApplication;
import com.mc.tool.framework.interfaces.EntityFactory;
import java.io.File;
import java.util.Collection;
import java.util.Objects;
import javafx.scene.image.Image;

/**
 * .
 */
@Singleton
public final class CaesarApplication extends DefaultApplication {
  @Override
  protected EntityFactory createEntityFactory() {
    return new CaesarEntityFactory(this);
  }

  @Override
  public Collection<String> getAppAdditionalStyleSheet() {
    Collection<String> styles = super.getAppAdditionalStyleSheet();
    String style =
        Objects.requireNonNull(getClass().getResource("/com/mc/tool/caesar/vpm/stagelogo.css"))
            .toExternalForm();
    styles.add(style);
    return styles;
  }

  @Override
  public Image getIcon() {
    return new Image(Objects.requireNonNull(getClass().getResourceAsStream("/icons/logo.png")));
  }

  @Override
  public String getAppName() {
    return "caesar";
  }

  @Override
  public String getAppTitle() {
    return "Caesar VPM";
  }

  @Override
  public String getFullVersion() {
    return Versions.VERSION;
  }

  @Override
  public int getMainVersion() {
    Integer version = Ints.tryParse(Versions.MAJOR_VERSION);
    return version != null ? version : 0;
  }

  @Override
  public int getSubVersion() {
    Integer version = Ints.tryParse(Versions.MINOR_VERSION);
    return version != null ? version : 0;
  }

  @Override
  public int getModifyVersion() {
    Integer version = Ints.tryParse(Versions.PATCH_VERSION);
    return version != null ? version : 0;
  }

  @Override
  public String getExtraVersionInfo() {
    return Versions.VERSION_QUALIFIER;
  }

  @Override
  public void importFile(File file) {
    getEntityFactory().createEntity(file, entity -> getEntityMananger().addEntity(entity));
  }
}
