<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<fx:root id="main-container" prefWidth="1100.0" stylesheets="@hotkey_view.css" type="VBox"
  xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
  <children>
    <HBox VBox.vgrow="ALWAYS">
      <children>
        <VBox fx:id="searchFieldBox" prefHeight="631.0" prefWidth="246.0" styleClass="border-panel">
          <children>
            <HBox prefHeight="50.0" styleClass="title-box">
              <children>
                <Label fx:id="sourceListTitleLabel" prefHeight="20.0" styleClass="title"/>
              </children>
            </HBox>
            <TableView fx:id="sourceList" styleClass="hotkey-table" VBox.vgrow="ALWAYS">
              <columns>
                <TableColumn fx:id="sourceIdColumn" prefWidth="89.0" text="%con_hotkey.table.id"/>
                <TableColumn fx:id="sourceNameColumn" prefWidth="150.0"
                  text="%con_hotkey.table.name"/>
              </columns>
            </TableView>
          </children>
        </VBox>
        <VBox styleClass="border-panel" HBox.hgrow="ALWAYS">
          <children>
            <HBox alignment="CENTER_LEFT" maxHeight="30.0" minHeight="30.0">
              <children>
                <Label fx:id="hotkeyBlockTitleLabel" prefHeight="20.0" styleClass="title"/>
              </children>
              <padding>
                <Insets left="10.0"/>
              </padding>
            </HBox>
            <HBox fx:id="hbox" VBox.vgrow="ALWAYS">
              <VBox.margin>
                <Insets bottom="10.0"/>
              </VBox.margin>
            </HBox>
          </children>
          <HBox.margin>
            <Insets left="10.0"/>
          </HBox.margin>
        </VBox>
      </children>
    </HBox>
    <HBox id="tool-box">
      <children>
        <Button fx:id="copyBtn" mnemonicParsing="false" onAction="#onCopy"
          styleClass="common-button" text="复制当前轮询热键列表到其他管控端">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </Button>
        <Button fx:id="applyBtn" mnemonicParsing="false" onAction="#onApply"
          styleClass="common-button" text="%con_hotkey.apply">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </Button>
        <Button fx:id="cancelBtn" mnemonicParsing="false" onAction="#onCancel"
          styleClass="common-button" text="%con_hotkey.cancel">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </Button>
      </children>
    </HBox>
  </children>
</fx:root>
