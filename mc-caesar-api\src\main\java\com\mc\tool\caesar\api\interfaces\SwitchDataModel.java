package com.mc.tool.caesar.api.interfaces;

import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import java.util.concurrent.locks.Lock;

/**
 * .
 */
public interface SwitchDataModel extends ConnectionModel, ConfigDataModel {

  String PROPERTY_CONNECTED = "SwitchDataModel.connected";

  /**
   * 设置连接信息.
   *
   * @param paramString  hostname
   * @param paramBoolean 如果为true，开启线程定时检查是否在线
   */
  void setConnection(String paramString, boolean paramBoolean)
      throws ConfigException, BusyException;

  boolean isIoCapable();

  Lock getLock();

  long getIdentifier();

  void restart(int paramInt1) throws ConfigException, BusyException;

  void factoryReset(int paramInt) throws ConfigException, BusyException;

  void shutdown(int paramInt) throws ConfigException, BusyException;

  boolean hasLocalChanges();

  boolean requiresSave();

  void save() throws ConfigException, BusyException;
}

