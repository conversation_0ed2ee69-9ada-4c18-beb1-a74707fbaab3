package com.mc.tool.caesar.vpm.util.vp;

import com.mc.tool.caesar.api.datamodel.vp.Vp6OutputData;
import com.mc.tool.caesar.api.datamodel.vp.Vp7ConfigData;

/**
 * .
 */
public interface VpVideoWall {
  String getName();

  int getColumns();

  int getRows();

  int getRowHeight(int row);

  int getColumnWidth(int column);

  int getTotalWidth();

  int getTotalHeight();

  int getVideoCount();

  boolean showBgImg();

  int getBgImgWidth();

  int getBgImgHeight();

  VpVideoData getVideoData(int index);

  VpScreenData getScreenData(int row, int column);

  VpOsdData getOsdData();

  int getCompensationScaleThreshold();

  int getLeftCompensation();

  int getRightCompensation();

  int getTopCompensation();

  int getBottomCompensation();

  Vp6OutputData createVp6OutputData();

  Vp7ConfigData.Vp7OutputResData createVp7OutputData();

  Vp7ConfigData.Vp7TpgConfig createVp7TestFrameData();
}
