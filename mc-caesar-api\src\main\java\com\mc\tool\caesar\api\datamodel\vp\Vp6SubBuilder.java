package com.mc.tool.caesar.api.datamodel.vp;

/**
 * .
 */
public class Vp6SubBuilder implements VpBuilder {

  @Override
  public VpConConfigData createConfigData() {
    return null;
  }

  @Override
  public int getVpconId(int extId, int conId) {
    return 0;
  }

  @Override
  public int getVpconInputIndex(int extId) {
    return 0;
  }

  @Override
  public String formatName(int id) {
    String format = Vp6Builder.NAME_FORMAT_STRING + "[%d]";
    return String.format(format, id & 0xfffffffe,  id & 0x07);
  }
}
