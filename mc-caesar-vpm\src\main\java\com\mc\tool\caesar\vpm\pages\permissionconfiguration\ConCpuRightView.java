package com.mc.tool.caesar.vpm.pages.permissionconfiguration;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.AccessControlObject;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.utils.AdvancedBitSet;
import com.mc.tool.caesar.vpm.pages.hotkeyconfiguration.CopyDialog;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.cpuright.CpuRightBaseView;
import com.mc.tool.caesar.vpm.util.cpuright.TempAccessControlObject;
import com.mc.tool.framework.interfaces.DeviceControllable;
import java.beans.PropertyChangeListener;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.ResourceBundle;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.layout.HBox;
import javafx.stage.Modality;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class ConCpuRightView extends CpuRightBaseView<ConsoleData> {
  @FXML protected HBox btnBox;
  protected Button copyBtn;

  /** Constructor. */
  public ConCpuRightView() {
    ResourceBundle bundle = ResourceBundle.getBundle("com.mc.tool.caesar.vpm.i18n.common");
    sourceListTitle.set(bundle.getString("con_right.con_list"));
    accessBlockTitle.set(bundle.getString("con_right.con_cpu_right_management"));
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    super.initialize(location, resources);

    sourceIdColumn.setCellValueFactory((feat) -> feat.getValue().getIdProperty());
    sourceNameColumn.setCellValueFactory((feat) -> feat.getValue().getNameProperty());

    copyBtn =
        new Button(CaesarI18nCommonResource.getString("copy_cpu_right_dialog.con_cpu_right.title"));
    copyBtn.setDisable(true);
    copyBtn.getStyleClass().add("common-button");
    copyBtn.setOnAction((event) -> onCopy());
    btnBox.getChildren().add(0, copyBtn);

    selectedItem.addListener(
        weakAdapter.wrap(
            (observable, oldValue, newValue) -> {
              if (newValue == null) {
                copyBtn.setDisable(true);
              } else if (newValue instanceof TempAccessControlObject) {
                copyBtn.setDisable(false);
              }
            }));
  }

  private void onCopy() {
    List<ConsoleData> conList =
        new ArrayList<>(deviceController.getDataModel().getConfigDataManager().getActiveConsoles());
    conList.remove(getTableSelectionModel().getSelectedItem());
    CopyDialog<ConsoleData> copyDialog = new CopyDialog<>();
    copyDialog.setTitle(
        CaesarI18nCommonResource.getString("copy_cpu_right_dialog.con_cpu_right.title"));
    ((Label) copyDialog.getCopySelectionView().getSourceHeader())
        .setText(
            CaesarI18nCommonResource.getString(
                "copy_cpu_right_dialog.con_cpu_right.source_header.title"));
    ((Label) copyDialog.getCopySelectionView().getTargetHeader())
        .setText(
            CaesarI18nCommonResource.getString(
                "copy_cpu_right_dialog.con_cpu_right.target_header.title"));
    copyDialog.getCopySelectionView().setCellFactory(col -> new SelectionViewCell());
    copyDialog.getCopySelectionView().getSourceItems().setAll(conList);
    if (sourceList != null && sourceList.getScene() != null) {
      copyDialog.initOwner(sourceList.getScene().getWindow());
    }
    copyDialog.initModality(Modality.APPLICATION_MODAL);
    copyDialog.showAndWait();
    Collection<ConsoleData> result = copyDialog.getResult();
    if (result != null) {
      AdvancedBitSet videoAccess =
          new AdvancedBitSet(getTableSelectionModel().getSelectedItem().getVideoAccessBitSet());
      AdvancedBitSet noAccess =
          new AdvancedBitSet(getTableSelectionModel().getSelectedItem().getNoAccessBitSet());

      updatingProperty.set(true);
      deviceController
          .submitAsync(
              () -> {
                List<ConsoleData> sendData = new ArrayList<>();
                for (ConsoleData consoleData : result) {
                  consoleData.setVideoAccessBits(videoAccess.getBits());
                  consoleData.setNoAccessBits(noAccess.getBits());
                  sendData.add(consoleData);
                }
                try {
                  deviceController.getDataModel().sendConsoleData(sendData);
                } catch (DeviceConnectionException | BusyException exception) {
                  log.warn("Fail to send console data!", exception);
                }
              })
          .whenComplete(
              (item, throwable) -> {
                if (throwable != null) {
                  log.warn("Fail to send console data!", throwable);
                }
                PlatformUtility.runInFxThread(() -> updatingProperty.set(false));
              });
    }
  }

  private static class SelectionViewCell extends ListCell<ConsoleData> {

    @Override
    protected void updateItem(ConsoleData item, boolean empty) {
      super.updateItem(item, empty);
      if (empty || item == null) {
        setText("");
      } else {
        setText(item.getName());
      }
    }
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    super.setDeviceController(deviceController);

    if (this.deviceController != null) {
      updateSourceRawList();

      CaesarSwitchDataModel model = this.deviceController.getDataModel();
      model.addPropertyChangeListener(
          ConsoleData.PROPERTY_STATUS,
          weakAdapter.wrap(
              (PropertyChangeListener) evt -> Platform.runLater(this::updateSourceRawList)));
    }
  }

  protected void updateSourceRawList() {
    sourceRawList.setAll(
        this.deviceController.getDataModel().getConfigDataManager().getActiveConsoles());
    sourceRawList.removeIf(ConsoleData::isStatusVpcon);
  }

  @Override
  protected void onSendNewAccess(AccessControlObject item) {
    if (item instanceof ConsoleData) {
      ConsoleData data = (ConsoleData) item;
      try {
        deviceController.getDataModel().sendConsoleData(Collections.singletonList(data));
      } catch (DeviceConnectionException | BusyException exception) {
        log.warn("Fail to send con data!", exception);
      }
    }
  }

  @Override
  public void updateEditableStatus() {
    if (deviceController == null) {
      return;
    }
    editableProperty.set(deviceController.getCaesarUserRight().isRxRightEditable());
  }
}
