package com.mc.tool.caesar.vpm.pages.operation.multiscreen.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.framework.operation.crossscreen.datamodel.MultiScreenObject;
import com.mc.tool.framework.systemedit.datamodel.VisualEditConnection;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.ObservableMap;
import lombok.Getter;

/**
 * .
 */
public class CaesarMultiScreenData implements MultiScreenObject {
  @Getter @Expose private StringProperty name = new SimpleStringProperty();

  @Expose @Getter private IntegerProperty rows = new SimpleIntegerProperty();

  @Expose @Getter private IntegerProperty columns = new SimpleIntegerProperty();

  @Expose @Getter
  private ObservableList<ObjectProperty<VisualEditTerminal>> targets =
      FXCollections.observableArrayList();

  @Getter @Expose
  private ObservableMap<VisualEditTerminal, VisualEditConnection> connections =
      FXCollections.observableHashMap();

  @Override
  public ObjectProperty<VisualEditTerminal> getTarget(int index) {
    if (index < 0 || index >= targets.size()) {
      return new SimpleObjectProperty<>();
    } else {
      return targets.get(index);
    }
  }

  @Override
  public int indexOfTarget(VisualEditTerminal target) {
    if (target == null) {
      return -1;
    }
    for (int i = 0; i < targets.size(); i++) {
      if (targets.get(i).get() == target) {
        return i;
      }
    }
    return -1;
  }

  @Override
  public void insertTarget(VisualEditTerminal target, int index) {
    int stop = index == 0 ? targets.size() - 1 : index - 1;
    while (target != null) {
      VisualEditTerminal temp = targets.get(index).get();
      targets.get(index).set(target);
      target = temp;
      if (index == stop) {
        break;
      }
      index = (index + 1) % targets.size();
    }
  }

  /**
   * 重置screen的容量.
   *
   * @param size 容量的大小
   */
  public void resetCapacity(int size) {
    size = Math.max(size, getValidTargetize());
    if (size > targets.size()) {
      int addLength = size - targets.size();
      for (int i = 0; i < addLength; i++) {
        targets.add(new SimpleObjectProperty<>());
      }
    } else if (size < targets.size()) {
      targets.remove(size, targets.size());
    }
  }

  /**
   * 获取实际的target的大小.
   *
   * @return 实际的target的大小
   */
  public int getValidTargetize() {
    int size = 0;
    for (int i = 0; i < targets.size(); i++) {
      if (targets.get(i).get() != null) {
        size = i + 1;
      }
    }
    return size;
  }

  /**
   * 检查是否所有target都有对应的terminal.
   *
   * @return 如果有，返回true
   */
  public boolean isFull() {
    for (ObjectProperty<VisualEditTerminal> item : getTargets()) {
      if (item.get() == null) {
        return false;
      }
    }
    return true;
  }

  @Override
  public <T extends MultiScreenObject> void copyTo(T item) {
    item.getRows().set(rows.get());
    item.getColumns().set(columns.get());

    if (item instanceof CaesarMultiScreenData) {
      CaesarMultiScreenData screenData = (CaesarMultiScreenData) item;
      for (ObjectProperty<VisualEditTerminal> target : screenData.getTargets()) {
        target.set(null);
      }

      int size = getRows().get() * getColumns().get();
      screenData.resetCapacity(size);
      for (int i = 0; i < size; i++) {
        screenData.getTarget(i).set(getTarget(i).get());
      }
    }
    // 连接的复制要放到最后，因为reset capacity会影响connection
    item.getConnections().clear();
    item.getConnections().putAll(connections);
  }
}
