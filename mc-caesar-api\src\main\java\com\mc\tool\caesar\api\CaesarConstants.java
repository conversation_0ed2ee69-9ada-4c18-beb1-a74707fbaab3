package com.mc.tool.caesar.api;

import com.mc.tool.caesar.api.interfaces.Idable;
import com.mc.tool.caesar.api.interfaces.Nameable;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.Getter;

/**
 * .
 *
 * @brief 设备的状态的常量的定义
 */
public interface CaesarConstants {

  String DATA_THREAD_PREFIX = "caesar_api_data_thread_";
  String DATE_TIME_FORMAT_yyyy_MM_dd_HH_mm_ss = "yyyy-MM-dd HH:mm:ss";
  String DEFAULT_SERIAL = "0000000000";
  int FTP_PORT = 21;
  int SNMP_AGENT_PORT = 161;
  int SNMP_TRAP_PORT = 162;
  int SYSLOG_PORT = 514;
  String CFG_PORT = "10001";
  int GRID_PORT = 5656;
  int MAX_ID = 32768;
  int SCENARIO_SIZE = 512; //32 * 8
  String INVALID_SERIAL = "Invalid";
  String CFG_FILE_EXTENSION = ".cfg";
  String CFGX_FILE_EXTENSION = ".cfgx";
  String CFG_FILE_CONFIG = "config";
  String CFG_FILE_CONFIG_NR = "config%02d";
  String CFG_FILE_DEFAULT = "default";
  String SWITCH_TYPE_COLOR_FULL = "#00e7d4";
  String SWITCH_TYPE_COLOR_VIDEO = "#f2bd00";
  String SWITCH_TYPE_COLOR_PRIVATE = "#1e6be3";
  String SWITCH_TYPE_COLOR_SHARED = "#eaea00";
  String SWITCH_TYPE_COLOR_FULL_SHARED = "#00e77d";

  String STATUS_CONFIG_NAME = "config.bin";
  String STATUS_EXTERNAL_CONFIG_NAME = "external_config_{0}.bin";
  String STATUS_VERSION_NAME = "version.json";
  String STATUS_VP_NAME = "vp.bin";

  String STATUS_EXTRA_CONF_NAME = "extra.conf.bin";
  String STATUS_VP_SCENARIO_NAME = "vpscenario.bin";
  String STATUS_VP_RES_NAME = "vpres.bin";
  String STATUS_SERIAL_NAME = "serial.json";
  String STATUS_EXTINFO_NAME = "extinfo.json";
  String STATUS_EXTINFO2_NAME = "extinfo2.json";
  String STATUS_MISC_NAME = "misc.json";
  String STATUS_MISC_MASTER = "masterIndex";
  String STATUS_DOUBLE_BACKUP = "doubleBackup";
  String STATUS_EDID_NAME = "edid.json";
  String STATUS_EXTARGS_NAME = "extargs.json";
  String STATUS_ENVIRONMENT_NAME = "environment.json";
  String STATUS_PORT_NAME = "port.bin";
  String STATUS_MODULE_NAME = "module.bin";

  String CFGX_CONFIG_NAME = "config.bin";
  String CFGX_EXTRA_CONF_NAME = "vp.bin";
  String CFGX_VP_BACKUP_NAME = "VpConBackup.dbs";
  String CFGX_VP_PRE_NAME = "VpConPre.dbs";

  String CFGX_DB_AUTH_USER = "authusers.json";

  String CFGX_DB_FR_DEVICE = "frdevices.json";

  String CFGX_DB_BK = "dump.sql";

  int MATRIX_NAME_LEN = 38;
  int MATRIX_DEVICE_LEN = 38;
  int MATRIX_INFO_LEN = 60;
  int NAME_LEN = MATRIX_NAME_LEN;
  int NAME_BYTE_LEN = NAME_LEN + 4;
  int PASSWORD_LEN = 8;

  int MATRIX_GRID_COUNT = 16;

  int MAX_VIDEOWALL_ROW = 10;
  int MAX_VIDEOWALL_COL = 20;

  int MIN_MOUSE_SPEED = 4;
  int MAX_MOUSE_SPEED = 16;

  int VP_BGIMG_UPLOAD_PAGE_SIZE = 512;

  int USB_BIND_RX = 1;
  int USB_UNBIND_RX = 2;
  int USB_BIND_TX = 3;
  int USB_UNBIND_TX = 4;

  int BIT_SIZE = 8;
  int ACTIVE_TX_BUF_SIZE = 250;
  int ACTIVE_RX_BUF_SIZE = 250;
  int ACTIVE_EXT_BUF_SIZE = 500;
  int ACTIVE_USER_BUF_SIZE = 32;
  int ACTIVE_USER_GROUP_BUF_SIZE = 8;
  int ACTIVE_MULTISCREEN_SIZE = 19;
  int ACTIVE_MACRO_BUF_SIZE = 1024;
  int ACTIVE_GRID_BUF_SIZE = 2;
  int ACTIVE_TX_RX_GROUP_BUF_SIZE = 63;

  List<String> CONFIG_NAMES =
      Collections.unmodifiableList(Arrays.asList(CaesarConstants.CFG_FILE_DEFAULT,
          String.format(CaesarConstants.CFG_FILE_CONFIG_NR, 1),
          String.format(CaesarConstants.CFG_FILE_CONFIG_NR, 2),
          String.format(CaesarConstants.CFG_FILE_CONFIG_NR, 3),
          String.format(CaesarConstants.CFG_FILE_CONFIG_NR, 4),
          String.format(CaesarConstants.CFG_FILE_CONFIG_NR, 5),
          String.format(CaesarConstants.CFG_FILE_CONFIG_NR, 6),
          String.format(CaesarConstants.CFG_FILE_CONFIG_NR, 7),
          String.format(CaesarConstants.CFG_FILE_CONFIG_NR, 8)));

  String IP_0 = "s0.0.0.0".substring(1);

  /**
   * .
   */
  enum CpuType {
    NONE(-1),
    DEFAULT(0),
    MASTER(0),
    SECONDARY(2),
    SLAVE(16);

    private final int value;

    CpuType(int value) {
      this.value = value;
    }

    public int getValue() {
      return this.value;
    }
  }

  /**
   * .
   */
  enum JavaToTeraDayOfWeek {
    MONDAY(2, 1),
    TUESDAY(3, 2),
    WEDNESDAY(4, 3),
    THURSDAY(5, 4),
    FRIDAY(6, 5),
    SATURDAY(7, 6),
    SUNDAY(1, 7);

    private final int javaId;
    private final int teraId;

    JavaToTeraDayOfWeek(int javaId, int teraId) {
      this.javaId = javaId;
      this.teraId = teraId;
    }

    public int getTeraId() {
      return this.teraId;
    }

    public int getJavaId() {
      return this.javaId;
    }

    /**
     * .
     */
    public static JavaToTeraDayOfWeek valueOf(int javaId) {
      for (JavaToTeraDayOfWeek dayOfWeek : JavaToTeraDayOfWeek.values()) {
        if (dayOfWeek.getJavaId() == javaId) {
          return dayOfWeek;
        }
      }
      throw new IllegalArgumentException(
          String.format("JAVAID(%d) out of Range", javaId));
    }
  }

  /**
   * .
   */
  enum Keyboard implements Nameable, Idable {
    EN_US_103P(0, Bundle.keyboard_En_Us_103P()),
    DE_DE_129(1, Bundle.keyboard_De_De_129()),
    FR_BE_120(2, Bundle.keyboard_Fr_Be_120()),
    EN_GB_168(3, Bundle.keyboard_En_Gb_168()),
    DE_CH_150G(4, Bundle.keyboard_De_Ch_150G()),
    FR_FR_189(5, Bundle.keyboard_Fr_Fr_189()),
    FR_CH_150F(6, Bundle.keyboard_Fr_Ch_150F()),
    EN_GB_166(7, Bundle.keyboard_En_Gb_166()),
    RU_1251(8, Bundle.keyboard_Ru_1251());

    private final int id;
    private final String nameable;

    Keyboard(int id, String nameable) {
      this.id = id;
      this.nameable = nameable;
    }

    @Override
    public String getName() {
      return this.nameable;
    }

    @Override
    public int getId() {
      return this.id;
    }

    /**
     * .
     */
    public static Keyboard valueOf(int id) {
      for (Keyboard keyboard : Keyboard.values()) {
        if (keyboard.getId() == id) {
          return keyboard;
        }
      }
      throw new IllegalArgumentException(
          String.format("ID(%d) out of Range", id));
    }
  }

  /**
   * .
   */
  enum VideoMode implements Nameable, Idable {
    VAR(0, Bundle.videoMode_var()),
    M1024x768(2, Bundle.videoMode_1024x768()),
    M1280x800(7, Bundle.videoMode_1280x800()),
    M1280x1024(3, Bundle.videoMode_1280x1024()),
    M1600x1200(4, Bundle.videoMode_1600x1200()),
    M1680x1050(8, Bundle.videoMode_1680x1050()),
    M1920x1080(5, Bundle.videoMode_1920x1080()),
    M1920x1200(6, Bundle.videoMode_1920x1200());

    private int id;
    private String nameable;

    VideoMode(int id, String nameable) {
      this.id = id;
      this.nameable = nameable;
    }

    @Override
    public int getId() {
      return this.id;
    }

    @Override
    public String getName() {
      return this.nameable;
    }

    /**
     * .
     */
    public static VideoMode valueOf(int id) {
      for (VideoMode osd : VideoMode.values()) {
        if (osd.getId() == id) {
          return osd;
        }
      }
      throw new IllegalArgumentException(
          String.format("ID(%d) out of Range", id));
    }
  }

  /**
   * .
   */
  interface Console {

    /**
     * .
     */
    interface Status {

      int PRIVATE_MODE = 0b0000000000000010;
      int VIDEO_MODE = 0b0000000000000100;

      int DELETE = 0b0000000000001000; // 没有用，只为了适配之前的代码
      int NEW = 0b0000000000010000; // 没有用，只为了适配之前的代码

      int VIRTUAL_DEVICE = 0b0000000000000001 << 16;
      int ALLOW_USER = 0b0000000000000010 << 16;
      int FORCE_LOGIN = 0b0000000000000100 << 16;
      int LOS_FRAME = 0b0000000000001000 << 16;
      int ALLOW_CPU_SCAN = 0b0000000000010000 << 16;
      int FORCE_CPU_SCAN = 0b0000000000100000 << 16;
      int RESERVED1 = 0b0000000011000000 << 16;
      int PORT_MODE = 0b0000000100000000 << 16;
      int REDUNDANT_OFF = 0b0000001000000000 << 16;
      int FORCE_MACRO = 0b0000010000000000 << 16;
      int OSD_DISABLED = 0b0000100000000000 << 16;
      int VP7_DEVICE = 0b0001000000000000 << 16;
      int VP6_DEVICE = 0b0010000000000000 << 16;
      int SUSPEND_ENABLE = 0b0100000000000000 << 16;
      int ACTIVE = 0b1000000000000000 << 16;
    }

    /**
     * .
     */
    enum SuspendPosition implements Nameable, Idable {

      TOPRIGHT(0, Bundle.suspendPosition_top_right()),
      TOPCENTER(1, Bundle.suspendPosition_top_center()),
      TOPLEFT(2, Bundle.suspendPosition_top_left());

      SuspendPosition(int id, String name) {
        this.id = id;
        this.name = name;
      }

      private final int id;
      private final String name;

      @Override
      public int getId() {
        return id;
      }

      @Override
      public String getName() {
        return name;
      }

      /**
       * .
       */
      public static SuspendPosition valueOf(int id) {
        for (SuspendPosition position : values()) {
          if (position.getId() == id) {
            return position;
          }
        }
        return SuspendPosition.TOPLEFT;
      }

    }
  }

  /**
   * .
   */
  interface VpConsole {

    int SIZE = 304;
    int COUNT = 512;
  }

  /**
   * .
   */
  interface Scenario {

    int CONNECTION = 0x01;
    int CONFIG = 0x02;
    int BACKUP = 0x04;
  }

  /**
   * .
   */
  interface Cpu {

    int FAVORITE = 16;

    /**
     * .
     */
    interface Status {

      int PRIVATE = 0b0000000000000010;
      int VIDEO_MODE = 0b0000000000000100;

      int DELETE = 0b0000000000001000; // 是否准备删除
      int NEW = 0b0000000000010000; // 是否是新的数据

      int VIRTUAL = 0b0000000000000001 << 16; // 是否是virtual con
      int ALLOWPRIVATE = 0b0000000000000010 << 16; // 是否允许private access
      int FORCEPRIVATE = 0b0000000000000100 << 16; // 连接时强制private access
      int FIXFRAME = 0b0000000000001000 << 16; // 强制显示红色画面
      int VIRTUAL_OUT = 0b0000000000100000 << 16; // 模拟拔出与插入

      int ACTIVE = 0b1000000000000000 << 16;
    }
  }

  /**
   * .
   */
  interface Extender {

    int CPUCON = 8; // cpu或con可对应的extender的个数
    String NAME_FORMAT_STRING = "EXT_%09d";
    int AUTOID = 90000000;
    int AUTOID_MAX = 99999999;

    /**
     * .
     */
    interface Type {

      int CPU = 1;
      int CON = 0x10000;
      int USB_CON = 0x30;
      int USB_CPU = 0x300000;
      int UNI_CON = 0x40;
      int UNI_CPU = 0x400000;
      int CUST_CON = 0x80;
      int CUST_CPU = 0x800000;
      int USB = 0x10101010;
      int AUDIO = 0x4040404;
    }

    /**
     * .
     */
    interface Osd {

      int ACTIVE = 1;
      int UPDATE = 2;
      int SELECT = 4;
    }

    /**
     * .
     */
    interface Status {

      int ONLINE = 0b0000000000000001;
      int NEWVERSION = 0b0010000000000000;
      int FIXPORT = 0b0000000000000001 << 16;
      int REDUNDANT = 0b0000000000000100 << 16;
      int ACTIVED_LINK = 0b0000000000001000 << 16;
      int DELETE = 0b0010000000000000 << 16;
      int NEW = 0b0100000000000000 << 16;
      int ACTIVE = 0b1000000000000000 << 16;
    }

    /**
     * .
     */
    interface ExtendedStatus {

      int MOUSE_ONLINE = 1 << 0;
      int KEYBOARD_ONLINE = 1 << 1;
      int SCREEN_ONLINE = 1 << 2;
      int VIDEO_INPUT = 1 << 3;
      int USB_CABLE_ONLINE = 1 << 4;
      int REDUNDANT = 1 << 5;
      int LINK1 = 1 << 6;
      int VP7 = 1 << 7;
      int VP6 = 1 << 8;
      int UDISK_ONLINE = 1 << 9;
      int HDMI_MODE = 1 << 10;
      int DVI_MODE = 1 << 11;
      int EDID_EXIST = 1 << 12;
      int EDID_VALID = 1 << 13;

    }

    /**
     * .
     */
    enum ExtenderInterfaceType {
      INTERFACE_TYPE_UNKNOWN(-1, "Unknown"),
      INTERFACE_TYPE_DVI(0, "DVI"),
      INTERFACE_TYPE_HDMI(1, "HDMI"),
      INTERFACE_TYPE_VGA(2, "VGA"),
      INTERFACE_TYPE_DMS59(3, "DMS59"),
      INTERFACE_TYPE_DP(4, "DP"),
      INTERFACE_TYPE_SDI(5, "SDI");

      private int value;
      private String text;

      ExtenderInterfaceType(int value, String text) {
        this.value = value;
        this.text = text;
      }

      public String getText() {
        return text;
      }

      public int getValue() {
        return value;
      }

      /**
       * .
       */
      public static ExtenderInterfaceType valueOf(int value) {
        for (ExtenderInterfaceType item : values()) {
          if (item.getValue() == value) {
            return item;
          }
        }
        return ExtenderInterfaceType.INTERFACE_TYPE_UNKNOWN;
      }
    }

    /**
     * .
     */
    @Getter
    enum ExtenderVideoResolutionType {
      VIDEO_UNKNOWN(-1, "Unknown"),
      VIDEO_2K(0, "2K"),
      VIDEO_4K_30HZ(1, "4K 30Hz"),
      VIDEO_4K_60HZ(2, "4K 60Hz");

      private int value;
      private String text;

      ExtenderVideoResolutionType(int value, String text) {
        this.value = value;
        this.text = text;
      }

      /**
       * .
       */
      public static ExtenderVideoResolutionType valueOf(int value) {
        for (ExtenderVideoResolutionType item : values()) {
          if (item.getValue() == value) {
            return item;
          }
        }
        return VIDEO_UNKNOWN;
      }
    }

    /**
     * .
     */
    @Getter
    enum SpecialExtenderType {
      HW_COMMON(0, "HW Common"),
      HW_FUSION(1, "HW Fusion"), // 融合通信设备
      HW_4VIEW(2, "HW 4VIEW"), // 四画面RX
      HW_KAITO_02(3, "HW KAITO_02"), //嗨动单画面子卡
      HW_4VIEW_KAITO_02(4, "HW 4VIEW_KAITO_02"), // 嗨动四画面子卡
      HW_VP7(5, "HW_VP7"), // VP7
      HW_PREVIEW(6, "HW_PREVIEW"), // Caesar-R2P4F-PreView-P预览终端
      HW_ATC(7, "HW_ATC"); // 空管行业4K60外设

      private final int value;
      private final String text;

      SpecialExtenderType(int value, String text) {
        this.value = value;
        this.text = text;
      }

      /**
       * .
       */
      public static SpecialExtenderType valueOf(int value) {
        for (SpecialExtenderType item : values()) {
          if (item.getValue() == value) {
            return item;
          }
        }
        return HW_COMMON;
      }
    }

    /**
     * 特殊硬件子类型.
     */
    @Getter
    enum SpecialExtenderSubType {
      NONE(0, "NONE"), //无

      // 四画面子类型
      NORMAL(0, "NORMAL"), //常规
      HALF_REDUNDANT(1, "HALF_REDUNDANT"), //1G半冗余
      HALF_REDUNDANT_2_5G(2, "HALF_REDUNDANT_2_5G"), // 2.5G半冗余

      // 预览终端子类型
      PRIMARY(0, "PRIMARY"), // 第一个端口
      SECONDARY(1, "SECONDARY"); // 第二个端口

      private final int value;
      private final String text;

      SpecialExtenderSubType(int value, String text) {
        this.value = value;
        this.text = text;
      }

      /**
       * .
       */
      public static SpecialExtenderSubType valueOf(SpecialExtenderType type, int value) {
        switch (type) {
          case HW_4VIEW:
          case HW_4VIEW_KAITO_02:
            if (value == 0) {
              return NORMAL;
            } else if (value == 1) {
              return HALF_REDUNDANT;
            } else if (value == 2) {
              return HALF_REDUNDANT_2_5G;
            } else {
              return NORMAL;
            }
          case HW_PREVIEW:
            if (PRIMARY.value == 0) {
              return PRIMARY;
            } else if (SECONDARY.value == 1) {
              return SECONDARY;
            } else {
              return PRIMARY;
            }
          default:
            return NONE;
        }
      }
    }


    /**
     * .
     */
    interface Display {

      int CSPEED_MAX = 800;
      int CSPEED_MIN = 100;
      int CSPEED_NORM = 200;
      int HSPEED_MAX = 9;
      int HSPEED_MIN = 1;
      int HSPEED_NORM = 4;
      int VSPEED_MAX = 9;
      int VSPEED_MIN = 1;
      int VSPEED_NORM = 5;
    }
  }

  /**
   * .
   */
  interface FunctionKey {

    int SIZE = 8;
    int COUNT = 8192;
    int FKEY_SIZE = 128;
    int MACRO_SIZE = 128;

    /**
     * .
     */
    interface Cmd {

      /**
       * .
       */
      enum Command implements Nameable, Idable {
        NoFunction(0, Bundle.functionKey_command_noFunction()),
        Connect(1, Bundle.functionKey_command_connect()),
        ConnectVideo(2, Bundle.functionKey_command_connectvideo()),
        Disconnect(3, Bundle.functionKey_command_disconnect()),
        Push(8, Bundle.functionKey_command_push()),
        PushVideo(9, Bundle.functionKey_command_pushvideo()),
        Get(10, Bundle.functionKey_command_get()),
        GetVideo(11, Bundle.functionKey_command_getvideo()),
        ActiveVideoWallScenario(0x0d, Bundle.functionKey_command_activeScenario()),
        PushVideoWallScenarioWindow(0x0e, Bundle.functionKey_command_activeScenarioWindow()),
        SWITCH_MULTIVIEW_LAYOUT(0x0f, Bundle.functionKey_command_switchMultiViewLayout()),
        SWITCH_MULTIVIEW_SIGNAL(0x10, Bundle.functionKey_command_switchMultiViewSignal()),
        SWITCH_MULTIVIEW_PUSH(0x11, Bundle.functionKey_command_switchMultiViewPush()),
        SWITCH_MULTIVIEW_GET(0x12, Bundle.functionKey_command_switchMultiViewGet());

        private int id;
        private String nameable;

        Command(int id, String nameable) {
          this.id = id;
          this.nameable = nameable;
        }

        @Override
        public int getId() {
          return this.id;
        }

        @Override
        public String getName() {
          return this.nameable;
        }

        /**
         * .
         */
        public static Command valueOf(int id) {
          for (Command command : Command.values()) {
            if (command.getId() == id) {
              return command;
            }
          }
          throw new IllegalArgumentException(
              String.format("ID(%d) out of Range", id));
        }
      }


    }

    /**
     * .
     */
    interface Status {

      int ACTIVE = 0x80;
    }

    /**
     * .
     */
    interface Pos {

      int MSK_POS_16 = 15728640;
      int MSK_POS_32 = 31457280;
      int SHIFT_POS_16 = 20;
      int SHIFT_POS_32 = 21;
      int P1 = 0;
      int P2 = 1048576;
      int P3 = 2097152;
      int P4 = 3145728;
      int P5 = 4194304;
      int P6 = 5242880;
      int P7 = 6291456;
      int P8 = 7340032;
      int P9 = 8388608;
      int P10 = 9437184;
      int P11 = 10485760;
      int P12 = 11534336;
      int P13 = 12582912;
      int P14 = 13631488;
      int P15 = 14680064;
      int P16 = 15728640;
      int SP1 = 16777216;
      int SP2 = 17825792;
      int SP3 = 18874368;
      int SP4 = 19922944;
      int SP5 = 20971520;
      int SP6 = 22020096;
      int SP7 = 23068672;
      int SP8 = 24117248;
      int SP9 = 25165824;
      int SP10 = 26214400;
      int SP11 = 27262976;
      int SP12 = 28311552;
      int SP13 = 29360128;
      int SP14 = 30408704;
      int SP15 = 31457280;
      int SP16 = 32505856;
    }

    /**
     * .
     */
    interface Key {

      int MSK_KEY_16 = 983040;
      int MSK_KEY_32 = 2031616;
      int MSK_INDEX = 65535;
      int MSK_FIND_16 = -1072693249;
      int MSK_FIND_32 = 538968063;
      int NEWDATA_16 = 536870912;
      int NEWDATA_32 = Integer.MIN_VALUE;
      int USER_16 = 1073741824;
      int CON_16 = Integer.MIN_VALUE;
      int CON_32 = 536870912;
      int SHIFT_KEY = 16;
      int F1 = 0;
      int F2 = 65536;
      int F3 = 131072;
      int F4 = 196608;
      int F5 = 262144;
      int F6 = 327680;
      int F7 = 393216;
      int F8 = 458752;
      int F9 = 524288;
      int F10 = 589824;
      int F11 = 655360;
      int F12 = 720896;
      int F13 = 786432;
      int F14 = 851968;
      int F15 = 917504;
      int F16 = 983040;
      int SF1 = 1048576;
      int SF2 = 1114112;
      int SF3 = 1179648;
      int SF4 = 1245184;
      int SF5 = 1310720;
      int SF6 = 1376256;
      int SF7 = 1441792;
      int SF8 = 1507328;
      int SF9 = 1572864;
      int SF10 = 1638400;
      int SF11 = 1703936;
      int SF12 = 1769472;
      int SF13 = 1835008;
      int SF14 = 1900544;
      int SF15 = 1966080;
      int SF16 = 2031616;
    }
  }

  /**
   * .
   */
  interface Port {

    /**
     * .
     */
    interface Status {

      int AVAILABLE = 1;
      int COMPLETE = 2;
      int TXENABLED = 4;
      int RXENABLED = 8;
      int CONNECTED = 16;
      int WITHEXTID = 32;
      int WITHHOSTID = 64;
      int RESETDONE = 128;
      int OSDACTIVE = 256;
      int VIDEOONLY = 512;
      int WITHSIGNAL = 1024;
      int SECONDARY = 4096;
      int EXTENDER = 8192;
      int MATRIX = 16384;
      int ERROR = 32768;
      int TXDISABLE = 65536;
      int TXENABLE = 131072;
      int RXDISABLE = 262144;
      int RXENABLE = 524288;
      int GETEXTID = 1048576;
      int SETHOSTID = 2097152;
      int ACTIVATE = 4194304;
      int DELETE = 536870912;
      int NEWDATA = 1073741824;
      int ACTIVE = Integer.MIN_VALUE;
      int MSK_STATUS = 65535;
      int MSK_REQUEST = 268369920;
      int MSK_REQCLEAR = -268369921;
    }
  }

  /**
   * .
   */
  interface ControlGroup {

    int COUNT = 512;

    /**
     * .
     */
    interface Status {

      int ARRANGEMENT = 1;
      int AVAILABLE = 4096;
      int ACTIVE = 8192;
      int MANUAL = Integer.MIN_VALUE;
    }

    /**
     * .
     */
    enum Arrangement implements Nameable, Idable {
      M1x4(0, Bundle.arrangement_M1x4()),
      M2x2(1, Bundle.arrangement_M2x2());

      private int id;
      private String nameable;

      Arrangement(int id, String nameable) {
        this.id = id;
        this.nameable = nameable;
      }

      @Override
      public int getId() {
        return this.id;
      }

      @Override
      public String getName() {
        return this.nameable;
      }

      /**
       * .
       */
      public static Arrangement valueOf(int id) {
        for (Arrangement value : Arrangement.values()) {
          if (value.getId() == id) {
            return value;
          }
        }
        throw new IllegalArgumentException(
            String.format("ID(%d) out of Range", id));
      }
    }

    /**
     * .
     */
    enum Owner implements Nameable, Idable {
      SHARED(0, Bundle.owner_Shared()),
      DISPLAY1(1, Bundle.owner_Screen1()),
      DISPLAY2(2, Bundle.owner_Screen2()),
      DISPLAY3(3, Bundle.owner_Screen3()),
      DISPLAY4(4, Bundle.owner_Screen4());

      private int id;
      private String nameable;

      Owner(int id, String nameable) {
        this.id = id;
        this.nameable = nameable;
      }

      @Override
      public int getId() {
        return this.id;
      }

      @Override
      public String getName() {
        return this.nameable;
      }

      /**
       * .
       */
      public static Owner valueOf(int id) {
        for (Owner value : Owner.values()) {
          if (value.getId() == id) {
            return value;
          }
        }
        throw new IllegalArgumentException(
            String.format("Owner ID(%d) out of Range", id));
      }
    }
  }

  /**
   * .
   */
  interface User {

    int SIZE_176 = 176;
    int SIZE_208 = 208;
    int SIZE_240 = 240;
    int SIZE = 276;
    int COUNT = 256;
    int ID_LENGTH = 5;
    String ADMIN_NAME = "admin";
    String ADMIN_PASS_WORD = "MediaC0mm";
    String ADMINISTRATOR_NAME = "administrator";
    String ADMINISTRATOR_PW = "f84626a3d2a05d1294c677ed740b176320f71496";

    /**
     * .
     */
    interface Rights {

      int HTTP = 1;
      int FTP = 2;
      int PPP = 4;
      int TELNET = 8;
      int POWER = 16;
      int ADMIN = 32;
      int PRIVATE = 64;
      int SUPER = 128;
      int LDAP = 256;
    }

    /**
     * .
     */
    interface Status {

      int DELETE = 536870912;
      int NEW = 1073741824;
      int ACTIVE = Integer.MIN_VALUE;
    }
  }

  /**
   * .
   */
  interface UserGroup {

    int COUNT = 256;

    /**
     * .
     */
    interface Status {

      int DELETE = 536870912;
      int NEW = 1073741824;
      int ACTIVE = Integer.MIN_VALUE;
    }
  }

  /**
   * .
   */
  interface MultiScreen {

    int MAX_CON = 16;

    /**
     * .
     */
    interface Status {

      int ACTIVE = 0x80;
      int NEW = 0x10000;
    }
  }

  /**
   * .
   */
  interface System {

    int SIZE_268 = 268;
    int SIZE_348 = 348;
    int SIZE_372 = 372;
    int SIZE_540 = 540;
    int SIZE_644 = 644;
    int SIZE = 660;

    /**
     * .
     */
    interface Status {

      int MASTERCPU = 16777216;
      int SLAVECPU = 33554432;
      int PRIMARYCPU = 67108864;
      int SECONDARYCPU = 134217728;
      int NEW = 1073741824;
      int ACTIVE = Integer.MIN_VALUE;
    }

    /**
     * .
     */
    interface Forcebits {

      int SLAVE = 1;
      int SYNCHRONIZE = 2;
      int AUTOSAVE = 4;
      int AUTOCONFIG = 8;
      int CONACCESS = 16;
      int USERACCESS = 32;
      int ECHOONLY = 64;
      int REDUNDANT = 128;
      int COMECHO = 256;
      int LANECHO = 512;
      int FORCEPUSHGET = 1024;
      int REMOVESLAVE = 2048;
      int ONLINECONFIG = 4096;
      int USERCONOR = 8192;
      int USERCONAND = 16384;
      int FKEYSINGLE = 32768;
      int LOGIN = 65536;
      int CONLOCK = 131072;
      int USERLOCK = 262144;
      int CPUWATCH = 524288;
      int CPUCONNECT = 1048576;
      int CONDISCONNECT = 2097152;
      int CPUDISCONNECT = 4194304;
      int AUTOCONNECT = 8388608;
      int KEYBOARD_MOUSE = 16777216;
      int INVALID = 67108864;
      int OLDECHO = 134217728;
      int UART_ENABLE = 0x20000000;
      int DEFAULT_BACKUP = 0x40000000;
      int DEFAULT = Integer.MIN_VALUE;
      int GRID = 268435456;
    }
  }

  /**
   * .
   */
  interface Network {

    /**
     * .
     */
    interface Loglevel {

      int DEBUG = 1;
      int INFO = 2;
      int NOTICE = 4;
      int WARNING = 8;
      int ERROR = 16;
    }

    /**
     * .
     */
    interface Snmp {

      int TRAP = 1;
      int TRAP01 = 2;
      int TRAP02 = 4;
      int TRAP10 = 8;
      int TRAP11 = 16;
      int TRAP12 = 32;
      int TRAP20 = 64;
      int TRAP21 = 128;
      int TRAP30 = 256;
      int TRAP40 = 512;
      int TRAP41 = 1024;
      int TRAP50 = 2048;
      int TRAP51 = 4096;
      int TRAP52 = 8192;
    }

    /**
     * .
     */
    interface Bits {

      int DHCP = 1;
      int API = 2;
      int FTP = 4;
      int HTTP = 8;
      int TELNET = 16;
      int SNMP = 32;
      int SYSLOG = 64;
      int SNTP = 128;
      int LDAP = 1024;
    }
  }

  /**
   * .
   */
  interface Licence {

    /**
     * .
     */
    interface Bits {

      int JAVA = 1;
      int EXTENDED = 2;
      int API = 4;
      int SYSLOG = 8;
      int SNMP = 16;
      int MULTISCREENCONTROL = 128;
      int CASCADING = 256;
    }
  }

  /**
   * .
   */
  interface Module {

    String FILE = "modul.sys";
    int SIZE_VERSION = 32;
    int COUNT = 65;
    int COUNT_V3 = 255;
    int SIZE = 48;
    int NONE = 0;
    int DEFAULT_PORTS = 16;
    int COMBINDED_PORTS = 32;

    /**
     * .
     */
    interface Status {

      int AVAILABLE = 1;
      int ONLINE = 2;
      int CFGDATA = 4;
      int ACTIVE = 8;
      int READY = 7;
      int SERVICEMODE = 16;
      int ERROR = 67108864;
      int ONHOLD = 1073741824;
      int INVALID = Integer.MIN_VALUE;
    }

    /**
     * .
     */
    enum Type {


      // int CPU = 0x102;
      // int COMBINDED_CPU = 0x101;
      // int IO_CAT = 0x201;
      // int IO_SPF = 0x202;
      SINGLE_CPU(0x102, "single", true),
      STACK_36T_9x36B(0x101, "36t-9x36b", true),
      STACK_96T_1x36B(0x103, "96t-1x36b", true),
      STACK_96T_12x24B(0x104, "96t-12x24b", true),
      PLUGIN_144(0x107, "144-plugin", true),
      PLUGIN_384(0x105, "384-plugin", true),
      PLUGIN_816(0x106, "816-plugin", true),
      IO_CAT(0x201, "cat", false),
      IO_SPF(0x202, "sfp", false);

      private final int value;
      private final String name;
      private final boolean matrix;

      Type(int value, String name, boolean matrix) {
        this.value = value;
        this.name = name;
        this.matrix = matrix;
      }

      public int getValue() {
        return value;
      }

      public String getName() {
        return name;
      }

      public boolean isMatrix() {
        return matrix;
      }

      /**
       * .
       */
      public static Type valueOfUpdateName(String value) {
        for (Type item : values()) {
          if (item.getName().equalsIgnoreCase(value)) {
            return item;
          }
        }
        return null;
      }

      /**
       * .
       */
      public static Type valueOf(int value) {
        for (Type item : values()) {
          if (item.getValue() == value) {
            return item;
          }
        }
        return null;
      }
    }

    /**
     * .
     */
    interface Mask {

      int LEVEL2_576 = 240;
      int LEVEL2 = 15;
    }

    /**
     * .
     */
    interface Index {

      int CPU = 0;
      int IOFIRST = 1;
      int IOLAST = 64;
      int IOLAST_V3 = 254;
      int IO_TOP_LAST_576 = 36;
      int IO_BOTTOM_FIRST_576 = 37;
    }
  }

  /**
   * .
   */
  interface Matrix {

    int COUNT_16 = 16;
    int COUNT = 24;
    int SIZE = 60;

    /**
     * .
     */
    interface Status {

      int ONLINE = 1;
      int GETMASTER = 2;
      int CONNECT = 4;
      int GETCONFIG = 8;
      int GETMODULE = 16;
      int SETCON = 32;
      int SETCPU = 64;
      int SETCPUCON = 128;
      int CONNECTPORT = 256;
      int POWERPORT = 512;
      int GETGRIDSTATUS = 1024;
      int GETGRIDREQUEST = 2048;
      int MASTER = 65536;
      int UPDATEGRID = 268435456;
      int DELETE = 536870912;
      int NEWDATA = 1073741824;
      int ACTIVE = Integer.MIN_VALUE;
    }

    /**
     * .
     */
    enum Ports implements Nameable, Idable {
      P36(36, "36"),
      P96(96, "96");

      private final int idable;
      private final String nameable;

      Ports(int idable, String nameable) {
        this.idable = idable;
        this.nameable = nameable;
      }

      @Override
      public int getId() {
        return this.idable;
      }

      @Override
      public String getName() {
        return this.nameable;
      }
    }
  }

  /**
   * .
   */
  interface TxRxGroup {

    /**
     * .
     */
    interface Status {

      int ACTIVE = 1;
    }
  }

  /**
   * .
   */
  enum TimeZone implements Nameable, Idable {
    UTC_MINUS_12(-12, Bundle.timeZone_Utc_Minus_12()),
    UTC_MINUS_11(-11, Bundle.timeZone_Utc_Minus_11()),
    UTC_MINUS_10(-10, Bundle.timeZone_Utc_Minus_10()),
    UTC_MINUS_9(-9, Bundle.timeZone_Utc_Minus_9()),
    UTC_MINUS_8(-8, Bundle.timeZone_Utc_Minus_8()),
    UTC_MINUS_7(-7, Bundle.timeZone_Utc_Minus_7()),
    UTC_MINUS_6(-6, Bundle.timeZone_Utc_Minus_6()),
    UTC_MINUS_5(-5, Bundle.timeZone_Utc_Minus_5()),
    UTC_MINUS_4(-4, Bundle.timeZone_Utc_Minus_4()),
    UTC_MINUS_3(-3, Bundle.timeZone_Utc_Minus_3()),
    UTC_MINUS_2(-2, Bundle.timeZone_Utc_Minus_2()),
    UTC_MINUS_1(-1, Bundle.timeZone_Utc_Minus_1()),
    UTC_0(0, Bundle.timeZone_Utc_0()),
    UTC_PLUS_1(1, Bundle.timeZone_Utc_Plus_1()),
    UTC_PLUS_2(2, Bundle.timeZone_Utc_Plus_2()),
    UTC_PLUS_3(3, Bundle.timeZone_Utc_Plus_3()),
    UTC_PLUS_4(4, Bundle.timeZone_Utc_Plus_4()),
    UTC_PLUS_5(5, Bundle.timeZone_Utc_Plus_5()),
    UTC_PLUS_6(6, Bundle.timeZone_Utc_Plus_6()),
    UTC_PLUS_7(7, Bundle.timeZone_Utc_Plus_7()),
    UTC_PLUS_8(8, Bundle.timeZone_Utc_Plus_8()),
    UTC_PLUS_9(9, Bundle.timeZone_Utc_Plus_9()),
    UTC_PLUS_10(10, Bundle.timeZone_Utc_Plus_10()),
    UTC_PLUS_11(11, Bundle.timeZone_Utc_Plus_11()),
    UTC_PLUS_12(12, Bundle.timeZone_Utc_Plus_12());

    private int id;
    private String nameable;

    TimeZone(int id, String nameable) {
      this.id = id;
      this.nameable = nameable;
    }

    @Override
    public String getName() {
      return this.nameable;
    }

    @Override
    public int getId() {
      return this.id;
    }

    /**
     * .
     */
    public static TimeZone valueOf(int id) {
      for (TimeZone timeZone : TimeZone.values()) {
        if (timeZone.getId() == id) {
          return timeZone;
        }
      }
      throw new IllegalArgumentException(
          String.format("ID(%d) out of Range", id));
    }
  }

  /**
   * 登录配置类型.
   */
  interface LoginSetupType {
    int PWD = 0x01;
    int FACE = 0x02;
    int FINGER = 0x04;
  }

  /**
   * Nova主机类型.
   */
  enum NovaDeviceType implements Nameable {
    E("E"),
    ALPHA("ALPHA");
    private final String name;

    NovaDeviceType(String name) {
      this.name = name;
    }

    @Override
    public String getName() {
      return name;
    }
  }

}
