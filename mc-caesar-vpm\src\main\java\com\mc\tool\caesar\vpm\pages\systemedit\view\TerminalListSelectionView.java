package com.mc.tool.caesar.vpm.pages.systemedit.view;

import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import javafx.scene.control.Skin;
import org.controlsfx.control.ListSelectionViewEx;

/**
 * .
 */
public class TerminalListSelectionView extends ListSelectionViewEx<VisualEditNode> {

  @Override
  protected Skin<ListSelectionViewEx<VisualEditNode>> createDefaultSkin() {
    return new TerminalListSelectionViewSkin(this);
  }
}
