package com.mc.tool.caesar.vpm.pages.matrixgrid;

import javafx.beans.property.BooleanProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;

/**
 * .
 */
public class MatrixGridModel {

  public BooleanProperty activate = new SimpleBooleanProperty();
  public StringProperty device = new SimpleStringProperty();
  public StringProperty host = new SimpleStringProperty();
  public IntegerProperty port = new SimpleIntegerProperty();
  public BooleanProperty master = new SimpleBooleanProperty();

  private final int index;

  public MatrixGridModel(int index) {
    this.index = index;
  }

  public int getIndex() {
    return index;
  }

  /** 重置数据. */
  public void reset() {
    activate.set(false);
    device.set("");
    host.set("");
    port.set(0);
    master.set(false);
  }
}
