package com.mc.tool.caesar.api.utils;

/**
 * 唯一的key的生成器.
 */
public final class UniqueKeyGenerator {

  private static final UniqueKeyGenerator INSTANCE = new UniqueKeyGenerator();
  private long id;

  private UniqueKeyGenerator() {
    this.id = 0L;
  }

  public static UniqueKeyGenerator getInstance() {
    return INSTANCE;
  }

  public synchronized long generateId() {
    return this.id++;
  }
}

