package com.mc.tool.caesar.vpm.util.vp;

import javafx.scene.paint.Color;

/**
 * .
 */
public interface VpOsdData {
  int getOsdLeft();

  int getOsdTop();

  int getOsdWidth();

  int getOsdHeight();

  int getOsdAlpha();

  Color getOsdColor();

  Color getBgColor();

  boolean isShowLogo();

  boolean isEnableBgImg();

  boolean isEnableBanner();

  boolean isEnableBannerBg();

  int getBgImgWidth();

  int getBgImgHeight();

  boolean isEnableRedundant();

  int getAnalogAudioVolume();
}
