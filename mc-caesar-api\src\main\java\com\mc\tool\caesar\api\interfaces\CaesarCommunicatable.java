package com.mc.tool.caesar.api.interfaces;

import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;

/**
 * CaesarCommunicatable.
 */
public interface CaesarCommunicatable {

  void readData(CfgReader paramCfgReader) throws ConfigException;

  void writeData(CfgWriter paramCfgWriter) throws ConfigException;
}

