<?xml version="1.0" encoding="UTF-8"?>

<?import com.mc.tool.caesar.vpm.pages.systemedit.view.RemoteSwitchingView?>
<?import com.mc.tool.caesar.vpm.pages.systemedit.view.MonitorDecoderView?>
<?import com.mc.tool.caesar.vpm.pages.systemedit.view.TerminalListView?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.control.ToggleButton?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import org.controlsfx.control.MasterDetailPane?>
<?import org.controlsfx.control.PropertySheet?>
<?import com.mc.tool.caesar.vpm.pages.systemedit.view.BranchSettingView?>
<?import com.mc.tool.caesar.vpm.pages.systemedit.view.OfflineManagerView?>
<fx:root type="javafx.scene.layout.AnchorPane" xmlns="http://javafx.com/javafx/8"
         xmlns:fx="http://javafx.com/fxml/1" stylesheets="@system_edit_view.css">
  <VBox AnchorPane.topAnchor="0" AnchorPane.bottomAnchor="0"
    AnchorPane.leftAnchor="0" AnchorPane.rightAnchor="0">
    <AnchorPane prefHeight="40" minHeight="40">
      <HBox fx:id="connectionFilterBox" AnchorPane.topAnchor="0"
        AnchorPane.leftAnchor="0" alignment="CENTER_LEFT" prefHeight="40"
        minHeight="40">

      </HBox>

      <HBox AnchorPane.topAnchor="0" AnchorPane.rightAnchor="10"
        alignment="CENTER_RIGHT" prefHeight="40" minHeight="40">
        <TextField fx:id="searchText"/>
        <ToggleButton id="search-btn" text="%toolbar.search"
          styleClass="image-button" fx:id="searchBtn"/>
        <Button id="refresh-btn" text="%toolbar.refresh" styleClass="image-button"
          onAction="#onRefresh"/>
        <Separator maxHeight="20.0" orientation="VERTICAL" prefHeight="20.0"/>
        <ComboBox fx:id="pageSelector" style="-fx-border-width: 0;"/>
      </HBox>
    </AnchorPane>
    <Region styleClass="seperator" minHeight="1"/>
    <HBox VBox.Vgrow="ALWAYS">
      <VBox prefWidth="215" minWidth="215" fx:id="itemboardContainer"
        visible="false" managed="false"/>
      <VBox fx:id="remoteSwitchingContainer" HBox.Hgrow="ALWAYS" styleClass="view-container">
        <RemoteSwitchingView fx:id="remoteSwitchingView" VBox.vgrow="ALWAYS"/>
      </VBox>
      <VBox fx:id="monitorDecoderContainer" HBox.Hgrow="ALWAYS" styleClass="view-container">
        <MonitorDecoderView fx:id="monitorDecoderView" VBox.vgrow="ALWAYS"/>
      </VBox>
      <VBox fx:id="branchSettingContainer" HBox.Hgrow="ALWAYS" styleClass="view-container">
        <BranchSettingView fx:id="branchSettingView" VBox.vgrow="ALWAYS"/>
      </VBox>
      <VBox fx:id="offlineContainer" HBox.Hgrow="ALWAYS" styleClass="view-container">
        <OfflineManagerView fx:id="offlineManagerView" VBox.vgrow="ALWAYS"/>
      </VBox>
      <MasterDetailPane HBox.Hgrow="ALWAYS" fx:id="masterDetailPane">
        <masterNode>
          <HBox>
            <StackPane HBox.Hgrow="ALWAYS">
              <VBox fx:id="listView" styleClass="view-container">
                <TerminalListView fx:id="terminalListView" VBox.vgrow="ALWAYS"/>
                <HBox styleClass="list-view-toolbar">
                  <Button id="export-optical-module-info-btn"
                    text="%export_optical_module_info" styleClass="common-button"
                    onAction="#onExportOpticalModuleInfo"/>
                  <Button id="import-extender-info-btn"
                    text="%import_extender_info" styleClass="common-button"
                    onAction="#onImportExtenderInfo"/>
                  <Button id="export-extender-info-btn"
                    text="%export_extender_info" styleClass="common-button"
                    onAction="#onExportExtenderInfo"/>
                </HBox>
              </VBox>
              <VBox fx:id="graphView" styleClass="view-container">
                <VBox fx:id="graphContainer" VBox.Vgrow="ALWAYS"/>
                <HBox alignment="center" minHeight="38" prefHeight="38"
                  id="graph-bottom-toolbar">
                  <ToggleButton id="preview-btn" styleClass="image-button"
                    fx:id="previewButton"/>
                  <Button id="zoomin-btn" fx:id="zoominBtn"
                    styleClass="image-button"
                    onAction="#onZoomin"/>
                  <Button id="zoomout-btn" fx:id="zoomoutBtn"
                    styleClass="image-button"
                    onAction="#onZoomout"/>
                  <Button id="restore-btn" styleClass="image-button"
                    onAction="#onRestore"/>
                  <Button id="switch-btn" styleClass="image-button"
                    fx:id="switchBtn"
                    onAction="#onSwitchMatrix"/>
                </HBox>
              </VBox>
            </StackPane>
            <VBox id="show-btn-container" prefWidth="26" maxWidth="26"
              fx:id="showBtnContainer">
              <VBox id="show-btn-sub-container">
                <HBox prefHeight="10" minHeight="10" alignment="CENTER_RIGHT">
                  <Button styleClass="image-button" id="show-property-btn"
                    onAction="#onShowProperty"/>
                </HBox>
                <Region styleClass="seperator" minHeight="1"/>
                <Button styleClass="image-button" id="property-btn"/>
                <Region styleClass="seperator" minHeight="1"/>
                <Button styleClass="image-button" id="icon-btn"/>
                <Region styleClass="seperator" minHeight="1"/>
              </VBox>
              <Region VBox.vgrow="ALWAYS"/>
            </VBox>
          </HBox>
        </masterNode>
        <detailNode>
          <VBox prefWidth="240" minWidth="240" id="right-panel">
            <HBox prefHeight="10" minHeight="10" styleClass="right-title"
              id="hide-btn-container">
              <Button styleClass="image-button" id="hide-property-btn"
                onAction="#onHideProperty"/>
            </HBox>
            <VBox>
              <Region styleClass="seperator" minHeight="1"/>
              <HBox prefHeight="20" minHeight="20" styleClass="right-title">
                <Label text="%title.property"/>
              </HBox>
              <Region styleClass="seperator" minHeight="1"/>
              <PropertySheet fx:id="newPropertySheet"/>
            </VBox>
            <VBox fx:id="actionsBox">
              <Region styleClass="seperator" minHeight="1"/>
              <HBox prefHeight="20" minHeight="20" styleClass="right-title">
                <Label text="%title.action"/>
              </HBox>
              <Region styleClass="seperator" minHeight="1"/>
              <VBox fx:id="actionsGridPaneContainer"/>
            </VBox>
            <VBox>
              <Region styleClass="seperator" minHeight="1"/>
              <HBox prefHeight="20" minHeight="20" styleClass="right-title">
                <Label text="%title.logo"/>
              </HBox>
              <Region styleClass="seperator" minHeight="1"/>
              <GridPane id="icon-grid-pane" fx:id="iconGridPane"/>
            </VBox>
          </VBox>
        </detailNode>
      </MasterDetailPane>
    </HBox>

  </VBox>
</fx:root>