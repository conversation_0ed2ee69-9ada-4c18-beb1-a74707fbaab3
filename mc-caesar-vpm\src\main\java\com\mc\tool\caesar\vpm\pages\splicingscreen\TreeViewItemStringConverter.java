package com.mc.tool.caesar.vpm.pages.splicingscreen;

import javafx.util.StringConverter;
import lombok.NoArgsConstructor;

/**
 * TreeViewItemStringConverter.
 */
@NoArgsConstructor
public class TreeViewItemStringConverter extends StringConverter<TreeViewItem> {

  public TreeViewItem fromString(String var1) {
    return null;
  }

  public String toString(TreeViewItem var) {
    return var.cellToString();
  }
}