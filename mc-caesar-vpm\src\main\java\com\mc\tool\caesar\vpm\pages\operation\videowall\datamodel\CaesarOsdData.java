package com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.JsonAdapter;
import com.mc.tool.caesar.api.datamodel.vp.OsdInfoGetter;
import com.mc.tool.caesar.api.datamodel.vp.OsdInfoSetter;
import com.mc.tool.framework.utility.ColorObjectPropertyTypeAdapter;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.scene.paint.Color;

/**
 * .
 */
public class CaesarOsdData implements OsdInfoGetter, OsdInfoSetter {
  @Expose public IntegerProperty osdLeft = new SimpleIntegerProperty(200);
  @Expose public IntegerProperty osdTop = new SimpleIntegerProperty(200);
  @Expose public IntegerProperty osdWidth = new SimpleIntegerProperty(80);
  @Expose public IntegerProperty osdHeight = new SimpleIntegerProperty(400);
  @Expose public DoubleProperty osdAlpha = new SimpleDoubleProperty(0.25);

  @Expose
  @JsonAdapter(ColorObjectPropertyTypeAdapter.class)
  public ObjectProperty<Color> osdColor = new SimpleObjectProperty<>(Color.rgb(0, 164, 255));

  @Expose
  @JsonAdapter(ColorObjectPropertyTypeAdapter.class)
  public ObjectProperty<Color> bgColor = new SimpleObjectProperty<>(Color.BLACK);

  @Expose public BooleanProperty showLogo = new SimpleBooleanProperty(false);
  @Expose public BooleanProperty enableBgImg = new SimpleBooleanProperty(false);
  @Expose public IntegerProperty bgImgWidth = new SimpleIntegerProperty(0);
  @Expose public IntegerProperty bgImgHeight = new SimpleIntegerProperty(0);
  @Expose public BooleanProperty disableSyncData = new SimpleBooleanProperty(false);
  @Expose public BooleanProperty enableRedundant = new SimpleBooleanProperty(false);
  @Expose public BooleanProperty enableBanner = new SimpleBooleanProperty(false);
  @Expose public BooleanProperty enableBannerBg = new SimpleBooleanProperty(false);

  @Expose
  public IntegerProperty analogAudioVolume = new SimpleIntegerProperty(0);


  @Override
  public int getOsdLeft() {
    return osdLeft.get();
  }

  public IntegerProperty getOsdLeftProperty() {
    return osdLeft;
  }

  @Override
  public int getOsdTop() {
    return osdTop.get();
  }

  public IntegerProperty getOsdTopProperty() {
    return osdTop;
  }

  @Override
  public int getOsdWidth() {
    return osdWidth.get();
  }

  public IntegerProperty getOsdWidthProperty() {
    return osdWidth;
  }

  @Override
  public int getOsdHeight() {
    return osdHeight.get();
  }

  public IntegerProperty getOsdHeightProperty() {
    return osdHeight;
  }

  @Override
  public double getOsdAlpha() {
    return osdAlpha.get();
  }

  public DoubleProperty getOsdAlphaProperty() {
    return osdAlpha;
  }

  @Override
  public Color getOsdColor() {
    return osdColor.get();
  }

  public ObjectProperty<Color> getOsdColorProperty() {
    return osdColor;
  }

  @Override
  public Color getBgColor() {
    return bgColor.get();
  }

  public ObjectProperty<Color> getBgColorProperty() {
    return bgColor;
  }

  public BooleanProperty getShowLogoProperty() {
    return getShowLogo();
  }

  public BooleanProperty getEnableBgImgProperty() {
    return enableBgImg;
  }

  public IntegerProperty getBgImgWidthProperty() {
    return bgImgWidth;
  }

  public IntegerProperty getBgImgHeightProperty() {
    return bgImgHeight;
  }

  public BooleanProperty getDisableSyncDataProperty() {
    return disableSyncData;
  }

  public BooleanProperty getEnableRedundantProperty() {
    return enableRedundant;
  }

  public BooleanProperty getEnableBannerProperty() {
    return enableBanner;
  }

  public BooleanProperty getEnableBannerBgProperty() {
    return enableBannerBg;
  }

  public IntegerProperty getAnalogAudioVolumeProperty() {
    return analogAudioVolume;
  }

  @Override
  public void setOsdLeft(int value) {
    osdLeft.set(value);
  }

  @Override
  public void setOsdTop(int value) {
    osdTop.set(value);
  }

  @Override
  public void setOsdWidth(int value) {
    osdWidth.set(value);
  }

  @Override
  public void setOsdHeight(int value) {
    osdHeight.set(value);
  }

  @Override
  public void setOsdAlpha(double value) {
    osdAlpha.set((int) (value * 100 + 0.5) / 100.0);
  }

  @Override
  public void setOsdColor(Color value) {
    osdColor.set(value);
  }

  @Override
  public void setBgColor(Color value) {
    bgColor.set(value);
  }

  @Override
  public void setShowLogo(boolean show) {
    getShowLogo().set(show);
  }

  @Override
  public void setEnableBgImg(boolean enable) {
    enableBgImg.set(enable);
  }

  @Override
  public void setBgImgWidth(int value) {
    bgImgWidth.set(value);
  }

  @Override
  public void setBgImgHeight(int value) {
    bgImgHeight.set(value);
  }

  @Override
  public void setDisableSyncData(boolean disable) {
    disableSyncData.set(disable);
  }

  @Override
  public void setEnableRedundant(boolean enable) {
    enableRedundant.set(enable);
  }

  @Override
  public void setEnableBanner(boolean enable) {
    enableBanner.set(enable);
  }

  @Override
  public void setEnableBannerBg(boolean enable) {
    enableBannerBg.set(enable);
  }

  @Override
  public void setAnalogAudioVolume(int value) {
    analogAudioVolume.set(value);
  }

  @Override
  public boolean isShowLogo() {
    return getShowLogo().get();
  }

  @Override
  public boolean isEnableBgImg() {
    return enableBgImg.get();
  }

  @Override
  public int getBgImgWidth() {
    return bgImgWidth.get();
  }

  @Override
  public int getBgImgHeight() {
    return bgImgHeight.get();
  }

  @Override
  public boolean isDisableSyncData() {
    return disableSyncData.get();
  }

  @Override
  public boolean isEnableRedundant() {
    return enableRedundant.get();
  }

  @Override
  public boolean isEnableBanner() {
    return enableBanner.get();
  }

  @Override
  public boolean isEnableBannerBg() {
    return enableBannerBg.get();
  }

  @Override
  public int getAnalogAudioVolume() {
    return analogAudioVolume.get();
  }

  protected BooleanProperty getShowLogo() {
    if (showLogo == null) {
      showLogo = new SimpleBooleanProperty(false);
    }
    return showLogo;
  }
}
