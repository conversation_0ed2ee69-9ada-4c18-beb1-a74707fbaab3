package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarTerminalBase;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbRxTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbTxTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.usb.UsbRebindConfigBean;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.usb.UsbUtility;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.utility.FormDialog;
import java.util.Optional;
import javafx.collections.FXCollections;
import javafx.scene.control.ButtonType;
import javafx.scene.control.ComboBox;
import javafx.scene.control.MenuItem;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class MenuUsbRebind extends MenuItem {
  private final SystemEditControllable controllable;
  private final CaesarDeviceController controller;
  private final CaesarMatrix matrix;

  /** . */
  public MenuUsbRebind(
      SystemEditControllable controllable,
      CaesarMatrix matrix,
      CaesarDeviceController deviceController) {
    this.controllable = controllable;
    this.matrix = matrix;
    this.controller = deviceController;
    setText(Bundle.NbBundle.getMessage("menu.rebind"));

    setOnAction((event) -> onAction());
  }

  protected void onAction() {
    VisualEditNode node = controllable.getSelectedNodes().iterator().next();
    if (!(node instanceof CaesarUsbTxTerminal || node instanceof CaesarUsbRxTerminal)) {
      return;
    }
    CaesarTerminalBase terminalBase = (CaesarTerminalBase) node;
    ExtenderData extenderData = terminalBase.getExtenderData();
    if (extenderData == null) {
      log.error("Terminal's extender is null!");
      return;
    }

    UsbRebindConfigBean bean = new UsbRebindConfigBean();
    if (terminalBase.isTx()) {
      bean.binding.set(extenderData.getCpuData());
    } else if (terminalBase.isRx()) {
      bean.binding.set(extenderData.getConsoleData());
    } else {
      log.error("Termianl's direction is error!");
      return;
    }

    FormDialog<UsbRebindConfigBean> dialog =
        new FormDialog<>(
            bean,
            CaesarI18nCommonResource.getResourceBundle("usb_binding_bean"),
            getParentPopup().getOwnerWindow());

    ComboBox comboBox = dialog.getElementControl("binding", ComboBox.class);
    ConfigDataManager dataManager = controller.getDataModel().getConfigDataManager();
    if (comboBox != null) {
      if (terminalBase.isTx()) {
        comboBox.setItems(
            FXCollections.observableArrayList(UsbUtility.getBindableCpus(dataManager, matrix)));
      } else {
        comboBox.setItems(
            FXCollections.observableArrayList(UsbUtility.getBindableConsoles(dataManager, matrix)));
      }
    }

    Optional<ButtonType> result = dialog.showAndWaitWithCommit();
    if (result.isPresent() && result.get() == ButtonType.APPLY) {
      controller.execute(() -> controller.rebindUsbExtender(extenderData, bean.getBinding()));
    }
  }
}
