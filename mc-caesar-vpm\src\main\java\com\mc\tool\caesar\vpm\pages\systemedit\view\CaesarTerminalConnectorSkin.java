package com.mc.tool.caesar.vpm.pages.systemedit.view;

import com.mc.graph.interfaces.Connector;
import com.mc.graph.util.LinkUtil;
import com.mc.tool.framework.systemedit.view.TerminalConnectorSkin;
import javafx.beans.binding.Bindings;
import javafx.beans.binding.DoubleBinding;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.scene.Parent;
import javafx.scene.paint.Color;
import javafx.scene.shape.Rectangle;

/**
 * .
 */
public class CaesarTerminalConnectorSkin extends TerminalConnectorSkin {
  public static final int TERMINAL_HEIGHT = 35;

  private BooleanProperty connectedProperty;

  private static final Color CONNECTED_COLOR = new Color(1 / 255.0, 209 / 255.0, 11 / 255.0, 1);
  private static final Color DISCONNECTED_COLOR = Color.web("#b3b3b3");

  public CaesarTerminalConnectorSkin(Connector connector, Parent parent, Parent container) {
    super(connector, parent, container);
  }

  /**
   * 端口连接状态.
   *
   * @return 连接状态
   */
  public BooleanProperty getConnectedProperty() {
    if (connectedProperty == null) {
      connectedProperty = new SimpleBooleanProperty(false);
    }
    return connectedProperty;
  }

  @Override
  protected void initNode() {
    node = new Rectangle();
    node.setWidth(CONNECTOR_WIDTH);
    node.setHeight(CONNECTOR_HEIGHT);
    node.fillProperty()
        .bind(
            Bindings.when(getConnectedProperty())
                .then(CONNECTED_COLOR)
                .otherwise(DISCONNECTED_COLOR));
    node.setUserData(this);
    //
    Parent region = parent.getParent();
    DoubleBinding xposBinding =
        new DoubleBinding() {
          {
            super.bind(
                getNode().parentProperty(),
                parent.layoutXProperty(),
                region.layoutXProperty(),
                region.translateXProperty());
          }

          @Override
          protected double computeValue() {
            return LinkUtil.localToGrandParent(
                parent, container, connectAtLeft ? -1 : CONNECTOR_WIDTH + 5, true);
          }
        };

    DoubleBinding yposBinding =
        new DoubleBinding() {
          {
            super.bind(
                getNode().parentProperty(),
                parent.layoutYProperty(),
                region.layoutYProperty(),
                region.translateYProperty());
          }

          @Override
          protected double computeValue() {
            return LinkUtil.localToGrandParent(
                parent, container, (((int) parent.maxHeight(-1)) - 1) / 2.0, false);
          }
        };

    containerXposProperty.bind(xposBinding);
    containerYposProperty.bind(yposBinding);
  }
}
