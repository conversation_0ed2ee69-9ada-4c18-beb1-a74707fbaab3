package com.mc.tool.caesar.api.interfaces;

import java.util.EventObject;

/**
 * .
 */
public final class ChangedEvent extends EventObject {

  /**
   * .
   */
  private static final long serialVersionUID = 1L;

  public ChangedEvent(Object source) {
    super(source);
  }

  /**
   * .
   */
  public CommitRollback getCommitRollback() {
    if (getSource() instanceof CommitRollback) {
      return (CommitRollback) this.source;
    }
    return null;
  }
}

