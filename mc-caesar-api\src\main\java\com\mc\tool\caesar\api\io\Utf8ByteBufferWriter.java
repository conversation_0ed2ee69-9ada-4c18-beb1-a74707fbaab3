/*
 * Javolution - Java(TM) Solution for Real-Time and Embedded Systems Copyright (C) 2012 - Javolution
 * (http://javolution.org/) All rights reserved.
 *
 * Permission to use, copy, modify, and distribute this software is freely granted, provided that
 * this notice is preserved.
 */

package com.mc.tool.caesar.api.io;

import java.io.CharConversionException;
import java.io.IOException;
import java.io.Writer;
import java.nio.ByteBuffer;

/**
 * <p>
 * A UTF-8 <code>java.nio.ByteBuffer</code> writer.
 * </p>
 *
 * <p>
 * This writer supports surrogate <code>char</code> pairs (representing characters in the range
 * [U+10000 .. U+10FFFF]). It can also be used to write characters from their unicodes (31 bits)
 * directly (ref. {@link #write(int)}).
 * </p>
 *
 * <p>
 * Instances of this class can be reused for different output streams and can be part of a higher
 * level component (e.g. serializer) in order to avoid dynamic buffer allocation when the
 * destination output changes. Also wrapping using a <code>java.io.BufferedWriter</code> is
 * unnescessary as instances of this class embed their own data buffers.
 * </p>
 *
 * <p>
 * Note: This writer is unsynchronized and always produces well-formed UTF-8 sequences.
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">Jean-Marie Dautelle</a>
 * @version 2.0, December 9, 2004
 * @see Utf8ByteBufferReader
 */
public final class Utf8ByteBufferWriter extends Writer {

  /**
   * Holds the byte buffer destination.
   */
  private ByteBuffer byteBuffer;

  /**
   * Default constructor.
   */
  public Utf8ByteBufferWriter() {

  }

  /**
   * Constructor to Provide a Byte Buffer on Initialization.
   *
   * @param buf Byte Buffer to use for Writing
   */
  public Utf8ByteBufferWriter(final ByteBuffer buf) {
    byteBuffer = buf;
  }

  protected ByteBuffer getOutput() {
    return byteBuffer;
  }

  /**
   * Sets the byte buffer to use for writing until this writer is closed.
   *
   * @param buf the destination byte buffer.
   * @return this UTF-8 writer.
   * @throws IllegalStateException if this writer is being reused and it has not been {@link #close
   *                               closed} or {@link #reset reset}.
   */
  public Utf8ByteBufferWriter setOutput(ByteBuffer buf) {
    if (byteBuffer != null) {
      throw new IllegalStateException("Writer not closed or reset");
    }
    byteBuffer = buf;
    return this;
  }

  /**
   * Writes a single character. This method supports 16-bits character surrogates.
   *
   * @param cc <code>char</code> the character to be written (possibly a surrogate).
   * @throws IOException if an I/O error occurs.
   */
  public void write(char cc) throws IOException {
    if (byteBuffer == null) {
      throw new IOException("Writer closed");
    }
    if (cc < 0xd800 || cc > 0xdfff) {
      write((int) cc);
    } else if (cc < 0xdc00) { // High surrogate.
      highSurrogate = cc;
    } else { // Low surrogate.
      int code = ((highSurrogate - 0xd800) << 10) + (cc - 0xdc00) + 0x10000;
      write(code);
    }
  }

  private char highSurrogate;

  /**
   * Writes a character given its 31-bits Unicode.
   *
   * @param code the 31 bits Unicode of the character to be written.
   * @throws IOException if an I/O error occurs.
   */
  @Override
  public void write(int code) throws IOException {
    if (byteBuffer == null) {
      throw new IOException("Writer closed");
    }
    if ((code & 0xffffff80) == 0) {
      byteBuffer.put((byte) code);
    } else { // Writes more than one byte.
      write2(code);
    }
  }

  /**
   * Writes a portion of an array of characters.
   *
   * @param cbuf the array of characters.
   * @param off  the offset from which to start writing characters.
   * @param len  the number of characters to write.
   * @throws IOException if an I/O error occurs.
   */
  @Override
  public void write(char[] cbuf, int off, int len) throws IOException {
    if (byteBuffer == null) {
      throw new IOException("Writer closed");
    }
    final int off_plus_len = off + len;
    for (int i = off; i < off_plus_len; ) {
      char cc = cbuf[i++];
      if (cc < 0x80) {
        byteBuffer.put((byte) cc);
      } else {
        write(cc);
      }
    }
  }

  /**
   * Writes a portion of a string.
   *
   * @param str a String.
   * @param off the offset from which to start writing characters.
   * @param len the number of characters to write.
   * @throws IOException if an I/O error occurs
   */
  @Override
  public void write(String str, int off, int len) throws IOException {
    if (byteBuffer == null) {
      throw new IOException("Writer closed");
    }
    final int off_plus_len = off + len;
    for (int i = off; i < off_plus_len; ) {
      char cc = str.charAt(i++);
      if (cc < 0x80) {
        byteBuffer.put((byte) cc);
      } else {
        write(cc);
      }
    }
  }

  /**
   * Writes the specified character sequence.
   *
   * @param csq the character sequence.
   * @throws IOException if an I/O error occurs
   */
  public void write(CharSequence csq) throws IOException {
    if (byteBuffer == null) {
      throw new IOException("Writer closed");
    }
    final int length = csq.length();
    for (int i = 0; i < length; ) {
      char cc = csq.charAt(i++);
      if (cc < 0x80) {
        byteBuffer.put((byte) cc);
      } else {
        write(cc);
      }
    }
  }

  private void write2(int cc) throws IOException {
    if ((cc & 0xfffff800) == 0) { // 2 bytes.
      byteBuffer.put((byte) (0xc0 | (cc >> 6)));
      byteBuffer.put((byte) (0x80 | (cc & 0x3f)));
    } else if ((cc & 0xffff0000) == 0) { // 3 bytes.
      byteBuffer.put((byte) (0xe0 | (cc >> 12)));
      byteBuffer.put((byte) (0x80 | ((cc >> 6) & 0x3f)));
      byteBuffer.put((byte) (0x80 | (cc & 0x3f)));
    } else if ((cc & 0xff200000) == 0) { // 4 bytes.
      byteBuffer.put((byte) (0xf0 | (cc >> 18)));
      byteBuffer.put((byte) (0x80 | ((cc >> 12) & 0x3f)));
      byteBuffer.put((byte) (0x80 | ((cc >> 6) & 0x3f)));
      byteBuffer.put((byte) (0x80 | (cc & 0x3f)));
    } else if ((cc & 0xf4000000) == 0) { // 5 bytes.
      byteBuffer.put((byte) (0xf8 | (cc >> 24)));
      byteBuffer.put((byte) (0x80 | ((cc >> 18) & 0x3f)));
      byteBuffer.put((byte) (0x80 | ((cc >> 12) & 0x3f)));
      byteBuffer.put((byte) (0x80 | ((cc >> 6) & 0x3f)));
      byteBuffer.put((byte) (0x80 | (cc & 0x3f)));
    } else if ((cc & 0x80000000) == 0) { // 6 bytes.
      byteBuffer.put((byte) (0xfc | (cc >> 30)));
      byteBuffer.put((byte) (0x80 | ((cc >> 24) & 0x3f)));
      byteBuffer.put((byte) (0x80 | ((cc >> 18) & 0x3f)));
      byteBuffer.put((byte) (0x80 | ((cc >> 12) & 0x3F)));
      byteBuffer.put((byte) (0x80 | ((cc >> 6) & 0x3F)));
      byteBuffer.put((byte) (0x80 | (cc & 0x3F)));
    } else {
      throw new CharConversionException("Illegal character U+" + Integer.toHexString(cc));
    }
  }


  /**
   * Flushes the stream (this method has no effect, the data is always directly written to the
   * <code>ByteBuffer</code>).
   *
   * @throws IOException if an I/O error occurs.
   */
  @Override
  public void flush() throws IOException {
    if (byteBuffer == null) {
      throw new IOException("Writer closed");
    }
  }

  /**
   * Closes and {@link #reset resets} this writer for reuse.
   *
   * @throws IOException if an I/O error occurs
   */
  @Override
  public void close() throws IOException {
    if (byteBuffer != null) {
      reset();
    }
  }

  // Implements Reusable.
  public void reset() {
    byteBuffer = null;
    highSurrogate = 0;
  }

  /**
   * .
   *
   * @param byteBuffer ByteBuffer to write to
   * @return Reference to this UTF8ByteBufferWriter
   * @deprecated Replaced by {@link #setOutput(ByteBuffer)}
   */
  public Utf8ByteBufferWriter setByteBuffer(ByteBuffer byteBuffer) {
    return this.setOutput(byteBuffer);
  }

}
