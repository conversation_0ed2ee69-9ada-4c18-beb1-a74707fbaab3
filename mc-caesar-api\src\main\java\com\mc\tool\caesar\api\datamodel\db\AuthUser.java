package com.mc.tool.caesar.api.datamodel.db;

import com.google.gson.annotations.SerializedName;

/**
 * 组合登录的认证用户.
 */
public class AuthUser {
  @SerializedName("id")
  int id;
  @SerializedName("name")
  String name;
  @SerializedName("gender")
  String gender;
  @SerializedName("mobile")
  String mobile;
  @SerializedName("status")
  Integer status;
  @SerializedName("bound_user")
  Integer boundUser;
  @SerializedName("face_feature")
  String faceFeature;
  @SerializedName("fingerprint_feature")
  String fingerprintFeature;
  @SerializedName("register_time")
  String registerTime;
  @SerializedName("last_login_time")
  String lastLoginTime;
}
