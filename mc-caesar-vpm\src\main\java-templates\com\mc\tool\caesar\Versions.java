package com.mc.tool.caesar;

public class Versions {
  public static final String VERSION = "${project.version}";
  public static final String MAJOR_VERSION = "${parsedVersion.majorVersion}";
  public static final String MINOR_VERSION = "${parsedVersion.minorVersion}";
  public static final String PATCH_VERSION = "${parsedVersion.incrementalVersion}";
  public static final String VERSION_QUALIFIER = "${parsedVersion.qualifier}";
}