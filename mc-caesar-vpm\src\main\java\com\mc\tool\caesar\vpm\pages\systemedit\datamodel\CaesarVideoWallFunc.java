package com.mc.tool.caesar.vpm.pages.systemedit.datamodel;

import com.google.common.eventbus.EventBus;
import com.google.gson.annotations.Expose;
import com.mc.common.beans.SimpleObservable;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarScreenData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoWallData;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.framework.event.VisualEditFuncBeginUpdateEvent;
import com.mc.tool.framework.event.VisualEditFuncEndUpdateEvent;
import com.mc.tool.framework.operation.videowall.datamodel.VirtualVideoData;
import com.mc.tool.framework.operation.videowall.datamodel.VirtualVideoWallData;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.ScreenObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoWallObject;
import com.mc.tool.framework.systemedit.datamodel.VideoWallFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.utility.EventBusProvider;
import java.util.Collection;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.util.Pair;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarVideoWallFunc extends VideoWallFunc {

  @Expose private CaesarVideoWallData videoWallData = new CaesarVideoWallData();

  @Expose @Getter private IntegerProperty videoWallIndexProperty = new SimpleIntegerProperty(-1);

  @Expose
  private ObservableList<CaesarVideoWallData> scenarios = FXCollections.observableArrayList();
  
  @Getter
  private SimpleObservable scenarioUpdateObservable = new SimpleObservable();

  @Override
  public void init() {
    super.init();
    videoWallData.init();
    children.addListener(
        (ListChangeListener<VisualEditNode>)
            change -> {
              EventBus eventBus = EventBusProvider.getEventBus();
              eventBus.post(new VisualEditFuncBeginUpdateEvent(this));
              try {
                // 更新屏幕数据
                while (change.next()) {
                  for (VisualEditNode node : change.getRemoved()) {
                    if (!(node instanceof VpGroup)) {
                      log.warn("The function's child is not terminal!");
                      continue;
                    }

                    VpGroup vpgroup = (VpGroup) node;
                    for (VpConsoleData vpConsoleData : vpgroup.getOutPortsList()) {
                      for (CaesarScreenData screenData : videoWallData.getScreens()) {
                        if (screenData.getVpScreen() == vpConsoleData) {
                          screenData.setBingdingScreen(new Pair<>(null, 0));
                        }
                      }
                    }
                  }
                }
              } finally {
                eventBus.post(new VisualEditFuncEndUpdateEvent(this));
              }
            });
  }

  @Override
  public VideoWallObject getVideoWallObject() {
    return videoWallData;
  }

  public CaesarVideoWallData getCaesarVideoWall() {
    return videoWallData;
  }

  @Override
  public VideoWallObject createVideoWallObject() {
    CaesarVideoWallData data = new CaesarVideoWallData();
    data.init();
    return data;
  }

  @Override
  public VideoWallObject toVideoWallObject(VirtualVideoWallData virtualData) {
    VideoWallObject input = getVideoWallObject();
    if (virtualData.getLayout() != null
        && (virtualData.getLayout().getRows().get() != input.getLayoutData().getRows()
            || virtualData.getLayout().getColumns().get() != input.getLayoutData().getColumns())) {
      return null;
    }
    CaesarVideoWallData data = new CaesarVideoWallData();
    data.init();
    input.copyTo(data, false);
    data.getVideos().clear();
    for (VirtualVideoData video : virtualData.getVideos()) {
      data.addVideo(
          video.toVideoData(
              createVideo(),
              data.getLayoutData().getTotalWidth(),
              data.getLayoutData().getTotalHeight()));
    }
    data.getName().set(virtualData.getName().get());
    return data;
  }

  @Override
  public ObservableList<? extends VideoWallObject> getScenarios() {
    return scenarios;
  }

  @Override
  public VideoObject createVideo() {
    CaesarVideoData data = new CaesarVideoData();
    data.getName().set(getUniqueName());
    return data;
  }

  @Override
  public ScreenObject createScreen() {
    return new CaesarScreenData();
  }

  @Override
  public boolean addScenario(VideoWallObject object) {
    if (object instanceof CaesarVideoWallData) {
      scenarios.add((CaesarVideoWallData) object);
      return true;
    } else {
      return false;
    }
  }

  @Override
  public boolean removeScenario(VideoWallObject object) {
    if (object instanceof CaesarVideoWallData) {
      scenarios.remove((CaesarVideoWallData) object);
      return true;
    } else {
      return false;
    }
  }

  public int getVideoWallIndex() {
    return videoWallIndexProperty.get();
  }

  public void setVideoWallIndex(int index) {
    videoWallIndexProperty.set(index);
  }

  /**
   * 重置屏幕绑定，并自动分配.
   */
  public void resetAndAutoBindScreens() {
    // 重置
    for (CaesarScreenData screenData : videoWallData.getScreens()) {
      screenData.setBingdingScreen(new Pair<>(null, -1));
    }
    // 自动分配
    int cnt = videoWallData.getScreens().size();
    int index = 0;
    Collection<VisualEditNode> nodes = children.filtered(VpGroup.class::isInstance);
    int maxScreenPerGroup = Math.max(1, cnt / nodes.size());
    for (VisualEditNode node : nodes) {
      VpGroup vpGroup = (VpGroup) node;
      int bindScreenCount = Math.min(maxScreenPerGroup, vpGroup.getOutPortsList().size());
      for (int i = 0; i < bindScreenCount; i++) {
        VpConsoleData data = vpGroup.getOutPortsList().get(i);
        int portIndex = data.getParent().getOutportIndex(data);
        videoWallData.getScreens().get(index).setBingdingScreen(new Pair<>(vpGroup, portIndex));
        index++;
      }
    }
  }

  public boolean isVp7() {
    return videoWallData.isVp7();
  }
}
