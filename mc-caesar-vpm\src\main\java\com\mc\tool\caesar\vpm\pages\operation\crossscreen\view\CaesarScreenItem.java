package com.mc.tool.caesar.vpm.pages.operation.crossscreen.view;

import com.google.gson.Gson;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.MultiviewData;
import com.mc.tool.caesar.api.datamodel.MultiviewSource;
import com.mc.tool.caesar.api.datamodel.extargs.DhdmiSelection;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.DraggedVideoSourceInfo;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminalWrapper;
import com.mc.tool.framework.operation.controller.DefaultSnapshotGetter;
import com.mc.tool.framework.operation.crossscreen.controller.CrossScreenControllable;
import com.mc.tool.framework.operation.crossscreen.controller.ScreenItem;
import com.mc.tool.framework.operation.interfaces.SnapshotGetter;
import com.mc.tool.framework.systemedit.datamodel.CrossScreenFunc;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.VisualEditConnection;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.TypeWrapper;
import java.net.URL;
import java.util.Collection;
import java.util.ResourceBundle;
import javafx.beans.binding.Bindings;
import javafx.beans.binding.ObjectBinding;
import javafx.beans.binding.StringBinding;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.ListChangeListener;
import javafx.geometry.Side;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.MenuItem;
import javafx.scene.image.Image;
import javafx.scene.input.DragEvent;
import javafx.scene.layout.Background;
import javafx.scene.layout.BackgroundImage;
import javafx.scene.layout.BackgroundPosition;
import javafx.scene.layout.BackgroundRepeat;
import javafx.scene.layout.BackgroundSize;
import javafx.scene.paint.Color;
import javafx.scene.text.Font;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarScreenItem extends ScreenItem {
  private static final int SNAPSHOT_X = 28;
  private static final int SNAPSHOT_Y = 34;
  private static final int SNAPSHOT_WIDTH = 140;
  private static final int SNAPSHOT_HEIGHT = 76;

  private SnapshotGetter snapshotGetter;

  private ObjectProperty<VisualEditTerminal> connectedTerminal;

  public CaesarScreenItem(
      int row,
      int column,
      ObjectProperty<VisualEditTerminal> target,
      VisualEditModel model,
      CrossScreenFunc func,
      CrossScreenControllable controllable) {
    super(row, column, target, model, func, controllable);
  }

  /**
   * 更新Multiview模式.
   */
  public void updateMultiviewMode() {
    if (target.get() instanceof CaesarConTerminal) {
      CaesarConTerminal conTerminal = (CaesarConTerminal) target.get();
      ConsoleData conData = conTerminal.getConsoleData();
      MultiviewData multiviewData = conData.getMultiviewData();
      if (multiviewData != null) {
        drawDividingLine(multiviewData);
      }
    }
  }

  private void drawDividingLine(MultiviewData multiviewData) {
    GraphicsContext gc = canvas.getGraphicsContext2D();
    double width = canvas.getWidth();
    double halfWidth = width / 2.0;
    double height = canvas.getHeight();
    double halfHeight = height / 2.0;
    gc.clearRect(0, 0, width, height);
    gc.setFill(Color.CADETBLUE);
    gc.setStroke(Color.LIGHTGRAY);
    gc.strokeRect(0, 0, width, height);
    gc.setFont(new Font(10));
    gc.beginPath();
    if (multiviewData != null) {
      switch (multiviewData.getLayoutType()) {

        case FULL_SCREEN:
          if (multiviewData.getSourceCount() > 0) {
            MultiviewSource source = multiviewData.getSource(0);
            if (source != null) {
              CpuData cpuData = source.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 5, 10);
              }
            }
          }
          break;
        case TWO_GRID:
          gc.strokeRect(0, 0, halfWidth, height);
          gc.strokeRect(halfWidth, 0, width, height);
          if (multiviewData.getSourceCount() > 1) {
            MultiviewSource source1 = multiviewData.getSource(0);
            if (source1 != null) {
              CpuData cpuData = source1.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 5, 10);
              }
            }
            MultiviewSource source2 = multiviewData.getSource(1);
            if (source2 != null) {
              CpuData cpuData = source2.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 75, 10);
              }
            }
          }
          break;
        case FOUR_GRID:
          gc.strokeRect(0, 0, halfWidth, halfHeight);
          gc.strokeRect(halfWidth, 0, width, halfHeight);
          gc.strokeRect(0, halfHeight, halfWidth, height);
          gc.strokeRect(halfWidth, halfHeight, width, height);
          if (multiviewData.getSourceCount() > 3) {
            MultiviewSource source1 = multiviewData.getSource(0);
            if (source1 != null) {
              CpuData cpuData = source1.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 5, 10);
              }
            }
            MultiviewSource source2 = multiviewData.getSource(1);
            if (source2 != null) {
              CpuData cpuData = source2.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 75, 10);
              }
            }
            MultiviewSource source3 = multiviewData.getSource(2);
            if (source3 != null) {
              CpuData cpuData = source3.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 5, 48);
              }
            }
            MultiviewSource source4 = multiviewData.getSource(3);
            if (source4 != null) {
              CpuData cpuData = source4.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 75, 48);
              }
            }
          }
          break;
        case SIDE_BAR:
          double twoThirdWidth = width / 3.0 * 2;
          double oneThirdHeight = height / 3.0;
          double twoThirdHeight = height / 3.0 * 2;
          gc.strokeRect(0, 0, twoThirdWidth, height);
          gc.strokeRect(twoThirdWidth, 0, width, oneThirdHeight);
          gc.strokeRect(twoThirdWidth, oneThirdHeight, width, twoThirdHeight);
          gc.strokeRect(twoThirdWidth, twoThirdHeight, width, height);
          if (multiviewData.getSourceCount() > 3) {
            MultiviewSource source1 = multiviewData.getSource(0);
            if (source1 != null) {
              CpuData cpuData = source1.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 5, 10);
              }
            }
            MultiviewSource source2 = multiviewData.getSource(1);
            if (source2 != null) {
              CpuData cpuData = source2.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 98, 10);
              }
            }
            MultiviewSource source3 = multiviewData.getSource(2);
            if (source3 != null) {
              CpuData cpuData = source3.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 98, 35);
              }
            }
            MultiviewSource source4 = multiviewData.getSource(3);
            if (source4 != null) {
              CpuData cpuData = source4.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 98, 60);
              }
            }
          }
          break;
        default:
          break;
      }
    }
    gc.stroke();
    gc.closePath();
  }

  private SnapshotGetter getSnapshotGetter() {
    if (snapshotGetter == null) {
      snapshotGetter = new DefaultSnapshotGetter(model);
    }
    return snapshotGetter;
  }

  private ObjectProperty<VisualEditTerminal> getConnectedTerminal() {
    if (connectedTerminal == null) {
      connectedTerminal = new SimpleObjectProperty<>();
    }
    return connectedTerminal;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    this.setStyle(
        "-fx-background-color: #e9ecf1;-fx-border-style: solid;"
            + "-fx-border-width: 0.5;-fx-border-color: #97989a;");

    screenImage.managedProperty().bind(screenImage.visibleProperty());
    screenText.managedProperty().bind(screenText.visibleProperty());
    screenImage.visibleProperty().bind(target.isNotNull());
    screenText.visibleProperty().bind(target.isNotNull());

    updateConnectedTerminal();
    updateTextBinding();

    target.addListener(getWeakAdapter().wrap((observable, oldValue, newValue) -> {
      updateConnectedTerminal();
      updateTextBinding();
    }));

    model.getConnections().addListener(getWeakAdapter().wrap(
        (ListChangeListener<VisualEditConnection>) change -> updateConnectedTerminal()));

    getConnectedTerminal()
        .addListener(getWeakAdapter().wrap((obs, oldVal, newVal) -> updateTextBinding()));

    updateImage();

    this.setOnDragDetected(this::onDragDetected);
    this.setOnDragOver(this::onDragOver);
    this.setOnDragDropped(this::onDragDrop);
  }

  @Override
  protected void onDragDrop(DragEvent event) {
    VisualEditTerminal terminal = getTerminal(event);
    if (terminal != null && terminal.isRx() && func.isScreenMovable()) {
      Pair<Integer, Integer> pos = func.posOfScreen(terminal);
      func.moveScreen(pos.getKey(), pos.getValue(), getRow(), getColumn());
    } else if (terminal != null && target.get() != null && terminal.isTx() && controllable
        .isConnetable(terminal, target.get())) {
      if (terminal instanceof CaesarCpuTerminal) {
        Collection<TypeWrapper> channels = controllable.getMultiviewChannel(target.get());
        if (!channels.isEmpty()) {
          ContextMenu menu = new ContextMenu();
          channels.forEach(channel -> {
            MenuItem channelMenu = new MenuItem(channel.getName());
            channelMenu.setOnAction(evt -> controllable.connectMultiview(terminal, target.get(), 0,
                Integer.parseInt(channel.getType())));
            menu.getItems().add(channelMenu);
          });
          menu.show(this.screenImage, event.getScreenX(), event.getScreenY());
        } else {
          int sourceIndex = getDraggedVideoSourceInfo(event).getSourceIndex();
          controllable.connectForCrossScreen(
              new CaesarCpuTerminalWrapper((CaesarCpuTerminal) terminal, sourceIndex),
              target.get());
        }
      } else {
        controllable.connectForCrossScreen(terminal, target.get());
      }
    } else if (terminal == null && event.getDragboard().getString()
        .equals(SystemEditDefinition.EMPTY_TERMINAL_GUID)
        && controllable.isConnetable(null, target.get())) {
      controllable.connectForCrossScreen(null, target.get());
    }
    event.setDropCompleted(true);
    event.consume();
  }

  protected void updateTextBinding() {
    if (target.get() != null) {
      if (getConnectedTerminal().get() == null) {
        screenText.textProperty().bind(target.get().nameProperty());
      } else {
        VisualEditTerminal tx = getConnectedTerminal().get();
        if (target.get() instanceof CaesarConTerminal && tx.canSeperate()) {
          CaesarConTerminal conTerminal = (CaesarConTerminal) target.get();
          ObjectProperty<DhdmiSelection> selectionProperty =
              conTerminal.getConsoleData().cpuHdmiProperty();
          StringBinding selectionBing =
              Bindings.createStringBinding(() -> (selectionProperty.get().getValue() + 1) + "",
                  selectionProperty);
          screenText
              .textProperty()
              .bind(target.get().nameProperty().concat("[").concat(tx.nameProperty()).concat("(")
                  .concat(selectionBing).concat(")").concat("]"));
        } else {
          screenText
              .textProperty()
              .bind(target.get().nameProperty().concat("[").concat(tx.nameProperty()).concat("]"));
        }
      }
    } else {
      screenText.textProperty().unbind();
    }
  }

  protected void updateConnectedTerminal() {
    if (target.get() == null) {
      getConnectedTerminal().set(null);
    } else {
      Collection<VisualEditTerminal> terminals = model.getConnectedTerminal(target.get());
      if (terminals.isEmpty()) {
        getConnectedTerminal().set(null);
      } else {
        getConnectedTerminal().set(terminals.iterator().next());
      }
    }
  }

  @Override
  protected void updateImage() {
    ObjectProperty<Image> snapshotProperty =
        getSnapshotGetter().getSnapshot(getConnectedTerminal());

    ObjectBinding<Background> backgroundBinding = Bindings.createObjectBinding(() -> {
      String noControlImage = "com/mc/tool/framework/operation/crossscreen/screen.png";
      String controlImage = "com/mc/tool/framework/operation/crossscreen/screen_control.png";
      boolean isControl = func.getCrossScreenData().getControlSource().get() == target.get()
          && target.get() != null;
      String image = isControl ? controlImage : noControlImage;
      ClassLoader classLoader = Thread.currentThread().getContextClassLoader();

      BackgroundImage screenImageBg;
      Image screenImageInstance = new Image(classLoader.getResourceAsStream(image));
      screenImageBg = new BackgroundImage(screenImageInstance, BackgroundRepeat.NO_REPEAT,
          BackgroundRepeat.NO_REPEAT, BackgroundPosition.CENTER,
          new BackgroundSize(-1, -1, false, false, false, false));
      BackgroundImage snapshotImageBg = null;
      if (snapshotProperty.get() != null) {
        snapshotImageBg = new BackgroundImage(snapshotProperty.get(), BackgroundRepeat.NO_REPEAT,
            BackgroundRepeat.NO_REPEAT,
            new BackgroundPosition(Side.LEFT, SNAPSHOT_X, false, Side.TOP, SNAPSHOT_Y, false),
            new BackgroundSize(SNAPSHOT_WIDTH, SNAPSHOT_HEIGHT, false, false, false, false));
      }
      return new Background(screenImageBg, snapshotImageBg);
    }, target, getConnectedTerminal(), func.getCrossScreenData().getControlSource());

    screenImage.backgroundProperty().bind(backgroundBinding);
  }

  protected DraggedVideoSourceInfo getDraggedVideoSourceInfo(DragEvent event) {

    try {
      if (event.getDragboard().hasUrl()) {
        String str = event.getDragboard().getUrl();
        DraggedVideoSourceInfo info = new Gson().fromJson(str, DraggedVideoSourceInfo.class);
        return info;
      } else {
        return new DraggedVideoSourceInfo();
      }
    } catch (RuntimeException exception) {
      log.warn("Fail to parse dragged video source info!", exception);
      return new DraggedVideoSourceInfo();
    }
  }
}
