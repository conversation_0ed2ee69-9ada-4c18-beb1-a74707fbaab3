package com.mc.tool.caesar.api.datamodel.vp;

import java.util.Collection;

/**
 * .
 */
public interface LayoutInfoSetter {

  void setHorzCount(int value);

  void setVertCount(int value);

  void setResolutionHeight(int value);

  void setResolutionWidth(int value);

  void setResolution(LayoutSize value);

  void setEnableMultipleResolution(boolean enable);

  void setResolutionWidths(Collection<Integer> widths);

  void setResolutionHeights(Collection<Integer> heights);

  void setCompensationScaleThreshold(int value);

  void setLeftCompensation(int value);

  void setRightCompensation(int value);

  void setTopCompensation(int value);

  void setBottomCompensation(int value);


  void setHorzMargins(Collection<Integer> horzMargins);

  void setVertMargins(Collection<Integer> vertMargins);
}
