package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.menu.MenuThumbnail;
import com.mc.tool.framework.systemedit.menu.predicate.MenuPredicateBinding;
import javafx.beans.binding.BooleanBinding;

/**
 * .
 */
public class MenuThumbnail4Caesar extends MenuThumbnail {

  public MenuThumbnail4Caesar(SystemEditControllable controllable) {
    super(controllable);
  }

  @Override
  public BooleanBinding getMenuDisableBinding(SystemEditControllable controllable) {
    BooleanBinding binding = super.getMenuDisableBinding(controllable);
    MenuPredicateBinding newBinding = new MenuPredicateBinding(controllable, true);
    newBinding.addSingleSelectionPredicate((node) -> node.getParent() instanceof VpGroup);
    newBinding.addSingleSelectionPredicate((node) -> !(node instanceof CaesarConTerminal));
    return binding.or(newBinding);
  }
}
