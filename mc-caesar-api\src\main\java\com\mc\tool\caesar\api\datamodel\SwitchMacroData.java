package com.mc.tool.caesar.api.datamodel;

import java.util.Collections;
import java.util.Map;

/**
 * SwitchMacroData.
 *
 * @brief switch preset的配置数据.
 */
public class SwitchMacroData {

  private String fileName; // 加载或保存的文件路径
  private String deviceName; // 设备名
  private String configName; // 当前配置的名字
  private long lastModified; // 文件的最后的一次修改的信息
  private Map<Integer, Integer> fullAccess = Collections.emptyMap(); // 有full
  // access的con与cpu的id(非oid)的对
  private Map<Integer, Integer> videoAccess = Collections.emptyMap(); // 有video
  // access的con与cpu的id(非oid)的对
  private Map<Integer, Integer> privateMode = Collections.emptyMap(); // 有private
  // access的con与cpu的id(非oid)的对

  public String getFileName() {
    return this.fileName;
  }

  public void setFileName(String fileName) {
    this.fileName = fileName;
  }

  public String getConfigName() {
    return this.configName;
  }

  public void setConfigName(String configName) {
    this.configName = configName;
  }

  public String getDeviceName() {
    return this.deviceName;
  }

  public void setDeviceName(String deviceName) {
    this.deviceName = deviceName;
  }

  public long getLastModified() {
    return this.lastModified;
  }

  public void setLastModified(long lastModified) {
    this.lastModified = lastModified;
  }

  public Map<Integer, Integer> getFullAccess() {
    return this.fullAccess;
  }

  public void setFullAccess(Map<Integer, Integer> fullAccess) {
    this.fullAccess = fullAccess;
  }

  public Map<Integer, Integer> getVideoAccess() {
    return this.videoAccess;
  }

  public void setVideoAccess(Map<Integer, Integer> videoAccess) {
    this.videoAccess = videoAccess;
  }

  public Map<Integer, Integer> getPrivateMode() {
    return this.privateMode;
  }

  public void setPrivateMode(Map<Integer, Integer> privateMode) {
    this.privateMode = privateMode;
  }
}

