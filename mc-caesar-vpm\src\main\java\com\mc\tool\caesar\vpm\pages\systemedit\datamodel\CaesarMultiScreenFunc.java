package com.mc.tool.caesar.vpm.pages.systemedit.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.vpm.pages.operation.multiscreen.datamodel.CaesarMultiScreenData;
import com.mc.tool.framework.systemedit.datamodel.MultiScreenFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import javafx.beans.property.ObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarMultiScreenFunc extends MultiScreenFunc<CaesarMultiScreenData> {
  public static final int DEFAULT_COLUMN_SIZE = 4;
  public static final String NAME = "CaesarMultiScreenFunc";

  @Expose private CaesarMultiScreenData multiScreenData = new CaesarMultiScreenData();

  @Expose
  private ObservableList<CaesarMultiScreenData> scenarios = FXCollections.observableArrayList();

  @Override
  public CaesarMultiScreenData getFuncData() {
    return multiScreenData;
  }

  @Override
  public Object getExtraData() {
    return NAME;
  }

  @Override
  public void init() {
    super.init();

    getObservableChildren()
        .addListener(
            (ListChangeListener<VisualEditNode>)
                change -> {
                  while (change.next()) {
                    if (change.wasRemoved()) {
                      for (VisualEditNode item : change.getRemoved()) {
                        removeItem(item);
                      }
                    }
                    if (change.wasAdded()) {
                      for (VisualEditNode item : change.getAddedSubList()) {
                        addItem(item);
                      }
                    }
                  }
                });
  }

  /**
   * 设置跨屏布局.
   *
   * @param row 行数
   * @param column 列数
   */
  public void setLayout(int row, int column) {
    multiScreenData.resetCapacity(row * column);
    multiScreenData.getRows().set(row);
    multiScreenData.getColumns().set(column);
  }

  protected void addItem(VisualEditNode item) {

    if (!(item instanceof VisualEditTerminal)) {
      log.warn("null item!");
      return;
    }

    VisualEditTerminal terminal = (VisualEditTerminal) item;

    if (multiScreenData.isFull()) {
      int column = multiScreenData.getColumns().get();
      if (column <= 0) {
        column = DEFAULT_COLUMN_SIZE;
      }
      int row = multiScreenData.getRows().get() + 1;
      setLayout(row, column);
    }
    for (ObjectProperty<VisualEditTerminal> property : multiScreenData.getTargets()) {
      if (property.get() == null) {
        property.set(terminal);
        if (!multiScreenData.getConnections().containsKey(terminal)) {
          multiScreenData.getConnections().put(terminal, null);
        }
        break;
      }
    }
  }

  protected void removeItem(VisualEditNode item) {
    if (item == null) {
      log.warn("null item!");
      return;
    }

    for (ObjectProperty<VisualEditTerminal> property : multiScreenData.getTargets()) {
      if (property.get() == item) {
        property.set(null);
      }
    }
    multiScreenData.getConnections().remove(item);
  }

  @Override
  public void moveScreen(int fromRow, int fromColumn, int toRow, int toColumn) {
    if (fromRow == toRow && fromColumn == toColumn) {
      return;
    }
    if (fromRow < 0
        || fromRow >= getMaxRow()
        || fromColumn < 0
        || fromColumn >= getMaxColumn()
        || toRow < 0
        || toRow >= getMaxRow()
        || toColumn < 0
        || toColumn >= getMaxColumn()) {
      return;
    }

    int fromIndex = getIndex(fromRow, fromColumn);
    int toIndex = getIndex(toRow, toColumn);
    ObjectProperty<VisualEditTerminal> from = multiScreenData.getTarget(fromIndex);
    VisualEditTerminal target = from.get();
    from.set(null);
    multiScreenData.insertTarget(target, toIndex);
  }

  @Override
  public boolean isScreenMovable() {
    return true;
  }

  @Override
  public Pair<Integer, Integer> posOfScreen(VisualEditTerminal terminal) {
    int index = multiScreenData.indexOfTarget(terminal);
    if (index < 0) {
      return new Pair<>(-1, -1);
    } else {
      return new Pair<>(index / getMaxColumn(), index % getMaxColumn());
    }
  }

  @Override
  public boolean addScenario(CaesarMultiScreenData object) {
    if (object == null || scenarios.contains(object)) {
      return false;
    }
    scenarios.add(object);
    return true;
  }

  @Override
  public boolean removeScenario(CaesarMultiScreenData object) {
    if (scenarios.contains(object)) {
      scenarios.remove(object);
      return true;
    } else {
      return false;
    }
  }

  @Override
  public ObservableList<CaesarMultiScreenData> getScenarios() {
    return scenarios;
  }

  @Override
  public int getMaxRow() {
    return multiScreenData.getRows().get();
  }

  @Override
  public int getMaxColumn() {
    return multiScreenData.getColumns().get();
  }

  protected int getIndex(int row, int column) {
    return row * getMaxColumn() + column;
  }
}
