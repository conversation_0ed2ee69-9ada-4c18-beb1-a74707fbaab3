<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ListView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<fx:root type="javafx.scene.layout.VBox" xmlns="http://javafx.com/javafx/8"
  xmlns:fx="http://javafx.com/fxml/1" stylesheets="@videowall.css">
  <HBox VBox.Vgrow="ALWAYS">
    <HBox HBox.Hgrow="ALWAYS" fx:id="graphBox"/>
  </HBox>
  <HBox prefHeight="36" minHeight="36" alignment="CENTER" id="graph-tool-bar">
    <Label id="preview-btn" prefWidth="18" prefHeight="18" onMouseClicked="#onPreview"/>
    <Label id="zoomin-btn" fx:id="zoominBtn" prefWidth="18" prefHeight="18"
      onMouseClicked="#onZoomin"/>
    <Label id="zoomout-btn" fx:id="zoomoutBtn" prefWidth="18" prefHeight="18"
      onMouseClicked="#onZoomout"/>
    <Label id="restore-btn" prefWidth="18" prefHeight="18" onMouseClicked="#onRestore"/>
  </HBox>
  <Region minHeight="1" styleClass="seperator"/>
  <HBox prefHeight="113" minHeight="113" id="scenario-list-container">
    <StackPane minWidth="44" prefWidth="44">
      <Button onAction="#onScenarioToLeft" styleClass="image-button" id="scenario-left-btn"/>
    </StackPane>
    <ListView fx:id="scenarioList" id="scenario-list" HBox.hgrow="ALWAYS" orientation="HORIZONTAL"/>
    <StackPane minWidth="44" prefWidth="44">
      <Button onAction="#onScenarioToRight" styleClass="image-button" id="scenario-right-btn"/>
    </StackPane>
  </HBox>
</fx:root>