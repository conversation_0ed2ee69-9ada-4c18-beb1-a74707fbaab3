package com.mc.tool.caesar.vpm.util.macrokey.datamodel;

import java.util.Objects;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import lombok.NoArgsConstructor;

/**
 * .
 */
@NoArgsConstructor
public class MacroKeyNameItem {

  private final StringProperty hotkey = new SimpleStringProperty();
  private final StringProperty name = new SimpleStringProperty();

  /** Constructor. */
  public MacroKeyNameItem(String hotkey, String name) {
    setHotkey(hotkey);
    setName(name);
  }

  /** 判等. */
  public boolean isEquals(MacroKeyNameItem item) {
    if (this == item) {
      return true;
    }
    if (item == null) {
      return false;
    }
    return Objects.equals(getHotkey(), item.getHotkey())
        && Objects.equals(getName(), item.getName());
  }

  public String getHotkey() {
    return hotkey.get();
  }

  public StringProperty hotkeyProperty() {
    return hotkey;
  }

  public void setHotkey(String hotkey) {
    this.hotkey.set(hotkey);
  }

  public Fkey getFkey() {
    return Fkey.valueOf(hotkey.get());
  }

  public String getName() {
    return name.get();
  }

  public StringProperty nameProperty() {
    return name;
  }

  public void setName(String name) {
    this.name.set(name);
  }
}
