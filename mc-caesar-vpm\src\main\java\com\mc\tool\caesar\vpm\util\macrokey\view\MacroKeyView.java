package com.mc.tool.caesar.vpm.util.macrokey.view;

import com.google.common.collect.Lists;
import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarConstants.FunctionKey.Cmd.Command;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData.MacroType;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.api.interfaces.NamepropertyAble;
import com.mc.tool.caesar.api.interfaces.Oidable;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.hotkeyconfiguration.CopyDialog;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.CaesarNamePredicate;
import com.mc.tool.caesar.vpm.util.control.SearchField;
import com.mc.tool.caesar.vpm.util.macrokey.datamodel.Fkey;
import com.mc.tool.caesar.vpm.util.macrokey.datamodel.MacroDataObjectParam;
import com.mc.tool.caesar.vpm.util.macrokey.datamodel.MacroDualHdmiParam;
import com.mc.tool.caesar.vpm.util.macrokey.datamodel.MacroKeyGroup;
import com.mc.tool.caesar.vpm.util.macrokey.datamodel.MacroKeyItem;
import com.mc.tool.caesar.vpm.util.macrokey.datamodel.MacroKeyNameItem;
import com.mc.tool.caesar.vpm.util.macrokey.datamodel.MacroMultiviewParam;
import com.mc.tool.caesar.vpm.util.macrokey.datamodel.MacroParam;
import com.mc.tool.caesar.vpm.util.macrokey.datamodel.MacroScenarioDataParam;
import com.mc.tool.caesar.vpm.util.macrokey.datamodel.MacroScenarioWindowParam;
import com.mc.tool.caesar.vpm.util.macrokey.datamodel.Param1List;
import com.mc.tool.caesar.vpm.util.macrokey.datamodel.Param2List;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.UndecoratedDialog;
import java.beans.PropertyChangeListener;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.concurrent.ExecutionException;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javafx.beans.binding.Bindings;
import javafx.beans.binding.BooleanBinding;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.ReadOnlyIntegerWrapper;
import javafx.beans.property.ReadOnlyObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredList;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.control.ListView;
import javafx.scene.control.SelectionMode;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableRow;
import javafx.scene.control.TableView;
import javafx.scene.control.cell.ComboBoxTableCell;
import javafx.scene.control.cell.TextFieldTableCell;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import javafx.stage.Modality;
import javafx.util.Callback;
import javafx.util.Pair;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public abstract class MacroKeyView<T extends DataObject & NamepropertyAble> extends VBox
    implements Initializable, ViewControllable {

  static Command[] generalCmd = {Command.NoFunction, Command.Connect, Command.ConnectVideo,
      Command.Disconnect, Command.Push, Command.PushVideo, Command.Get, Command.GetVideo,
      Command.ActiveVideoWallScenario, Command.PushVideoWallScenarioWindow};

  static Command[] multiViewConCmd = Command.values();
  protected CaesarDeviceController deviceController;
  @FXML
  private Label sourceListTitleLabel;
  @FXML
  private Label accessBlockTitleLabel;
  @FXML
  private VBox keyListViewVbox;
  @FXML
  private ListView<Fkey> keyListView;
  @FXML
  private TableView<MacroKeyItem> macroTable;
  @FXML
  private TableColumn<MacroKeyItem, Integer> indexColumn;
  @FXML
  private TableColumn<MacroKeyItem, CaesarConstants.FunctionKey.Cmd.Command> cmdColumn;
  @FXML
  private TableColumn<MacroKeyItem, MacroParam> param1Column;
  @FXML
  private TableColumn<MacroKeyItem, MacroParam> param2Column;

  @FXML
  protected TableColumn<T, Number> sourceIdColumn;
  @FXML
  protected TableColumn<T, String> sourceNameColumn;
  @FXML
  @Getter
  protected TableView<T> sourceList;

  @FXML
  protected SearchField<T> searchField;

  @FXML
  protected Button copyToRxBtn;
  @FXML
  private Button copyMacroListBtn;
  @FXML
  private Button pasteMacroListBtn;
  @FXML
  private Button deleteMacroListBtn;

  @FXML
  private Button applyBtn;
  @FXML
  private Button cancelBtn;

  @FXML
  private TableView<MacroKeyNameItem> keyTable;
  @FXML
  private TableColumn<MacroKeyNameItem, String> keyColumn;
  @FXML
  private TableColumn<MacroKeyNameItem, String> nameColumn;

  protected ObjectProperty<DataObject> currentObject = new SimpleObjectProperty<>();

  protected ObservableList<FunctionKeyData> deviceData = FXCollections.observableArrayList();

  protected ObjectProperty<MacroKeyItem[]> copiedMacroList = new SimpleObjectProperty<>();

  private MacroKeyGroup macroKeyGroup =
      new MacroKeyGroup(FXCollections.synchronizedObservableList(deviceData));

  protected ObservableList<T> sourceRawList = FXCollections.observableArrayList();

  protected FilteredList<T> sourceFilteredList = new FilteredList<>(sourceRawList);

  protected StringProperty sourceHeader = new SimpleStringProperty();
  protected StringProperty targetHeader = new SimpleStringProperty();

  protected BooleanProperty updatingProperty = new SimpleBooleanProperty(false);

  protected WeakAdapter weakAdapter = new WeakAdapter();

  protected int fkeySize;
  protected int macroSize;

  protected ObservableList<MacroKeyNameItem> keyNameList = FXCollections.observableArrayList();
  protected List<MacroKeyNameItem> keyNameTempList = new ArrayList<>();
  protected BooleanProperty hasFkeyNameProperty = new SimpleBooleanProperty(false);
  protected BooleanProperty fkeyNameChangeProperty = new SimpleBooleanProperty(false);
  protected boolean reloading = false;
  protected ObjectProperty<String> copiedFkeyName = new SimpleObjectProperty<>();
  protected ObservableList<Command> cmdList = FXCollections.observableArrayList();

  /**
   * Constructor.
   */
  public MacroKeyView() {
    URL location =
        Thread.currentThread()
            .getContextClassLoader()
            .getResource("com/mc/tool/caesar/vpm/util/macrokey/macrokey_view.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    loader.setRoot(this);
    loader.setController(this);
    loader.setResources(CaesarI18nCommonResource.getResourceBundle());
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load macrokey_view.fxml", exception);
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    // 版本是否支持宏名称
    keyListViewVbox.managedProperty().bind(keyListViewVbox.visibleProperty());
    keyListViewVbox.visibleProperty().bind(hasFkeyNameProperty.not());
    keyTable.managedProperty().bind(keyTable.visibleProperty());
    keyTable.visibleProperty().bind(hasFkeyNameProperty);

    // 索引列
    indexColumn.setCellFactory(new IndexCellFactory());
    // 命令列
    cmdColumn.setCellValueFactory(cell -> cell.getValue().getCmd());
    currentObject.addListener((observable, oldValue, newValue) -> {
      if (newValue instanceof ConsoleData && ((ConsoleData) newValue).isMultiview()) {
        cmdList.setAll(multiViewConCmd);
      } else {
        cmdList.setAll(generalCmd);
      }
    });
    cmdColumn.setCellFactory(col -> new ComboBoxTableCell<>(new CommandStringConverter(), cmdList));
    // 参数1列
    param1Column.setCellValueFactory(cell -> cell.getValue().getParam1());
    param1Column.setCellFactory(
        (column) -> {
          Param1List param1List = new Param1List(currentObject, deviceController.getDataModel());
          MacroParamTableCell<MacroKeyItem> cell =
              new MacroParamTableCell<>(new MacroParamStringConverter(), param1List);
          param1List.setItemProperty(getItemProperty(cell.tableRowProperty()));

          // 当列表为空时不需要弹出combobox
          cell.editableProperty()
              .bind(
                  Bindings.createBooleanBinding(() -> !cell.getItems().isEmpty(), cell.getItems()));
          // 当当前项与列表冲突时，重新选择
          cell.getItems().addListener(weakAdapter.wrap(new Param1CellItemListChangeListener(cell)));
          return cell;
        });
    // 参数2列
    param2Column.setCellValueFactory(cell -> cell.getValue().getParam2());
    param2Column.setCellFactory(column -> {
      Param2List param2List =
          new Param2List(currentObject, deviceController.getDataModel());
      MacroParamTableCell<MacroKeyItem> cell =
          new MacroParamTableCell<>(new MacroParamStringConverter(), param2List);

      param2List.setItemProperty(getItemProperty(cell.tableRowProperty()));

      // 当列表为空时不需要弹出combobox
      cell.editableProperty().bind(
          Bindings.createBooleanBinding(() -> !cell.getItems().isEmpty(), cell.getItems()));
      // 当当前项与列表冲突时，重新选择
      cell.getItems().addListener(weakAdapter.wrap(new Param2CellItemListChangeListener(cell)));
      return cell;
    });
    // 按键列表
    keyListView.setCellFactory(list -> new FkeyListCell(macroKeyGroup.getNonEmptyList()));

    keyListView.getSelectionModel().setSelectionMode(SelectionMode.SINGLE);
    keyListView.getSelectionModel().selectedItemProperty().addListener(
        weakAdapter.wrap((obs, oldVal, newVal) ->
            macroTable.getItems().setAll(
                macroKeyGroup.getMacroKeys(keyListView.getSelectionModel().getSelectedItem()))));

    // 带名字的按键列表
    keyTable.setItems(keyNameList);
    // keyColumn.set
    keyColumn.setCellValueFactory(item -> item.getValue().hotkeyProperty());
    nameColumn.setCellValueFactory(item -> item.getValue().nameProperty());
    TableCell<MacroKeyNameItem, String> cell = new TableCell<>();
    cell.getTooltip();
    nameColumn.setCellFactory(TextFieldTableCell.forTableColumn());
    keyTable.getSelectionModel().setSelectionMode(SelectionMode.SINGLE);
    keyTable.getSelectionModel().selectedItemProperty().addListener(
        weakAdapter.wrap((obs, oldVal, newVal) ->
            macroTable.getItems().setAll(
                macroKeyGroup.getMacroKeys(
                    keyTable.getSelectionModel().getSelectedItem().getFkey()))));
    keyTable.setRowFactory(tv -> {
      TableRow<MacroKeyNameItem> row = new TableRow<>();
      row.setOnMouseClicked(event -> {
        int doubleClick = 2;
        if (event.getClickCount() == doubleClick && !row.isEmpty()
            && currentObject.isNotNull().get()) {
          MacroKeyNameItem item = row.getItem();
          Optional<String> result =
              ViewUtility.getNameFromDialog(
                  keyTable.getScene().getWindow(),
                  item.getName(),
                  null,
                  new CaesarNamePredicate());
          result.ifPresent(res ->
              setKeyName(keyNameList, Fkey.valueOf(item.getHotkey()).ordinal(), res));
        }
      });
      return row;
    });

    BooleanBinding nullCurrentObjectBinding = currentObject.isNull();
    // 按键
    copyToRxBtn.disableProperty().bind(nullCurrentObjectBinding);
    copyMacroListBtn.disableProperty().bind(nullCurrentObjectBinding);
    pasteMacroListBtn.disableProperty()
        .bind(nullCurrentObjectBinding.or(copiedMacroList.isNull()).or(copiedFkeyName.isNull()));
    deleteMacroListBtn.disableProperty().bind(nullCurrentObjectBinding);

    applyBtn.disableProperty().bind(
        macroKeyGroup.changeProperty()
            .not()
            .and(fkeyNameChangeProperty.not())
            .or(nullCurrentObjectBinding)
            .or(updatingProperty));
    cancelBtn.disableProperty().bind(
        macroKeyGroup.changeProperty()
            .not()
            .and(fkeyNameChangeProperty.not())
            .or(nullCurrentObjectBinding)
            .or(updatingProperty));

    // 其他
    sourceListTitleLabel.setText(getSourceListTitle());
    accessBlockTitleLabel.setText(getAccessBlockListTitle());

    macroTable.editableProperty().bind(nullCurrentObjectBinding.not());
    macroTable.addEventFilter(KeyEvent.KEY_PRESSED, event -> {
      if (event.getCode() == KeyCode.DELETE) {
        MacroKeyItem item = macroTable.getSelectionModel().getSelectedItem();
        if (item != null) {
          item.delete();
        }
      }
    });

    // source list

    sourceIdColumn.setCellValueFactory(feat -> {
      if (feat.getValue() instanceof ConsoleData) {
        return ((ConsoleData) feat.getValue()).getIdProperty();
      } else {
        return new ReadOnlyIntegerWrapper(feat.getValue().getId());
      }
    });
    sourceNameColumn.setCellValueFactory(feat -> feat.getValue().getNameProperty());
    sourceList.getSelectionModel().setSelectionMode(SelectionMode.SINGLE);

    sourceList.getSelectionModel().selectedItemProperty().addListener(
        weakAdapter.wrap((obs, oldVal, newVal) -> {
          if (!cancelBtn.isDisable()) {
            macroKeyGroup.reload();
          }
          T data = sourceList.getSelectionModel().getSelectedItem();
          currentObject.set(data);
          updateDeviceDataList();
        }));

    sourceList.setItems(sourceFilteredList);

    searchField.filterPredicateProperty().addListener(weakAdapter.wrap((obs, oldVal, newVal) ->
        sourceFilteredList.setPredicate(
            searchField.getFilterPredicate().and(getSourcePredicate()))));
    sourceFilteredList.setPredicate(getSourcePredicate());
  }

  protected ObjectProperty<MacroKeyItem> getItemProperty(
      ReadOnlyObjectProperty<TableRow> tableRow) {
    ObjectProperty<MacroKeyItem> property = new SimpleObjectProperty<>();
    if (tableRow.get() != null) {
      property.bind(tableRow.get().itemProperty());
    }
    tableRow.addListener(weakAdapter.wrap(new TableRowChangeListener(property)));
    return property;
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
      hasFkeyNameProperty.set(
          this.deviceController
              .getDataModel()
              .getConfigMetaData()
              .getUtilVersion()
              .hasMacroKeyName());
      fkeySize =
          this.deviceController.getDataModel().getConfigMetaData().getUtilVersion().getFkeySize();
      Fkey[] fkeyArray = Arrays.copyOf(Fkey.values(), fkeySize);
      keyListView.getItems().addAll(fkeyArray);
      for (Fkey fkey : fkeyArray) {
        keyNameList.add(new MacroKeyNameItem("F" + (fkey.ordinal() + 1), ""));
        keyNameTempList.add(new MacroKeyNameItem("F" + (fkey.ordinal() + 1), ""));
      }
      initFkeyNameListener();

      macroSize =
          this.deviceController.getDataModel().getConfigMetaData().getUtilVersion().getMacroSize();
      macroKeyGroup.init(this.deviceController);
      macroKeyGroup.reload();

      this.deviceController
          .getDataModel()
          .addPropertyChangeListener(
              new String[] {
                  CpuData.PROPERTY_ID,
                  ConsoleData.PROPERTY_ID,
                  CpuData.PROPERTY_NAME,
                  ConsoleData.PROPERTY_NAME
              },
              weakAdapter.wrap(
                  (PropertyChangeListener)
                      evt -> PlatformUtility.runInFxThread(() -> macroTable.refresh())));
    }
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceController;
  }

  /**
   * 选择源.
   *
   * @param item 源
   */
  public void selectSourceItem(T item) {
    searchField.setSearchText("");
    getSourceList().requestFocus();
    getSourceList().scrollTo(item);
    getSourceList().getSelectionModel().select(item);
  }

  @FXML
  protected void onApply() {
    if (currentObject.get() == null) {
      return;
    }
    updatingProperty.set(true);
    deviceController.submitAsync(() -> {
      try {
        List<FunctionKeyData> changedFunctionKeys =
            applyForMacroKeyGroup(macroKeyGroup, currentObject.get(), false);
        deviceController.getDataModel().sendFunctionKeyData(changedFunctionKeys);
      } catch (DeviceConnectionException | BusyException exception) {
        log.warn("Fail to send functionkey data!", exception);
      }
    }).whenComplete((result, throwable) -> {
      if (throwable != null) {
        log.warn("Fail to send functionkey data!", throwable);
      }
      PlatformUtility.runInFxThread(() -> {
        updatingProperty.set(false);
        updateDeviceDataList();
        macroKeyGroup.reload();
      });
    });
  }

  protected List<FunctionKeyData> applyForMacroKeyGroup(
      MacroKeyGroup macroKeyGroup, DataObject macroSource, boolean copyToOther) {
    List<Integer> fkeyNameChangeEx;
    if (copyToOther) {
      fkeyNameChangeEx =
          Arrays.stream(Fkey.values()).map(Enum::ordinal).collect(Collectors.toList());
    } else {
      fkeyNameChangeEx = getFkeyNameChangedItems();
    }
    Map<Pair<Integer, Integer>, MacroKeyItem> items =
        macroKeyGroup.getChangedItems(fkeyNameChangeEx);
    return applyChangeToFunctionKeyData(macroSource, items);
  }

  private List<FunctionKeyData> applyChangeToFunctionKeyData(
      DataObject macroSource,
      Map<Pair<Integer, Integer>, MacroKeyItem> items) {
    List<FunctionKeyData> changedFunctionKeys = new ArrayList<>();
    for (Map.Entry<Pair<Integer, Integer>, MacroKeyItem> entry : items.entrySet()) {
      MacroKeyItem item = entry.getValue();
      int oid = item.getFunctionKeyId();
      if (oid < 0) {
        log.warn("Error oid!");
        continue;
      }
      FunctionKeyData functionKeyData =
          deviceController.getDataModel().getConfigDataManager().getFunctionKeyData(oid);
      if (functionKeyData == null) {
        log.warn("Fail to find function key data!");
        continue;
      }
      // 写数据到function key data
      if (item.getCmd().get() != null && item.getCmd().get() != Command.NoFunction) {
        // if (!copyToOther) {}
        // 宏名称写入function key data
        if (hasFkeyNameProperty.get()
            && keyNameList.size() > entry.getKey().getKey()
            && keyNameList.get(entry.getKey().getKey()).getFkey().ordinal()
            == entry.getKey().getKey()) {
          functionKeyData.setMacroName(keyNameList.get(entry.getKey().getKey()).getName());
        }
        functionKeyData.setStatusActive(true);
        functionKeyData.setHostSubscript(((Oidable) macroSource).getOid() + 1);
        functionKeyData.setHotkey(entry.getKey().getKey());
        functionKeyData.setKeyline(entry.getKey().getValue());
        functionKeyData.setMacroCmd(item.getCmd().get().getId());
        if (macroSource instanceof ConsoleData) {
          functionKeyData.setMacroType(MacroType.CON);
        } else {
          functionKeyData.setMacroType(MacroType.USER);
        }

        MacroParam param1 = item.getParam1().get();
        MacroParam param2 = item.getParam2().get();
        if (param1 != null) {
          if (param1.hasOid()) {
            functionKeyData.setIntParam(0, param1.getOid() + 1);
          } else if (item.getCmd().get() == Command.ActiveVideoWallScenario
              && param1 instanceof MacroScenarioDataParam) {
            functionKeyData.setIntParam(2, ((MacroScenarioDataParam) param1).getIndex() + 1);
          } else if (item.getCmd().get() == Command.PushVideoWallScenarioWindow
              && param1 instanceof MacroScenarioWindowParam) {
            functionKeyData.setIntParam(2, ((MacroScenarioWindowParam) param1).getIndex() + 1);
            functionKeyData.setStringParam(0, ((MacroScenarioWindowParam) param1).getWindowName());
          } else if (item.getCmd().get() == Command.SWITCH_MULTIVIEW_LAYOUT) {
            functionKeyData.setIntParam(2, ((MacroMultiviewParam) param1).getIndex() + 1);
          } else if (item.getCmd().get() == Command.SWITCH_MULTIVIEW_SIGNAL) {
            functionKeyData.setIntParam(2, ((MacroMultiviewParam) param1).getIndex() + 1);
          }
        }
        if (param2 != null && param2.hasOid()) {
          if (param2 instanceof MacroDataObjectParam) {
            functionKeyData.setIntParam(1, param2.getOid() + 1);
          } else if (param2 instanceof MacroDualHdmiParam) {
            // 最高位: 0：HDMI1（默认），1：HDMI2
            if (((MacroDualHdmiParam) param2).getIndex() == 1) {
              functionKeyData.setIntParam(1, param2.getOid() + 1);
            } else if (((MacroDualHdmiParam) param2).getIndex() == 2) {
              functionKeyData.setIntParam(1, param2.getOid() + 1 + 0x8000);
            }
          }
        }

      } else {
        functionKeyData.reset();
      }
      changedFunctionKeys.add(functionKeyData);
    }
    return changedFunctionKeys;
  }

  @FXML
  protected void onCancel() {
    updateFkeyNameList();
    macroKeyGroup.reload();
  }

  @FXML
  protected void onCopyMacroList() {
    if (macroTable.getItems().size() == macroSize) {
      MacroKeyItem[] list = new MacroKeyItem[macroSize];
      for (int i = 0; i < macroSize; i++) {
        list[i] = new MacroKeyItem();
        list[i].getCmd().set(macroTable.getItems().get(i).getCmd().get());
        list[i].getParam1().set(macroTable.getItems().get(i).getParam1().get());
        list[i].getParam2().set(macroTable.getItems().get(i).getParam2().get());
      }
      copiedMacroList.set(list);
      MacroKeyNameItem selectedItem = keyTable.getSelectionModel().getSelectedItem();
      if (selectedItem != null) {
        copiedFkeyName.set(selectedItem.getName());
      }
    }
  }

  @FXML
  protected void onPasteMacroList() {
    if (macroTable.getItems().size() == macroSize && copiedMacroList.get() != null) {
      MacroKeyItem[] list = copiedMacroList.get();
      for (int i = 0; i < macroSize; i++) {
        macroTable.getItems().get(i).getCmd().set(list[i].getCmd().get());
        macroTable.getItems().get(i).getParam1().set(list[i].getParam1().get());
        macroTable.getItems().get(i).getParam2().set(list[i].getParam2().get());
      }
      if (copiedFkeyName.get() != null) {
        MacroKeyNameItem selectedItem = keyTable.getSelectionModel().getSelectedItem();
        if (selectedItem != null) {
          selectedItem.setName(copiedFkeyName.getName());
        }
      }
    }
  }

  @FXML
  protected void onDeleteMacroList() {
    UndecoratedDialog<ButtonType> deleteDialog = new UndecoratedDialog<>();
    if (macroTable != null && macroTable.getScene() != null) {
      deleteDialog.initOwner(macroTable.getScene().getWindow());
    }
    deleteDialog.setHeaderText(null);
    deleteDialog.setTitle(
        CaesarI18nCommonResource.getString("macro_key.delete_macro_list.delete_confirm_title"));
    deleteDialog.setContentText(
        CaesarI18nCommonResource.getString("macro_key.delete_macro_list.delete_confirm_content"));
    deleteDialog.getDialogPane().getButtonTypes().add(ButtonType.OK);
    deleteDialog.getDialogPane().getButtonTypes().add(ButtonType.FINISH);
    deleteDialog.getDialogPane().getButtonTypes().add(ButtonType.CLOSE);
    deleteDialog.getDialogPane().lookupButton(ButtonType.CLOSE).setVisible(false);
    deleteDialog.getDialogPane().lookupButton(ButtonType.CLOSE).setManaged(false);
    Button yesButton = (Button) deleteDialog.getDialogPane().lookupButton(ButtonType.OK);
    yesButton.setText(
        CaesarI18nCommonResource.getString("macro_key.delete_macro_list.delete_current"));

    Button noButton = (Button) deleteDialog.getDialogPane().lookupButton(ButtonType.FINISH);
    noButton.setText(CaesarI18nCommonResource.getString("macro_key.delete_macro_list.delete_all"));
    Optional<ButtonType> result = deleteDialog.showAndWait();
    if (result.isPresent()) {
      if (result.get() == ButtonType.OK) {
        if (hasFkeyNameProperty.get()) {
          for (MacroKeyItem item :
              macroKeyGroup.getMacroKeys(
                  keyTable.getSelectionModel().getSelectedItem().getFkey())) {
            item.delete();
          }
        } else {
          for (MacroKeyItem item :
              macroKeyGroup.getMacroKeys(keyListView.getSelectionModel().getSelectedItem())) {
            item.delete();
          }
        }
      } else if (result.get() == ButtonType.FINISH) {
        macroKeyGroup.delete();
      }
    }
  }

  @FXML
  protected void onCopyToRx() {
    CopyDialog<T> dialog = new CopyDialog<>();
    dialog.setTitle(CaesarI18nCommonResource.getString("copy_macro_dialog.title"));
    ((Label) dialog.getCopySelectionView().getSourceHeader()).textProperty().bind(sourceHeader);
    ((Label) dialog.getCopySelectionView().getTargetHeader()).textProperty().bind(targetHeader);
    dialog.getCopySelectionView().setCellFactory((view) -> new SelectionViewCell());

    dialog.getCopySelectionView().getSourceItems().setAll(getCopyableMacroItemSources());
    if (this.getScene() != null) {
      dialog.initOwner(this.getScene().getWindow());
    }
    dialog.initModality(Modality.APPLICATION_MODAL);
    Optional<Collection<T>> result = dialog.showAndWait();
    if (result.isPresent()) {
      Collection<T> copyItems = result.get();
      ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
      app.getTaskManager().addForegroundTask(new Task<Void>() {
        @Override
        protected Void call() {
          updatingProperty.set(true);
          try {
            deviceController.submit(() -> {
              try {
                List<FunctionKeyData> changedFunctionKeyDatas =
                    new ArrayList<>(
                        applyForMacroKeyGroup(macroKeyGroup, currentObject.get(), false));

                for (T item : copyItems) {
                  Collection<FunctionKeyData> functionKeyDatas =
                      getFunctionKeyDatasForSource(item);
                  MacroKeyGroup tempGroup =
                      new MacroKeyGroup(FXCollections.observableArrayList(functionKeyDatas));
                  tempGroup.init(deviceController);
                  tempGroup.reload();

                  macroKeyGroup.copyTo(tempGroup);
                  changedFunctionKeyDatas.addAll(applyForMacroKeyGroup(tempGroup, item, true));
                }
                deviceController.getDataModel().sendFunctionKeyData(changedFunctionKeyDatas);
              } catch (DeviceConnectionException | BusyException ex) {
                log.warn("Fail to send function key data!", ex);
              }
            }).get();
          } catch (InterruptedException | ExecutionException ex) {
            log.warn("Fail to copy macro key!", ex);
          }
          PlatformUtility.runInFxThread(() -> {
            updatingProperty.set(false);
            updateDeviceDataList();
          });
          return null;
        }
      });
    }
  }

  /**
   * 更新设备数据列表.
   */
  protected void updateDeviceDataList() {
    if (updatingProperty.get()) {
      return;
    }
    T data = sourceList.getSelectionModel().getSelectedItem();
    deviceData.clear();
    if (data != null) {
      deviceData.setAll(getFunctionKeyDatasForSource(data));
    }
    updateFkeyNameList();
  }

  /**
   * 更新Fkey名称列表.
   */
  protected void updateFkeyNameList() {
    for (MacroKeyNameItem item : keyNameList) {
      item.setName("");
    }
    for (FunctionKeyData deviceDatum : deviceData) {
      setKeyName(keyNameList, deviceDatum.getHotkey(), deviceDatum.getMacroName());
    }
    fkeyNameChangeProperty.set(false);
  }

  /**
   * 设置Fkey名称.
   */
  protected void setKeyName(ObservableList<MacroKeyNameItem> nameList, int index, String name) {
    if (nameList.size() > index) {
      nameList.get(index).setName(name);
    }
  }

  /**
   * 设置Fkey名称.
   */
  protected void setKeyName(List<MacroKeyNameItem> nameList, int index, String name) {
    if (nameList.size() > index) {
      nameList.get(index).setName(name);
    }
  }

  /**
   * 初始化Fkey名称监听器.
   */
  protected void initFkeyNameListener() {
    ChangeListener<String> cmdChangeListener = (obs, oldVal, newVal) -> checkFkeyNameChange();
    for (MacroKeyNameItem item : keyNameList) {
      item.nameProperty().addListener(weakAdapter.wrap(cmdChangeListener));
    }
  }

  /**
   * 检查Fkey名称是否改变.
   */
  protected void checkFkeyNameChange() {
    fkeyNameChangeProperty.set(false);
    if (!getFkeyNameChangedItems().isEmpty()) {
      fkeyNameChangeProperty.set(true);
    }
  }

  /**
   * 获取Fkey名称改变的项.
   */
  protected List<Integer> getFkeyNameChangedItems() {
    List<Integer> fkeyNameChangedItems = Lists.newArrayList();
    if (reloading) {
      return fkeyNameChangedItems;
    }
    reloading = true;
    try {
      reloadImpl();
      for (int i = 0; i < fkeySize; i++) {
        if (!keyNameList.get(i).isEquals(keyNameTempList.get(i))) {
          fkeyNameChangedItems.add(keyNameList.get(i).getFkey().ordinal());
        }
      }
    } finally {
      reloading = false;
    }
    return fkeyNameChangedItems;
  }

  protected void reloadImpl() {
    for (MacroKeyNameItem item : keyNameTempList) {
      item.setName("");
    }
    for (FunctionKeyData deviceDatum : deviceData) {
      setKeyName(keyNameTempList, deviceDatum.getHotkey(), deviceDatum.getMacroName());
    }
  }

  protected boolean isFkeyNameChange() {
    return fkeyNameChangeProperty.get();
  }

  protected BooleanProperty fkeyNameChangeProperty() {
    return fkeyNameChangeProperty;
  }

  protected Collection<T> getCopyableMacroItemSources() {
    List<T> result = new ArrayList<>(sourceList.getItems());
    result.remove(sourceList.getSelectionModel().getSelectedItem());
    return result;
  }

  protected abstract Collection<FunctionKeyData> getFunctionKeyDatasForSource(T source);

  protected abstract String getSourceListTitle();

  protected abstract String getAccessBlockListTitle();

  protected Predicate<T> getSourcePredicate() {
    return (item) -> true;
  }

  static class IndexCellFactory
      implements Callback<TableColumn<MacroKeyItem, Integer>, TableCell<MacroKeyItem, Integer>> {

    @Override
    public TableCell<MacroKeyItem, Integer> call(TableColumn<MacroKeyItem, Integer> param) {
      TableCell<MacroKeyItem, Integer> cell = new TableCell<>();
      cell.textProperty()
          .bind(
              Bindings.createStringBinding(
                  () -> {
                    if (cell.isEmpty()) {
                      return null;
                    } else {
                      return (cell.getIndex() + 1) + "";
                    }
                  },
                  cell.indexProperty(),
                  cell.emptyProperty()));
      return cell;
    }
  }

  static class TableRowChangeListener implements ChangeListener<TableRow> {
    private final ObjectProperty<MacroKeyItem> property;

    public TableRowChangeListener(ObjectProperty<MacroKeyItem> property) {
      this.property = property;
    }

    @Override
    public void changed(
        ObservableValue<? extends TableRow> observable, TableRow oldValue, TableRow newValue) {
      if (newValue != null) {
        property.bind(newValue.itemProperty());
      } else {
        property.unbind();
        property.set(null);
      }
    }
  }

  static class Param1CellItemListChangeListener implements ListChangeListener<MacroParam> {
    private final ComboBoxTableCell<MacroKeyItem, MacroParam> cell;

    public Param1CellItemListChangeListener(ComboBoxTableCell<MacroKeyItem, MacroParam> tableCell) {
      this.cell = tableCell;
    }

    @Override
    public void onChanged(ListChangeListener.Change<? extends MacroParam> change) {
      Object object = cell.getTableRow().getItem();
      if (object instanceof MacroKeyItem) {
        MacroKeyItem item = (MacroKeyItem) object;
        Collection<MacroParam> items = MacroKeyGroup.getAllEndPointParams(cell.getItems());
        if (item.getParam1().get() == null || !items.contains(item.getParam1().get())) {
          if (items.isEmpty()) {
            item.getParam1().set(null);
          } else {
            MacroParam param = items.iterator().next();
            item.getParam1().set(param);
          }
        }
      }
    }
  }

  static class Param2CellItemListChangeListener implements ListChangeListener<MacroParam> {
    private final ComboBoxTableCell<MacroKeyItem, MacroParam> cell;

    public Param2CellItemListChangeListener(ComboBoxTableCell<MacroKeyItem, MacroParam> tableCell) {
      this.cell = tableCell;
    }

    @Override
    public void onChanged(ListChangeListener.Change<? extends MacroParam> change) {
      Object object = cell.getTableRow().getItem();
      if (object instanceof MacroKeyItem) {
        MacroKeyItem item = (MacroKeyItem) object;
        Collection<MacroParam> items = MacroKeyGroup.getAllEndPointParams(cell.getItems());
        if (item.getParam2().get() == null || !items.contains(item.getParam2().get())) {
          if (items.size() == 0) {
            item.getParam2().set(null);
          } else {
            item.getParam2().set(items.iterator().next());
          }
        }
      }
    }
  }

  static class FkeyListCell extends ListCell<Fkey> {
    private final ObservableList<Fkey> nonEmptyList;

    public FkeyListCell(ObservableList<Fkey> nonEmptyList) {
      this.nonEmptyList = nonEmptyList;

      this.nonEmptyList.addListener((ListChangeListener<Fkey>) change -> updateTextFill());
    }

    @Override
    public void updateItem(Fkey item, boolean empty) {
      super.updateItem(item, empty);

      if (empty) {
        setText(null);
        setGraphic(null);
      } else {
        setText(item == null ? "null" : item.toString());
        setGraphic(null);
        updateTextFill();
      }
    }

    protected void updateTextFill() {
      if (nonEmptyList.contains(getItem())) {
        setTextFill(Color.web("#007db3"));
      } else {
        setTextFill(Color.BLACK);
      }
    }
  }

  private class SelectionViewCell extends ListCell<T> {

    @Override
    protected void updateItem(T item, boolean empty) {
      super.updateItem(item, empty);
      if (empty || item == null) {
        setText("");
      } else {
        setText(item.getName());
      }
    }
  }
}
