package com.mc.tool.caesar.vpm.pages.systemlog;

import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.caesar.vpm.pages.systemlog.Bundle.NbBundle;
import com.mc.tool.framework.interfaces.Page;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.scene.layout.Pane;

/**
 * .
 */
public class CaesarSystemsLogPage implements Page {
  private final SystemsLogPageView view;
  private final BooleanProperty visibility = new SimpleBooleanProperty(true);
  public static final String NAME = "systemlog";

  public CaesarSystemsLogPage(CaesarEntity entity) {
    view = new SystemsLogPageView();
    view.getController().setDeviceController(entity.getController());
  }

  @Override
  public String getTitle() {
    return NbBundle.getMessage("SystemLog.pageTitle.text");
  }

  @Override
  public String getName() {
    return NAME;
  }

  @Override
  public Pane getView() {
    return view;
  }

  @Override
  public String getStyleClass() {
    return null;
  }

  @Override
  public BooleanProperty getVisibleProperty() {
    return visibility;
  }

  @Override
  public void showObject(Object object) {}
}
