package com.mc.tool.caesar.vpm.util.update;

import com.mc.tool.caesar.api.CaesarConstants.Module.Type;
import com.mc.tool.caesar.api.CaesarCpuTypeProperty;
import com.mc.tool.caesar.api.datamodel.VersionDef;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.cpio.CpioArchiveOutputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * .
 */
public class CaesarSampleSwuCreater {

  private static final Logger log = LoggerFactory.getLogger(CaesarSampleSwuCreater.class);
  private static final String PRODUCT_MAT_FORMAT = "product=dkm_mat-hw{0}.{1}\n";
  private static final String PRODUCT_TX_FORMAT = "product=dkm_cpu-hw{0}.{1}\n";
  private static final String PRODUCT_RX_FORMAT = "product=dkm_con-hw{0}.{1}\n";
  private static final String PRODUCT_VP6_FORMAT = "product=EXT_VP6-hw{0}.{1}\n";
  private static final String TYPE_FORMAT = "type={0}\n";
  private static final String SYS_FORMAT = "sys=V%d.%d-%02d%02d%02d\n";
  private static final String APP_FORMAT = "app=V%d.%d-%02d%02d%02d\n";
  private static final String FPGA_FORMAT = "fpga=V%d.%d-%02d%02d%02d\n";
  private static final String IO_FORMAT = "io=V%d.%d-%02d%02d%02d\n";
  private static final String TRUNK_FORMAT = "trunk=V%d.%d-%02d%02d%02d\n";
  private static final String TRUNK_384_FORMAT = "trunk_384-Plugin=V%d.%d-%02d%02d%02d\n";
  private static final String TRUNK_816_FORMAT = "trunk_816-Plugin=V%d.%d-%02d%02d%02d\n";
  private static final String BRANCH_FORMAT = "branch=V%d.%d-%02d%02d%02d\n";
  private static final String PORTS_FORMAT = "ports={0}\n";
  private static final String INTERFACE_FORMAT = "interface={0}\n";

  public static final int MIX_IO_384_MASTER_VERSION = 14;
  public static final int MIX_IO_816_MASTER_VERSION = 15;

  private static final Map<SwuType, VersionTextCreater> versionTextCreaters = new HashMap<>();

  static {
    versionTextCreaters.put(SwuType.MAT, CaesarSampleSwuCreater::createMatVersionText);
    versionTextCreaters.put(SwuType.TX, CaesarSampleSwuCreater::createTxVersionText);
    versionTextCreaters.put(SwuType.RX, CaesarSampleSwuCreater::createRxVersionText);
    versionTextCreaters.put(SwuType.SINGLE_IO, CaesarSampleSwuCreater::createSingleIoVersionText);
    versionTextCreaters.put(SwuType.STACK_IO, CaesarSampleSwuCreater::createStackIoVersionText);
    versionTextCreaters.put(
        SwuType.PLUGIN_384_816_IO, CaesarSampleSwuCreater::create384816IoVersionText);
    versionTextCreaters.put(SwuType.VP6, CaesarSampleSwuCreater::createExtVp6VersionText);
  }

  public static void main(String[] args) {
    createSampleSwuForTest("E://");
  }

  /**
   * 为测试生成升级包.
   *
   * @param dir 升级包存放目录
   */
  private static void createSampleSwuForTest(String dir) {
    VersionDef hardwareVersion = new VersionDef();
    hardwareVersion.setMasterVersion(1);
    hardwareVersion.setSubVersion(2);

    VersionDef version = new VersionDef();
    version.setMasterVersion(10);
    version.setSubVersion(11);
    version.setYear(2020);
    version.setMonth(12);
    version.setDay(13);

    final Type[] types =
        new Type[] {
          Type.SINGLE_CPU,
          Type.SINGLE_CPU,
          Type.STACK_96T_1x36B,
          Type.STACK_96T_12x24B,
          Type.PLUGIN_384,
          Type.PLUGIN_816, Type.PLUGIN_144
        };

    final int[] ports = new int[] {36, 96, 144, 288, 384, 816, 144};

    final int[] hardWardMasterVersion = new int[] {1, 2, 3, 3, 3, 3, 3};

    SwuInfo info = new SwuInfo();
    info.app = version.copy();
    info.sys = version.copy();
    info.fpga = version.copy();
    info.branch = version.copy();
    info.trunk = version.copy();
    info.trunk384 = version.copy();
    info.trunk384.setMasterVersion(MIX_IO_384_MASTER_VERSION);
    info.trunk816 = version.copy();
    info.trunk816.setMasterVersion(MIX_IO_816_MASTER_VERSION);
    info.io = version.copy();
    info.ioType = Type.IO_SPF;
    info.hardware = hardwareVersion.copy();

    String fileNameFormat = "{0}_{1}_hw{2}.{3}_{4}_sample.swu";
    // 主控与IO
    for (int i = 0; i < types.length; i++) {
      info.hardware.setMasterVersion(hardWardMasterVersion[i]);
      info.matTypes = new Type[] {types[i]};
      info.port = ports[i];
      String fileName =
          MessageFormat.format(
              fileNameFormat,
              types[i].getName(),
              ports[i],
              info.hardware.getMasterVersion(),
              hardwareVersion.getSubVersion(),
              "mat");
      createSwu(SwuType.MAT, info, new File(dir + fileName));
      CaesarCpuTypeProperty property = CaesarCpuTypeProperty.getProperty(types[i].getValue());
      fileName =
          MessageFormat.format(
              fileNameFormat,
              types[i].getName(),
              ports[i],
              info.hardware.getMasterVersion(),
              hardwareVersion.getSubVersion(),
              "io");
      if (property.hasTrunk()) {
        createSwu(SwuType.STACK_IO, info, new File(dir + fileName));
      } else {
        createSwu(SwuType.SINGLE_IO, info, new File(dir + fileName));
      }
    }

    info.matTypes = new Type[] {Type.PLUGIN_384, Type.PLUGIN_816};
    createSwu(SwuType.PLUGIN_384_816_IO, info, new File(dir + "384_816_hw3.2_io_sample.swu"));

    fileNameFormat = "{0}_hw{1}.{2}_sample.swu";
    info.hardware = hardwareVersion.copy();
    // TX
    String fileName =
        MessageFormat.format(
            fileNameFormat,
            "tx",
            info.hardware.getMasterVersion(),
            hardwareVersion.getSubVersion());
    createSwu(SwuType.TX, info, new File(dir + fileName));
    // RX
    fileName =
        MessageFormat.format(
            fileNameFormat,
            "rx",
            info.hardware.getMasterVersion(),
            hardwareVersion.getSubVersion());
    createSwu(SwuType.RX, info, new File(dir + fileName));
    // VP6
    fileName =
        MessageFormat.format(
            fileNameFormat,
            "vp6",
            info.hardware.getMasterVersion(),
            hardwareVersion.getSubVersion());
    createSwu(SwuType.VP6, info, new File(dir + fileName));
  }

  /**
   * 创建升级包.
   *
   * @param swuType 升级包类型
   * @param info 信息
   * @param targetFile 保存到的文件
   */
  public static void createSwu(SwuType swuType, SwuInfo info, File targetFile) {
    SwuCreater creater = new SwuCreater(targetFile);
    if (!creater.begin()) {
      return;
    }

    File versionFile = saveVersionFile(versionTextCreaters.get(swuType).create(info));
    if (versionFile != null) {
      creater.addFile(versionFile, "version.txt");
    }
    creater.end();
  }

  /**
   * 保存版本文件.
   *
   * @param content 文件内容
   * @return 保存到的文件
   */
  public static File saveVersionFile(String content) {
    String tempPath = System.getProperty("java.io.tmpdir");
    String versionFile = tempPath + "version.txt";
    try (FileOutputStream fos = new FileOutputStream(versionFile)) {
      fos.write(content.getBytes(StandardCharsets.UTF_8));
      return new File(versionFile);
    } catch (IOException exception) {
      log.warn("Fail to save version file");
      return null;
    }
  }

  /**
   * 获取主机升级包的版本文本.
   *
   * @param info 信息
   * @return 版本文本
   */
  public static String createMatVersionText(SwuInfo info) {
    String result = "";
    result +=
        MessageFormat.format(
            PRODUCT_MAT_FORMAT, info.hardware.getMasterVersion(), info.hardware.getSubVersion());
    result += MessageFormat.format(TYPE_FORMAT, getMatTypeString(info.matTypes));
    result += formatVersion(SYS_FORMAT, info.sys);
    result += formatVersion(APP_FORMAT, info.app);
    result += formatVersion(FPGA_FORMAT, info.fpga);
    return result;
  }

  /**
   * 获取TX升级包的版本文本.
   *
   * @param info 信息
   * @return 版本文本
   */
  public static String createTxVersionText(SwuInfo info) {
    String result = "";
    result +=
        MessageFormat.format(
            PRODUCT_TX_FORMAT, info.hardware.getMasterVersion(), info.hardware.getSubVersion());
    result += formatVersion(SYS_FORMAT, info.sys);
    result += formatVersion(APP_FORMAT, info.app);
    result += formatVersion(FPGA_FORMAT, info.fpga);
    return result;
  }

  /**
   * 获取RX升级包的版本文本.
   *
   * @param info 信息
   * @return 版本文本
   */
  public static String createRxVersionText(SwuInfo info) {
    String result = "";
    result +=
        MessageFormat.format(
            PRODUCT_RX_FORMAT, info.hardware.getMasterVersion(), info.hardware.getSubVersion());
    result += formatVersion(SYS_FORMAT, info.sys);
    result += formatVersion(APP_FORMAT, info.app);
    result += formatVersion(FPGA_FORMAT, info.fpga);
    return result;
  }

  /**
   * 创建单主机io升级包的版本文本.
   *
   * @param info 信息
   * @return 版本文本
   */
  public static String createSingleIoVersionText(SwuInfo info) {
    String result = "";
    result +=
        MessageFormat.format(
            PRODUCT_MAT_FORMAT, info.hardware.getMasterVersion(), info.hardware.getSubVersion());
    result += MessageFormat.format(TYPE_FORMAT, getMatTypeString(info.matTypes));
    result += MessageFormat.format(PORTS_FORMAT, info.port);
    result += MessageFormat.format(INTERFACE_FORMAT, info.ioType.getName());
    result += formatVersion(IO_FORMAT, info.io);
    return result;
  }

  /**
   * 获取堆叠主机的io升级包的版本文本.
   *
   * @param info 信息
   * @return 版本文本
   */
  public static String createStackIoVersionText(SwuInfo info) {
    String result = "";
    result +=
        MessageFormat.format(
            PRODUCT_MAT_FORMAT, info.hardware.getMasterVersion(), info.hardware.getSubVersion());
    result += MessageFormat.format(TYPE_FORMAT, getMatTypeString(info.matTypes));
    result += MessageFormat.format(INTERFACE_FORMAT, info.ioType.getName());
    result += formatVersion(TRUNK_FORMAT, info.trunk);
    result += formatVersion(BRANCH_FORMAT, info.branch);
    return result;
  }

  /**
   * 获取384、816的io升级的版本文本.
   *
   * @param info 信息
   * @return 版本文本
   */
  public static String create384816IoVersionText(SwuInfo info) {
    String result = "";
    result +=
        MessageFormat.format(
            PRODUCT_MAT_FORMAT, info.hardware.getMasterVersion(), info.hardware.getSubVersion());
    result += MessageFormat.format(TYPE_FORMAT, getMatTypeString(info.matTypes));
    result += MessageFormat.format(INTERFACE_FORMAT, info.ioType.getName());
    result += formatVersion(TRUNK_384_FORMAT, info.trunk384);
    result += formatVersion(TRUNK_816_FORMAT, info.trunk816);
    result += formatVersion(BRANCH_FORMAT, info.branch);
    return result;
  }

  /**
   * 获取vp6升级包版本文本.
   *
   * @param info 信息
   * @return 版本文本
   */
  public static String createExtVp6VersionText(SwuInfo info) {
    String result = "";
    result +=
        MessageFormat.format(
            PRODUCT_VP6_FORMAT, info.hardware.getMasterVersion(), info.hardware.getSubVersion());
    result += formatVersion(FPGA_FORMAT, info.fpga);
    return result;
  }

  /**
   * .
   */
  public static String formatVersion(String format, VersionDef version) {
    return String.format(
        format,
        version.getMasterVersion(),
        version.getSubVersion(),
        version.getYear() - 2000,
        version.getMonth(),
        version.getDay());
  }

  /**
   * 获取矩阵类型字符串. 用逗号隔开
   *
   * @param types 矩阵类型集合.
   * @return 矩阵类型字符串
   */
  public static String getMatTypeString(Type[] types) {
    StringBuilder result = new StringBuilder();
    for (Type type : types) {
      result.append(type.getName());
      result.append(',');
    }
    return result.toString();
  }

  static class SwuCreater {

    private File targetFile;
    private FileOutputStream fos = null;
    private CpioArchiveOutputStream caos;

    public SwuCreater(File targetFile) {
      this.targetFile = targetFile;
    }

    public boolean begin() {
      fos = null;
      try {
        fos = new FileOutputStream(targetFile);
        caos = new CpioArchiveOutputStream(fos);
        return true;
      } catch (IOException exception) {
        log.warn("Fail to creat output stream!", exception);
        return false;
      }
    }

    public boolean addFile(File file, String name) {
      if (caos == null) {
        return false;
      }
      try (FileInputStream in = new FileInputStream(file)) {
        ArchiveEntry entry = caos.createArchiveEntry(file, name);
        caos.putArchiveEntry(entry);

        byte[] ioBuffer = new byte[4096];
        int bytesRead;
        while ((bytesRead = in.read(ioBuffer)) > 0) {
          caos.write(ioBuffer, 0, bytesRead);
        }
        caos.closeArchiveEntry();
        return true;
      } catch (IOException exception) {
        log.warn("Fail to zip!", exception);
        return false;
      }
    }

    public void end() {
      try {
        if (caos != null) {
          caos.close();
        }
      } catch (IOException exception) {
        log.warn("Fail to close output stream!", exception);
      }
    }
  }

  static class SwuInfo {

    VersionDef sys;
    VersionDef fpga;
    VersionDef app;
    VersionDef branch;
    VersionDef trunk;
    VersionDef trunk384;
    VersionDef trunk816;
    VersionDef io;
    VersionDef hardware;

    Type[] matTypes;
    int port;
    Type ioType;
  }

  enum SwuType {
    MAT,
    TX,
    RX,
    SINGLE_IO,
    STACK_IO,
    PLUGIN_384_816_IO,
    VP6
  }

  @FunctionalInterface
  interface VersionTextCreater {

    String create(SwuInfo info);
  }
}
