package com.mc.tool.caesar.api.exception;

import java.util.ResourceBundle;

/**
 * .
 */
public class FirmwareUpdateException extends Exception {

  /**
   * .
   */
  private static final long serialVersionUID = 1L;
  private final UpdateState updateState;

  /**
   * .
   */
  public enum UpdateState {
    OK(Bundle.updateState_Ok()), NO_UPDATE_REQUIRED(Bundle.updateState_NoUpdateRequired()), ABORTED(
        Bundle.updateState_Aborted()), INIT_ERROR(
        Bundle.updateState_InitError()), FILE_NOT_FOUND_ERROR(
        Bundle.updateState_FileNotFoundError()), CONFIG_FILE_ERROR(
        Bundle.updateState_ConfigFileError()), CHECKSUM_ERROR(
        Bundle.updateState_ChecksumError()), OPEN_ERROR(
        Bundle.updateState_OpenError()), WRITE_ERROR(
        Bundle.updateState_WriteError()), CLOSE_ERROR(
        Bundle.updateState_CloseError()), RESTART_ERROR(
        Bundle.updateState_CloseError()), BUSY_ERROR(
        Bundle.updateState_BusyError()), UNKNOWN_ERROR(Bundle
        .updateState_UnknownError()), IORESTART,
    SWITCH_RESTART, VERSION_CHECK;

    private String message;

    UpdateState() {

    }

    UpdateState(String message) {
      this.message = message;
    }

    public String getMessage() {
      return message;
    }
  }

  public FirmwareUpdateException(UpdateState updateState) {
    this.updateState = updateState;
  }

  public UpdateState getState() {
    return this.updateState;
  }

  private static class Bundle {

    @SuppressWarnings("unused")
    private static final String path = "firmware_update_exception";

    public static String updateState_Aborted() {
      return ResourceBundle.getBundle("").getString("UpdateState.Aborted");
    }

    public static String updateState_BusyError() {
      return ResourceBundle.getBundle("").getString("UpdateState.BusyError");
    }

    public static String updateState_ChecksumError() {
      return ResourceBundle.getBundle("").getString("UpdateState.ChecksumError");
    }

    public static String updateState_CloseError() {
      return ResourceBundle.getBundle("").getString("UpdateState.CloseError");
    }

    public static String updateState_ConfigFileError() {
      return ResourceBundle.getBundle("").getString("UpdateState.ConfigFileError");
    }

    public static String updateState_FileNotFoundError() {
      return ResourceBundle.getBundle("").getString("UpdateState.FileNotFoundError");
    }

    public static String updateState_InitError() {
      return ResourceBundle.getBundle("").getString("UpdateState.InitError");
    }

    public static String updateState_NoUpdateRequired() {
      return ResourceBundle.getBundle("").getString("UpdateState.NoUpdateRequired");
    }

    public static String updateState_Ok() {
      return ResourceBundle.getBundle("").getString("UpdateState.Ok");
    }

    public static String updateState_OpenError() {
      return ResourceBundle.getBundle("").getString("UpdateState.OpenError");
    }

    @SuppressWarnings("unused")
    public static String updateState_RestartError() {
      return ResourceBundle.getBundle("").getString("UpdateState.RestartError");
    }

    public static String updateState_UnknownError() {
      return ResourceBundle.getBundle("").getString("UpdateState.UnknownError");
    }

    public static String updateState_WriteError() {
      return ResourceBundle.getBundle("").getString("UpdateState.WriteError");
    }
  }
}

