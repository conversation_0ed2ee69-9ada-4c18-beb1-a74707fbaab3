package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import lombok.Setter;

/**
 * IpcStreamInfo.
 */
@Setter
@JsonPropertyOrder({
    IpcStreamInfo.JSON_PROPERTY_STREAM_ID,
    IpcStreamInfo.JSON_PROPERTY_STREAM_INDEX
})
public class IpcStreamInfo {
  public static final String JSON_PROPERTY_STREAM_ID = "streamId";
  private Integer streamId;

  public static final String JSON_PROPERTY_STREAM_INDEX = "streamIndex";
  private Integer streamIndex;


  /**
   * set streamId.
   */
  public IpcStreamInfo streamId(Integer streamId) {

    this.streamId = streamId;
    return this;
  }

  /**
   * Get streamId.
   **/
  @JsonProperty(JSON_PROPERTY_STREAM_ID)
  @JsonInclude()
  public Integer getStreamId() {
    return streamId;
  }


  /**
   * set streamIndex.
   */
  public IpcStreamInfo streamIndex(Integer streamIndex) {
    this.streamIndex = streamIndex;
    return this;
  }

  /**
   * Get streamIndex.
   **/
  @JsonProperty(JSON_PROPERTY_STREAM_INDEX)
  @JsonInclude()
  public Integer getStreamIndex() {
    return streamIndex;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    IpcStreamInfo ipcStreamInfo = (IpcStreamInfo) o;
    return Objects.equals(this.streamId, ipcStreamInfo.streamId)
        && Objects.equals(this.streamIndex, ipcStreamInfo.streamIndex);
  }

  @Override
  public int hashCode() {
    return Objects.hash(streamId, streamIndex);
  }


  @Override
  public String toString() {
    return "IpcStreamInfo {\n"
        + "    streamId: " + toIndentedString(streamId) + "\n"
        + "    streamIndex: " + toIndentedString(streamIndex) + "\n"
        + "}";
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
