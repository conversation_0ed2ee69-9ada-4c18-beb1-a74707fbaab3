package com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.datamodel.vp.OutputInfoGetter;
import com.mc.tool.caesar.api.datamodel.vp.OutputInfoSetter;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.LongProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleLongProperty;

/**
 * .
 */
public class CaesarOutputData implements OutputInfoGetter, OutputInfoSetter {

  @Expose public BooleanProperty enable = new SimpleBooleanProperty();
  @Expose public IntegerProperty horzSync = new SimpleIntegerProperty();
  @Expose public IntegerProperty horzBackPorch = new SimpleIntegerProperty();
  @Expose public IntegerProperty horzFrontPorch = new SimpleIntegerProperty();
  @Expose public BooleanProperty horzPolarity = new SimpleBooleanProperty();
  @Expose public IntegerProperty vertSync = new SimpleIntegerProperty();
  @Expose public IntegerProperty vertBackPorch = new SimpleIntegerProperty();
  @Expose public IntegerProperty vertFrontPorch = new SimpleIntegerProperty();
  @Expose public BooleanProperty vertPolarity = new SimpleBooleanProperty();
  @Expose public LongProperty clock = new SimpleLongProperty();

  @Override
  public boolean isEnable() {
    return enable.get();
  }

  @Override
  public int getHorzSync() {
    return horzSync.get();
  }

  @Override
  public int getHorzBackPorch() {
    return horzBackPorch.get();
  }

  @Override
  public int getHorzFrontPorch() {
    return horzFrontPorch.get();
  }

  @Override
  public boolean isHorzPolarity() {
    return horzPolarity.get();
  }

  @Override
  public int getVertSync() {
    return vertSync.get();
  }

  @Override
  public int getVertBackPorch() {
    return vertBackPorch.get();
  }

  @Override
  public int getVertFrontPorch() {
    return vertFrontPorch.get();
  }

  @Override
  public boolean isVertPolarity() {
    return vertPolarity.get();
  }

  @Override
  public long getClock() {
    return clock.get();
  }

  @Override
  public void setEnable(boolean enable) {
    this.enable.set(enable);
  }

  @Override
  public void setHorzSync(int horzSync) {
    this.horzSync.set(horzSync);
  }

  @Override
  public void setHorzBackPorch(int horzBackPorch) {
    this.horzBackPorch.set(horzBackPorch);
  }

  @Override
  public void setHorzFrontPorch(int horzFrontPorch) {
    this.horzFrontPorch.set(horzFrontPorch);
  }

  @Override
  public void setHorzPolarity(boolean horzPolarity) {
    this.horzPolarity.set(horzPolarity);
  }

  @Override
  public void setVertSync(int vertSync) {
    this.vertSync.set(vertSync);
  }

  @Override
  public void setVertBackPorch(int vertBackPorch) {
    this.vertBackPorch.set(vertBackPorch);
  }

  @Override
  public void setVertFrontPorch(int vertFrontPorch) {
    this.vertFrontPorch.set(vertFrontPorch);
  }

  @Override
  public void setVertPolarity(boolean vertPolarity) {
    this.vertPolarity.set(vertPolarity);
  }

  @Override
  public void setClock(long clock) {
    this.clock.set(clock);
  }
}
