package com.mc.tool.caesar.vpm.pages.hostconfiguration;

import com.mc.tool.caesar.api.datamodel.SystemConfigData;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * .

 * <AUTHOR>
 */
public class EventConfigurationBean {

  private SystemConfigData systemConfigData;
  private Boolean enable;
  private Boolean osdWarning;
  private Boolean buzzerWarning;
  private Integer audioThreshold;

  public EventConfigurationBean(SystemConfigData systemConfigData) {
    this.systemConfigData = systemConfigData;
  }

  public boolean getEnable() {
    enable = systemConfigData.getEventSetup().isEnable();
    return enable;
  }

  public void setEnable(boolean enable) {
    systemConfigData.getEventSetup().setEnable(enable);
  }

  public boolean getOsdWarning() {
    osdWarning = systemConfigData.getEventSetup().isOsdWarning();
    return osdWarning;
  }

  public void setOsdWarning(boolean osdWarning) {
    systemConfigData.getEventSetup().setOsdWarning(osdWarning);
  }

  public boolean getBuzzerWarning() {
    buzzerWarning = systemConfigData.getEventSetup().isBuzzerWarning();
    return buzzerWarning;
  }

  public void setBuzzerWarning(boolean buzzerWarning) {
    systemConfigData.getEventSetup().setBuzzerWarning(buzzerWarning);
  }

  @Min(value = 0)
  @Max(value = 16777215)
  @NotNull(message = "{com.mc.tool.caesar.vpm.pages.hostconfiguration.constraints.notnull.message}")
  public int getAudioThreshold() {
    audioThreshold = systemConfigData.getEventSetup().getAudioThreshold();
    return audioThreshold;
  }

  public void setAudioThreshold(int audioThreshold) {
    systemConfigData.getEventSetup().setAudioThreshold(audioThreshold);
  }
}
