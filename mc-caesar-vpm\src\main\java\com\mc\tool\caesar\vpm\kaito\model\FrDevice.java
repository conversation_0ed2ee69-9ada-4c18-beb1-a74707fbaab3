package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import lombok.Setter;

/**
 * 人脸识别终端信息.
 */
@Setter
@JsonPropertyOrder({
    FrDevice.JSON_PROPERTY_DEVICEID,
    FrDevice.JSON_PROPERTY_IP,
    FrDevice.JSON_PROPERTY_PORT,
    FrDevice.JSON_PROPERTY_MASK,
    FrDevice.JSON_PROPERTY_GATEWAY,
    FrDevice.JSON_PROPERTY_KVM_IP,
    FrDevice.JSON_PROPERTY_KVM_PORT,
    FrDevice.JSON_PROPERTY_ALIAS,
    FrDevice.JSON_PROPERTY_BOUND_RX
})
public class FrDevice {
  public static final String JSON_PROPERTY_DEVICEID = "deviceid";
  private String deviceid;

  public static final String JSON_PROPERTY_IP = "ip";
  private String ip;

  public static final String JSON_PROPERTY_PORT = "port";
  private Integer port;

  public static final String JSON_PROPERTY_MASK = "mask";
  private String mask;

  public static final String JSON_PROPERTY_GATEWAY = "gateway";
  private String gateway;

  public static final String JSON_PROPERTY_KVM_IP = "kvm_ip";
  private String kvmIp;

  public static final String JSON_PROPERTY_KVM_PORT = "kvm_port";
  private Integer kvmPort;

  public static final String JSON_PROPERTY_ALIAS = "alias";
  private String alias;

  public static final String JSON_PROPERTY_BOUND_RX = "bound_rx";
  private Integer boundRx;


  public FrDevice deviceid(String deviceid) {
    this.deviceid = deviceid;
    return this;
  }

  /**
   * 设备ID，通常是设备MAC地址.
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_DEVICEID)
  @JsonInclude()
  public String getDeviceid() {
    return deviceid;
  }


  public FrDevice ip(String ip) {
    this.ip = ip;
    return this;
  }

  /**
   * 人脸识别终端IP.
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_IP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public String getIp() {
    return ip;
  }


  public FrDevice port(Integer port) {
    this.port = port;
    return this;
  }

  /**
   * 人脸识别终端服务端口.
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_PORT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getPort() {
    return port;
  }


  public FrDevice mask(String mask) {
    this.mask = mask;
    return this;
  }

  /**
   * 子网掩码.
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_MASK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public String getMask() {
    return mask;
  }


  public FrDevice gateway(String gateway) {
    this.gateway = gateway;
    return this;
  }

  /**
   * 网关.
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_GATEWAY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public String getGateway() {
    return gateway;
  }


  public FrDevice kvmIp(String kvmIp) {
    this.kvmIp = kvmIp;
    return this;
  }

  /**
   * KVM主机IP.
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_KVM_IP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getKvmIp() {
    return kvmIp;
  }


  public FrDevice kvmPort(Integer kvmPort) {
    this.kvmPort = kvmPort;
    return this;
  }

  /**
   * KVM主机端口.
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_KVM_PORT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getKvmPort() {
    return kvmPort;
  }


  public FrDevice alias(String alias) {
    this.alias = alias;
    return this;
  }

  /**
   * 人脸识别终端别名.
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_ALIAS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public String getAlias() {
    return alias;
  }


  public FrDevice boundRx(Integer boundRx) {
    this.boundRx = boundRx;
    return this;
  }

  /**
   * 绑定的KVM RX的ID，如果没有绑定，值为-1.
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_BOUND_RX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getBoundRx() {
    return boundRx;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FrDevice frDevice = (FrDevice) o;
    return Objects.equals(this.deviceid, frDevice.deviceid)
        && Objects.equals(this.ip, frDevice.ip)
        && Objects.equals(this.port, frDevice.port)
        && Objects.equals(this.mask, frDevice.mask)
        && Objects.equals(this.gateway, frDevice.gateway)
        && Objects.equals(this.kvmIp, frDevice.kvmIp)
        && Objects.equals(this.kvmPort, frDevice.kvmPort)
        && Objects.equals(this.alias, frDevice.alias)
        && Objects.equals(this.boundRx, frDevice.boundRx);
  }

  @Override
  public int hashCode() {
    return Objects.hash(deviceid, ip, port, mask, gateway, kvmIp, kvmPort, alias, boundRx);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("FrDevice {\n");
    sb.append("    deviceid: ").append(toIndentedString(deviceid)).append("\n");
    sb.append("    ip: ").append(toIndentedString(ip)).append("\n");
    sb.append("    port: ").append(toIndentedString(port)).append("\n");
    sb.append("    mask: ").append(toIndentedString(mask)).append("\n");
    sb.append("    gateway: ").append(toIndentedString(gateway)).append("\n");
    sb.append("    kvmIp: ").append(toIndentedString(kvmIp)).append("\n");
    sb.append("    kvmPort: ").append(toIndentedString(kvmPort)).append("\n");
    sb.append("    alias: ").append(toIndentedString(alias)).append("\n");
    sb.append("    boundRx: ").append(toIndentedString(boundRx)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
