package com.mc.tool.framework.utility.dialogues;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.mc.tool.framework.interfaces.AppConfig;
import java.io.File;
import java.util.Locale;

/**
 * Manager for file dialogue directory tracking.
 * This service manages the last opened directories for different types of file dialogs.
 */
@Singleton
public class FileDialogueManager {
  
  private final AppConfig appConfig;

  /**
   * Constructor.
   *
   * @param appConfig the application configuration
   */
  @Inject
  public FileDialogueManager(AppConfig appConfig) {
    this.appConfig = appConfig;
  }

  /**
   * Get the last opened directory for a specific dialog type.
   *
   * @param dialogType the dialog type or file extension
   * @return the last opened directory, or null if not found
   */
  public File getLastOpenedDirectory(String dialogType) {
    if (dialogType == null || dialogType.trim().isEmpty()) {
      return null;
    }
    
    String directoryPath = appConfig.getConfigBean().getLastOpenedDirectory(dialogType);
    if (directoryPath != null && !directoryPath.trim().isEmpty()) {
      File directory = new File(directoryPath);
      if (directory.exists() && directory.isDirectory()) {
        return directory;
      }
    }
    return null;
  }

  /**
   * Save the last opened directory for a specific dialog type.
   *
   * @param dialogType the dialog type or file extension
   * @param selectedFile the file that was selected (we'll use its parent directory)
   */
  public void saveLastOpenedDirectory(String dialogType, File selectedFile) {
    if (dialogType == null || dialogType.trim().isEmpty() || selectedFile == null) {
      return;
    }
    
    File directory = selectedFile.isDirectory() ? selectedFile : selectedFile.getParentFile();
    if (directory != null && directory.exists() && directory.isDirectory()) {
      appConfig.getConfigBean().setLastOpenedDirectory(dialogType, directory.getAbsolutePath());
    }
  }

  /**
   * Configure a file dialogue with the last opened directory for its type.
   *
   * @param fileDialogue the file dialogue to configure
   */
  public void configureFileDialogue(FileDialogue fileDialogue) {
    if (fileDialogue == null) {
      return;
    }
    
    String dialogType = fileDialogue.getDialogType();
    if (dialogType != null) {
      File lastDirectory = getLastOpenedDirectory(dialogType);
      if (lastDirectory != null) {
        fileDialogue.setInitialDirectory(lastDirectory);
      }
    }
  }

  /**
   * Handle the result of a file dialogue operation.
   * This should be called after a file is selected to save the directory.
   *
   * @param fileDialogue the file dialogue that was used
   * @param selectedFile the file that was selected
   */
  public void handleFileDialogueResult(FileDialogue fileDialogue, File selectedFile) {
    if (fileDialogue == null || selectedFile == null) {
      return;
    }
    
    String dialogType = fileDialogue.getDialogType();
    if (dialogType != null) {
      saveLastOpenedDirectory(dialogType, selectedFile);
    }
  }

  /**
   * Determine dialog type from extension filters.
   * This is a helper method to automatically determine dialog type from file extensions.
   *
   * @param extensionDescription the description from ExtensionFilter
   * @return a suitable dialog type string
   */
  public static String getDialogTypeFromExtension(String extensionDescription) {
    if (extensionDescription == null) {
      return "DEFAULT";
    }
    
    String upper = extensionDescription.toUpperCase(Locale.ENGLISH);
    if (upper.contains("SWU")) {
      return "SWU";
    } else if (upper.contains("BIN")) {
      return "BIN";
    } else if (upper.contains("EDID")) {
      return "EDID";
    } else if (upper.contains("CSV")) {
      return "CSV";
    } else if (upper.contains("PNG")) {
      return "PNG";
    } else if (upper.contains("EXCEL") || upper.contains("XLS")) {
      return "EXCEL";
    } else if (upper.contains("CONFIG") || upper.contains("CFGX")) {
      return "CONFIG";
    } else {
      return "DEFAULT";
    }
  }
}
