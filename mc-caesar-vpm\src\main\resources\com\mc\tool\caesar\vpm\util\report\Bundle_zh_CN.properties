CreateReportWizardPanel.name=\u9009\u62E9\u5185\u5BB9
CreateReportWizardPanel.selectall=<html><b>\u9009\u62E9\u6240\u6709
ReportConstants.Content.Assignment=\u5206\u914D
ReportConstants.Content.ConAcls=\u8BBF\u95EE\u63A7\u5236
ReportConstants.Content.ConDevices=CON\u7BA1\u63A7\u7AEF
ReportConstants.Content.ConFavorites=\u6536\u85CF\u5939
ReportConstants.Content.ConMacros=\u5B8F
ReportConstants.Content.CpuDevices=CPU\u63A5\u5165\u7AEF
ReportConstants.Content.ExtUnits=\u6269\u5C55\u5355\u5143
ReportConstants.Content.MatrixView=\u4E3B\u673A\u89C6\u56FE
ReportConstants.Content.System=\u7CFB\u7EDF
ReportConstants.Content.User=\u7528\u6237
ReportConstants.Content.UserAcls=\u8BBF\u95EE\u63A7\u5236
ReportConstants.Content.UserFavorites=\u6536\u85CF\u5939
ReportConstants.Content.UserMacros=\u5B8F
ReportGenerator.access=\u767B\u5F55
ReportGenerator.date=\u65E5\u671F:
ReportGenerator.dateandtime=\u65E5\u671F\u4E0E\u65F6\u95F4
ReportGenerator.device=\u8BBE\u5907:
ReportGenerator.favorites=\u6536\u85CF\u5939
ReportGenerator.favorites.missing=\u6CA1\u6709\u6536\u85CF\u8BBE\u5B9A
ReportGenerator.fullAccess=ACL -\u64CD\u4F5C\u6A21\u5F0F
ReportGenerator.fullAccess.missing=\u6CA1\u6709\u914D\u7F6E\u64CD\u4F5C\u6A21\u5F0F\u6743\u9650
ReportGenerator.info=\u914D\u7F6E\u4FE1\u606F:
ReportGenerator.macros=\u5B8F
ReportGenerator.macros.missing=\u6CA1\u6709\u8BBE\u5B9A\u5B8F
ReportGenerator.matrixgrid=\u4E3B\u673A\u7EA7\u8054\u7F51
ReportGenerator.msc=\u591A\u5C4F\u5E55\u63A7\u5236
ReportGenerator.msc.missing=\u6CA1\u6709\u8BBE\u5B9A\u591A\u5C4F\u5E55\u63A7\u5236
ReportGenerator.msc.onlineonly=\u4E0D\u5141\u8BB8\u5728\u79BB\u7EBF\u72B6\u6001\u7F16\u8F91
ReportGenerator.name=\u914D\u7F6E\u540D\u79F0:
ReportGenerator.network=\u7F51\u7EDC
ReportGenerator.network.general=\u901A\u7528
ReportGenerator.network.interface1=\u7F51\u7EDC\u63A5\u53E31
ReportGenerator.network.interface2=\u7F51\u7EDC\u63A5\u53E32
ReportGenerator.network.ldap=LDAP
ReportGenerator.network.snmp=SNMP
ReportGenerator.network.syslog=\u7CFB\u7EDF\u65E5\u5FD7
ReportGenerator.network.syslog.server1=Syslog\u670D\u52A1\u5668 1
ReportGenerator.network.syslog.server2=Syslog\u670D\u52A1\u5668 2
ReportGenerator.network.trap.receiver1=Trap\u63A5\u6536\u5668 1
ReportGenerator.network.trap.receiver2=Trap\u63A5\u6536\u5668 2
ReportGenerator.switch=\u5207\u6362
ReportGenerator.systemdata=\u7CFB\u7EDF\u6570\u636E
ReportGenerator.systemdata.automaticId=\u81EA\u52A8ID
ReportGenerator.systemdata.general=\u901A\u7528
ReportGenerator.systemdata.osd=OSD\u6570\u636E (CPU)
ReportGenerator.title=\u914D\u7F6E\u62A5\u544A
ReportGenerator.vcon=\u865A\u62DFCON\u63A5\u6536\u7AEF\u8BBE\u5907
ReportGenerator.vcon.missing=\u865A\u62DFCON\u7BA1\u63A7\u7AEF\u672A\u5B9A\u4E49
ReportGenerator.vcpu=\u865A\u62DFCPU\u63A5\u5165\u7AEF\u8BBE\u5907
ReportGenerator.vcpu.missing=\u865A\u62DFCPU\u63A5\u5165\u7AEF\u672A\u5B9A\u4E49
ReportGenerator.videoAccess=ACL - \u89C6\u9891\u6A21\u5F0F
ReportGenerator.videoAccess.missing=\u6CA1\u6709\u914D\u7F6E\u89C6\u9891\u7684\u8BBF\u95EE\u6743\u5229
ReportPlugin.wizard.title=\u914D\u7F6E\u62A5\u544A
ReportWizardIterator.name=\u4FDD\u5B58\u62A5\u544A
SystemConfigData.SystemData.Device=\u8BBE\u5907
SystemConfigData.SystemData.Name=\u540D\u79F0
SystemConfigData.SystemData.Info=\u4FE1\u606F
SystemConfigData.SystemData.ForceBits.AutoSave=\u81EA\u52A8\u4FDD\u5B58
SystemConfigData.SystemData.ForceBits.ComEcho=\u5141\u8BB8COM\u6570\u636E\u56DE\u4F20
SystemConfigData.SystemData.ForceBits.LanEcho=\u5141\u8BB8LAN\u53E3\u6570\u636E\u56DE\u4F20
SystemConfigData.SystemData.ForceBits.Redundancy=\u5141\u8BB8\u5197\u4F59
SystemConfigData.SystemData.ForceBits.Synchronize=\u540C\u6B65
SystemConfigData.SystemData.ForceBits.EchoOnly=\u53EA\u56DE\u4F20
SystemConfigData.SystemData.MasterIP=\u4E3B\u7528\u4E3B\u673AIP\u5730\u5740
SystemConfigData.SystemData.ForceBits.RemoveSlave=\u79FB\u9664I/O\u677F\u5361 
SystemConfigData.SystemData.ForceBits.OnlineConfig=\u5728\u7EBF\u914D\u7F6E
SystemConfigData.AutoIdData.ForceBits.AutoConfig=\u5FAA\u5E8F\u81EA\u52A8\u914D\u7F6E
SystemConfigData.AutoIdData.AutoId.RealCpuId=\u771F\u5B9E\u7684CPU\u63A5\u5165\u7AEFID
SystemConfigData.AutoIdData.AutoId.VirtualCpuId=\u865A\u62DF\u7684CPU\u63A5\u5165\u7AEFID
SystemConfigData.AutoIdData.AutoId.RealConId=\u771F\u5B9E\u7684CON\u7BA1\u63A7\u7AEFID
SystemConfigData.AutoIdData.AutoId.VirtualConId=\u865A\u62DF\u7684CON\u7BA1\u63A7\u7AEFID
DisplayData.H_Speed=\u9F20\u6807\u6C34\u5E73\u901F\u5EA6[1/x]
DisplayData.V_Speed=\u9F20\u6807\u5782\u76F4\u901F\u5EA6[1/x]
DisplayData.C_Speed=\u53CC\u51FB\u65F6\u95F4[\u6BEB\u79D2]
DisplayData.Keyboard=\u952E\u76D8\u5E03\u5C40
DisplayData.VideoMode=\u89C6\u9891\u6A21\u5F0F
SystemConfigData.AccessData.ForceBits.UserAccess=\u5141\u8BB8\u65B0\u7528\u6237
SystemConfigData.AccessData.ForceBits.ConAccess=\u5141\u8BB8\u65B0\u7684CON\u7BA1\u63A7\u7AEF
SystemConfigData.AccessData.ForceBits.CpuDisconnct=\u81EA\u52A8\u65AD\u5F00
SystemConfigData.AccessData.TimeoutDisplay=OSD\u8D85\u65F6[\u79D2]
SystemConfigData.AccessData.TimeoutLogout=\u81EA\u52A8\u9000\u51FA[\u5206\u949F]
SystemConfigData.SwitchData.ForceBits.CpuWatch=\u5141\u8BB8\u89C6\u9891\u5171\u4EAB
SystemConfigData.SwitchData.ForceBits.CpuConnect=\u5F3A\u5236\u8FDE\u63A5
SystemConfigData.SwitchData.ForceBits.ConDisconnect=\u5F3A\u5236\u65AD\u5F00
SystemConfigData.SwitchData.TimeoutDisconnect=CPU\u63A5\u5165\u7AEF\u8D85\u65F6[\u5206\u949F]
SystemConfigData.SwitchData.ForceBits.KeyboardConnect=\u952E\u76D8\u9F20\u6807\u8FDE\u63A5
SystemConfigData.SwitchData.TimeoutShare=\u91CA\u653E\u65F6\u95F4[\u79D2]
SystemConfigData.SwitchData.ForceBits.FKeySingle=\u5355\u6B65\u9AA4\u5B8F\u63A7\u5236
SystemConfigData.NetworkData.NetworkBits.Dhcp=DHCP
SystemConfigData.NetworkData.Address=IP\u5730\u5740
SystemConfigData.NetworkData.Netmask=\u5B50\u7F51\u63A9\u7801
SystemConfigData.NetworkData.Gateway=\u7F51\u5173
SystemConfigData.NetworkData.MacAddress=MAC\u5730\u5740
SystemData.InternalLogLevel=\u65E5\u5FD7\u7EA7\u522B
SystemData.InternalLogLevel.Debug=\u8C03\u8BD5
SystemData.InternalLogLevel.Info=\u4FE1\u606F
SystemData.InternalLogLevel.Notice=\u901A\u77E5
SystemData.InternalLogLevel.Warning=\u8B66\u544A
SystemData.InternalLogLevel.Error=\u9519\u8BEF
SystemConfigData.SyslogData.Port=\u7AEF\u53E3
SystemConfigData.SyslogData.SysLevel=\u65E5\u5FD7\u7EA7\u522B
SystemConfigData.SyslogData.SysLevel.Debug=\u8C03\u8BD5
SystemConfigData.SyslogData.SysLevel.Info=\u4FE1\u606F
SystemConfigData.SyslogData.SysLevel.Notice=\u901A\u77E5
SystemConfigData.SyslogData.SysLevel.Warning=\u8B66\u544A
SystemConfigData.SyslogData.SysLevel.Error=\u9519\u8BEF
SystemConfigData.NetworkData.NetworkBits.Syslog=\u7CFB\u7EDF\u65E5\u5FD7
SystemConfigData.NetworkData.NetworkBits.Snmp=SNMP\u4EE3\u7406
SystemConfigData.SnmpData.SnmpBits.Trap=\u5141\u8BB8\u8FC7\u6EE4
SystemConfigData.SnmpData.Port=\u7AEF\u53E3
SystemConfigData.SnmpData.SnmpBits.Status=\u72B6\u6001
SystemConfigData.SnmpData.SnmpBits.Temperature=\u6E29\u5EA6
SystemConfigData.SnmpData.SnmpBits.InsertBoard=\u63D2\u5165I/O\u677F\u5361
SystemConfigData.SnmpData.SnmpBits.RemoveBoard=\u79FB\u9664I/O\u677F\u5361
SystemConfigData.SnmpData.SnmpBits.InvalidBoard=\u65E0\u6548I/O\u677F\u5361
SystemConfigData.SnmpData.SnmpBits.InsertExtender=\u63D2\u5165\u6269\u5C55\u5668
SystemConfigData.SnmpData.SnmpBits.RemoveExtener=\u79FB\u9664\u6269\u5C55\u5668
SystemConfigData.SnmpData.SnmpBits.SwitchCommand=\u5207\u6362\u547D\u4EE4
SystemConfigData.SnmpData.SnmpBits.FanTray1=\u98CE\u6247\u69FD1
SystemConfigData.SnmpData.SnmpBits.FanTray2=\u98CE\u6247\u69FD2
SystemConfigData.SnmpData.SnmpBits.PowerSupply1=\u7535\u6E90\u4F9B\u5E94\u56681
SystemConfigData.SnmpData.SnmpBits.PowerSupply2=\u7535\u6E90\u4F9B\u5E94\u56682
SystemConfigData.SnmpData.SnmpBits.PowerSupply3=\u7535\u6E90\u4F9B\u5E94\u56683
SystemConfigData.LdapData.Address=LDAP\u670D\u52A1\u5668
SystemConfigData.LdapData.Port=\u7AEF\u53E3
SystemConfigData.LdapData.BaseDN=\u57FA\u672CDN
SystemConfigData.NetworkData.NetworkBits.Ldap=LDAP
SystemConfigData.SntpData.TimeZone=\u65F6\u533A
SystemConfigData.NetworkData.NetworkBits.Sntp=SNTP
SystemConfigData.MatrixGridData.ForceBits.Grid=\u5141\u8BB8\u4E3B\u673A\u7EA7\u8054
ControlGroupData.Arrangement=\u6392\u5217
ControlGroupData.Manual=\u624B\u52A8
ExtenderData.Port=\u7AEF\u53E3
ExtenderData.RDPort=\u5197\u4F59\u7AEF\u53E3
ExtenderData.ID=ID
ExtenderData.Name=\u540D\u79F0
CpuData.ID=ID
CpuData.Name=\u540D\u79F0
CpuData.Status.Virtual=\u865A\u62DF\u8BBE\u5907
CpuData.Status.AllowPrivate=\u5141\u8BB8\u79C1\u6709\u6A21\u5F0F
CpuData.Status.ForcePrivate=\u5F3A\u5236\u79C1\u6709\u6A21\u5F0F
ConsoleData.ID=ID
ConsoleData.Name=\u540D\u79F0
ConsoleData.ConsoleVirtual=\u64CD\u63A7\u53F0\u5206\u914D
ConsoleData.Status.AllowLogin=\u5141\u8BB8\u7528\u6237 ACL
ConsoleData.Status.ForceLogin=\u5F3A\u5236\u767B\u5F55
ConsoleData.Status.LOSFrame=LOS\u67B6\u6784
ConsoleData.Status.ForceScan=\u5F3A\u5236CPU\u63A5\u5165\u7AEF\u8F6E\u8BE2
ConsoleData.ScanTime=\u8F6E\u8BE2\u65F6\u95F4[sec]
ConsoleData.Status.ForceMacro=\u5C55\u793A\u5B8F\u5217\u8868
ConsoleData.Status.OsdDisabled=OSD\u5DF2\u7981\u7528
UserData.Name=\u540D\u5B57
UserData.FullName=\u5168\u540D
UserData.Rights.ADMIN=\u7BA1\u7406\u5458
UserData.Rights.SUPER=\u8D85\u7EA7\u7528\u6237
UserData.Rights.POWER=\u6743\u9650\u7528\u6237
UserData.Rights.LDAP=LDAP \u7528\u6237
UserData.Rights.FTP=FTP
