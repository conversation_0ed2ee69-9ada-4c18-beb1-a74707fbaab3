package com.mc.tool.caesar.vpm.pages.splicingscreen;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.kaito.model.InputCard;
import com.mc.tool.framework.utility.UndecoratedDialog;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Pos;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonBar;
import javafx.scene.control.ButtonType;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.control.ListView;
import javafx.scene.control.MenuItem;
import javafx.scene.control.ProgressIndicator;
import javafx.scene.input.MouseButton;
import javafx.scene.layout.HBox;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.stage.Modality;
import javafx.stage.Window;
import javafx.util.Callback;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.SearchableComboBox;

/**
 * NewDecodeCardsDialog.
 */
@Slf4j
public class DecodeCardListDialog {
  private final UndecoratedDialog<List<NewDecodeCard>> dialog;
  @Getter
  private ObservableList<DecodeCard> data;
  private ObservableList<ConsoleData> consoleOptions;
  private ObservableList<InputCard> inputOptions;
  private ProgressIndicator progressIndicator;
  private final CaesarDeviceController controller;
  private final Integer videoWallGroupId;

  /**
   * DecodeCardListDialog.
   */
  public DecodeCardListDialog(Integer videoWallGroupId, CaesarDeviceController controller,
                              Window owner) {
    this.controller = controller;
    this.videoWallGroupId = videoWallGroupId;
    // 初始化 Dialog
    dialog = new UndecoratedDialog<>();
    dialog.initModality(Modality.WINDOW_MODAL);
    dialog.initOwner(owner);
    dialog.setTitle(Bundle.getMessage("new_decode_card_dialog.title"));
    dialog.setHeaderText(Bundle.getMessage("new_decode_card_dialog.header_text"));

    // 添加按钮（OK 和 Cancel）
    dialog.getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);
    dialog.setResultConverter(dialogButton -> {
      ButtonBar.ButtonData buttonData = dialogButton == null ? null : dialogButton.getButtonData();
      List<NewDecodeCard> result = new ArrayList<>();
      if (buttonData == ButtonBar.ButtonData.OK_DONE) {
        for (DecodeCard card : data) {
          if (card.getConsoleData() == null || card.getInputCard() == null) {
            continue;
          }
          NewDecodeCard newDecodeCard = new NewDecodeCard();
          newDecodeCard.setDecodeCardId(card.getConsoleData().getId());
          newDecodeCard.setInputId(card.getInputCard().getInputId());
          newDecodeCard.setSlotId(card.getInputCard().getSlotId());
          newDecodeCard.setSn(card.getInputCard().getSn());
          result.add(newDecodeCard);
        }
      }
      return result;
    });

    // 数据列表
    data = FXCollections.observableArrayList();

    // 初始化 ChoiceBox 数据
    consoleOptions = FXCollections.observableArrayList();
    inputOptions = FXCollections.observableArrayList();

    // 加载动画
    progressIndicator = new ProgressIndicator();
    progressIndicator.setVisible(false);

    // ListView
    ListView<DecodeCard> listView = new ListView<>(data);
    listView.setPrefWidth(320);
    listView.setCellFactory(new Callback<ListView<DecodeCard>, ListCell<DecodeCard>>() {
      @Override
      public ListCell<DecodeCard> call(ListView<DecodeCard> param) {
        return new ListCell<DecodeCard>() {
          @Override
          protected void updateItem(DecodeCard item, boolean empty) {
            super.updateItem(item, empty);
            if (empty || item == null) {
              setGraphic(null);
            } else {
              SearchableComboBox<ConsoleData> conComboBox =
                  new SearchableComboBox<>(consoleOptions);
              SearchableComboBox<InputCard> inputComboBox = new SearchableComboBox<>(inputOptions);

              conComboBox.setPrefWidth(160);
              inputComboBox.setPrefWidth(110);

              // 设置转换器
              conComboBox.setConverter(new ConsoleDataStringConverter());
              inputComboBox.setConverter(new InputCardStringConverter());

              // 绑定数据
              conComboBox.setValue(item.getConsoleData());
              inputComboBox.setValue(item.getInputCard());

              // 监听 ChoiceBox 的变化
              conComboBox.getSelectionModel().selectedItemProperty()
                  .addListener((obs, oldVal, newVal) -> item.setConsoleData(newVal));
              inputComboBox.getSelectionModel().selectedItemProperty()
                  .addListener((obs, oldVal, newVal) -> item.setInputCard(newVal));

              // 将 ChoiceBox 放入 HBox
              HBox hbox = new HBox(10, conComboBox, inputComboBox);
              setGraphic(hbox);

              // 右键菜单
              ContextMenu contextMenu = new ContextMenu();
              MenuItem deleteMenuItem =
                  new MenuItem(Bundle.getMessage("new_decode_card_dialog.delete"));
              deleteMenuItem.setOnAction(event -> data.remove(item));
              contextMenu.getItems().add(deleteMenuItem);

              // 设置右键菜单
              setOnMouseClicked(event -> {
                if (event.getButton() == MouseButton.SECONDARY) {
                  contextMenu.show(this, event.getScreenX(), event.getScreenY());
                }
              });
            }
          }
        };
      }
    });

    // 添加按钮
    Button addButton = new Button(Bundle.getMessage("new_decode_card_dialog.add_input_card"));
    addButton.disableProperty().bind(progressIndicator.visibleProperty());
    addButton.setOnAction(event -> {
      // 添加新行，默认选择第一个选项
      if (!consoleOptions.isEmpty() && !inputOptions.isEmpty()) {
        data.add(new DecodeCard(consoleOptions.get(0), inputOptions.get(0)));
      }
    });
    // 删除按钮
    Button deleteButton = new Button(Bundle.getMessage("new_decode_card_dialog.delete_input_card"));
    deleteButton.disableProperty()
        .bind(listView.getSelectionModel().selectedItemProperty().isNull());
    deleteButton.setOnAction(event -> {
      DecodeCard selectedItem = listView.getSelectionModel().getSelectedItem();
      if (selectedItem != null) {
        data.remove(selectedItem);
      }
    });

    Label rxLabel = new Label("RX ID");
    rxLabel.setMinWidth(160);
    rxLabel.setAlignment(Pos.CENTER);
    Label inputCardLabel = new Label(Bundle.getMessage("new_decode_card_dialog.input_card"));
    inputCardLabel.setMinWidth(110);
    inputCardLabel.setAlignment(Pos.CENTER);
    HBox hbox = new HBox(10, rxLabel, inputCardLabel);

    // 布局
    StackPane stackPane = new StackPane();
    stackPane.getChildren().add(listView);
    stackPane.getChildren().add(progressIndicator);

    // 按钮布局
    HBox buttonBox = new HBox(10, addButton, deleteButton);
    VBox dialogContent = new VBox(10, buttonBox, hbox, stackPane);
    dialog.getDialogPane().setContent(dialogContent);

    // 初始加载数据
    loadDataAsync();
  }

  private void loadDataAsync() {
    // 显示加载动画
    progressIndicator.setVisible(true);

    controller.execute(() -> {
      // 模拟 HTTP 请求获取数据
      List<ConsoleData> consoles = fetchConsoleData();
      List<InputCard> inputs = fetchInputCardData();

      // 更新 UI
      Platform.runLater(() -> {
        consoleOptions.setAll(consoles);
        inputOptions.setAll(inputs);
        progressIndicator.setVisible(false);
      });
    });
  }

  private List<ConsoleData> fetchConsoleData() {
    return controller.getDataModel().getConfigDataManager().getActiveConsolesWithoutVpcon()
        .parallelStream().filter(item -> {
          ExtenderData extenderData = item.getExtenderData(0);
          if (extenderData != null
              && extenderData.getExtenderStatusInfo().getInterfaceCount() == 1) {
            CaesarConstants.Extender.ExtenderVideoResolutionType type =
                extenderData.getExtenderStatusInfo().getVideoResolutionType();
            return CaesarConstants.Extender.ExtenderVideoResolutionType.VIDEO_4K_60HZ.equals(
                type)
                || CaesarConstants.Extender.ExtenderVideoResolutionType.VIDEO_4K_30HZ.equals(
                type)
                || CaesarConstants.Extender.ExtenderVideoResolutionType.VIDEO_2K.equals(type);
          }
          return false;
        }).collect(Collectors.toList());
  }

  private List<InputCard> fetchInputCardData() {
    return controller.getKaitoApi()
        .kaitoInputCardListVideowallGroupIdGet(videoWallGroupId);
  }

  /**
   * 显示对话框.
   */
  public Optional<List<NewDecodeCard>> showAndWait() {
    return dialog.showAndWait();
  }

}
