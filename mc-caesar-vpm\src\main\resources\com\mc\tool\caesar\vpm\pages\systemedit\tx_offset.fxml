<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.CheckBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.RowConstraints?>
<GridPane alignment="CENTER_LEFT" maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity"
  minWidth="-Infinity" prefHeight="400.0" prefWidth="300.0" xmlns="http://javafx.com/javafx/8.0.171"
  xmlns:fx="http://javafx.com/fxml/1">
  <columnConstraints>
    <ColumnConstraints hgrow="SOMETIMES" maxWidth="150.0" minWidth="35.0" prefWidth="138.0"/>
    <ColumnConstraints hgrow="SOMETIMES" maxWidth="463.0" minWidth="10.0" prefWidth="162.0"/>
  </columnConstraints>
  <rowConstraints>
    <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
    <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
    <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
    <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
    <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
    <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
    <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
    <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
    <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
  </rowConstraints>
  <children>
    <Label fx:id="deviceNameLabel" text="%txOffset.dialog.deviceName" GridPane.rowIndex="1"/>
    <Label fx:id="totalResolutionLabel" text="%txOffset.dialog.totalResolution"
      GridPane.rowIndex="2"/>
    <Label fx:id="resolutionLabel1" text="%txOffset.dialog.resolution1" GridPane.rowIndex="3"/>
    <Label fx:id="resolutionOffsetLabelX1" text="%txOffset.dialog.offsetX" GridPane.rowIndex="4"/>
    <Label fx:id="resolutionOffsetLabelY1" text="%txOffset.dialog.offsetY" GridPane.rowIndex="5"/>
    <Label fx:id="resolutionLabel2" text="%txOffset.dialog.resolution2" GridPane.rowIndex="6"/>
    <Label fx:id="resolutionOffsetLabelX2" text="%txOffset.dialog.offsetX" GridPane.rowIndex="7"/>
    <Label fx:id="resolutionOffsetLabelY2" text="%txOffset.dialog.offsetY" GridPane.rowIndex="8"/>
    <TextField fx:id="deviceName" GridPane.columnIndex="1" GridPane.rowIndex="1"/>
    <TextField fx:id="resolutionOffsetY2" GridPane.columnIndex="1" GridPane.rowIndex="8"/>
    <TextField fx:id="resolutionOffsetX2" GridPane.columnIndex="1" GridPane.rowIndex="7"/>
    <TextField fx:id="resolutionOffsetY1" GridPane.columnIndex="1" GridPane.rowIndex="5"/>
    <TextField fx:id="resolutionOffsetX1" GridPane.columnIndex="1" GridPane.rowIndex="4"/>
    <HBox alignment="CENTER_LEFT" GridPane.columnIndex="1" GridPane.rowIndex="2">
      <children>
        <TextField fx:id="totalResolutionX"/>
        <ImageView fitHeight="25.0" fitWidth="25.0" pickOnBounds="true" preserveRatio="true"/>
        <TextField fx:id="totalResolutionY"/>
      </children>
      <GridPane.margin>
        <Insets/>
      </GridPane.margin>
    </HBox>
    <HBox alignment="CENTER_LEFT" GridPane.columnIndex="1" GridPane.rowIndex="3">
      <children>
        <TextField fx:id="resolutionX1"/>
        <ImageView fitHeight="25.0" fitWidth="25.0" pickOnBounds="true" preserveRatio="true"/>
        <TextField fx:id="resolutionY1"/>
      </children>
    </HBox>
    <HBox alignment="CENTER_LEFT" GridPane.columnIndex="1" GridPane.rowIndex="6">
      <children>
        <TextField fx:id="resolutionX2"/>
        <ImageView fitHeight="25.0" fitWidth="25.0" pickOnBounds="true" preserveRatio="true"/>
        <TextField fx:id="resolutionY2"/>
      </children>
    </HBox>
    <CheckBox fx:id="offsetSettings" mnemonicParsing="false"
      text="%txOffset.dialog.activateSetting"/>
  </children>
</GridPane>
