package com.mc.tool.caesar.vpm.pages.systemedit.datamodel;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.ResolutionData;
import com.mc.tool.framework.systemedit.datamodel.MultimediaInterfaceType;
import com.mc.tool.framework.systemedit.datamodel.Resolution;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import java.lang.ref.WeakReference;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.ReadOnlyObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.NumberPropertyItem;
import org.controlsfx.control.PropertySheet;
import org.controlsfx.control.StringPropertyItem;

/**
 * .
 */
@Slf4j
public class CaesarCpuTerminal extends CaesarTerminalBase {
  private final ObjectProperty<WeakReference<CpuData>> cpuDataProperty =
      new SimpleObjectProperty<>(new WeakReference<>(null));
  private final ObservableList<PropertySheet.Item> sysOperationProperties =
      FXCollections.observableArrayList();

  /**
   * Constructor.
   *
   * @param cpuData cpu data.
   */
  public CaesarCpuTerminal(CpuData cpuData) {
    setCpuData(cpuData);
  }

  @Override
  public void reset() {
    super.reset();
    CpuData realCpu = cpuDataProperty.get().get();
    if (realCpu != null && realCpu.getNameProperty() != null) {
      nameProperty.unbindBidirectional(realCpu.getNameProperty());
    }
  }

  /**
   * 设置cpu数据.
   *
   * @param cpuData cpu数据.
   */
  public void setCpuData(CpuData cpuData) {
    final CpuData oldCpuData = this.cpuDataProperty.get().get();
    this.cpuDataProperty.set(new WeakReference<>(cpuData));
    ExtenderData extenderData = null;
    if (cpuData != null) {
      if (oldCpuData != null && oldCpuData.getNameProperty() != null) {
        nameProperty.unbindBidirectional(oldCpuData.getNameProperty());
      }
      nameProperty.bindBidirectional(cpuData.getNameProperty());
      extenderData = cpuData.getExtenderData(0);
      if (extenderData == null) {
        log.debug("Empty extenderdata!");
      } else {
        idProperty.set(extenderData.getId());
      }
    }

    onlineProperty.set(
        cpuData != null
            && extenderData != null
            && extenderData.isStatusActive()
            && (extenderData.getPort() != 0 || extenderData.getRdPort() != 0));

    targetDeviceConnected.set(
        extenderData != null
            && extenderData.getExtenderStatusInfo().getVideoInput().getChStatus(0));
    targetDeviceConnected2.set(
        extenderData != null
            && extenderData.getExtenderStatusInfo().getVideoInput().getChStatus(1));

    port1ConnectedProperty.set(onlineProperty.get() && extenderData.getPort() != 0);
    port2ConnectedProperty.set(onlineProperty.get() && extenderData.getRdPort() != 0);

    if (extenderData != null) {
      if (extenderData.getExtenderStatusInfo().getSpecialExtType()
          == CaesarConstants.Extender.SpecialExtenderType.HW_ATC
          && extenderData.getExtenderStatusInfo().getHwSpecial().isIcron()) {
        multimediaInterfaceType.set(MultimediaInterfaceType.USB);
      } else if (extenderData.getExtenderStatusInfo().getInterfaceType()
          == CaesarConstants.Extender.ExtenderInterfaceType.INTERFACE_TYPE_DVI) {
        multimediaInterfaceType.set(MultimediaInterfaceType.DVI);
      } else if (extenderData.getExtenderStatusInfo().getInterfaceType()
          == CaesarConstants.Extender.ExtenderInterfaceType.INTERFACE_TYPE_DP) {
        multimediaInterfaceType.set(MultimediaInterfaceType.DP);
      } else {
        multimediaInterfaceType.set(MultimediaInterfaceType.HDMI);
      }
    }

    if (oldCpuData != cpuData) {
      initCpuProperties();
    }

    updateConnectors();
  }

  private void initCpuProperties() {
    properties.clear();
    CpuData realCpu = cpuDataProperty.get().get();
    if (realCpu == null) {
      return;
    }
    // 名称
    StringPropertyItem item = new StringPropertyItem(realCpu.getNameProperty());
    item.setName(Bundle.NbBundle.getMessage("name"));
    item.setEditable(false);

    // ID
    NumberPropertyItem idItem = new NumberPropertyItem(realCpu.getIdProperty(), Integer.class);
    idItem.setName(Bundle.NbBundle.getMessage("id"));
    idItem.setEditable(false);

    sysOperationProperties.clear();
    sysOperationProperties.add(idItem);
    sysOperationProperties.add(item);
  }

  public CaesarCpuTerminal() {}

  @Override
  public boolean isRx() {
    return false;
  }

  @Override
  public boolean isTx() {
    return true;
  }

  @Override
  public ExtenderData getExtenderData() {
    CpuData realCpu = cpuDataProperty.get().get();
    if (isValid() && realCpu != null) {
      return realCpu.getExtenderData(0);
    } else {
      return null;
    }
  }

  public CpuData getCpuData() {
    return cpuDataProperty.get().get();
  }

  public ObjectProperty<WeakReference<CpuData>> cpuDataProperty() {
    return cpuDataProperty;
  }

  public ReadOnlyObjectProperty<VisualEditNode> readOnlyParentProperty() {
    return parentProperty;
  }

  @Override
  public Resolution getResolution(int index) {
    ExtenderData extenderData = getExtenderData();
    if (extenderData != null) {
      ResolutionData resolutionData = extenderData.getResolution(index);
      return new Resolution((int) resolutionData.getWidth(), (int) resolutionData.getHeight());
    } else {
      return new Resolution(0, 0);
    }
  }

  @Override
  public boolean canSeperate() {
    return getExtenderData() != null
        && getExtenderData().getExtenderStatusInfo().getInterfaceCount() > 1;
  }
}
