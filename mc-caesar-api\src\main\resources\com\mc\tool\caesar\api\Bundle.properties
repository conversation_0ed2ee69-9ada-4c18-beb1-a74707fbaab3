Arrangement.M1x4=1 x 4
Arrangement.M2x2=2 x 2
FunctionKey.command.connect=Full Connect (X=RX, Y=TX)
FunctionKey.command.connectvideo=Video Connect (X=RX, Y=TX)
FunctionKey.command.disconnect=Disconnect (X=RX)
FunctionKey.command.get=Get(Full) (X=RX)
FunctionKey.command.getvideo=Get Video (X=RX)
FunctionKey.command.noFunction=\ 
FunctionKey.command.private=Private Connect (X=RX, Y=TX)
FunctionKey.command.push=Push(Full) (X=RX)
FunctionKey.command.pushvideo=Push(Video) (X=RX)
FunctionKey.command.login=Login User (X=RX, Y=USER)
Keyboard.DE_CH_150G=German (CH, 150G)
Keyboard.DE_DE_129=German (DE, 129)
Keyboard.EN_GB_166=English (GB, 166)
Keyboard.EN_GB_168=English (GB, 168)
Keyboard.EN_US_103P=English (US, 103P)
Keyboard.FR_BE_120=French (BE, 120)
Keyboard.FR_CH_150F=French (CH, 150F)
Keyboard.FR_FR_189=French (FR, 189)
Keyboard.RU_1251=Russian (RU, 1251)
MultiviewLayout.fullScreen=FullScreen
MultiviewLayout.twoGrid=TwoGrid
MultiviewLayout.fourGrid=FourGrid
MultiviewLayout.sideBar=SideBar
OsdPosition.bottom.left=Bottom Left
OsdPosition.bottom.right=Bottom Right
OsdPosition.centered=Centered
OsdPosition.custom=Custom
OsdPosition.top.left=Top Left
OsdPosition.top.right=Top Right
Owner.Screen1=Screen 1
Owner.Screen2=Screen 2
Owner.Screen3=Screen 3
Owner.Screen4=Screen 4
Owner.Shared=Shared
SuspendPosition.topcenter=Top Center
SuspendPosition.topleft=Top Left
SuspendPosition.topright=Top Right
TeraConnectionModel.file.conflict.cpu.message=You have tried to upload a matrix configuration into a SNMP board. This is not allowed.
TeraConnectionModel.file.conflict.snmp.message=You have tried to upload a SNMP configuration into a KVM matrix. This is not allowed.
TeraConnectionModel.file.conflict.title=File version conflict
TeraVersion.MATX008C=MATX008C
TeraVersion.MATX016=MATX016
TeraVersion.MATX048=MATX048
TeraVersion.MATX048C=MATX048C
TeraVersion.MATX080=MATX080
TeraVersion.MATX080C=MATX080C
TeraVersion.MATX160=MATX160
TeraVersion.MATX21R=MATX21R
TeraVersion.MATX288=MATX288
TeraVersion.MATX576M=MATX576M
TeraVersion.MATX576S=MATX576S
TeraVersion.MATX6BP=MATX6BP
TeraVersion.TERA048=TERA048
TeraVersion.TERA080=TERA080
TeraVersion.TERA160=TERA160
TeraVersion.TERA288=TERA288
TeraVersion.TERASW=TERASW
TimeZone.UTC_0=(GMT) Coordinated Universal Time, Casablanca, Dublin, Lisbon, London
TimeZone.UTC_MINUS_1=(GMT -01:00) Azores, Cape Verde Is.
TimeZone.UTC_MINUS_10=(GMT -10:00) Hawaii
TimeZone.UTC_MINUS_11=(GMT -11:00) Coordinated Universal Time-11
TimeZone.UTC_MINUS_12=(GMT -12:00) International Date Line West
TimeZone.UTC_MINUS_2=(GMT -02:00) Coordinated Universal Time-02
TimeZone.UTC_MINUS_3=(GMT -03:00) Brasilia, Buenos Aires, Cayenne, Greenland
TimeZone.UTC_MINUS_4=(GMT -04:00) Atlantic Time (Canada)
TimeZone.UTC_MINUS_5=(GMT -05:00) Eastern Time (US & Canada)
TimeZone.UTC_MINUS_6=(GMT -06:00) Central Time (US & Canada)
TimeZone.UTC_MINUS_7=(GMT -07:00) Mountain Time (US & Canada)
TimeZone.UTC_MINUS_8=(GMT -08:00) Pacific Time (US & Canada)
TimeZone.UTC_MINUS_9=(GMT -09:00) Alaska
TimeZone.UTC_PLUS_1=(GMT +01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna
TimeZone.UTC_PLUS_10=(GMT +10:00) Brisbane, Canberra, Guam, Melbourne, Sydney, Vladivostok
TimeZone.UTC_PLUS_11=(GMT +11:00) Solomon Is., New Caledonia
TimeZone.UTC_PLUS_12=(GMT +12:00) Auckland, Fiji, Marshall Is. 12, Wellington
TimeZone.UTC_PLUS_2=(GMT +02:00) Athens, Bucharest, Cairo, Helsinki, Istanbul, Jerusalem
TimeZone.UTC_PLUS_3=(GMT +03:00) Baghdad, Kuwait, Moscow, St. Petersburg, Volgograd
TimeZone.UTC_PLUS_4=(GMT +04:00) Abu Dhabi, Baku, Muscat
TimeZone.UTC_PLUS_5=(GMT +05:00) Islamabad, Karachi
TimeZone.UTC_PLUS_6=(GMT +06:00) Astana, Dhaka, Novosibirsk
TimeZone.UTC_PLUS_7=(GMT +07:00) Bangkok, Hanoi, Jakarta
TimeZone.UTC_PLUS_8=(GMT +08:00) Beijing, Hong Kong, Irkutsk, Kuala Lumpur, Perth, Singapore
TimeZone.UTC_PLUS_9=(GMT +09:00) Osaka, Sapporo, Seoul, Tokyo
VideoMode.1024x768=1024 x 768
VideoMode.1280x1024=1280 x 1024
VideoMode.1280x800=1280 x 800
VideoMode.1600x1200=1600 x 1200
VideoMode.1680x1050=1680 x 1050
VideoMode.1920x1080=1920 x 1080
VideoMode.1920x1200=1920 x 1200
VideoMode.800x600=800 x 600
VideoMode.var=Variable
FunctionKey.command.activeVideoWallScenario=Activate Scenario(X=Scenario)
FunctionKey.command.activeVideoWallScenarioWindow=Switch Scenario Window Source
FunctionKey.command.switchMultiViewLayout=SwitchMultiViewLayout
FunctionKey.command.switchMultiViewSignal=SwitchMultiViewSignal
multiview.channel=Channel
FunctionKey.command.switchMultiViewPush=FourScreenPush(X=con)
FunctionKey.command.switchMultiViewGet=FourScreenGet(X=con)
multiview.output_mode=interfaceMode
ext.dp.mode.single=SingleDpMode
ext.dp.mode.double=DoubleDpMode
