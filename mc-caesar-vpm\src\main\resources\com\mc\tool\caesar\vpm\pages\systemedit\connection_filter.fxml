<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<HBox id="root-container" fx:id="rootContainer" alignment="CENTER_LEFT" fillHeight="false"
  maxHeight="14.0" minHeight="14.0" stylesheets="@connection_filter.css"
  xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
  <Region id="fullRegion" fx:id="fullRegion" maxHeight="13.0" maxWidth="13.0" prefHeight="13.0"
    prefWidth="13.0"/>
  <Label text="%ConnectionFilter.full"/>
  <Region id="videoRegion" fx:id="videoRegion" maxHeight="13.0" maxWidth="13.0" prefHeight="13.0"
    prefWidth="13.0">
    <HBox.margin>
      <Insets left="50.0"/>
    </HBox.margin>
  </Region>
  <Label text="%ConnectionFilter.video"/>
  <Region id="privateRegion" fx:id="privateRegion" maxHeight="13.0" maxWidth="13.0"
    prefHeight="13.0" prefWidth="13.0">
    <HBox.margin>
      <Insets left="50.0"/>
    </HBox.margin>
  </Region>
  <Label text="%ConnectionFilter.private"/>
  <Region id="privateRegion" fx:id="shareRegion" maxHeight="13.0" maxWidth="13.0" prefHeight="13.0"
    prefWidth="13.0">
    <HBox.margin>
      <Insets left="50.0"/>
    </HBox.margin>
  </Region>
  <Label text="%ConnectType.share_video"/>
  <Region id="privateRegion" fx:id="shareFullRegion" maxHeight="13.0" maxWidth="13.0"
    prefHeight="13.0" prefWidth="13.0">
    <HBox.margin>
      <Insets left="50.0"/>
    </HBox.margin>
  </Region>
  <Label text="%ConnectType.share_full"/>
</HBox>
