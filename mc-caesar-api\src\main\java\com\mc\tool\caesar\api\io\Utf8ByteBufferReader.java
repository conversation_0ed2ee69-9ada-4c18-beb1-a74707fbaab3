/*
 * Javolution - Java(TM) Solution for Real-Time and Embedded Systems Copyright (C) 2012 - Javolution
 * (http://javolution.org/) All rights reserved.
 *
 * Permission to use, copy, modify, and distribute this software is freely granted, provided that
 * this notice is preserved.
 */

package com.mc.tool.caesar.api.io;

import java.io.CharConversionException;
import java.io.IOException;
import java.io.Reader;
import java.nio.BufferUnderflowException;
import java.nio.ByteBuffer;

/**
 * <p>
 * A UTF-8 <code>java.nio.ByteBuffer</code> reader.
 * </p>
 *
 * <p>
 * This reader can be used for efficient decoding of native byte buffers (e.g.
 * <code>MappedByteBuffer</code>), high-performance messaging (no intermediate buffer), etc.
 * </p>
 *
 * <p>
 * This reader supports surrogate <code>char</code> pairs (representing characters in the range
 * [U+10000 .. U+10FFFF]). It can also be used to read characters unicodes (31 bits) directly (ref.
 * {@link #read()}).
 * </p>
 *
 * <p>
 * Each invocation of one of the <code>read()</code> methods may cause one or more bytes to be read
 * from the underlying byte buffer. The end of stream is reached when the byte buffer position and
 * limit coincide.
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">Jean-Marie Dautelle</a>
 * @version 2.0, December 9, 2004
 * @see Utf8ByteBufferWriter
 */
public final class Utf8ByteBufferReader extends Reader {

  /**
   * Holds the byte buffer source.
   */
  private ByteBuffer byteBuffer;

  /**
   * Default constructor.
   */
  public Utf8ByteBufferReader() {

  }

  /**
   * Constructor for Initializing to Read from the Specified Byte Buffer.
   *
   * @param buf ByteBuffer to Read From
   */
  public Utf8ByteBufferReader(ByteBuffer buf) {
    byteBuffer = buf;
  }

  /**
   * Sets the <code>ByteBuffer</code> to use for reading available bytes from current buffer
   * position.
   *
   * @param buf the <code>ByteBuffer</code> source.
   * @return this UTF-8 reader.
   * @throws IllegalStateException if this reader is being reused and it has not been {@link #close
   *                               closed} or {@link #reset reset}.
   */
  public Utf8ByteBufferReader setInput(ByteBuffer buf) {
    if (byteBuffer != null) {
      throw new IllegalStateException("Reader not closed or reset");
    }
    byteBuffer = buf;
    return this;
  }

  /**
   * Indicates if this stream is ready to be read.
   *
   * @return <code>true</code> if the byte buffer has remaining bytes to read; <code>false</code>
   *     otherwise.
   * @throws IOException if an I/O error occurs.
   */
  @Override
  public boolean ready() throws IOException {
    if (byteBuffer != null) {
      return byteBuffer.hasRemaining();
    } else {
      throw new IOException("Reader closed");
    }
  }

  /**
   * Closes and {@link #reset resets} this reader for reuse.
   *
   * @throws IOException if an I/O error occurs.
   */
  @Override
  public void close() throws IOException {
    if (byteBuffer != null) {
      reset();
    }
  }

  /**
   * Reads a single character. This method does not block, <code>-1</code> is returned if the
   * buffer's limit has been reached.
   *
   * @return the 31-bits Unicode of the character read, or -1 if there is no more remaining bytes to
   *     be read.
   * @throws IOException if an I/O error occurs (e.g. incomplete character sequence being read).
   */
  @Override
  public int read() throws IOException {
    if (byteBuffer != null) {
      if (byteBuffer.hasRemaining()) {
        byte bb = byteBuffer.get();
        return bb >= 0 ? bb : read2(bb);
      } else {
        return -1;
      }
    } else {
      throw new IOException("Reader closed");
    }
  }

  /**
   * Reads characters into a portion of an array. This method does not block.
   *
   * <p>
   * Note: Characters between U+10000 and U+10FFFF are represented by surrogate pairs (two
   * <code>char</code>).
   * </p>
   *
   * @param cbuf the destination buffer.
   * @param off  the offset at which to start storing characters.
   * @param len  the maximum number of characters to read
   * @return the number of characters read, or -1 if there is no more byte remaining.
   * @throws IOException if an I/O error occurs.
   */
  @Override
  public int read(char[] cbuf, int off, int len) throws IOException {
    if (byteBuffer == null) {
      throw new IOException("Reader closed");
    }
    final int off_plus_len = off + len;
    int remaining = byteBuffer.remaining();
    if (remaining <= 0) {
      return -1;
    }
    for (int i = off; i < off_plus_len; ) {
      if (remaining-- > 0) {
        byte bb = byteBuffer.get();
        if (bb >= 0) {
          cbuf[i++] = (char) bb; // Most common case.
        } else {
          if (i < off_plus_len - 1) { // Up to two 'char' can be read.
            int code = read2(bb);
            remaining = byteBuffer.remaining(); // Recalculates.
            if (code < 0x10000) {
              cbuf[i++] = (char) code;
            } else if (code <= 0x10ffff) { // Surrogates.
              cbuf[i++] = (char) (((code - 0x10000) >> 10) + 0xd800);
              cbuf[i++] = (char) (((code - 0x10000) & 0x3ff) + 0xdc00);
            } else {
              throw new CharConversionException("Cannot convert U+" + Integer.toHexString(code)
                  + " to char (code greater than U+10FFFF)");
            }
          } else { // Not enough space in destination (go back).
            byteBuffer.position(byteBuffer.position() - 1);
            remaining++;
            return i - off;
          }
        }
      } else {
        return i - off;
      }
    }
    return len;
  }

  /**
   * Reads characters into the specified appendable. This method does not block.
   *
   * <p>
   * Note: Characters between U+10000 and U+10FFFF are represented by surrogate pairs (two
   * <code>char</code>).
   * </p>
   *
   * @param dest the destination buffer.
   * @throws IOException if an I/O error occurs.
   */
  public void read(Appendable dest) throws IOException {
    if (byteBuffer == null) {
      throw new IOException("Reader closed");
    }
    while (byteBuffer.hasRemaining()) {
      byte bb = byteBuffer.get();
      if (bb >= 0) {
        dest.append((char) bb); // Most common case.
      } else {
        int code = read2(bb);
        if (code < 0x10000) {
          dest.append((char) code);
        } else if (code <= 0x10ffff) { // Surrogates.
          dest.append((char) (((code - 0x10000) >> 10) + 0xd800));
          dest.append((char) (((code - 0x10000) & 0x3ff) + 0xdc00));
        } else {
          throw new CharConversionException("Cannot convert U+" + Integer.toHexString(code)
              + " to char (code greater than U+10FFFF)");
        }
      }
    }
  }

  // Reads one full character, throws CharConversionException if limit reached.
  private int read2(byte bb) throws IOException {
    try {
      // Decodes UTF-8.
      if (bb >= 0 && moreBytes == 0) {
        // 0xxxxxxx
        return bb;
      } else if ((bb & 0xc0) == 0x80 && moreBytes != 0) {
        // 10xxxxxx (continuation byte)
        code = (code << 6) | (bb & 0x3f); // Adds 6 bits to code.
        if (--moreBytes == 0) {
          return code;
        } else {
          return read2(byteBuffer.get());
        }
      } else if ((bb & 0xe0) == 0xc0 && moreBytes == 0) {
        // 110xxxxx
        code = bb & 0x1f;
        moreBytes = 1;
        return read2(byteBuffer.get());
      } else if ((bb & 0xf0) == 0xe0 && moreBytes == 0) {
        // 1110xxxx
        code = bb & 0x0f;
        moreBytes = 2;
        return read2(byteBuffer.get());
      } else if ((bb & 0xf8) == 0xf0 && moreBytes == 0) {
        // 11110xxx
        code = bb & 0x07;
        moreBytes = 3;
        return read2(byteBuffer.get());
      } else if ((bb & 0xfc) == 0xf8 && moreBytes == 0) {
        // 111110xx
        code = bb & 0x03;
        moreBytes = 4;
        return read2(byteBuffer.get());
      } else if ((bb & 0xfe) == 0xfc && moreBytes == 0) {
        // 1111110x
        code = bb & 0x01;
        moreBytes = 5;
        return read2(byteBuffer.get());
      } else {
        throw new CharConversionException("Invalid UTF-8 Encoding");
      }
    } catch (BufferUnderflowException ex) {
      throw new CharConversionException("Incomplete Sequence");
    }
  }

  private int code;

  private int moreBytes;


  /**
   * .
   */
  @Override
  public void reset() {
    byteBuffer = null;
    code = 0;
    moreBytes = 0;
  }

  /**
   * .
   *
   * @param byteBuffer ByteBuffer to use as input
   * @return Reference to this UTF8ByteBufferReader
   * @deprecated Replaced by {@link #setInput(ByteBuffer)}
   */
  public Utf8ByteBufferReader setByteBuffer(ByteBuffer byteBuffer) {
    return this.setInput(byteBuffer);
  }

}
