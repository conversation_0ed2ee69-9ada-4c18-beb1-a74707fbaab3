package com.mc.tool.caesar.vpm.userright;

import com.mc.tool.caesar.vpm.devices.CaesarUserRightGetter;
import com.mc.tool.framework.systemedit.datamodel.VideoWallFunc;

/**
 * .
 */
public class CaesarSubMatrixUserRightDecorator extends CaesarUserRightDecoratorBase {

  public CaesarSubMatrixUserRightDecorator(CaesarUserRightGetter userRight) {
    super(userRight);
  }

  @Override
  public boolean isDeviceConnectable() {
    return false;
  }

  @Override
  public boolean isCrossScreenConnectable() {
    return false;
  }

  @Override
  public boolean isVideoWallEditable(VideoWallFunc func) {
    return false;
  }

  @Override
  public boolean isVideoWallConnectable(VideoWallFunc func) {
    return false;
  }

  @Override
  public boolean isVideoWallCreateDeletable() {
    return false;
  }

  @Override
  public boolean isCrossScreenCreateDeletable() {
    return false;
  }

  @Override
  public boolean isMultiScreenCreateDeletable() {
    return false;
  }

  @Override
  public boolean isTxGroupCreateDeletable() {
    return false;
  }
}
