package com.mc.tool.caesar.vpm.pages.hotkeyconfiguration;

import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.caesar.vpm.pages.hotkeyconfiguration.Bundle.NbBundle;
import com.mc.tool.framework.interfaces.Page;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.scene.layout.Pane;
import javafx.util.Pair;

/**
 * .
 */
public class CaesarHotkeyConfigurationPage implements Page {
  public static final String NAME = "hotkeyconfiguration";
  public static final String CON_HOTKET_TAB = "conhotkeytab";
  public static final String CON_MACRO_TAB = "conmacrotab";
  private final CaesarHotkeyConfigurationPageView view;
  private final BooleanProperty visibility = new SimpleBooleanProperty(true);

  public CaesarHotkeyConfigurationPage(CaesarEntity entity) {
    view = new CaesarHotkeyConfigurationPageView();
    view.setDeviceController(entity.getController());
  }

  @Override
  public String getTitle() {
    return NbBundle.getMessage("HotkeyConfigurationPage.title");
  }

  @Override
  public String getName() {
    return NAME;
  }

  @Override
  public Pane getView() {
    return view;
  }

  @Override
  public String getStyleClass() {
    return "caesar-hotkeyconfiguration-page";
  }

  @Override
  public BooleanProperty getVisibleProperty() {
    return visibility;
  }

  @Override
  public void showObject(Object object) {
    Pair<String, ConsoleData> pair = (Pair<String, ConsoleData>) object;
    switch (pair.getKey()) {
      case CON_HOTKET_TAB:
        view.getTabPane().getSelectionModel().select(view.getConHotkeyTab());
        view.getConHotkeyView().selectSourceItem(pair.getValue());
        break;
      case CON_MACRO_TAB:
        view.getTabPane().getSelectionModel().select(view.getConsoleMacroKeyTab());
        view.getConsoleMacroKeyView().selectSourceItem(pair.getValue());
        break;
      default:
        break;
    }
  }
}
