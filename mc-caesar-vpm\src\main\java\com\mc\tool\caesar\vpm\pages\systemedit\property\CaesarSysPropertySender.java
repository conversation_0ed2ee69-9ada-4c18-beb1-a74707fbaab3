package com.mc.tool.caesar.vpm.pages.systemedit.property;

import com.mc.tool.caesar.api.datamodel.SystemData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarSysPropertySender implements CaesarPropertySender<String> {

  private final CaesarDeviceController controller;
  private final SystemData systemData;

  /** Constructor. */
  public CaesarSysPropertySender(CaesarDeviceController controller, SystemData systemData) {
    this.controller = controller;
    this.systemData = systemData;
  }

  @Override
  public void sendValue(String value) {
    try {
      systemData.setDevice(value);
      controller.getDataModel().sendSystemData();
    } catch (DeviceConnectionException | BusyException ex) {
      log.error("Fail to send value!", ex);
    }
  }
}
