package com.mc.tool.caesar.vpm.gui;

import com.mc.common.control.TextWrapper;
import com.mc.common.util.RunUtil;
import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.util.GraphSelectionModel;
import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.pages.systemedit.CaesarSystemEditPage;
import com.mc.tool.caesar.vpm.pages.systemedit.CaesarSystemEditPageView;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.update.UpdateData;
import com.mc.tool.caesar.vpm.view.CaesarView;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.view.MatrixCellSkin;
import com.mc.tool.framework.systemedit.view.TerminalCellSkin;
import com.mc.tool.framework.utility.InjectorProvider;
import com.sun.javafx.scene.control.skin.ContextMenuContent.MenuItemContainer;
import java.awt.Toolkit;
import java.awt.datatransfer.Clipboard;
import java.awt.datatransfer.StringSelection;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.Collection;
import java.util.function.Predicate;
import javafx.geometry.Bounds;
import javafx.scene.Node;
import javafx.scene.control.Button;
import javafx.scene.control.CheckMenuItem;
import javafx.scene.control.ListCell;
import javafx.scene.control.ScrollPane;
import javafx.scene.control.TableCell;
import javafx.scene.control.TreeItem;
import javafx.scene.input.KeyCode;
import javafx.stage.Stage;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.testfx.api.FxRobot;
import org.testfx.assertions.api.Assertions;
import org.testfx.framework.junit.ApplicationTest;
import org.testfx.service.query.NodeQuery;

/**
 * .
 */
public class AppGuiTestBase extends ApplicationTest {

  @Override
  public void start(Stage stage) throws Exception {
    new CaesarView().start(stage);
  }

  /**
   * .
   */
  @BeforeClass
  public static void setupProperties() {
    System.setProperty("headless.geometry", "1920x1080-32");
    System.setProperty("testfx.robot", "glass");
    System.setProperty("testfx.headless", "true");
    System.setProperty("prism.order", "sw");
    System.setProperty("prism.text", "t2k");
  }

  /**
   * .
   */
  @Before
  public void beforeAll() {
    login();
    for (int i = 0; i < 22; i++) {
      clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
    }
  }

  /**
   * .
   */
  @After
  public void afterAll() {
    // 关闭页面
    clickOn(GuiTestConstants.BUTTON_LIST_MENU);
    clickOn(GuiTestConstants.MENU_CLOSE_ALL);
  }

  protected <T extends Node> Node lookupTableCell(int rowIndex, String columnId, Class<T> clazz) {
    return lookup(".table-cell")
        .match(
            (item) -> {
              if (item instanceof TableCell) {
                TableCell cell = (TableCell) item;

                return checkParentType(cell, clazz)
                    && cell.getTableColumn().getId().equals(columnId)
                    && cell.getIndex() == rowIndex;
              } else {
                return false;
              }
            })
        .query();
  }

  protected <T extends Node> Node lookupTableCell(NodeQuery query, int rowIndex, String columnId) {
    return query
        .lookup(".table-cell")
        .match(
            (item) -> {
              if (item instanceof TableCell) {
                TableCell cell = (TableCell) item;
                return cell.getTableColumn().getId().equals(columnId)
                    && cell.getIndex() == rowIndex;
              } else {
                return false;
              }
            })
        .query();
  }

  protected <T extends Node> Node lookupFirstTableCell(FxRobot robot, int rowIndex) {
    return robot
        .lookup(".table-cell")
        .match(
            (item) -> {
              if (item instanceof TableCell) {
                TableCell cell = (TableCell) item;

                return cell.getIndex() == rowIndex;
              } else {
                return false;
              }
            })
        .query();
  }

  protected <T extends Node> Node lookupFirstTableCell(NodeQuery query, int rowIndex) {
    return query
        .lookup(".table-cell")
        .match(
            (item) -> {
              if (item instanceof TableCell) {
                TableCell cell = (TableCell) item;

                return cell.getIndex() == rowIndex;
              } else {
                return false;
              }
            })
        .query();
  }

  protected <T extends Node> Node lookupFirstListCell(NodeQuery query, int rowIndex) {
    return query
        .lookup(".list-cell")
        .match(
            (item) -> {
              if (item instanceof ListCell) {
                ListCell cell = (ListCell) item;
                return cell.getIndex() == rowIndex;
              } else {
                return false;
              }
            })
        .query();
  }

  protected <T extends Node> boolean checkParentType(Node node, Class<T> parentType) {
    if (parentType.isInstance(node)) {
      return true;
    } else if (node == null || node.getParent() == null) {
      return false;
    } else {
      return checkParentType(node.getParent(), parentType);
    }
  }

  protected void textInTableCell(Node cell, String text) {
    doubleClickOn(cell);
    write(text);
    type(KeyCode.ENTER);
  }

  protected void login() {

    clickOn(GuiTestConstants.BUTTON_CONNET_DEV);
    clickOn(GuiTestConstants.TEXT_IP_INPUT);
    write(GuiTestConstants.INPUT_IP);
    clickOn(GuiTestConstants.TEXT_USER_INPUT);
    write(GuiTestConstants.INPUT_USER);
    clickOn(GuiTestConstants.TEXT_PWD_INPUT);
    write(GuiTestConstants.INPUT_PWD);
    clickOn(GuiTestConstants.BUTTON_CONFIRM);

    ApplicationBase applicationBase =
        InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    int oldSize = applicationBase.getEntityMananger().getAllEntity().size();
    while (applicationBase.getEntityMananger().getAllEntity().size() - oldSize != 1) {
      try {
        Thread.sleep(100);
      } catch (InterruptedException exc) {
        Assert.fail("Expect no exception!");
      }
    }
    try {
      Thread.sleep(1000);
    } catch (InterruptedException exc) {
      Assert.fail("Expect no exception!");
    }

    clickOn(GuiTestConstants.RESTORE_GRAPH);
  }

  protected <T> Node findTerminalNode(Class<T> clazz) {
    return lookup(".terminal")
        .lookup(
            (Predicate<Node>)
                (node) -> {
                  if (node.getUserData() instanceof TerminalCellSkin) {
                    TerminalCellSkin skin = (TerminalCellSkin) node.getUserData();
                    CellBindedObject object = skin.getCell().getBindedObject();
                    return clazz.isInstance(object);
                  } else {
                    return false;
                  }
                })
        .query();
  }

  protected <T extends VisualEditTerminal> Node findTerminalNode(Class<T> clazz, String name) {
    return lookup(".terminal")
        .lookup(
            (Predicate<Node>)
                (node) -> {
                  if (node.getUserData() instanceof TerminalCellSkin) {
                    TerminalCellSkin skin = (TerminalCellSkin) node.getUserData();
                    CellBindedObject object = skin.getCell().getBindedObject();
                    return clazz.isInstance(object) && ((T) object).getName().equals(name);
                  } else {
                    return false;
                  }
                })
        .query();
  }

  protected Node findMatrixNode() {
    return lookup(".matrix")
        .lookup(
            (Predicate<Node>)
                (node) -> {
                  return (node.getUserData() instanceof MatrixCellSkin) && isShowing(node);
                })
        .query();
  }

  protected <T extends VisualEditTerminal> void selectTerminal(
      GraphSelectionModel model, Class<T> clazz, String name, boolean select) {
    Node node = findTerminalNode(clazz, name);
    if (node != null && node.getUserData() instanceof TerminalCellSkin) {
      model.select((TerminalCellSkin) node.getUserData(), select);
    }
  }

  protected <T> Collection<Node> findAllTerminalNode(Class<T> clazz) {
    return lookup(".terminal")
        .lookup(
            (Predicate<Node>)
                (node) -> {
                  if (node.getUserData() instanceof TerminalCellSkin) {
                    TerminalCellSkin skin = (TerminalCellSkin) node.getUserData();
                    CellBindedObject object = skin.getCell().getBindedObject();
                    return clazz.isInstance(object);
                  } else {
                    return false;
                  }
                })
        .queryAll();
  }

  protected File copyResourceToTemp(String resourcePath, String prefix, String suffix)
      throws IOException {
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    InputStream stream = classLoader.getResourceAsStream(resourcePath);
    File targetFile = File.createTempFile(prefix, suffix);
    Files.copy(stream, targetFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
    return targetFile;
  }

  protected File loadResourceStatus(String filePath) throws IOException {
    return loadResourceImpl(filePath, ".status");
  }

  protected File loadResourceCfgx(String filePath) throws IOException {
    return loadResourceImpl(filePath, ".cfgx");
  }

  private File loadResourceImpl(String filePath, String ext) throws IOException {
    ApplicationBase applicationBase =
        InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    int oldSize = applicationBase.getEntityMananger().getAllEntity().size();
    File tempFile = copyResourceToTemp(filePath, "hello", ext);
    applicationBase.importFile(tempFile);

    while (applicationBase.getEntityMananger().getAllEntity().size() - oldSize != 1) {
      try {
        Thread.sleep(100);
      } catch (InterruptedException exc) {
        Assert.fail("Expect no exception!");
      }
    }
    try {
      Thread.sleep(1000);
    } catch (InterruptedException exc) {
      Assert.fail("Expect no exception!");
    }
    return tempFile;
  }

  protected File loadResourceFileToFileChooser(
      String filePath, String filePrefix, String fileSuffix) throws IOException {
    File tempFile = copyResourceToTemp(filePath, filePrefix, fileSuffix);
    StringSelection selection = new StringSelection(tempFile.getAbsolutePath());
    Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
    clipboard.setContents(selection, selection);
    try {
      Thread.sleep(1000);
    } catch (InterruptedException exception) {
      Assert.fail("Expect no exception!");
    }
    press(KeyCode.CONTROL).press(KeyCode.V).release(KeyCode.V).release(KeyCode.CONTROL);
    push(KeyCode.ENTER);
    return tempFile;
  }

  protected void loadFileStatus(File file) {
    ApplicationBase applicationBase =
        InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    int oldSize = applicationBase.getEntityMananger().getAllEntity().size();
    applicationBase.importFile(file);
    while (applicationBase.getEntityMananger().getAllEntity().size() - oldSize != 1) {
      try {
        Thread.sleep(100);
      } catch (InterruptedException exc) {
        Assert.fail("Expect no exception!");
      }
    }
    try {
      Thread.sleep(1000);
    } catch (InterruptedException exc) {
      Assert.fail("Expect no exception!");
    }
  }

  protected static void scrollToShow(Node node) {
    Node parent = node.getParent();
    Bounds bounds = node.getBoundsInParent();
    while (parent != null && !(parent instanceof ScrollPane)) {
      bounds = parent.localToParent(bounds);
      parent = parent.getParent();
    }
    if (parent == null) {
      return;
    }
    ScrollPane pane = (ScrollPane) parent;
    ensureVisible(pane, node);
  }

  protected static void ensureVisible(ScrollPane pane, Bounds bounds) {
    double width = pane.getContent().getBoundsInLocal().getWidth();
    double height = pane.getContent().getBoundsInLocal().getHeight();

    double x = bounds.getMaxX();
    double y = bounds.getMaxY();

    // scrolling values range from 0 to 1
    pane.setVvalue(y / height);
    pane.setHvalue(x / width);
  }

  /**
   * 滚动，确保node显示出来.
   *
   * @param pane 滚动窗口
   * @param node 要显示的node
   */
  private static void ensureVisible(ScrollPane pane, Node node) {
    Bounds viewport = pane.getViewportBounds();
    double contentHeight =
        pane.getContent().localToScene(pane.getContent().getBoundsInLocal()).getHeight();
    double contentWidth =
        pane.getContent().localToScene(pane.getContent().getBoundsInLocal()).getWidth();
    double nodeMinY = node.localToScene(node.getBoundsInLocal()).getMinY();
    double nodeMaxY = node.localToScene(node.getBoundsInLocal()).getMaxY();
    double nodeMinX = node.localToScene(node.getBoundsInLocal()).getMinX();
    double nodeMaxX = node.localToScene(node.getBoundsInLocal()).getMaxX();

    double verValueDelta = 0;
    if (nodeMaxY < 0) {
      // currently located above (remember, top left is (0,0))
      verValueDelta = (nodeMinY - viewport.getHeight()) / contentHeight;
    } else if (nodeMinY > viewport.getHeight()) {
      // currently located below
      verValueDelta = (nodeMinY + viewport.getHeight()) / contentHeight;
    }

    double horValueDelta = 0;
    if (nodeMaxX < 0) {
      horValueDelta = (nodeMinX - viewport.getWidth()) / contentWidth;
    } else if (nodeMinX > viewport.getWidth()) {
      horValueDelta = (nodeMinX + viewport.getWidth()) / contentWidth;
    }
    double verValueCurrent = pane.getVvalue();
    pane.setVvalue(verValueCurrent + verValueDelta);
    double horValueCurrent = pane.getHvalue();
    pane.setHvalue(horValueCurrent + horValueDelta);
  }

  protected boolean isShowing(Node node) {
    if (node == null) {
      return false;
    }
    if (!node.isVisible()) {
      return false;
    }
    if (node.getParent() != null) {
      return isShowing(node.getParent());
    } else {
      return true;
    }
  }

  protected void checkUpdateData(
      TreeItem<UpdateData> treeItem, Predicate<TreeItem<UpdateData>> predicate, int level) {
    if (treeItem == null) {
      return;
    }
    for (int i = 0; i < level; i++) {
      System.out.print('\t');
    }
    System.out.println(treeItem.getValue().getName() + "_" + treeItem.getValue().getType());
    Assert.assertTrue(predicate.test(treeItem));
    for (TreeItem<UpdateData> item : treeItem.getChildren()) {
      checkUpdateData(item, predicate, level + 1);
    }
  }

  /**
   * 创建usb.
   *
   * @param typeIndex 类型选型，1为RX，2为TX
   * @param port 输入的端口
   * @param bindingIndex 选择的TX或RX的序号.
   * @return 选择的tx或者rx的combobox的列表的项
   */
  protected Object createUsbImpl(int typeIndex, String port, int bindingIndex) {
    rightClickOn(GuiTestConstants.MATRIX_CELL);

    clickOn(GuiTestConstants.BUTTON_USB_BIND);

    if (typeIndex > 0) {
      clickOn(GuiTestConstants.COMBO_USB_TYPE);
      for (int i = 0; i < typeIndex; i++) {
        type(KeyCode.DOWN);
      }
      type(KeyCode.ENTER);
    }

    clickOn(GuiTestConstants.TEXT_PORT_INPUT);
    type(KeyCode.BACK_SPACE, 5);
    write(port);
    clickOn(GuiTestConstants.COMBO_BINDING);
    for (int i = 0; i < bindingIndex; i++) {
      type(KeyCode.DOWN);
    }
    type(KeyCode.ENTER);
    Assert.assertTrue(
        !lookup(
                (n) ->
                    n instanceof Button
                        && n.isVisible()
                        && window(n) == targetWindow()
                        && ((Button) n).getText().equals(GuiTestConstants.BUTTON_APPLY))
            .queryButton()
            .disableProperty()
            .get());


    Node node =
        lookup((n) -> n instanceof TextWrapper && ((TextWrapper) n).getText().equals(port)).query();
    Assertions.assertThat(node.isVisible());
    clickOn(node);
    Object selectedItem =
        lookup(GuiTestConstants.COMBO_BINDING)
            .queryComboBox()
            .getSelectionModel()
            .getSelectedItem();
    clickOn(GuiTestConstants.BUTTON_APPLY);
    return selectedItem;
  }

  /**
   * 创建跨屏.
   *
   * @param rxs rx的名称列表
   * @param row 行数
   * @param column 列数
   * @param name 跨屏名称
   * @param switchView 是否切换到跨屏界面
   */
  protected void createCrossScreenImpl(
      Collection<String> rxs, int row, int column, String name, boolean switchView) {
    ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    CaesarEntity entity = (CaesarEntity) app.getEntityMananger().getAllEntity().iterator().next();

    CaesarSystemEditPageView view =
        (CaesarSystemEditPageView) entity.getPageByName(CaesarSystemEditPage.NAME).getView();

    RunUtil.waitForUiDone(
        () -> {
          view.getControllable().getGraph().getSelectionModel().deselectAll();
          for (String rx : rxs) {
            selectTerminal(
                view.getControllable().getGraph().getSelectionModel(),
                CaesarConTerminal.class,
                rx,
                true);
          }
        });
    rightClickOn(findTerminalNode(CaesarConTerminal.class, rxs.iterator().next()));

    clickOn(GuiTestConstants.MENU_CREATE_CROSS_SCREEN);

    targetWindow(GuiTestConstants.DIALOG_SET_NAME)
        .clickOn(GuiTestConstants.CROSS_SCREEN_NAME_INPUT);
    targetWindow(GuiTestConstants.DIALOG_SET_NAME).write(name);
    targetWindow(GuiTestConstants.DIALOG_SET_NAME).clickOn(GuiTestConstants.CROSS_SCREEN_ROW_INPUT);
    targetWindow(GuiTestConstants.DIALOG_SET_NAME).write(row + "");
    targetWindow(GuiTestConstants.DIALOG_SET_NAME)
        .clickOn(GuiTestConstants.CROSS_SCREEN_COLUMN_INPUT);
    targetWindow(GuiTestConstants.DIALOG_SET_NAME).write(column + "");
    targetWindow(GuiTestConstants.DIALOG_SET_NAME).clickOn(GuiTestConstants.BUTTON_CONFIRM);
    if (switchView) {
      targetWindow(GuiTestConstants.DIALOG_SWITCH).clickOn(GuiTestConstants.BUTTON_YES);
    } else {
      targetWindow(GuiTestConstants.DIALOG_SWITCH).clickOn(GuiTestConstants.BUTTON_NO);
    }
  }

  /**
   * 创建预案组.
   *
   * @param rxs rx的名称列表
   */
  protected void createMultiScreenImpl(Collection<String> rxs) {
    ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    CaesarEntity entity = (CaesarEntity) app.getEntityMananger().getAllEntity().iterator().next();

    CaesarSystemEditPageView view =
        (CaesarSystemEditPageView) entity.getPageByName(CaesarSystemEditPage.NAME).getView();

    RunUtil.waitForUiDone(
        () -> {
          for (String rx : rxs) {
            view.getControllable().getGraph().getSelectionModel().deselectAll();
            selectTerminal(
                view.getControllable().getGraph().getSelectionModel(),
                CaesarConTerminal.class,
                rx,
                true);
          }
        });
    rightClickOn(findTerminalNode(CaesarConTerminal.class, rxs.iterator().next()));
    clickOn(GuiTestConstants.MENU_CREATE_MULTI_SCREEN);
  }

  protected boolean traverseTree(TreeItem<?> item, Predicate<Object> test) {
    if (item == null) {
      return false;
    }
    if (test.test(item.getValue())) {
      return true;
    } else {
      for (TreeItem child : item.getChildren()) {
        if (traverseTree(child, test)) {
          return true;
        }
      }
      return false;
    }
  }

  protected Node lookupGraphAnotherPageMenu() {
    return lookup(
            (node) -> {
              if (!(node instanceof MenuItemContainer)) {
                return false;
              }
              MenuItemContainer container = (MenuItemContainer) node;
              if (!(container.getItem() instanceof CheckMenuItem)) {
                return false;
              }
              CheckMenuItem item = (CheckMenuItem) container.getItem();
              return !item.isSelected()
                  && !item.getText().equals(GuiTestConstants.GRID_TOPOLOGICAL_GRAPH);
            })
        .query();
  }
}
