package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.graph.interfaces.CellSkin;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.vpm.pages.systemedit.controller.CaesarVideoWallFuncManager;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarData2VisualDataModel;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarVideoWallFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.menu.predicate.MenuPredicateBinding;
import com.mc.tool.framework.systemedit.menu.predicate.TypePredicate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javafx.scene.control.Menu;
import javafx.scene.control.MenuItem;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class MenuVideoWall4CaesarReuse extends Menu {

  /**
   * Constructor.
   */
  public MenuVideoWall4CaesarReuse(
      SystemEditControllable controllable, CaesarSwitchDataModel dataModel,
      CaesarVideoWallFuncManager videoWallFuncManager,
      CaesarMatrix matrix) {
    setText(Bundle.NbBundle.getMessage("menu.videowall.reuse"));
    this.disableProperty().bind(getMenuDisableBinding(controllable));
    for (int i = 0; i < VideoWallGroupData.GROUP_COUNT; i++) {
      CaesarVideoWallFunc func = videoWallFuncManager.getVideoWallFunc(i);
      if (func == null && dataModel.getVpDataModel().getVideoWallData(i).isDataValid()
          || func != null && func.getAllOnlineTerminalChild().isEmpty()
          && func.getVideoWallObject().getScreenCount() > 0) {
        this.getItems()
            .add(new MenuVideoWallFromOffline(controllable, dataModel, videoWallFuncManager, matrix,
                i));
      }
    }
  }

  protected MenuPredicateBinding getMenuDisableBinding(SystemEditControllable controllable) {
    MenuPredicateBinding binding = new MenuPredicateBinding(controllable, true);
    binding.addSingleSelectionPredicate(new TypePredicate(VpGroup.class).negate());
    binding.addSingleSelectionPredicate((node) -> node.getParent() instanceof CaesarVideoWallFunc);
    return binding;
  }

  static class MenuVideoWallFromOffline extends MenuItem {
    private final int index;
    private final CaesarVideoWallFuncManager funcManager;
    private final SystemEditControllable controllable;

    private final CaesarSwitchDataModel dataModel;
    private final CaesarMatrix matrix;

    /**
     * Constructor.
     *
     * @param controllable controllable
     */
    public MenuVideoWallFromOffline(SystemEditControllable controllable,
                                    CaesarSwitchDataModel dataModel,
                                    CaesarVideoWallFuncManager videoWallFuncManager,
                                    CaesarMatrix matrix, int index) {
      setText(String.format("[%d]%s", index,
          dataModel.getVpDataModel().getVideoWallData(index).getName()));
      this.index = index;
      this.funcManager = videoWallFuncManager;
      this.controllable = controllable;
      this.dataModel = dataModel;
      this.matrix = matrix;
      this.setOnAction((e) -> onAction());
    }

    protected void onAction() {
      // 获取选中的节点
      Collection<CellSkin> skins =
          controllable.getGraph().getSelectionModel().getSelectedCellSkin();
      List<VisualEditNode> nodeList = new ArrayList<>();
      for (CellSkin skin : skins) {
        if (skin.getCell().getBindedObject() instanceof VisualEditNode) {
          nodeList.add((VisualEditNode) skin.getCell().getBindedObject());
        } else {
          log.warn("Cell's bindedobject is not VisualEditNode but {}",
              skin.getCell().getBindedObject());
        }
      }
      //
      CaesarVideoWallFunc func = funcManager.getVideoWallFunc(index);
      if (func == null) {
        func =
            controllable.addGroup(
                "VideoWall", CaesarVideoWallFunc.class, nodeList.toArray(new VisualEditNode[0]));
        func.setVideoWallIndex(index);
        CaesarData2VisualDataModel.loadVideoWall4Index(controllable.getModel(), dataModel, index,
            func, matrix);
      } else {
        // 删除原来的children
        for (VisualEditNode child : func.getChildren()) {
          controllable.getModel().moveToBase(child, matrix);
        }
        // 添加新的children
        for (VisualEditNode child : nodeList) {
          controllable.getModel().moveToBase(child, func);
        }
      }
      func.resetAndAutoBindScreens();
      funcManager.setVideoWallFunc(index, func);
    }
  }
}