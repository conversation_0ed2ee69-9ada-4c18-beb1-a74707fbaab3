<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.CheckBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>

<?import com.jfoenix.controls.JFXDatePicker?>
<?import com.jfoenix.controls.JFXTimePicker?>
<?import javafx.scene.control.TextField?>
<?import com.dooapp.fxform.view.control.ConstraintLabel?>
<VBox stylesheets="@eventForm.css" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
  <GridPane VBox.vgrow="ALWAYS">
    <columnConstraints>
      <ColumnConstraints hgrow="SOMETIMES" maxWidth="164.0" minWidth="10.0" prefWidth="164.0" />
      <ColumnConstraints hgrow="ALWAYS" />
    </columnConstraints>
    <rowConstraints>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
    </rowConstraints>
    <Label text="%event.configuration" />
    <HBox id="tool-box" GridPane.columnIndex="1">
      <CheckBox id="enable-form-editor" mnemonicParsing="false" />
    </HBox>
    <HBox id="tool-box" prefHeight="100.0" prefWidth="300.0" GridPane.columnIndex="1" GridPane.rowIndex="1">
      <Label minWidth="120.0" text="%event.notifications" />
      <CheckBox id="osdWarning-form-editor" mnemonicParsing="false" />
      <Label id="osdWarning-form-label" />
      <CheckBox id="buzzerWarning-form-editor" mnemonicParsing="false" />
      <Label id="buzzerWarning-form-label" />
    </HBox>
    <HBox id="tool-box" prefHeight="100.0" prefWidth="300.0" GridPane.columnIndex="1" GridPane.rowIndex="2">
      <Label minWidth="120.0" text="%event.audio.threshold" />
      <TextField id="audioThreshold-form-editor" prefWidth="100.0"/>
      <ConstraintLabel id="audioThreshold-form-constraint"/>
    </HBox>
    <HBox id="tool-box" prefHeight="100.0" prefWidth="400.0" GridPane.columnIndex="1" GridPane.rowIndex="3">
      <Label minWidth="120.0" text="%event.record" />
      <Label minWidth="30.0" text="%event.from" />
      <JFXDatePicker fx:id="startDatePicker" id="start-date-form-editor" minWidth="110.0"/>
      <JFXTimePicker fx:id="startTimePicker" id="start-time-form-editor" minWidth="100.0"/>
      <Label minWidth="30.0" text="%event.to" />
      <JFXDatePicker fx:id="endDatePicker" id="end-date-form-editor" minWidth="110.0"/>
      <JFXTimePicker fx:id="endTimePicker" id="end-time-form-editor" minWidth="100.0"/>
      <Button fx:id="exportReport" minWidth="50.0" text="%event.export" styleClass="common-button" />
    </HBox>
  </GridPane>
</VBox>
