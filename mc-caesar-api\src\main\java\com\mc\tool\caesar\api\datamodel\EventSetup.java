package com.mc.tool.caesar.api.datamodel;

import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;

/**
 * .

 * <AUTHOR>
 */
public class EventSetup extends AbstractData {

  public static final String PROPERTY_ENABLE = "SystemConfigData.EventSetup.Enable";
  public static final String PROPERTY_OSD_WARNING = "SystemConfigData.EventSetup.OSD.Warning";
  public static final String PROPERTY_BUZZER_WARNING = "SystemConfigData.EventSetup.Buzzer.Warning";
  public static final String PROPERTY_AUDIO_THRESHOLD =
      "SystemConfigData.EventSetup.Audio.Threshold";

  private boolean enable;
  private boolean osdWarning;
  private boolean buzzerWarning;
  private int audioThreshold;

  /** . */
  public EventSetup(
      CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager, String fqn) {
    super(pcs, configDataManager, -1, fqn);
  }

  @Override
  public void initDefaults() {
    setEnable(false);
    setOsdWarning(false);
    setBuzzerWarning(false);
    setAudioThreshold(0);
  }

  public boolean isEnable() {
    return enable;
  }

  /**
   * setEnable.
   */
  public void setEnable(boolean enable) {
    boolean oldValue = this.enable;
    this.enable = enable;
    firePropertyChange(EventSetup.PROPERTY_ENABLE, oldValue, this.enable);
  }

  public boolean isOsdWarning() {
    return osdWarning;
  }

  /**
   * setOsdWarning.
   */
  public void setOsdWarning(boolean osdWarning) {
    boolean oldValue = this.osdWarning;
    this.osdWarning = osdWarning;
    firePropertyChange(EventSetup.PROPERTY_OSD_WARNING, oldValue, this.osdWarning);
  }

  public boolean isBuzzerWarning() {
    return buzzerWarning;
  }

  /**
   * setBuzzerWarning.
   */
  public void setBuzzerWarning(boolean buzzerWarning) {
    boolean oldValue = this.buzzerWarning;
    this.buzzerWarning = buzzerWarning;
    firePropertyChange(EventSetup.PROPERTY_BUZZER_WARNING, oldValue, this.buzzerWarning);
  }

  public int getAudioThreshold() {
    return audioThreshold;
  }

  /**
   * setAudioThreshold.
   */
  public void setAudioThreshold(int audioThreshold) {
    int oldValue = this.audioThreshold;
    this.audioThreshold = audioThreshold;
    firePropertyChange(EventSetup.PROPERTY_AUDIO_THRESHOLD, oldValue, this.audioThreshold);
  }

  /** . */
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    int data = getAudioThreshold() << 3;
    data = Utilities.setBits(data, isEnable(), 1);
    data = Utilities.setBits(data, isOsdWarning(), 2);
    data = Utilities.setBits(data, isBuzzerWarning(), 4);
    cfgWriter.writeInteger(data);
  }

  /** . */
  public void readData(CfgReader cfgReader) throws ConfigException {
    int data = cfgReader.readInteger();
    setEnable(Utilities.areBitsSet(data, 1));
    setOsdWarning(Utilities.areBitsSet(data, 2));
    setBuzzerWarning(Utilities.areBitsSet(data, 4));
    setAudioThreshold(data >> 3);
  }
}
