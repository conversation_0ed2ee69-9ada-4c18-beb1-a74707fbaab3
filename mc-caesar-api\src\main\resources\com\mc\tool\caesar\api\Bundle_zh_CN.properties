Arrangement.M1x4=1 x 4
Arrangement.M2x2=2 x 2
FunctionKey.command.connect=\u5B8C\u5168\u8BBF\u95EE\u8FDE\u63A5 (X=\u7BA1\u63A7\u7AEF, Y=\u63A5\u5165\u7AEF)
FunctionKey.command.connectvideo=\u89C6\u9891\u8FDE\u63A5 (X=\u7BA1\u63A7\u7AEF, Y=\u63A5\u5165\u7AEF)
FunctionKey.command.disconnect=\u65AD\u5F00\u8FDE\u63A5 (X=\u7BA1\u63A7\u7AEF)
FunctionKey.command.get=\u6293\u53D6(\u5B8C\u5168\u8BBF\u95EE) (X=\u7BA1\u63A7\u7AEF)
FunctionKey.command.getvideo=\u6293\u53D6\u89C6\u9891 (X=\u7BA1\u63A7\u7AEF)
FunctionKey.command.login=\u7528\u6237\u767B\u5F55 (X=\u7BA1\u63A7\u7AEF, Y=\u7528\u6237)
FunctionKey.command.private=\u79C1\u6709\u8FDE\u63A5 (X=\u7BA1\u63A7\u7AEF, Y=\u63A5\u5165\u7AEF)
FunctionKey.command.push=\u63A8\u9001(\u5B8C\u5168\u8BBF\u95EE) (X=\u7BA1\u63A7\u7AEF)
FunctionKey.command.pushvideo=\u63A8\u9001(\u89C6\u9891) (X=\u7BA1\u63A7\u7AEF)
#FunctionKey.command.private=Connect Private (P1=CON, P2=
#FunctionKey.command.push=Push (P1=
#FunctionKey.command.pushvideo=Push Video (P1=
Keyboard.DE_CH_150G=German (CH, 150G)
Keyboard.DE_DE_129=German (DE, 129)
Keyboard.EN_GB_166=English (GB, 166)
Keyboard.EN_GB_168=English (GB, 168)
Keyboard.EN_US_103P=English (US, 103P)
Keyboard.FR_BE_120=French (BE, 120)
Keyboard.FR_CH_150F=French (CH, 150F)
Keyboard.FR_FR_189=French (FR, 189)
Keyboard.RU_1251=Russian (RU, 1251)
MultiviewLayout.fullScreen=\u5355\u7A97\u53E3
MultiviewLayout.twoGrid=\u53CC\u7A97\u53E3
MultiviewLayout.fourGrid=\u56DB\u7A97\u53E3
MultiviewLayout.sideBar=\u4E00\u5927\u4E09\u5C0F
OsdPosition.bottom.left=\u5DE6\u4E0B\u65B9
OsdPosition.bottom.right=\u53F3\u4E0B\u65B9
OsdPosition.centered=\u4E2D\u95F4
OsdPosition.custom=\u81EA\u5B9A\u4E49
OsdPosition.top.left=\u5DE6\u4E0A\u65B9
OsdPosition.top.right=\u53F3\u4E0A\u65B9
Owner.Screen1=\u5C4F\u5E551
Owner.Screen2=\u5C4F\u5E552
Owner.Screen3=\u5C4F\u5E553
Owner.Screen4=\u5C4F\u5E554
Owner.Shared=\u5171\u4EAB
SuspendPosition.topcenter=\u4E0A\u4E2D\u95F4
SuspendPosition.topleft=\u5DE6\u4E0A\u89D2
SuspendPosition.topright=\u53F3\u4E0A\u89D2
TeraConnectionModel.file.conflict.cpu.message=\u60A8\u5C1D\u8BD5\u4E00\u4E2A\u4E3B\u673A\u914D\u7F6E\u4E0A\u4F20\u5230SNMP\u677F\u5361\uFF0C\u8FD9\u662F\u4E0D\u5141\u8BB8\u7684
TeraConnectionModel.file.conflict.snmp.message=\u60A8\u5C1D\u8BD5\u4E00\u4E2ASNMP\u914D\u7F6E\u4E0A\u4F20\u5230\u4E3B\u673A\uFF0C\u8FD9\u662F\u4E0D\u5141\u8BB8\u7684
TeraConnectionModel.file.conflict.title=\u6587\u4EF6\u7248\u672C\u51B2\u7A81
TeraVersion.MATX008C=MATX008C
TeraVersion.MATX016=MATX016
TeraVersion.MATX048=MATX048
TeraVersion.MATX048C=MATX048C
TeraVersion.MATX080=MATX080
TeraVersion.MATX080C=MATX080C
TeraVersion.MATX160=MATX160
TeraVersion.MATX21R=MATX21R
TeraVersion.MATX288=MATX288
TeraVersion.MATX576M=MATX576M
TeraVersion.MATX576S=MATX576S
TeraVersion.MATX6BP=MATX6BP
TeraVersion.TERA048=TERA048
TeraVersion.TERA080=TERA080
TeraVersion.TERA160=TERA160
TeraVersion.TERA288=TERA288
TeraVersion.TERASW=TERASW
TimeZone.UTC_0=(GMT) Coordinated Universal Time, Casablanca, Dublin, Lisbon, London
TimeZone.UTC_MINUS_1=(GMT -01:00) Azores, Cape Verde Is.
TimeZone.UTC_MINUS_10=(GMT -10:00) Hawaii
TimeZone.UTC_MINUS_11=(GMT -11:00) Coordinated Universal Time-11
TimeZone.UTC_MINUS_12=(GMT -12:00) International Date Line West
TimeZone.UTC_MINUS_2=(GMT -02:00) Coordinated Universal Time-02
TimeZone.UTC_MINUS_3=(GMT -03:00) Brasilia, Buenos Aires, Cayenne, Greenland
TimeZone.UTC_MINUS_4=(GMT -04:00) Atlantic Time (Canada)
TimeZone.UTC_MINUS_5=(GMT -05:00) Eastern Time (US & Canada)
TimeZone.UTC_MINUS_6=(GMT -06:00) Central Time (US & Canada)
TimeZone.UTC_MINUS_7=(GMT -07:00) Mountain Time (US & Canada)
TimeZone.UTC_MINUS_8=(GMT -08:00) Pacific Time (US & Canada)
TimeZone.UTC_MINUS_9=(GMT -09:00) Alaska
TimeZone.UTC_PLUS_1=(GMT +01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna
TimeZone.UTC_PLUS_10=(GMT +10:00) Brisbane, Canberra, Guam, Melbourne, Sydney, Vladivostok
TimeZone.UTC_PLUS_11=(GMT +11:00) Solomon Is., New Caledonia
TimeZone.UTC_PLUS_12=(GMT +12:00) Auckland, Fiji, Marshall Is. 12, Wellington
TimeZone.UTC_PLUS_2=(GMT +02:00) Athens, Bucharest, Cairo, Helsinki, Istanbul, Jerusalem
TimeZone.UTC_PLUS_3=(GMT +03:00) Baghdad, Kuwait, Moscow, St. Petersburg, Volgograd
TimeZone.UTC_PLUS_4=(GMT +04:00) Abu Dhabi, Baku, Muscat
TimeZone.UTC_PLUS_5=(GMT +05:00) Islamabad, Karachi
TimeZone.UTC_PLUS_6=(GMT +06:00) Astana, Dhaka, Novosibirsk
TimeZone.UTC_PLUS_7=(GMT +07:00) Bangkok, Hanoi, Jakarta
TimeZone.UTC_PLUS_8=(GMT +08:00) Beijing, Hong Kong, Irkutsk, Kuala Lumpur, Perth, Singapore
TimeZone.UTC_PLUS_9=(GMT +09:00) Osaka, Sapporo, Seoul, Tokyo
VideoMode.1024x768=1024 x 768
VideoMode.1280x1024=1280 x 1024
VideoMode.1280x800=1280 x 800
VideoMode.1600x1200=1600 x 1200
VideoMode.1680x1050=1680 x 1050
VideoMode.1920x1080=1920 x 1080
VideoMode.1920x1200=1920 x 1200
VideoMode.800x600=800 x 600
VideoMode.var=\u53EF\u6539\u53D8\u7684
FunctionKey.command.activeVideoWallScenario=\u5207\u6362\u9884\u6848(X=\u9884\u6848)
FunctionKey.command.activeVideoWallScenarioWindow=\u5207\u6362\u9884\u6848\u7A97\u53E3(X=\u9884\u6848\u7A97\u53E3\uFF0CY=\u63A5\u5165\u7AEF)
FunctionKey.command.switchMultiViewLayout=\u5207\u6362\u591A\u753B\u9762\u7A97\u53E3\u5E03\u5C40(X=\u5E03\u5C40)
FunctionKey.command.switchMultiViewSignal=\u5207\u6362\u591A\u753B\u9762\u901A\u9053\u4FE1\u53F7(X=\u901A\u9053\uFF0CY=\u63A5\u5165\u7AEF)
multiview.channel=\u901A\u9053
FunctionKey.command.switchMultiViewPush=\u56DB\u753B\u9762\u63A8\u9001(X=\u7BA1\u63A7\u7AEF)
FunctionKey.command.switchMultiViewGet=\u56DB\u753B\u9762\u6293\u53D6(X=\u7BA1\u63A7\u7AEF)
multiview.output_mode=\u63A5\u53E3\u6A21\u5F0F
ext.dp.mode.single=\u5355DP\u6A21\u5F0F
ext.dp.mode.double=\u53CCDP\u6A21\u5F0F
