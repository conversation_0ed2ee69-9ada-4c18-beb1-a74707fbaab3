package com.mc.tool.caesar.vpm.util.vp;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Delegate;

/**
 * .
 */
public class VpVideoDataWrapper implements VpVideoData {

  @Getter
  private final VpResolution resolution;

  @Getter
  private final VpTxCropData txCropData;

  @Getter
  @Delegate(types = VpVideoData.class)
  private final VpVideoData videoData;

  /**
   * 窗口的索引.
   */
  @Getter
  private final int index;

  /**
   * 是否为4k分割的窗口.
   */
  @Getter
  private final boolean is4kSplit;

  @Getter
  @Setter
  private int port = -1;

  @Getter
  @Setter
  private int channel = -1;

  public VpVideoDataWrapper(VpVideoData videoData, VpResolution resolution, int index) {
    this(videoData, resolution, index, false);
  }

  /**
   * .
   */
  public VpVideoDataWrapper(
      VpVideoData videoData, VpResolution resolution, int index, boolean is4kSplit) {
    this(videoData, resolution, new VpTxCropData(0, 0, 0, 0), index, is4kSplit);
  }

  /**
   * .
   *
   * @param videoData 视频窗口数据
   * @param resolution 信号源分辨率
   * @param txCropData 信号源裁剪信息
   * @param index      窗口索引
   * @param is4kSplit  是否为4k切割的窗口
   */
  public VpVideoDataWrapper(
      VpVideoData videoData, VpResolution resolution, VpTxCropData txCropData, int index,
      boolean is4kSplit) {
    this.videoData = videoData;
    this.resolution = resolution;
    this.txCropData = txCropData;
    this.index = index;
    this.is4kSplit = is4kSplit;
  }

  public int getCroppedResolutionWidth() {
    return Math.max(resolution.width - txCropData.getLeft() - txCropData.getRight(), 0);
  }

  public int getCroppedResolutionHeight() {
    return Math.max(resolution.height - txCropData.getTop() - txCropData.getBottom(), 0);
  }

  public boolean isValid() {
    return getCroppedResolutionWidth() > 0 && getCroppedResolutionHeight() > 0;
  }
}
