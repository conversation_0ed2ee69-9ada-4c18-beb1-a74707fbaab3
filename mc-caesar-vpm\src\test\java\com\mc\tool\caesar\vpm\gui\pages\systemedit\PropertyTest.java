package com.mc.tool.caesar.vpm.gui.pages.systemedit;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.caesar.vpm.pages.hotkeyconfiguration.CaesarHotkeyConfigurationPage;
import com.mc.tool.caesar.vpm.pages.permissionconfiguration.CaesarPermissionConfigurationPage;
import com.mc.tool.caesar.vpm.pages.systemedit.CaesarSystemEditPage;
import com.mc.tool.framework.utility.dialogues.FileDialogue;
import com.mc.tool.framework.utility.dialogues.FileDialogueFactory;
import com.mc.tool.framework.view.FrameView;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import javafx.scene.Node;
import javafx.scene.control.TabPane;
import javafx.scene.control.TableView;
import javafx.scene.control.TextField;
import javafx.stage.Stage;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * .
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class PropertyTest extends AppGuiTestBase {

  @Mock private FileDialogueFactory fileDialogueFactory;

  @Mock private FileDialogue fileDialogue;

  @Override
  public void beforeAll() {}

  @Override
  public void start(Stage stage) throws Exception {
    FrameView.setModule(
        binder -> binder.bind(FileDialogueFactory.class).toInstance(fileDialogueFactory));
    when(fileDialogueFactory.createFileDialogue()).thenReturn(fileDialogue);
    super.start(stage);
  }

  @Test
  public void testActionMacro() {
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }
      clickOn(GuiTestConstants.CON_00);
      clickOn(GuiTestConstants.ACTION_MACRO);
      // 检查是否有跳转
      TabPane pane = lookup(GuiTestConstants.TAB_PANE_PAGES).query();
      Assert.assertEquals(
          CaesarHotkeyConfigurationPage.NAME, pane.getSelectionModel().getSelectedItem().getId());
      // 检查是否选中
      TableView<ConsoleData> tableView =
          lookup(GuiTestConstants.HOTKEY_MACRO_SOURCE_LIST)
              .match((item) -> isShowing(item))
              .query();
      Assert.assertEquals(
          GuiTestConstants.CON_00, tableView.getSelectionModel().getSelectedItem().getName());
      clickOn(GuiTestConstants.HOTKEY_MACRO_SEARCH_TEXT);
      write("hello");
      clickOn("#" + CaesarSystemEditPage.NAME);
      // 选择另外一个
      clickOn(GuiTestConstants.CON_01);
      clickOn(GuiTestConstants.ACTION_MACRO);
      tableView =
          lookup(GuiTestConstants.HOTKEY_MACRO_SOURCE_LIST)
              .match((item) -> isShowing(item))
              .query();
      Assert.assertEquals(
          GuiTestConstants.CON_01, tableView.getSelectionModel().getSelectedItem().getName());
      TextField field =
          lookup(GuiTestConstants.HOTKEY_MACRO_SEARCH_TEXT)
              .match((item) -> isShowing(item))
              .query();
      // 检查搜索框清空
      Assert.assertEquals("", field.getText());

      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testActionCpuRights() {
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }
      clickOn(GuiTestConstants.CON_00);
      clickOn(GuiTestConstants.ACTION_CPU_RIGHTS);
      // 检查是否有跳转
      TabPane pane = lookup(GuiTestConstants.TAB_PANE_PAGES).query();
      Assert.assertEquals(
          CaesarPermissionConfigurationPage.NAME,
          pane.getSelectionModel().getSelectedItem().getId());
      // 检查是否选中
      TableView<ConsoleData> tableView =
          lookup(GuiTestConstants.PERMISSION_RIGHTS_SOURCE_LIST)
              .match((item) -> isShowing(item))
              .query();
      Assert.assertEquals(
          GuiTestConstants.CON_00, tableView.getSelectionModel().getSelectedItem().getName());
      clickOn(GuiTestConstants.PERMISSION_RIGHTS_SEARCH_TEXT);
      write("hello");
      clickOn("#" + CaesarSystemEditPage.NAME);
      // 选择另外一个
      clickOn(GuiTestConstants.CON_01);
      clickOn(GuiTestConstants.ACTION_CPU_RIGHTS);
      tableView =
          lookup(GuiTestConstants.PERMISSION_RIGHTS_SOURCE_LIST)
              .match((item) -> isShowing(item))
              .query();
      Assert.assertEquals(
          GuiTestConstants.CON_01, tableView.getSelectionModel().getSelectedItem().getName());
      TextField field =
          lookup(GuiTestConstants.PERMISSION_RIGHTS_SEARCH_TEXT)
              .match((item) -> isShowing(item))
              .query();
      // 检查搜索框清空
      Assert.assertEquals("", field.getText());

      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testActionFavourites() {
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }
      clickOn(GuiTestConstants.CON_00);
      clickOn(GuiTestConstants.ACTION_FAVOURITES);
      // 检查是否有跳转
      TabPane pane = lookup(GuiTestConstants.TAB_PANE_PAGES).query();
      Assert.assertEquals(
          CaesarHotkeyConfigurationPage.NAME, pane.getSelectionModel().getSelectedItem().getId());
      // 检查是否选中
      TableView<ConsoleData> tableView =
          lookup(GuiTestConstants.HOTKEY_FAVOURITE_SOURCE_LIST)
              .match((item) -> isShowing(item))
              .query();
      Assert.assertEquals(
          GuiTestConstants.CON_00, tableView.getSelectionModel().getSelectedItem().getName());
      clickOn(GuiTestConstants.HOTKEY_FAVOURITE_SEARCH_TEXT);
      write("hello");
      clickOn("#" + CaesarSystemEditPage.NAME);
      // 选择另外一个
      clickOn(GuiTestConstants.CON_01);
      clickOn(GuiTestConstants.ACTION_FAVOURITES);
      tableView =
          lookup(GuiTestConstants.HOTKEY_FAVOURITE_SOURCE_LIST)
              .match((item) -> isShowing(item))
              .query();
      Assert.assertEquals(
          GuiTestConstants.CON_01, tableView.getSelectionModel().getSelectedItem().getName());
      TextField field =
          lookup(GuiTestConstants.HOTKEY_FAVOURITE_SEARCH_TEXT)
              .match((item) -> isShowing(item))
              .query();
      // 检查搜索框清空
      Assert.assertEquals("", field.getText());

      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testSaveEdid() {
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }
      // 测试有edid情况
      clickOn(GuiTestConstants.CON_00);
      Node edidControl = lookup(GuiTestConstants.EDID_1_PROP).query();
      Assert.assertTrue(edidControl != null);
      Node button = edidControl.lookup(".button");
      Assert.assertTrue(button != null);
      scrollToShow(button);

      File file = File.createTempFile("con0", ".edid");
      when(fileDialogue.showSaveDialog(any())).thenReturn(file);
      clickOn(button);

      try {
        FileInputStream fis = new FileInputStream(file);
        byte[] bytes = new byte[1024];
        int count = fis.read(bytes);
        Assert.assertEquals(3, count);
        for (int i = 1; i < 4; i++) {
          Assert.assertEquals(i, bytes[i - 1]);
        }
      } catch (IOException exception) {
        Assert.fail();
      }
      file.delete();

      // 测试没有edid情况
      clickOn(GuiTestConstants.CON_01);
      edidControl = lookup(GuiTestConstants.EDID_1_PROP).query();
      Assert.assertTrue(edidControl != null);
      button = edidControl.lookup(".button");
      Assert.assertTrue(button != null);
      scrollToShow(button);
      clickOn(button);
      // 啥事都没有
      when(fileDialogue.showSaveDialog(any()))
          .then(
              (input) -> {
                Assert.fail();
                return null;
              });

      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/systemedit/36_hw1.2_edidtest.status");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }
}
