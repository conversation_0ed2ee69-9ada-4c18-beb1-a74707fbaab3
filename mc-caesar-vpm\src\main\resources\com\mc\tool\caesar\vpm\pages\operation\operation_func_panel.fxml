<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import org.controlsfx.control.MasterDetailPane?>
<fx:root id="root" prefHeight="720" prefWidth="1280"
  stylesheets="@operation_func_panel.css" type="javafx.scene.layout.VBox"
  xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
  <HBox id="header-pane" alignment="CENTER" minHeight="40"
    prefHeight="40">
    <HBox id="header-toolbox" alignment="CENTER_RIGHT" fx:id="toolBoxContainer" HBox.Hgrow="ALWAYS">
    </HBox>
  </HBox>
  <Region id="seperator" minHeight="1"/>
  <MasterDetailPane fx:id="masterDetailPane" VBox.vgrow="ALWAYS">
    <masterNode>
      <HBox id="body-pane" HBox.hgrow="ALWAYS">
        <HBox HBox.hgrow="ALWAYS" id="body-pane-main">
          <VBox id="function-source" fx:id="sourceListContainer"
            prefWidth="200" minWidth="200">
          </VBox>
          <VBox id="function-content" HBox.hgrow="ALWAYS">
            <StackPane fx:id="functionViewContainer" VBox.vgrow="ALWAYS"/>
          </VBox>
        </HBox>
        <VBox id="show-btn-container" fx:id="showBtnContainer"
          maxWidth="26" prefWidth="26">
          <VBox id="show-btn-sub-container">
            <HBox alignment="CENTER_RIGHT" minHeight="10" prefHeight="10">
              <Button id="show-property-btn" onAction="#onShowProperty"
                styleClass="image-button"/>
            </HBox>
            <Region minHeight="1" styleClass="seperator"/>
            <Button id="property-btn" styleClass="image-button"/>
            <Region minHeight="1" styleClass="seperator"/>
            <Button id="icon-btn" styleClass="image-button"/>
            <Region minHeight="1" styleClass="seperator"/>
          </VBox>
          <Region VBox.vgrow="ALWAYS"/>
        </VBox>
      </HBox>
    </masterNode>
    <detailNode>
      <VBox id="right-panel" minWidth="240" prefWidth="240"
        styleClass="right-panel">
        <HBox id="hide-btn-container" minHeight="10" prefHeight="10"
          styleClass="right-title">
          <Button id="hide-property-btn" onAction="#onHideProperty"
            styleClass="image-button"/>
        </HBox>
        <VBox VBox.vgrow="ALWAYS" fx:id="propertyContainer"/>
      </VBox>
    </detailNode>
  </MasterDetailPane>


</fx:root>
