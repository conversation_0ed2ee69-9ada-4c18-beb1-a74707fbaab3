package com.mc.tool.caesar.api.datamodel;

import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import lombok.Data;

/**
 * .
 */
@Data
public class VersionDef implements CaesarCommunicatable {

  private int masterVersion;
  private int subVersion;
  private int year;
  private int month;
  private int day;
  private boolean hasPatchVersion = false;


  @Override
  public void readData(CfgReader paramCfgReader) throws ConfigException {
    setMasterVersion(paramCfgReader.read2ByteValue());
    setSubVersion(paramCfgReader.read2ByteValue());
    setYear(paramCfgReader.readByteValue());
    setMonth(paramCfgReader.readByteValue());
    setDay(paramCfgReader.readByteValue());
  }

  @Override
  public void writeData(CfgWriter paramCfgWriter) throws ConfigException {
    paramCfgWriter.write2ByteSmallEndian(getMasterVersion());
    paramCfgWriter.write2ByteSmallEndian(getSubVersion());
    paramCfgWriter.writeByte((byte) year);
    paramCfgWriter.writeByte((byte) month);
    paramCfgWriter.writeByte((byte) day);
  }

  /**
   * 复制.
   */
  public VersionDef copy() {
    VersionDef result = new VersionDef();
    result.setMasterVersion(masterVersion);
    result.setSubVersion(subVersion);
    result.setYear(year);
    result.setMonth(month);
    result.setDay(day);
    result.setHasPatchVersion(hasPatchVersion);
    return result;
  }

  @Override
  public String toString() {
    if (hasPatchVersion) {
      return String.format("%d.%d.%d", masterVersion, subVersion >> 8, subVersion & 0xff);
    } else {
      return String.format("%d.%d", masterVersion, subVersion);
    }
  }

  public String getDate() {
    return String.format("%04d.%02d.%02d", year, month, day);
  }


  public boolean isZero() {
    return masterVersion == 0 && subVersion == 0;
  }

  public boolean isHasPatchVersion() {
    return hasPatchVersion;
  }

  public void setHasPatchVersion(boolean hasPatchVersion) {
    this.hasPatchVersion = hasPatchVersion;
  }

  /**
   * .
   */
  public int compareTo(VersionDef version) {
    if (this == version) {
      return 0;
    }
    if (version == null) {
      return 1;
    }
    if (masterVersion < version.masterVersion) {
      return -1;
    } else if (masterVersion > version.masterVersion) {
      return 1;
    } else if (subVersion < version.subVersion) {
      return -1;
    } else if (subVersion > version.subVersion) {
      return 1;
    } else if (year < version.year) {
      return -1;
    } else if (year > version.year) {
      return 1;
    } else if (month < version.month) {
      return -1;
    } else if (month > version.month) {
      return 1;
    } else {
      return Integer.compare(day, version.day);
    }
  }

}
