package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import lombok.Setter;

/**
 * 创建认证用户的信息.
 */
@Setter
@JsonPropertyOrder({
    CreateAuthUserInfo.JSON_PROPERTY_NAME,
    CreateAuthUserInfo.JSON_PROPERTY_GENDER,
    CreateAuthUserInfo.JSON_PROPERTY_MOBILE
})
public class CreateAuthUserInfo {
  public static final String JSON_PROPERTY_NAME = "name";
  private String name;

  public static final String JSON_PROPERTY_GENDER = "gender";
  private String gender;

  public static final String JSON_PROPERTY_MOBILE = "mobile";
  private String mobile;


  public CreateAuthUserInfo name(String name) {
    this.name = name;
    return this;
  }

  /**
   * 用户名.
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_NAME)
  @JsonInclude()
  public String getName() {
    return name;
  }


  public CreateAuthUserInfo gender(String gender) {
    this.gender = gender;
    return this;
  }

  /**
   * 性别.
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_GENDER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public String getGender() {
    return gender;
  }


  public CreateAuthUserInfo mobile(String mobile) {
    this.mobile = mobile;
    return this;
  }

  /**
   * 电话号码.
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_MOBILE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public String getMobile() {
    return mobile;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CreateAuthUserInfo createAuthUserInfo = (CreateAuthUserInfo) o;
    return Objects.equals(this.name, createAuthUserInfo.name)
        && Objects.equals(this.gender, createAuthUserInfo.gender)
        && Objects.equals(this.mobile, createAuthUserInfo.mobile);
  }

  @Override
  public int hashCode() {
    return Objects.hash(name, gender, mobile);
  }


  @Override
  public String toString() {
    return "CreateAuthUserInfo {\n"
        + "    name: " + toIndentedString(name) + "\n"
        + "    gender: " + toIndentedString(gender) + "\n"
        + "    mobile: " + toIndentedString(mobile) + "\n" + "}";
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
