package com.mc.tool.caesar.api.datamodel;

import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import lombok.Data;

/**
 * .
 */
@Data
public class VersionSet implements CaesarCommunicatable {

  private VersionDef appVersion = new VersionDef();
  private VersionDef fpgaVersion = new VersionDef();
  private VersionDef systemVersion = new VersionDef();
  private VersionDef otherVersion = new VersionDef();
  private VersionDef hwVersionDef = new VersionDef();

  private static final int RESERVED_COUNT = 6;

  @Override
  public void readData(CfgReader paramCfgReader) throws ConfigException {
    appVersion.readData(paramCfgReader);
    fpgaVersion.readData(paramCfgReader);
    systemVersion.readData(paramCfgReader);
    otherVersion.readData(paramCfgReader);
    hwVersionDef.readData(paramCfgReader);
    paramCfgReader.readByteArray(RESERVED_COUNT);
  }

  @Override
  public void writeData(CfgWriter paramCfgWriter) throws ConfigException {
    appVersion.writeData(paramCfgWriter);
    fpgaVersion.writeData(paramCfgWriter);
    systemVersion.writeData(paramCfgWriter);
    otherVersion.writeData(paramCfgWriter);
    hwVersionDef.writeData(paramCfgWriter);
    paramCfgWriter.writeByteArray(new byte[RESERVED_COUNT]);
  }

  @Override
  public String toString() {
    return String.format("app version:%s, fpga version:%s, system version:%s.",
        appVersion.toString(), fpgaVersion.toString(), systemVersion.toString());
  }

  /**
   * 复制版本信息.
   *
   * @return 复制的结果.
   */
  public VersionSet copy() {
    VersionSet copy = new VersionSet();
    copy.appVersion = appVersion.copy();
    copy.fpgaVersion = fpgaVersion.copy();
    copy.systemVersion = systemVersion.copy();
    copy.otherVersion = otherVersion.copy();
    copy.hwVersionDef = hwVersionDef.copy();
    return copy;
  }

}
