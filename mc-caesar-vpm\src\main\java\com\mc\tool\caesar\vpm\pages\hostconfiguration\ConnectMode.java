package com.mc.tool.caesar.vpm.pages.hostconfiguration;

/**
 * 连接模式.
 */
public enum ConnectMode {
  NONE(Bundle.NbBundle.getMessage("general.connect_mode.none")),
  FULL_CONNECT(Bundle.NbBundle.getMessage("general.connect_mode.full")),
  VIDEO_CONNECT(Bundle.NbBundle.getMessage("general.connect_mode.video"));
  private final String name;

  ConnectMode(String nameable) {
    this.name = nameable;
  }

  @Override
  public String toString() {
    return name;
  }
}
