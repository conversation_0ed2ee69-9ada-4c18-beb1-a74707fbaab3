package com.mc.tool.caesar.vpm.pages.systemedit.controller;

import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.tool.caesar.api.utils.CpuDataStateWrapper;
import com.mc.tool.caesar.api.utils.ExtendedSwitchUtility;
import com.mc.tool.caesar.api.utils.SwitchType;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceConnection;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceConnections;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarDataUtility;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarTerminalBase;
import com.mc.tool.framework.systemedit.datamodel.VisualEditConnection;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javafx.collections.ListChangeListener;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarConnectionHelper {
  private boolean updatingConnection = false;

  private WeakReference<CaesarDeviceController> deviceControllerRef = new WeakReference<>(null);
  private WeakReference<VisualEditModel> modelRef = new WeakReference<>(null);

  public CaesarConnectionHelper() {}

  public void setModel(VisualEditModel model) {
    modelRef = new WeakReference<>(model);
  }

  public ListChangeListener<VisualEditConnection> createConnectionChangeListener() {
    return new ConnectionChangeListener();
  }

  public void setDeviceController(CaesarDeviceController controller) {
    deviceControllerRef = new WeakReference<>(controller);
  }

  protected CaesarDeviceController getDeviceController() {
    return deviceControllerRef.get();
  }

  protected VisualEditModel getModel() {
    return modelRef.get();
  }

  private boolean isUpdatingConnection() {
    return updatingConnection;
  }

  private void beginUpdateConnection() {
    updatingConnection = true;
  }

  private void endUpdateConnection() {
    updatingConnection = false;
  }

  /** 加载连接信息. */
  public void loadConnections() {
    if (getDeviceController().getConnections().get().size() != getModel().getConnections().size()) {
      updateConnections(getDeviceController().getConnections().get());
    }
    initConnectionColor();
  }

  /**
   * 更新连接信息.
   *
   * @param connections 连接的集合
   */
  public void updateConnections(CaesarDeviceConnections connections) {
    beginUpdateConnection();
    List<VisualEditConnection> conns = new ArrayList<>();
    for (Map.Entry<CaesarDeviceConnection, SwitchType> entries : connections.entrySet()) {
      CaesarTerminalBase cpu =
          CaesarDataUtility.findTerminal(
              getModel(), entries.getKey().getCpuExtenderId(), false, true);
      CaesarTerminalBase console =
          CaesarDataUtility.findTerminal(
              getModel(), entries.getKey().getConExtenderId(), true, false);
      if (cpu == null || console == null) {
        continue;
      }

      VisualEditConnection connection =
          new VisualEditConnection(
              cpu,
              ConnectorIdentifier.getIdentifier(
                  entries.getKey().isCpuRedundant()
                      ? CaesarTerminalBase.CAESAR_RD_PORT
                      : CaesarTerminalBase.CAESAR_NORMAL_PORT),
              console,
              ConnectorIdentifier.getIdentifier(
                  entries.getKey().isConRedundant()
                      ? CaesarTerminalBase.CAESAR_RD_PORT
                      : CaesarTerminalBase.CAESAR_NORMAL_PORT),
              entries.getValue().toString(),
              entries.getKey().getConChannel());
      conns.add(connection);
    }
    getModel().setAllConnections(conns);
    initConnectionColor();
    endUpdateConnection();
  }

  class ConnectionChangeListener implements ListChangeListener<VisualEditConnection> {

    @Override
    public void onChanged(ListChangeListener.Change<? extends VisualEditConnection> change) {
      if (isUpdatingConnection()) {
        return;
      }

      CaesarDeviceController deviceController = getDeviceController();
      if (deviceController == null) {
        return;
      }
      while (change.next()) {

        for (VisualEditConnection conn : change.getAddedSubList()) {
          CaesarTerminalBase rx = (CaesarTerminalBase) conn.getRxTerminal();
          CaesarTerminalBase tx = (CaesarTerminalBase) conn.getTxTerminal();
          if (rx instanceof CaesarConTerminal && tx instanceof CaesarCpuTerminal) {
            CaesarConTerminal con = (CaesarConTerminal) rx;
            CaesarCpuTerminal cpu = (CaesarCpuTerminal) tx;
            SwitchType type = SwitchType.valueOf(conn.getMode());
            switch (type) {
              case FULL:
                if (conn.getRxChannel() > 0) {
                  deviceController.execute(() ->
                      ExtendedSwitchUtility.connectMultiview(
                          getDeviceController().getDataModel(),
                          cpu.getCpuData(),
                          con.getConsoleData(),
                          conn.getRxChannel() - 1, 0));
                } else if (conn.getRxChannel() == 0) {
                  deviceController.execute(() ->
                      ExtendedSwitchUtility.fullAccess(
                          getDeviceController().getDataModel(),
                          con.getConsoleData(),
                          new CpuDataStateWrapper(
                              cpu.getCpuData(), CpuDataStateWrapper.Access.FULL),
                          true));
                }
                break;
              case VIDEO:
                if (conn.getRxChannel() > 0) {
                  deviceController.execute(() ->
                      ExtendedSwitchUtility.connectMultiview(
                          getDeviceController().getDataModel(),
                          cpu.getCpuData(),
                          con.getConsoleData(),
                          conn.getRxChannel() - 1, 1));
                } else if (conn.getRxChannel() == 0) {
                  deviceController.execute(() ->
                      ExtendedSwitchUtility.videoAccess(
                          getDeviceController().getDataModel(),
                          con.getConsoleData(),
                          new CpuDataStateWrapper(
                              cpu.getCpuData(), CpuDataStateWrapper.Access.VIDEO),
                          true));
                }
                break;
              case PRIVATE:
                deviceController.execute(() ->
                    ExtendedSwitchUtility.privateMode(
                        getDeviceController().getDataModel(),
                        con.getConsoleData(),
                        new CpuDataStateWrapper(
                            cpu.getCpuData(), CpuDataStateWrapper.Access.PRIVATE),
                        true));
                break;
              default:
                break;
            }
          }
        }
      }
      initConnectionColor();
    }
  }

  private void initConnectionColor() {
    for (VisualEditTerminal terminal : getModel().getAllTerminals()) {
      if (terminal instanceof CaesarTerminalBase) {
        CaesarTerminalBase terminalBase = (CaesarTerminalBase) terminal;
        terminalBase.getConnectionType().setAll(
            ExtendedSwitchUtility.getSwitchType(terminalBase.getExtenderData(),
                getDeviceController().getDataModel()));
      }
    }
  }
}
