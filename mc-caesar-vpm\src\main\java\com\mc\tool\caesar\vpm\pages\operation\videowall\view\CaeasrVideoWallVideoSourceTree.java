package com.mc.tool.caesar.vpm.pages.operation.videowall.view;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.datamodel.SourceCropData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminalCropWrapper;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminalWrapper;
import com.mc.tool.framework.operation.view.VideoSourceTree;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javafx.beans.property.ObjectProperty;
import javafx.scene.control.TreeItem;
import javafx.util.Pair;

/**
 * .
 */
public class CaeasrVideoWallVideoSourceTree extends VideoSourceTree {

  private CaesarDeviceController deviceController;

  public CaeasrVideoWallVideoSourceTree() {
    setCellFactory((view) -> new CaesarVideoWallVideoSourceTreeCell(deviceController));
  }

  /**
   * 初始化.
   */
  public void init(CaesarDeviceController deviceController, VisualEditModel model,
                   ObjectProperty<VisualEditFunc> currentFunc,
                   SourceMode mode, Predicate<VisualEditTerminal> terminalFilter) {
    this.deviceController = deviceController;
    deviceController.getDataModel().addPropertyChangeListener(
        new String[] {SourceCropData.PROPERTY_NAME, SourceCropData.PROPERTY_LEFT, SourceCropData.PROPERTY_TOP,
            SourceCropData.PROPERTY_RIGHT,
            SourceCropData.PROPERTY_BOTTOM, SourceCropData.PROPERTY_SOURCE_INDEX, SourceCropData.PROPERTY_CPU_INDEX},
        evt -> PlatformUtility.runInFxThread(() -> {
          updateSourceTree(currentFunc.get());
        }));
    super.init(model, currentFunc, mode, terminalFilter);
  }

  @Override
  protected void updateTreeItem(
      VisualEditNode nodeParent,
      TreeItem<VisualEditNode> treeParent,
      Set<VisualEditTerminal> visibleTerminals) {
    Map<VisualEditNode, TreeItem<VisualEditNode>> existItems = new HashMap<>();
    for (TreeItem<VisualEditNode> child : treeParent.getChildren()) {
      existItems.put(child.getValue(), child);
    }
    Map<Pair<Integer, Integer>, List<SourceCropData>> activeCropDataMap = new HashMap<>();
    if (deviceController != null) {
      // 生成信号源裁剪的索引
      Collection<SourceCropData> activeCropDatas =
          deviceController.getDataModel().getConfigDataManager().getActiveSourceCropDatas();
      for (SourceCropData cropData : activeCropDatas) {
        Pair<Integer, Integer> key = new Pair<>(cropData.getCpuIndex(), cropData.getSourceIndex());
        List<SourceCropData> list = activeCropDataMap.get(key);
        if (list == null) {
          list = new ArrayList<>();
          activeCropDataMap.put(key, list);
        }
        list.add(cropData);
      }
    }
    // 生成树
    List<TreeItem<VisualEditNode>> nodes = new ArrayList<>();
    for (VisualEditNode node : nodeParent.getTxChildren()) {
      if (!isNodeVisible(node, visibleTerminals)) {
        continue;
      }

      if (node instanceof CaesarCpuTerminal) {
        CaesarCpuTerminal cpuTerminal = (CaesarCpuTerminal) node;
        if (cpuTerminal.getExtenderData() == null) {
          continue;
        }
        for (int i = 0;
            i < cpuTerminal.getExtenderData().getExtenderStatusInfo().getInterfaceCount();
            i++) {
          VisualEditNode item = new CaesarCpuTerminalWrapper(cpuTerminal, i);
          TreeItem<VisualEditNode> treeItem = existItems.get(item);
          if (treeItem == null) {
            treeItem = new SourceTreeItem(item, updateTreeFunction);
          }
          nodes.add(treeItem);
          if (deviceController == null) {
            continue;
          }
          final TreeItem<VisualEditNode> finalTreeItem = treeItem;
          Map<VisualEditNode, TreeItem<VisualEditNode>> cpuExistChildren =
              finalTreeItem.getChildren().stream().collect(Collectors.toMap((t) -> t.getValue(), (t) -> t));
          if (activeCropDataMap.containsKey(new Pair<>(cpuTerminal.getCpuData().getOid() + 1, i))) {
            List<TreeItem<VisualEditNode>> cpuNewChildren =
                activeCropDataMap.get(new Pair<>(cpuTerminal.getCpuData().getOid() + 1, i)).stream().map(cropData -> {
                  CaesarCpuTerminalCropWrapper cropDataWrapper =
                      new CaesarCpuTerminalCropWrapper((CaesarCpuTerminalWrapper) item, cropData.getOid());
                  TreeItem<VisualEditNode> cropDataTreeItem = cpuExistChildren.get(cropDataWrapper);
                  if (cropDataTreeItem == null) {
                    cropDataTreeItem = new SourceTreeItem(cropDataWrapper, updateTreeFunction);
                  }
                  return cropDataTreeItem;
                }).collect(Collectors.toList());
            finalTreeItem.getChildren().setAll(cpuNewChildren);
          } else {
            finalTreeItem.getChildren().clear();
          }
        }
      } else {
        TreeItem<VisualEditNode> treeItem = existItems.get(node);
        if (treeItem == null) {
          treeItem = new SourceTreeItem(node, updateTreeFunction);
        }
        nodes.add(treeItem);
        updateTreeItem(node, treeItem, visibleTerminals);
      }
    }
    treeParent.getChildren().setAll(nodes);
  }
}
