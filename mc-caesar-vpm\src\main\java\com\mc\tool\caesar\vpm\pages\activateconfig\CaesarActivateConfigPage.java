package com.mc.tool.caesar.vpm.pages.activateconfig;

import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.caesar.vpm.pages.activateconfig.Bundle.NbBundle;
import com.mc.tool.framework.interfaces.Page;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.scene.layout.Pane;

/**
 * .
 */
public class CaesarActivateConfigPage implements Page {

  private final ActivateConfigPageView view;
  public static final String NAME = "activateconfig";
  private final BooleanProperty visibility = new SimpleBooleanProperty(true);

  public CaesarActivateConfigPage(CaesarEntity entity) {
    view = new ActivateConfigPageView();
    view.getController().setDeviceController(entity.getController());
  }

  @Override
  public String getTitle() {
    return NbBundle.getMessage("ActivateConfig.pageTitle.text");
  }

  @Override
  public String getName() {
    return NAME;
  }

  @Override
  public Pane getView() {
    return view;
  }

  @Override
  public String getStyleClass() {
    return null;
  }

  @Override
  public BooleanProperty getVisibleProperty() {
    return visibility;
  }

  @Override
  public void showObject(Object object) {}
}
