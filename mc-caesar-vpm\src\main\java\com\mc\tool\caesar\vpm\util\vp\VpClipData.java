package com.mc.tool.caesar.vpm.util.vp;

import lombok.Data;

/**
 * .
 */
@Data
public class VpClipData {
  /**
   * 缩放前的宽度.
   */
  private int originWidth = 1;
  /**
   * 缩放前的高度.
   */
  private int originHeight = 1;

  /** 缩放前的裁剪的横坐标位置. */
  private int originClipX = 0;

  /** 缩放前的裁剪的纵坐标位置. */
  private int originClipY = 0;

  /** 缩放前的裁剪的宽度. */
  private int originClipWidth = 1;

  /** 缩放前的裁剪的高度. */
  private int originClipHeight = 1;

  /**
   * 缩放后的裁剪的横坐标位置.
   */
  private int scaledClipX = 0;
  /**
   * 缩放后的裁剪的纵坐标位置.
   */
  private int scaledClipY = 0;
  /**
   * 缩放后的裁剪的宽度.
   */
  private int scaledClipWidth = 1;
  /**
   * 缩放后的裁剪的高度.
   */
  private int scaledClipHeight = 1;
  /**
   * 缩放后的宽度.
   */
  private int scaledWidth = 1;
  /**
   * 缩放后的高度.
   */
  private int scaledHeight = 1;

  private int outX = 0;
  private int outY = 0;
  private int outWidth = 1;
  private int outHeight = 1;
  private int alpha = 255;
  private int port;
  private int channel;
  private int rxId;
  private int clipIndex;
  private int videoIndex; // 窗口索引

  private int leftCompensation = 0;
  private int rightCompensation = 0;
  private int topCompensation = 0;
  private int bottomCompensation = 0;
}
