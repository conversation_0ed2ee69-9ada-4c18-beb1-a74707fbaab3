package com.mc.tool.caesar.vpm.util;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import lombok.extern.slf4j.Slf4j;

/**
 * 主机HTTP接口.
 */
@Slf4j
public class DkmHttpApi {
  private static final int PORT = 8080;
  private static final String URL_FORMAT = "http://%s:%d%s";
  private static final String URI_PATH = "/mediacomm/log";

  /**
   * 保存日志.
   *
   * @param dir        保存路径
   * @param dkmAddress 主机ip地址
   * @return 保存成功与否
   */
  public static boolean saveLog(Path dir, String dkmAddress) {
    String url = String.format(URL_FORMAT, dkmAddress, PORT, URI_PATH);
    try {
      return saveFile(new URL(url), dir, dkmAddress);
    } catch (MalformedURLException exc) {
      log.error("Fail to save log!", exc);
      return false;
    } catch (RuntimeException exc) {
      log.error("Fail to save log!", exc);
      return false;
    }
  }

  private static boolean saveFile(URL url, Path dir, String fileNamePrefix) {
    HttpURLConnection connection = null;
    try {
      connection = (HttpURLConnection) url.openConnection();
      connection.setConnectTimeout(5000);
      connection.setReadTimeout(100000);
      connection.connect();

      String cd = connection.getHeaderField("Content-Disposition");

      String fileName = cd.replaceFirst("(?i)^.*filename=\"?([^\"]+)\"?.*$", "$1");
      fileName = fileName.replaceAll("[\\\\/:*?\"<>|]", "_");
      fileName = fileNamePrefix + fileName;
      return saveFile(connection.getInputStream(),
          dir.resolve(fileName));
    } catch (IOException ex) {
      log.error("Fail to save file", ex);
      return false;
    } finally {
      if (connection != null) {
        connection.disconnect();
      }
    }
  }

  private static boolean saveFile(InputStream inputStream, Path savePath)
      throws IOException {
    File tmpFile = null;
    try {
      Path parent = savePath.getParent();
      if (parent == null) {
        return false;
      }
      File parentFile = new File(parent.toString());
      File imageFile = File.createTempFile("temp", null, parentFile);
      tmpFile = imageFile;
      try (FileOutputStream outStream = new FileOutputStream(imageFile)) {
        readInputStream(inputStream, outStream);
      }
      Files.move(imageFile.toPath(), savePath, StandardCopyOption.ATOMIC_MOVE);
      return true;
    } catch (IOException ex) {
      log.error("Fail to save file", ex);
      return false;
    } finally {
      if (tmpFile != null && tmpFile.exists() && !tmpFile.delete()) {
        log.error("Fail to delete " + tmpFile.getName());
      }
    }
  }

  private static void readInputStream(InputStream inputStream, OutputStream outputStream)
      throws IOException {
    byte[] buffer = new byte[1024];
    int len;
    while ((len = inputStream.read(buffer)) != -1) {
      outputStream.write(buffer, 0, len);
    }
  }
}
