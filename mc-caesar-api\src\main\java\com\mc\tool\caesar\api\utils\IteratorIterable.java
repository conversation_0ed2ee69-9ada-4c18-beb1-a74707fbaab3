package com.mc.tool.caesar.api.utils;

import java.util.Iterator;

/**
 * 只能获取一次iterator的itreable.
 */
public class IteratorIterable<T> implements Iterable<T> {

  private final Iterator<T> iterator;
  private boolean used = false;

  public IteratorIterable(Iterator<T> iterator) {
    this.iterator = iterator;
  }

  @Override
  public Iterator<T> iterator() {
    if (this.used) {
      throw new IllegalStateException("Iterator already consumed!");
    }
    this.used = true;
    return this.iterator;
  }
}

