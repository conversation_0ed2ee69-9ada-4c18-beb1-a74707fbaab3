package com.mc.tool.caesar.api.datamodel;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.datamodel.extargs.DhdmiSelection;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * MultiviewSource.
 */
@Getter
@Slf4j
public class MultiviewSource extends AbstractData implements CaesarCommunicatable {
  public static final String PROPERTY_BASE = "MultiviewSource.";
  public static final String PROPERTY_UC_CONNECTION_STATE = PROPERTY_BASE + "UcConnectionState";
  public static final String PROPERTY_US_CONNECTED_CPU_INDEX =
      PROPERTY_BASE + "UsConnectedCpuIndex";
  public static final String PROPERTY_CPU_HDMI = PROPERTY_BASE + "CpuHdmi";

  private int ucConnectionState; // connect_state: 0:full 2:private  4:video
  private int usConnectedCpuIndex; // CPU索引+1
  private DhdmiSelection cpuHdmi = DhdmiSelection.HDMI1;

  /**
   * .
   */
  public MultiviewSource(CustomPropertyChangeSupport pcs,
                         ConfigDataManager configDataManager, int oid,
                         String fqn) {
    super(pcs, configDataManager, oid, fqn);
  }

  @Override
  public void readData(CfgReader cfgReader) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    setUcConnectionState(cfgReader.readByteValue());
    setUsConnectedCpuIndex(cfgReader.read2ByteValue());
    int whichCpuHdmi = cfgReader.readByteValue();
    if (whichCpuHdmi >= 0 && whichCpuHdmi < DhdmiSelection.values().length) {
      setCpuHdmi(DhdmiSelection.values()[whichCpuHdmi]);
    } else {
      log.warn("Error read MultiviewSource cpuHdmi");
    }
  }

  @Override
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    cfgWriter.writeByte((byte) ucConnectionState);
    cfgWriter.write2ByteSmallEndian(usConnectedCpuIndex);
    cfgWriter.writeByte((byte) cpuHdmi.getValue());
  }

  /**
   * .
   */
  public void setUcConnectionState(int ucConnectionState) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (this.ucConnectionState != ucConnectionState) {
      int oldValue = this.ucConnectionState;
      this.ucConnectionState = ucConnectionState;
      //PlatformUtility.runInFxThread(() -> ucConnectionStateProperty.set(ucConnectionState));
      firePropertyChange(PROPERTY_UC_CONNECTION_STATE, oldValue, ucConnectionState);
    }
  }

  /**
   * .
   */
  public void setUsConnectedCpuIndex(int usConnectedCpuIndex) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (this.usConnectedCpuIndex != usConnectedCpuIndex) {
      int oldValue = this.usConnectedCpuIndex;
      this.usConnectedCpuIndex = usConnectedCpuIndex;
      //PlatformUtility.runInFxThread(() -> usConnectedCpuIndexProperty.set(usConnectedCpuIndex));
      firePropertyChange(PROPERTY_US_CONNECTED_CPU_INDEX, oldValue, usConnectedCpuIndex);
    }
  }

  /**
   * .
   */
  public void setCpuHdmi(DhdmiSelection cpuHdmi) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    DhdmiSelection oldValue = this.cpuHdmi;
    if (oldValue != cpuHdmi) {
      this.cpuHdmi = cpuHdmi;
      //PlatformUtility.runInFxThread(() -> cpuHdmiProperty.set(cpuHdmi));
      firePropertyChange(ConsoleData.PROPERTY_CPU_HDMI, oldValue, cpuHdmi);
    }
  }

  /**
   * 获取信号源的CPU数据.
   */
  public CpuData getCpuData() {
    if (usConnectedCpuIndex == 0) {
      return null;
    }
    return getConfigDataManager().getCpuData(usConnectedCpuIndex - 1);
  }

  public boolean isStatusPrivate() {
    return Utilities.areBitsSet(ucConnectionState, CaesarConstants.Console.Status.PRIVATE_MODE);
  }

  public boolean isStatusVideoOnly() {
    return Utilities.areBitsSet(ucConnectionState, CaesarConstants.Console.Status.VIDEO_MODE);
  }
}
