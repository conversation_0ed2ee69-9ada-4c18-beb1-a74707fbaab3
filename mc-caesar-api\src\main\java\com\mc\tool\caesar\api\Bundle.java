package com.mc.tool.caesar.api;

import java.util.ResourceBundle;

/**
 * Bundle.
 */
public class Bundle {

  static class NbBundle {

    public static String getMessage(Class<?> clazz, String id) {
      return ResourceBundle.getBundle("com.mc.tool.caesar.api.Bundle").getString(id);
    }
  }

  static String arrangement_M1x4() {
    return NbBundle.getMessage(Bundle.class, "Arrangement.M1x4");
  }

  static String arrangement_M2x2() {
    return NbBundle.getMessage(Bundle.class, "Arrangement.M2x2");
  }

  static String functionKey_command_assigncon() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.assigncon");
  }

  static String functionKey_command_assigncpu() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.assigncpu");
  }

  static String functionKey_command_connect() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.connect");
  }

  static String functionKey_command_connectvideo() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.connectvideo");
  }

  static String functionKey_command_disconnect() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.disconnect");
  }

  static String functionKey_command_get() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.get");
  }

  static String functionKey_command_getvideo() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.getvideo");
  }

  static String functionKey_command_activeScenario() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.activeVideoWallScenario");
  }

  static String functionKey_command_activeScenarioWindow() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.activeVideoWallScenarioWindow");
  }

  static String functionKey_command_switchMultiViewLayout() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.switchMultiViewLayout");
  }

  static String functionKey_command_switchMultiViewSignal() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.switchMultiViewSignal");
  }


  static String functionKey_command_switchMultiViewPush() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.switchMultiViewPush");
  }


  static String functionKey_command_switchMultiViewGet() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.switchMultiViewGet");
  }

  static String functionKey_command_login() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.login");
  }

  static String functionKey_command_logout() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.logout");
  }

  static String functionKey_command_noFunction() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.noFunction");
  }

  static String functionKey_command_private() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.private");
  }

  static String functionKey_command_push() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.push");
  }

  static String functionKey_command_pushvideo() {
    return NbBundle.getMessage(Bundle.class, "FunctionKey.command.pushvideo");
  }

  static String keyboard_De_Ch_150G() {
    return NbBundle.getMessage(Bundle.class, "Keyboard.DE_CH_150G");
  }

  static String keyboard_De_De_129() {
    return NbBundle.getMessage(Bundle.class, "Keyboard.DE_DE_129");
  }

  static String keyboard_En_Gb_166() {
    return NbBundle.getMessage(Bundle.class, "Keyboard.EN_GB_166");
  }

  static String keyboard_En_Gb_168() {
    return NbBundle.getMessage(Bundle.class, "Keyboard.EN_GB_168");
  }

  static String keyboard_En_Us_103P() {
    return NbBundle.getMessage(Bundle.class, "Keyboard.EN_US_103P");
  }

  static String keyboard_Fr_Be_120() {
    return NbBundle.getMessage(Bundle.class, "Keyboard.FR_BE_120");
  }

  static String keyboard_Fr_Ch_150F() {
    return NbBundle.getMessage(Bundle.class, "Keyboard.FR_CH_150F");
  }

  static String keyboard_Fr_Fr_189() {
    return NbBundle.getMessage(Bundle.class, "Keyboard.FR_FR_189");
  }

  static String keyboard_Ru_1251() {
    return NbBundle.getMessage(Bundle.class, "Keyboard.RU_1251");
  }

  static String osdPosition_bottom_left() {
    return NbBundle.getMessage(Bundle.class, "OsdPosition.bottom.left");
  }

  static String osdPosition_bottom_right() {
    return NbBundle.getMessage(Bundle.class, "OsdPosition.bottom.right");
  }

  static String osdPosition_centered() {
    return NbBundle.getMessage(Bundle.class, "OsdPosition.centered");
  }

  static String osdPosition_custom() {
    return NbBundle.getMessage(Bundle.class, "OsdPosition.custom");
  }

  static String osdPosition_top_left() {
    return NbBundle.getMessage(Bundle.class, "OsdPosition.top.left");
  }

  static String osdPosition_top_right() {
    return NbBundle.getMessage(Bundle.class, "OsdPosition.top.right");
  }

  static String suspendPosition_top_left() {
    return NbBundle.getMessage(Bundle.class, "SuspendPosition.topleft");
  }

  static String suspendPosition_top_right() {
    return NbBundle.getMessage(Bundle.class, "SuspendPosition.topright");
  }

  public static String suspendPosition_top_center() {
    return NbBundle.getMessage(Bundle.class, "SuspendPosition.topcenter");
  }

  static String owner_Screen1() {
    return NbBundle.getMessage(Bundle.class, "Owner.Screen1");
  }

  static String owner_Screen2() {
    return NbBundle.getMessage(Bundle.class, "Owner.Screen2");
  }

  static String owner_Screen3() {
    return NbBundle.getMessage(Bundle.class, "Owner.Screen3");
  }

  static String owner_Screen4() {
    return NbBundle.getMessage(Bundle.class, "Owner.Screen4");
  }

  static String owner_Shared() {
    return NbBundle.getMessage(Bundle.class, "Owner.Shared");
  }

  static String teraConnectionModel_file_conflict_cpu_message() {
    return NbBundle.getMessage(Bundle.class, "TeraConnectionModel.file.conflict.cpu.message");
  }

  static String teraConnectionModel_file_conflict_snmp_message() {
    return NbBundle.getMessage(Bundle.class, "TeraConnectionModel.file.conflict.snmp.message");
  }

  static String teraConnectionModel_file_conflict_title() {
    return NbBundle.getMessage(Bundle.class, "TeraConnectionModel.file.conflict.title");
  }

  static String teraVersion_Matx008C() {
    return NbBundle.getMessage(Bundle.class, "TeraVersion.MATX008C");
  }

  static String teraVersion_Matx016() {
    return NbBundle.getMessage(Bundle.class, "TeraVersion.MATX016");
  }

  static String teraVersion_Matx048() {
    return NbBundle.getMessage(Bundle.class, "TeraVersion.MATX048");
  }

  static String teraVersion_Matx048C() {
    return NbBundle.getMessage(Bundle.class, "TeraVersion.MATX048C");
  }

  static String teraVersion_Matx080() {
    return NbBundle.getMessage(Bundle.class, "TeraVersion.MATX080");
  }

  static String teraVersion_Matx080C() {
    return NbBundle.getMessage(Bundle.class, "TeraVersion.MATX080C");
  }

  static String teraVersion_Matx160() {
    return NbBundle.getMessage(Bundle.class, "TeraVersion.MATX160");
  }

  static String teraVersion_Matx21R() {
    return NbBundle.getMessage(Bundle.class, "TeraVersion.MATX21R");
  }

  static String teraVersion_Matx288() {
    return NbBundle.getMessage(Bundle.class, "TeraVersion.MATX288");
  }

  static String teraVersion_Matx576M() {
    return NbBundle.getMessage(Bundle.class, "TeraVersion.MATX576M");
  }

  static String teraVersion_Matx576S() {
    return NbBundle.getMessage(Bundle.class, "TeraVersion.MATX576S");
  }

  static String teraVersion_Matx6Bp() {
    return NbBundle.getMessage(Bundle.class, "TeraVersion.MATX6BP");
  }

  static String teraVersion_Tera048() {
    return NbBundle.getMessage(Bundle.class, "TeraVersion.TERA048");
  }

  static String teraVersion_Tera080() {
    return NbBundle.getMessage(Bundle.class, "TeraVersion.TERA080");
  }

  static String teraVersion_Tera160() {
    return NbBundle.getMessage(Bundle.class, "TeraVersion.TERA160");
  }

  static String teraVersion_Tera288() {
    return NbBundle.getMessage(Bundle.class, "TeraVersion.TERA288");
  }

  static String teraVersion_TeraSw() {
    return NbBundle.getMessage(Bundle.class, "TeraVersion.TERASW");
  }

  static String timeZone_Utc_0() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_0");
  }

  static String timeZone_Utc_Minus_1() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_MINUS_1");
  }

  static String timeZone_Utc_Minus_10() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_MINUS_10");
  }

  static String timeZone_Utc_Minus_11() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_MINUS_11");
  }

  static String timeZone_Utc_Minus_12() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_MINUS_12");
  }

  static String timeZone_Utc_Minus_2() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_MINUS_2");
  }

  static String timeZone_Utc_Minus_3() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_MINUS_3");
  }

  static String timeZone_Utc_Minus_4() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_MINUS_4");
  }

  static String timeZone_Utc_Minus_5() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_MINUS_5");
  }

  static String timeZone_Utc_Minus_6() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_MINUS_6");
  }

  static String timeZone_Utc_Minus_7() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_MINUS_7");
  }

  static String timeZone_Utc_Minus_8() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_MINUS_8");
  }

  static String timeZone_Utc_Minus_9() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_MINUS_9");
  }

  static String timeZone_Utc_Plus_1() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_PLUS_1");
  }

  static String timeZone_Utc_Plus_10() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_PLUS_10");
  }

  static String timeZone_Utc_Plus_11() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_PLUS_11");
  }

  static String timeZone_Utc_Plus_12() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_PLUS_12");
  }

  static String timeZone_Utc_Plus_2() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_PLUS_2");
  }

  static String timeZone_Utc_Plus_3() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_PLUS_3");
  }

  static String timeZone_Utc_Plus_4() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_PLUS_4");
  }

  static String timeZone_Utc_Plus_5() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_PLUS_5");
  }

  static String timeZone_Utc_Plus_6() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_PLUS_6");
  }

  static String timeZone_Utc_Plus_7() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_PLUS_7");
  }

  static String timeZone_Utc_Plus_8() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_PLUS_8");
  }

  static String timeZone_Utc_Plus_9() {
    return NbBundle.getMessage(Bundle.class, "TimeZone.UTC_PLUS_9");
  }

  static String videoMode_1024x768() {
    return NbBundle.getMessage(Bundle.class, "VideoMode.1024x768");
  }

  static String videoMode_1280x1024() {
    return NbBundle.getMessage(Bundle.class, "VideoMode.1280x1024");
  }

  static String videoMode_1280x800() {
    return NbBundle.getMessage(Bundle.class, "VideoMode.1280x800");
  }

  static String videoMode_1600x1200() {
    return NbBundle.getMessage(Bundle.class, "VideoMode.1600x1200");
  }

  static String videoMode_1680x1050() {
    return NbBundle.getMessage(Bundle.class, "VideoMode.1680x1050");
  }

  static String videoMode_1920x1080() {
    return NbBundle.getMessage(Bundle.class, "VideoMode.1920x1080");
  }

  static String videoMode_1920x1200() {
    return NbBundle.getMessage(Bundle.class, "VideoMode.1920x1200");
  }

  static String videoMode_800x600() {
    return NbBundle.getMessage(Bundle.class, "VideoMode.800x600");
  }

  static String videoMode_var() {
    return NbBundle.getMessage(Bundle.class, "VideoMode.var");
  }

  //private void Bundle() {}

  public static String multiviewLayout_full_screen() {
    return NbBundle.getMessage(Bundle.class, "MultiviewLayout.fullScreen");
  }

  public static String multiviewLayout_two_grid() {
    return NbBundle.getMessage(Bundle.class, "MultiviewLayout.twoGrid");
  }

  public static String multiviewLayout_four_grid() {
    return NbBundle.getMessage(Bundle.class, "MultiviewLayout.fourGrid");
  }

  public static String multiviewLayout_side_bar() {
    return NbBundle.getMessage(Bundle.class, "MultiviewLayout.sideBar");
  }

  public static String multiviewChannel(int ch) {
    return NbBundle.getMessage(Bundle.class, "multiview.channel") + ch;
  }

  public static String getMessage(String key) {
    return NbBundle.getMessage(Bundle.class, key);
  }

  public static String multiviewOutputMode(int ch) {
    return ch + Bundle.NbBundle.getMessage(Bundle.class, "multiview.output_mode");
  }
}

