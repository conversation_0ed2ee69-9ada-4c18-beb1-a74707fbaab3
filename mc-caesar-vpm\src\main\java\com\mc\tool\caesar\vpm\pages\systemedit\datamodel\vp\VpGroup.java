package com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import lombok.Getter;

/**
 * .
 */
public class VpGroup extends VisualEditGroup {
  @Getter private VpConsoleData vpConsoleData;
  @Getter private ObservableList<VpConsoleData> outPortsList = FXCollections.observableArrayList();
  @Expose private StringProperty[] outPortNames = new StringProperty[8];
  @Getter
  @Expose private int id;

  public VpGroup() {}

  /**
   * Constructor.
   *
   * @param vpConsoleData vp数据
   */
  public VpGroup(VpConsoleData vpConsoleData) {
    this.nameProperty.set(vpConsoleData.getName());
    setVpConsoleData(vpConsoleData);
  }

  @Override
  public void init() {
    this.nameProperty.addListener(
        (observable, oldValue, newValue) -> {
          if (newValue != null) {
            int index = 0;
            for (VpConsoleData out : vpConsoleData.getOutPortList()) {
              if (out.getName().equals(oldValue + "[" + (index + 1) + "]")) {
                out.setName(newValue + "[" + (index + 1) + "]");
              }
              vpConsoleData.setOutPort(index, out);
              index++;
            }
          }
        });
    super.init();
  }

  /** . */
  public void setVpConsoleData(VpConsoleData vpConsoleData) {
    if (this.vpConsoleData != vpConsoleData) {
      int index = 0;
      if (this.vpConsoleData != null) {
        for (VpConsoleData out : this.vpConsoleData.getOutPortList()) {
          unbindOutPortName(index, out.nameProperty());
          index++;
        }
      }
      index = 0;
      for (VpConsoleData out : vpConsoleData.getOutPortList()) {
        bindOutPortName(index, out.nameProperty(), nameProperty.get() + "[" + (index + 1) + "]");
        vpConsoleData.setOutPort(index, out);

        index++;
      }
    }
    this.vpConsoleData = vpConsoleData;
    this.id = vpConsoleData.getId();
    this.outPortsList.setAll(vpConsoleData.getOutPortList());
    // 规避序列化问题
    for (int i = 0; i < outPortNames.length; i++) {
      if (outPortNames[i] == null) {
        outPortNames[i] = new SimpleStringProperty();
      }
    }
  }

  protected void bindOutPortName(int index, StringProperty vpName, String defaultName) {
    if (index < 0 || index >= outPortNames.length) {
      return;
    }
    if (outPortNames[index] == null) {
      outPortNames[index] = new SimpleStringProperty(defaultName);
    }
    vpName.bindBidirectional(outPortNames[index]);
  }

  protected void unbindOutPortName(int index, StringProperty vpName) {
    if (index < 0 || index >= outPortNames.length) {
      return;
    }
    vpName.unbindBidirectional(outPortNames[index]);
  }

}
