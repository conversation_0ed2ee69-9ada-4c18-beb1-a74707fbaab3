package com.mc.tool.caesar.vpm.gui.pages.hostupdate;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.caesar.vpm.pages.hostupdate.CaesarHostUpdatePage;
import com.mc.tool.caesar.vpm.pages.update.UpdateData;
import com.mc.tool.caesar.vpm.util.update.CaesarSampleSwuCreater;
import com.mc.tool.caesar.vpm.util.update.UpdateConstant;
import com.mc.tool.framework.utility.dialogues.FileDialogue;
import com.mc.tool.framework.utility.dialogues.FileDialogueFactory;
import com.mc.tool.framework.view.FrameView;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Predicate;
import javafx.scene.control.TreeItem;
import javafx.scene.control.TreeTableView;
import javafx.stage.Stage;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * .
 */
@RunWith(MockitoJUnitRunner.class)
public class LoadTest extends AppGuiTestBase {

  private static final String UPDATE_RESOURCE_DIR = "com/mc/tool/caesar/vpm/update/";

  @Mock private FileDialogueFactory fileDialogueFactory;

  @Mock private FileDialogue fileDialogue;

  @Override
  public void beforeAll() {}

  @Override
  public void start(Stage stage) throws Exception {
    FrameView.setModule(
        binder -> binder.bind(FileDialogueFactory.class).toInstance(fileDialogueFactory));
    when(fileDialogueFactory.createFileDialogue()).thenReturn(fileDialogue);
    super.start(stage);
  }

  /**
   * .
   */
  public void testMatTemplate(String statusName, Map<String, Boolean> swuNameMatchMap) {
    try {
      File tempFile = loadResourceStatus(UPDATE_RESOURCE_DIR + statusName);
      clickOn("#" + CaesarHostUpdatePage.NAME);
      for (Map.Entry<String, Boolean> entry : swuNameMatchMap.entrySet()) {
        File updateFile = copyResourceToTemp(UPDATE_RESOURCE_DIR + entry.getKey(), "test", ".swu");
        when(fileDialogue.showOpenDialog(any())).thenReturn(updateFile);
        // 加载升级文件
        System.out.println(entry.getKey());
        clickOn(GuiTestConstants.HOST_LOAD_BUTTON);
        clickOn(GuiTestConstants.HOST_SELECT_ALL_BUTTON);
        // 检查各行的选中情况
        TreeTableView<UpdateData> tableView =
            (TreeTableView)
                lookup(GuiTestConstants.HOST_TABLE_VIEW).match((node) -> isShowing(node)).query();
        checkUpdateData(
            tableView.getRoot(),
            (treeItem) -> {
              UpdateData updateData = treeItem.getValue();
              if (updateData.getType().equals(UpdateConstant.UPDATE_APP_TYPE)
                  || updateData.getType().equals(UpdateConstant.UPDATE_FPGA_TYPE)
                  || updateData.getType().equals(UpdateConstant.UPDATE_SYS_TYPE)) {
                if (treeItem.getParent() != null
                    && treeItem
                        .getParent()
                        .getValue()
                        .getType()
                        .equals(UpdateConstant.UPDATE_CPU_TYPE)
                    && entry.getValue()) {
                  Assert.assertTrue(updateData.getSelected());
                  Assert.assertFalse(updateData.getDisabled());
                } else {
                  Assert.assertFalse(updateData.getSelected());
                  Assert.assertTrue(updateData.getDisabled());
                }
              }
              return true;
            },
            0);
        // 删除文件
        updateFile.delete();
      }
      tempFile.delete();
      // 关闭页面
      clickOn(GuiTestConstants.BUTTON_LIST_MENU);
      clickOn(GuiTestConstants.MENU_CLOSE_ALL);
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  /**
   * .
   */
  public void testIoTemplate(
      String statusName,
      Map<String, Boolean> swuNameMatchMap,
      boolean virtualIo,
      Predicate<TreeItem<UpdateData>> itemTest) {
    try {
      File tempFile = loadResourceStatus(UPDATE_RESOURCE_DIR + statusName);
      clickOn("#" + CaesarHostUpdatePage.NAME);
      for (Map.Entry<String, Boolean> entry : swuNameMatchMap.entrySet()) {
        File updateFile = copyResourceToTemp(UPDATE_RESOURCE_DIR + entry.getKey(), "test", ".swu");
        when(fileDialogue.showOpenDialog(any())).thenReturn(updateFile);
        // 加载升级文件
        System.out.println(entry.getKey());
        clickOn(GuiTestConstants.HOST_LOAD_BUTTON);
        clickOn(GuiTestConstants.HOST_SELECT_ALL_BUTTON);
        // 检查各行的选中情况
        TreeTableView<UpdateData> tableView =
            (TreeTableView)
                lookup(GuiTestConstants.HOST_TABLE_VIEW).match((node) -> isShowing(node)).query();
        Predicate<TreeItem<UpdateData>> test = null;
        if (itemTest != null) {
          test = itemTest;
        } else {
          test =
              (treeItem) -> {
                UpdateData updateData = treeItem.getValue();
                if (updateData.getType().equals(UpdateConstant.UPDATE_APP_TYPE)
                    || updateData.getType().equals(UpdateConstant.UPDATE_FPGA_TYPE)
                    || updateData.getType().equals(UpdateConstant.UPDATE_SYS_TYPE)) {
                  if (treeItem.getParent() != null
                      && treeItem
                          .getParent()
                          .getValue()
                          .getType()
                          .equals(UpdateConstant.UPDATE_IO_TYPE)
                      && entry.getValue()) {

                    if (virtualIo
                        && !treeItem.getParent().getValue().getName().equals("IO BOARD1")) {
                      Assert.assertFalse(updateData.getSelected());
                      Assert.assertTrue(updateData.getDisabled());
                    } else {
                      Assert.assertFalse(updateData.getDisabled());
                      Assert.assertTrue(updateData.getSelected());
                    }
                  } else {
                    Assert.assertFalse(updateData.getSelected());
                    Assert.assertTrue(updateData.getDisabled());
                  }
                }
                return true;
              };
        }
        checkUpdateData(tableView.getRoot(), test, 0);
        // 删除文件
        updateFile.delete();
      }
      tempFile.delete();
      // 关闭页面
      clickOn(GuiTestConstants.BUTTON_LIST_MENU);
      clickOn(GuiTestConstants.MENU_CLOSE_ALL);
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testSingle36Mat() {
    Map<String, Boolean> map = new HashMap<>();
    map.put("single_36_hw1.2_mat_sample.swu", true);
    map.put("single_96_hw2.2_mat_sample.swu", false);
    map.put("96t-1x36b_144_hw3.2_mat_sample.swu", false);
    map.put("96t-12x24b_288_hw3.2_mat_sample.swu", false);
    map.put("384-plugin_384_hw3.2_mat_sample.swu", false);
    map.put("816-plugin_816_hw3.2_mat_sample.swu", false);
    testMatTemplate("single_36_hw1.2_sample.status", map);
  }

  @Test
  public void testSingle36Io() {
    Map<String, Boolean> map = new HashMap<>();
    map.put("single_36_hw1.2_io_sample.swu", true);
    map.put("single_96_hw2.2_io_sample.swu", false);
    map.put("96t-1x36b_144_hw3.2_io_sample.swu", false);
    map.put("96t-12x24b_288_hw3.2_io_sample.swu", false);
    map.put("384-plugin_384_hw3.2_io_sample.swu", false);
    map.put("816-plugin_816_hw3.2_io_sample.swu", false);
    testIoTemplate("single_36_hw1.2_sample.status", map, true, null);
  }

  @Test
  public void testSingle96Mat() {
    Map<String, Boolean> map = new HashMap<>();
    map.put("single_36_hw1.2_mat_sample.swu", false);
    map.put("single_96_hw2.2_mat_sample.swu", true);
    map.put("96t-1x36b_144_hw3.2_mat_sample.swu", false);
    map.put("96t-12x24b_288_hw3.2_mat_sample.swu", false);
    map.put("384-plugin_384_hw3.2_mat_sample.swu", false);
    map.put("816-plugin_816_hw3.2_mat_sample.swu", false);
    testMatTemplate("single_96_hw2.2_sample.status", map);
  }

  @Test
  public void testSingle96Io() {
    Map<String, Boolean> map = new HashMap<>();
    map.put("single_36_hw1.2_io_sample.swu", false);
    map.put("single_96_hw2.2_io_sample.swu", true);
    map.put("96t-1x36b_144_hw3.2_io_sample.swu", false);
    map.put("96t-12x24b_288_hw3.2_io_sample.swu", false);
    map.put("384-plugin_384_hw3.2_io_sample.swu", false);
    map.put("816-plugin_816_hw3.2_io_sample.swu", false);
    testIoTemplate("single_96_hw2.2_sample.status", map, true, null);
  }

  @Test
  public void test96t1x36bMat() {
    Map<String, Boolean> map = new HashMap<>();
    map.put("single_36_hw1.2_mat_sample.swu", false);
    map.put("single_96_hw2.2_mat_sample.swu", false);
    map.put("96t-1x36b_144_hw3.2_mat_sample.swu", true);
    map.put("96t-12x24b_288_hw3.2_mat_sample.swu", false);
    map.put("384-plugin_384_hw3.2_mat_sample.swu", false);
    map.put("816-plugin_816_hw3.2_mat_sample.swu", false);
    testMatTemplate("96t-1x36b_144_hw3.2_sample.status", map);
  }

  @Test
  public void test96t1x36bIo() {
    Map<String, Boolean> map = new HashMap<>();
    map.put("single_36_hw1.2_io_sample.swu", false);
    map.put("single_96_hw2.2_io_sample.swu", false);
    map.put("96t-1x36b_144_hw3.2_io_sample.swu", true);
    map.put("96t-12x24b_288_hw3.2_io_sample.swu", false);
    map.put("384-plugin_384_hw3.2_io_sample.swu", false);
    map.put("816-plugin_816_hw3.2_io_sample.swu", false);
    testIoTemplate("96t-1x36b_144_hw3.2_sample.status", map, false, null);
  }

  @Test
  public void test96t12x24bMat() {
    Map<String, Boolean> map = new HashMap<>();
    map.put("single_36_hw1.2_mat_sample.swu", false);
    map.put("single_96_hw2.2_mat_sample.swu", false);
    map.put("96t-1x36b_144_hw3.2_mat_sample.swu", false);
    map.put("96t-12x24b_288_hw3.2_mat_sample.swu", true);
    map.put("384-plugin_384_hw3.2_mat_sample.swu", false);
    map.put("816-plugin_816_hw3.2_mat_sample.swu", false);
    testMatTemplate("96t-12x24b_288_hw3.2_sample.status", map);
  }

  @Test
  public void test96t12x24bIo() {
    Map<String, Boolean> map = new HashMap<>();
    map.put("single_36_hw1.2_io_sample.swu", false);
    map.put("single_96_hw2.2_io_sample.swu", false);
    map.put("96t-1x36b_144_hw3.2_io_sample.swu", false);
    map.put("96t-12x24b_288_hw3.2_io_sample.swu", true);
    map.put("384-plugin_384_hw3.2_io_sample.swu", false);
    map.put("816-plugin_816_hw3.2_io_sample.swu", false);
    testIoTemplate("96t-12x24b_288_hw3.2_sample.status", map, false, null);
  }

  @Test
  public void test384Mat() {
    Map<String, Boolean> map = new HashMap<>();
    map.put("single_36_hw1.2_mat_sample.swu", false);
    map.put("single_96_hw2.2_mat_sample.swu", false);
    map.put("96t-1x36b_144_hw3.2_mat_sample.swu", false);
    map.put("96t-12x24b_288_hw3.2_mat_sample.swu", false);
    map.put("384-plugin_384_hw3.2_mat_sample.swu", true);
    map.put("816-plugin_816_hw3.2_mat_sample.swu", false);
    testMatTemplate("384-plugin_384_hw3.2_sample.status", map);
  }

  @Test
  public void test384Io() {
    Map<String, Boolean> map = new HashMap<>();
    map.put("single_36_hw1.2_io_sample.swu", false);
    map.put("single_96_hw2.2_io_sample.swu", false);
    map.put("96t-1x36b_144_hw3.2_io_sample.swu", false);
    map.put("96t-12x24b_288_hw3.2_io_sample.swu", false);
    map.put("384-plugin_384_hw3.2_io_sample.swu", true);
    map.put("816-plugin_816_hw3.2_io_sample.swu", false);
    map.put("384_816_hw3.2_io_sample.swu", true);
    testIoTemplate("384-plugin_384_hw3.2_sample.status", map, false, null);

    map.clear();
    ;
    map.put("384_816_hw3.2_io_sample.swu", true);
    testIoTemplate(
        "384-plugin_384_hw3.2_sample.status",
        map,
        false,
        (item) -> {
          UpdateData updateData = item.getValue();
          if (updateData.getType().equals(UpdateConstant.UPDATE_FPGA_TYPE)
              && updateData.getLevel1() == 0
              && item.getParent() != null
              && item.getParent().getValue().getType().equals(UpdateConstant.UPDATE_IO_TYPE)) {
            Assert.assertEquals(
                CaesarSampleSwuCreater.MIX_IO_384_MASTER_VERSION,
                updateData.getUpdateVersion().getMasterVersion());
          }
          return true;
        });
  }

  @Test
  public void test816Mat() {
    Map<String, Boolean> map = new HashMap<>();
    map.put("single_36_hw1.2_mat_sample.swu", false);
    map.put("single_96_hw2.2_mat_sample.swu", false);
    map.put("96t-1x36b_144_hw3.2_mat_sample.swu", false);
    map.put("96t-12x24b_288_hw3.2_mat_sample.swu", false);
    map.put("384-plugin_384_hw3.2_mat_sample.swu", false);
    map.put("816-plugin_816_hw3.2_mat_sample.swu", true);
    testMatTemplate("816-plugin_816_hw3.2_sample.status", map);
  }

  @Test
  public void test816Io() {
    Map<String, Boolean> map = new HashMap<>();
    map.put("single_36_hw1.2_io_sample.swu", false);
    map.put("single_96_hw2.2_io_sample.swu", false);
    map.put("96t-1x36b_144_hw3.2_io_sample.swu", false);
    map.put("96t-12x24b_288_hw3.2_io_sample.swu", false);
    map.put("384-plugin_384_hw3.2_io_sample.swu", false);
    map.put("816-plugin_816_hw3.2_io_sample.swu", true);
    map.put("384_816_hw3.2_io_sample.swu", true);

    testIoTemplate("816-plugin_816_hw3.2_sample.status", map, false, null);

    map.clear();
    ;
    map.put("384_816_hw3.2_io_sample.swu", true);
    testIoTemplate(
        "816-plugin_816_hw3.2_sample.status",
        map,
        false,
        (item) -> {
          UpdateData updateData = item.getValue();
          if (updateData.getType().equals(UpdateConstant.UPDATE_FPGA_TYPE)
              && updateData.getLevel1() == 0
              && item.getParent() != null
              && item.getParent().getValue().getType().equals(UpdateConstant.UPDATE_IO_TYPE)) {
            Assert.assertEquals(
                CaesarSampleSwuCreater.MIX_IO_816_MASTER_VERSION,
                updateData.getUpdateVersion().getMasterVersion());
          }
          return true;
        });
  }
}
