package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarConstants.Extender.Status;
import com.mc.tool.caesar.api.CaesarExtArg;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.datamodel.extargs.ExtAudioTrigger;
import com.mc.tool.caesar.api.datamodel.extargs.ExtDpMode;
import com.mc.tool.caesar.api.datamodel.extargs.ExtUartBaudRate;
import com.mc.tool.caesar.api.datamodel.extargs.ExtVideoQp;
import com.mc.tool.caesar.api.datamodel.extargs.ExtenderAnalogAudioInput;
import com.mc.tool.caesar.api.datamodel.vp.VpType;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.BitFieldEntry;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.api.version.ApiDataConverter;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.CharacterCodingException;
import java.nio.charset.StandardCharsets;
import java.util.BitSet;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.util.Pair;
import javax.xml.bind.DatatypeConverter;
import lombok.Getter;

/**
 * ExtenderData.
 *
 * @brief extender的配置信息.
 */
public final class ExtenderData extends AbstractData implements DataObject, CaesarCommunicatable {

  public static final String PROPERTY_BASE = "ExtenderData.";
  public static final String PROPERTY_NAME = "ExtenderData.Name";
  public static final String PROPERTY_STATUS = "ExtenderData.Status";
  public static final String PROPERTY_EXTENDEDSTATUS = "ExtenderData.ExtendedStatus";
  public static final String PROPERTY_STATUS_ACTIVE = "ExtenderData.Status.Active";
  public static final String PROPERTY_STATUS_DELETE = "ExtenderData.Status.Delete";
  public static final String PROPERTY_STATUS_FIX_PORT = "ExtenderData.Status.FixPort";
  public static final String PROPERTY_STATUS_NEW_DATA = "ExtenderData.Status.NewData";
  public static final String PROPERTY_STATUS_ONLINE = "ExtenderData.Status.Online";
  public static final String PROPERTY_STATUS_USB_DISK_ENABLE = "ExtenderData.Status.UsbDiskEnable";
  public static final String PROPERTY_DHDMI_SELECTION = "ExtenderData.Status.DhdmiSelection";
  public static final String PROPERTY_VIDEO_QP = "ExtenderData.Status.VideoQp";
  public static final String PROPERTY_TOUCHING_SCREEN = "ExtenderData.Status.TouchingSreen";
  public static final String PROPERTY_STATUS_MULTICONTROLERROR =
      "ExtenderData.Status.MultiControlError";
  public static final String PROPERTY_PORT = "ExtenderData.Port";
  public static final String PROPERTY_RDPORT = "ExtenderData.RDPort";
  public static final String PROPERTY_REMOTE_PORT = PROPERTY_BASE + "RemotePort";
  public static final String PROPERTY_REMOTE_RDPORT = PROPERTY_BASE + "RemoveRDPort";
  public static final String PROPERTY_ID = "ExtenderData.ID";
  public static final String PROPERTY_TYPE = "ExtenderData.Type";
  public static final String PROPERTY_CPU_CON = "ExtenderData.CpuCon";
  public static final String PROPERTY_GENERALOSD_DATA = "ExtenderData.GeneralOsdData";
  public static final String PROPERTY_EXTENDEDSTATUSINFO = "ExtenderData.ExtendedStatusInfo";
  public static final String PROPERTY_EDID = "ExtenderData.Edid";
  public static final String PROPERTY_VERSION = "ExtenderData.Version";
  public static final String PROPERTY_SERIAL = "ExtenderData.Serial";
  public static final String PROPERTY_ANALOG_AUDIO_INPUT = "ExtenderData.AnalogAudioInput";
  public static final String PROPERTY_AUDIO_TRIGGER = "ExtenderData.AudioTrigger";
  public static final String PROPERTY_EVENT_ENABLE = "ExtenderData.EventEnable";
  public static final String PROPERTY_TRIGGER_HOLD_TIME = "ExtenderData.TriggerHoldTime";

  public static final String PROPERTY_HIGH_COMPRESSION_RATIO = "Extender.HighCompressionRatio";
  public static final String PROPERTY_OSD_MENU = "ExtenderData.OsdMenu";
  public static final String PROPERTY_ICRON_ENABLE = "ExtenderData.IcronEnable";
  public static final String PROPERTY_UART_BAUDRATE = "ExtenderData.UartBaudrate";
  public static final String PROPERTY_DOUBLEDP_ENABLE = "ExtenderData.DoubledpEnable";
  public static final Map<String, Collection<BitFieldEntry>> BIT_FIELD_MAP;


  static {
    Collection<BitFieldEntry> bitMapStatus = new HashSet<>();
    bitMapStatus.add(new BitFieldEntry(ExtenderData.PROPERTY_STATUS_ACTIVE, Status.ACTIVE));
    bitMapStatus.add(new BitFieldEntry(ExtenderData.PROPERTY_STATUS_DELETE, Status.DELETE));
    bitMapStatus.add(new BitFieldEntry(ExtenderData.PROPERTY_STATUS_FIX_PORT, Status.FIXPORT));
    bitMapStatus.add(new BitFieldEntry(ExtenderData.PROPERTY_STATUS_NEW_DATA, Status.NEW));
    bitMapStatus.add(new BitFieldEntry(ExtenderData.PROPERTY_STATUS_ONLINE, Status.ONLINE));

    Map<String, Collection<BitFieldEntry>> bitMaps = new HashMap<>();

    bitMaps.put(ExtenderData.PROPERTY_STATUS, Collections.unmodifiableCollection(bitMapStatus));

    BIT_FIELD_MAP = Collections.unmodifiableMap(bitMaps);
  }


  @Getter
  @Expose
  private int status = 0;
  @Getter
  @Expose
  private int extendedStatus = 0;
  @Expose
  @Getter
  private int remotePort = 0; // 有连接关系时，这里保存的是端口的连接端口
  @Getter
  @Expose
  private int rdPort = 0; // redundant 端口连接的port, grid port
  @Expose
  @Getter
  private int rdRemotePort = 0; // 有连接关系时，这里保存的是端口的连接端口
  @Expose
  private int id = 0;
  @Getter
  @Expose
  private int type = 0; // 类型，见TeraConstants.EXTENDER.TYPE.CON~TYPE_UNITYPE
  @Getter
  @Expose
  private int cpuCon = 0; // 关联的cpu或con的oid + 1
  @Expose
  @Getter
  private VersionSet version = new VersionSet();
  @Expose
  @Getter
  private ExtenderStatusInfo extenderStatusInfo = new ExtenderStatusInfo();

  //properties raw data
  @Expose
  private String name;
  @Expose
  private Map<Integer, byte[]> edidMap = new HashMap<>();
  @Expose
  private Map<Integer, ResolutionData> resolutionMap = new HashMap<>();
  @Getter
  @Expose
  private int port;
  @Getter
  @Expose
  private String serial;

  // properties for ui updating.
  @Getter
  @Expose
  private StringProperty nameProperty = new SimpleStringProperty("");
  @Expose
  private Map<Integer, StringProperty> edidPropertyMap = new HashMap<>();
  @Getter
  @Expose
  private StringProperty serialProperty = new SimpleStringProperty("");
  @Getter
  @Expose
  private BooleanProperty usbDiskEnableProperty = new SimpleBooleanProperty(false);
  @Getter
  @Expose
  private StringProperty portProperty = new SimpleStringProperty(); // 连接的port，grid port

  @Expose
  private ObjectProperty<ExtenderAnalogAudioInput> analogAudioInput = new SimpleObjectProperty<>(
      ExtenderAnalogAudioInput.LINE_IN);
  @Getter
  @Expose
  private ObjectProperty<ExtVideoQp> videoQpProperty = new SimpleObjectProperty<>(
      ExtVideoQp.QP_1);
  @Getter
  @Expose
  private BooleanProperty touchEnableProperty = new SimpleBooleanProperty(false);
  @Getter
  @Expose
  private ObjectProperty<ExtAudioTrigger> audioTriggerProperty =
      new SimpleObjectProperty<>(ExtAudioTrigger.DISABLE);
  @Getter
  @Expose private BooleanProperty eventEnableProperty = new SimpleBooleanProperty(false);
  @Getter
  @Expose
  private IntegerProperty triggerHoldTimeProperty = new SimpleIntegerProperty();
  @Getter
  @Expose
  private BooleanProperty highCompressionRatioProperty = new SimpleBooleanProperty(false);
  @Getter
  @Expose
  private BooleanProperty osdMenuProperty = new SimpleBooleanProperty(false);
  @Getter
  @Expose
  private BooleanProperty icronEnableProperty = new SimpleBooleanProperty(false);
  @Getter
  @Expose
  private ObjectProperty<ExtUartBaudRate> uartBaudRateProperty = new SimpleObjectProperty<>(
      ExtUartBaudRate.BR_115200);
  @Getter
  @Expose
  private ObjectProperty<ExtDpMode> dpModeProperty =
      new SimpleObjectProperty<>(ExtDpMode.SINGLE_DP_MODE);
  /**
   * 参数是否存在，用于判断是否需要发送给设备.
   */
  @Expose
  private final BitSet argBitSet = new BitSet(256);


  /**
   * .
   */
  public ExtenderData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager, int oid,
      String fqn) {
    super(pcs, configDataManager, oid, fqn);

    initCommitRollback();
  }

  public void setExtArgExist(CaesarExtArg arg, boolean exist) {
    argBitSet.set(arg.getValue(), exist);
  }

  public boolean hasExtArg(CaesarExtArg arg) {
    return argBitSet.get(arg.getValue());
  }

  /**
   * 设置属性.
   *
   * @param property 属性名称.
   * @param value    属性值
   */
  public void setProperty(String property, Object value) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (property.startsWith(PROPERTY_EDID) && value instanceof String) {
      int index = Integer.parseInt(property.substring(PROPERTY_EDID.length()));
      setEdid(DatatypeConverter.parseHexBinary((String) value), index);
    } else if (property.equals(PROPERTY_STATUS_USB_DISK_ENABLE) && value instanceof Boolean) {
      PlatformUtility.runInFxThread(() -> usbDiskEnableProperty.set((Boolean) value));
    } else if (property.equals(PROPERTY_ANALOG_AUDIO_INPUT)
        && value instanceof ExtenderAnalogAudioInput) {
      PlatformUtility.runInFxThread(() -> analogAudioInput.set((ExtenderAnalogAudioInput) value));
    } else if (property.equals(PROPERTY_VIDEO_QP) && value instanceof ExtVideoQp) {
      PlatformUtility.runInFxThread(() -> videoQpProperty.set((ExtVideoQp) value));
    } else if (property.equals(PROPERTY_TOUCHING_SCREEN) && value instanceof Boolean) {
      PlatformUtility.runInFxThread(() -> touchEnableProperty.set((Boolean) value));
    } else if (property.equals(PROPERTY_AUDIO_TRIGGER) && value instanceof ExtAudioTrigger) {
      PlatformUtility.runInFxThread(() -> audioTriggerProperty.set((ExtAudioTrigger) value));
    } else if (property.equals(PROPERTY_EVENT_ENABLE) && value instanceof Boolean) {
      PlatformUtility.runInFxThread(() -> eventEnableProperty.set((Boolean) value));
    } else if (property.equals(PROPERTY_TRIGGER_HOLD_TIME) && value instanceof Number) {
      PlatformUtility.runInFxThread(() -> triggerHoldTimeProperty.set(((Number) value).intValue()));
    } else if (property.equals(PROPERTY_HIGH_COMPRESSION_RATIO) && value instanceof Boolean) {
      PlatformUtility.runInFxThread(() -> highCompressionRatioProperty.set((Boolean) value));
    } else if (property.equals(PROPERTY_OSD_MENU) && value instanceof Boolean) {
      PlatformUtility.runInFxThread(() -> osdMenuProperty.set((Boolean) value));
    } else if (property.equals(PROPERTY_ICRON_ENABLE) && value instanceof Boolean) {
      PlatformUtility.runInFxThread(() -> icronEnableProperty.set((Boolean) value));
    } else if (property.equals(PROPERTY_UART_BAUDRATE) && value instanceof ExtUartBaudRate) {
      PlatformUtility.runInFxThread(() -> uartBaudRateProperty.set((ExtUartBaudRate) value));
    } else if (property.equals(PROPERTY_DOUBLEDP_ENABLE) && value instanceof ExtDpMode) {
      PlatformUtility.runInFxThread(() -> dpModeProperty.set((ExtDpMode) value));
    }
  }

  /**
   * 设置序列号.
   */
  public void setSerial(String serial) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    String oldValue = this.serial;
    try {
      byte[] bytes = DatatypeConverter.parseHexBinary(serial);
      String value =
          StandardCharsets.US_ASCII.newDecoder().decode(ByteBuffer.wrap(bytes)).toString();
      this.serial = value;
    } catch (CharacterCodingException exception) {
      this.serial = "Invalid";
    }
    this.pcs.fireIndexedPropertyChange(PROPERTY_SERIAL, getOid(), oldValue, serial);
    PlatformUtility.runInFxThread(() -> this.serialProperty.set(this.serial));
  }

  /**
   * 获取序列号的字节形式.
   *
   * @return 序列号字节数组
   */
  public byte[] getSerialBytes() {
    try {
      String serial = getSerial();
      if (serial == null || serial.isEmpty()) {
        return new byte[0];
      }
      return StandardCharsets.US_ASCII.newEncoder()
          .encode(CharBuffer.wrap(getSerial().toCharArray())).array();
    } catch (CharacterCodingException exc) {
      return new byte[0];
    }
  }

  /**
   * 设置版本信息.
   *
   * @param version 版本信息
   */
  public void setVersion(VersionSet version) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    VersionSet oldValue = this.version;
    this.version = version;
    this.pcs.fireIndexedPropertyChange(PROPERTY_VERSION, getOid(), oldValue, version);
  }

  @Override
  public void initDefaults() {
    super.initDefaults();

    setType(0);
    setCpuCon(0);

    setId(0);
    setName("");
    setPort(0);
    setRdPort(0);
    setStatus(0);
    setExtendedStatus(0);
    setSerial("");
  }

  /**
   * 获取edid数据.
   *
   * @return edid数据.
   */
  public byte[] getEdid(int index) {
    byte[] result = edidMap.get(index);
    if (result != null) {
      return result.clone();
    }
    return new byte[0];
  }

  /**
   * 获取分辨率.
   *
   * @param index 接口索引.
   * @return 分辨率
   */
  public ResolutionData getResolution(int index) {
    ResolutionData result = new ResolutionData();
    ResolutionData item = resolutionMap.get(index);
    if (item != null) {
      result.setWidth((int) item.getWidth());
      result.setHeight((int) item.getHeight());
    }
    return result;
  }

  /**
   * .
   */
  public void setEdid(byte[] edid, int index) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (edid == null) {
      return;
    }
    StringProperty edidProperty = getEdidProperty(index);
    PlatformUtility.runInFxThread(() -> edidProperty.set(DatatypeConverter.printHexBinary(edid)));
    byte[] oldValue = getEdid(index);
    edidMap.put(index, edid.clone());
    this.pcs.fireIndexedPropertyChange(PROPERTY_EDID, getOid(), oldValue, edid);
  }

  /**
   * 设置分辨率.
   *
   * @param index          索引
   * @param resolutionData 分辨率数据
   */
  public void setResolution(int index, ResolutionData resolutionData) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    resolutionMap.put(index, resolutionData);
  }

  /**
   * .
   */
  public void setCpuCon(int cpuCon) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.cpuCon;
    this.cpuCon = cpuCon;
    firePropertyChange(ExtenderData.PROPERTY_CPU_CON, oldValue, this.cpuCon);
  }

  @Override
  public int getId() {
    return this.id;
  }

  /**
   * .
   */
  public void setId(int id) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.id;
    this.id = id;
    firePropertyChange(ExtenderData.PROPERTY_ID, oldValue, this.id);
  }

  @Override
  public String getName() {
    return this.name;
  }

  /**
   * .
   */
  public void setName(String name) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    String oldValue = this.name;
    this.name = name;
    firePropertyChange(ExtenderData.PROPERTY_NAME, oldValue, this.name, new int[0]);
    PlatformUtility.runInFxThread(() -> {
      nameProperty.set(name);
    });
  }

  public ObjectProperty<ExtenderAnalogAudioInput> getAnalogAudioInputProperty() {
    return this.analogAudioInput;
  }


  /**
   * 获取edid属性.
   *
   * @param index edid索引
   * @return edid属性
   */
  public StringProperty getEdidProperty(int index) {
    return edidPropertyMap.computeIfAbsent(index, k -> new SimpleStringProperty());
  }

  protected void updatePortProperty() {
    if (isStatusRedundant()) {
      portProperty.set(String.format("%d/%d", port, rdPort));
    } else {
      portProperty.set(String.format("%d", port));
    }
  }

  /**
   * 解析端口属性字符串.
   *
   * @param input 端口字符串
   * @return 首端口与冗余端口的pair
   * @throws Exception 解析失败
   */
  public static Pair<Integer, Integer> parsePortProperty(String input) throws Exception {
    try {
      if (input.contains("/")) {
        String[] items = input.split("/");
        return new Pair<>(Integer.valueOf(items[0]), Integer.valueOf(items[1]));
      } else {
        return new Pair<>(Integer.valueOf(input), 0);
      }
    } catch (RuntimeException exception) {
      throw new Exception("Error port property!");
    }
  }

  /**
   * .
   */
  public void setPort(int port) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.port;
    this.port = port;
    firePropertyChange(ExtenderData.PROPERTY_PORT, oldValue, this.port);
    PlatformUtility.runInFxThread(this::updatePortProperty);
  }

  /**
   * .
   */
  public void setRemotePort(int remotePort) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.remotePort;
    this.remotePort = remotePort;
    firePropertyChange(ExtenderData.PROPERTY_REMOTE_PORT, oldValue, this.remotePort);
  }

  /**
   * .
   */
  public void setRdPort(int rdPort) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.rdPort;
    this.rdPort = rdPort;
    firePropertyChange(ExtenderData.PROPERTY_RDPORT, oldValue, this.rdPort);
    PlatformUtility.runInFxThread(this::updatePortProperty);
  }

  /**
   * .
   */
  public void setRdRemotePort(int rdRemotePort) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.rdRemotePort;
    this.rdRemotePort = rdRemotePort;
    firePropertyChange(ExtenderData.PROPERTY_REMOTE_RDPORT, oldValue, this.rdRemotePort);
  }

  /**
   * .
   */
  public void setStatus(int status) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.status;
    this.status = status;
    firePropertyChange(ExtenderData.PROPERTY_STATUS, oldValue, this.status);
  }

  /**
   * .
   */
  public void setExtendedStatus(int extendedStatus) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.extendedStatus;
    this.extendedStatus = extendedStatus;
    firePropertyChange(ExtenderData.PROPERTY_EXTENDEDSTATUS, oldValue, this.extendedStatus);
  }

  public boolean isExtendedStatusMouseOnline() {
    return extenderStatusInfo.isMouseOnline();
  }

  public boolean isExtendedStatusKeyboardOnline() {
    return extenderStatusInfo.isKeyboardOnline();
  }

  public boolean isExtendedStatusUsbCableOnline() {
    return extenderStatusInfo.isUsbcableOnline();
  }

  public boolean isExtendedStatusUdiskOnline() {
    return extenderStatusInfo.isUdiskOnline();
  }

  public boolean isStatusActive() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Extender.Status.ACTIVE);
  }

  public boolean isStatusDelete() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Extender.Status.DELETE);
  }

  public boolean isStatusNewVersion() {
    return Utilities.areBitsSet(getStatus(), Status.NEWVERSION);
  }

  public boolean isStatusFixPort() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Extender.Status.FIXPORT);
  }

  public boolean isStatusNewData() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Extender.Status.NEW);
  }

  public boolean isStatusOnline() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Extender.Status.ONLINE);
  }

  public boolean isStatusRedundant() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Extender.Status.REDUNDANT);
  }

  public boolean isStatusLink2() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Extender.Status.ACTIVED_LINK);
  }

  public void setStatusActive(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, Status.ACTIVE));
  }

  public void setStatusDelete(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, Status.DELETE));
  }

  public void setStatusFixPort(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, Status.FIXPORT));
  }

  public void setStatusNewData(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, Status.NEW));
  }

  public void setStatusOnline(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, Status.ONLINE));
  }

  public void setStatusRedundant(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled,
        new int[]{CaesarConstants.Extender.Status.REDUNDANT}));
  }

  public boolean isVideoConType() {
    return Utilities.areBitsSet(getType(), true, CaesarConstants.Extender.Type.CON);
  }

  public boolean isVideoCpuType() {
    return Utilities.areBitsSet(getType(), true, CaesarConstants.Extender.Type.CPU);
  }

  public boolean isVpConType() {
    return isConType() && getConsoleData() != null && getConsoleData().isStatusVpcon();
  }

  public boolean isVp6ConType() {
    return isVpConType() && getConsoleData().isStatusVp6();
  }

  public boolean isVp7ConType() {
    return isVpConType() && getConsoleData().isStatusVp7();
  }

  /**
   * .
   */
  public VpType getVpType() {
    if (isVp6ConType()) {
      return VpType.VP6;
    } else if (isVp7ConType()) {
      return VpType.VP7;
    } else {
      return null;
    }
  }

  public boolean isCpuType() {
    return Utilities.areBitsSet(getType(), true, CaesarConstants.Extender.Type.CPU)
        || Utilities.areBitsSet(getType(), false, CaesarConstants.Extender.Type.USB_CPU);
  }

  public boolean isConType() {
    return Utilities.areBitsSet(getType(), true, CaesarConstants.Extender.Type.CON)
        || Utilities.areBitsSet(getType(), false, CaesarConstants.Extender.Type.USB_CON);
  }

  public boolean isUsbConType() {
    return Utilities.areBitsSet(getType(), false, CaesarConstants.Extender.Type.USB_CON)
        && !Utilities.areBitsSet(getType(), true, CaesarConstants.Extender.Type.CON);
  }

  public boolean isUsbCpuType() {
    return Utilities.areBitsSet(getType(), false, CaesarConstants.Extender.Type.USB_CPU)
        && !Utilities.areBitsSet(getType(), true, CaesarConstants.Extender.Type.CPU);
  }

  public boolean isUsbType() {
    return isUsbConType() || isUsbCpuType();
  }

  public boolean isUsbUpgradeConType() {
    return Utilities.areBitsSet(getType(), false, CaesarConstants.Extender.Type.USB_CON)
        && Utilities.areBitsSet(getType(), true, CaesarConstants.Extender.Type.CON);
  }

  public boolean isUsbUpgradeCpuType() {
    return Utilities.areBitsSet(getType(), false, CaesarConstants.Extender.Type.USB_CPU)
        && Utilities.areBitsSet(getType(), true, CaesarConstants.Extender.Type.CPU);
  }

  public boolean isAudioType() {
    return Utilities.areBitsSet(getType(), false, CaesarConstants.Extender.Type.AUDIO);
  }

  public boolean isCustConType() {
    return Utilities.areBitsSet(getType(), false, CaesarConstants.Extender.Type.CUST_CON);
  }

  public boolean isCustCpuType() {
    return Utilities.areBitsSet(getType(), false, CaesarConstants.Extender.Type.CUST_CPU);
  }

  public boolean isUniConType() {
    return Utilities.areBitsSet(getType(), false, CaesarConstants.Extender.Type.UNI_CON);
  }

  public boolean isUniCpuType() {
    return Utilities.areBitsSet(getType(), false, CaesarConstants.Extender.Type.UNI_CPU);
  }

  public boolean isUniType() {
    return Utilities.areBitsSet(getType(), false,
        CaesarConstants.Extender.Type.UNI_CON | CaesarConstants.Extender.Type.UNI_CPU);
  }

  /**
   * .
   */
  public void setType(int type) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.type;
    this.type = type;
    firePropertyChange(ExtenderData.PROPERTY_TYPE, oldValue, this.type);
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (ExtenderData.PROPERTY_NAME.equals(propertyName)) {
      setName((String) value);
    } else if (ExtenderData.PROPERTY_STATUS.equals(propertyName)) {
      setStatus((Integer) value);
    } else if (ExtenderData.PROPERTY_EXTENDEDSTATUS.equals(propertyName)) {
      setExtendedStatus((Integer) value);
    } else if (ExtenderData.PROPERTY_PORT.equals(propertyName)) {
      setPort((Integer) value);
    } else if (ExtenderData.PROPERTY_RDPORT.equals(propertyName)) {
      setRdPort((Integer) value);
    } else if (ExtenderData.PROPERTY_ID.equals(propertyName)) {
      setId((Integer) value);
    } else if (ExtenderData.PROPERTY_TYPE.equals(propertyName)) {
      setType((Integer) value);
    } else if (ExtenderData.PROPERTY_CPU_CON.equals(propertyName)) {
      setCpuCon((Integer) value);
    }
  }

  @Override
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    ApiDataConverter<ExtenderData> converter = getConfigDataManager().getConfigMetaData()
        .getUtilVersion().getDataConverter(ExtenderData.class);
    converter.writeData(this, cfgWriter);
  }

  @Override
  public void readData(CfgReader cfgReader) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    ApiDataConverter<ExtenderData> converter = getConfigDataManager().getConfigMetaData()
        .getUtilVersion().getDataConverter(ExtenderData.class);
    converter.readData(this, cfgReader);
  }

  /**
   * .
   */
  public ConsoleData getConsoleData() {
    if (isConType() || isUsbConType() || isCustConType() || isUniConType()) {
      return getConfigDataManager().getConsoleData(getCpuCon() - 1);
    }
    return null;
  }

  /**
   * .
   */
  public void setConsoleData(ConsoleData consoleData) {
    if (isConType() || isUsbConType() || isCustConType() || isUniConType()) {
      setCpuCon(null == consoleData ? 0 : consoleData.getOid() + 1);
    } else {
      throw new IllegalStateException("Cannot set Console when Extender is not of type Console");
    }
  }

  /**
   * .
   */
  public CpuData getCpuData() {
    if (isCpuType() || isUsbCpuType() || isCustCpuType() || isUniCpuType()) {
      return getConfigDataManager().getCpuData(getCpuCon() - 1);
    }
    return null;
  }

  /**
   * .
   */
  public void setCpuData(CpuData cpuData) {
    if (isCpuType() || isUsbCpuType() || isCustCpuType() || isUniCpuType()) {
      setCpuCon(null == cpuData ? 0 : cpuData.getOid() + 1);
    } else {
      throw new IllegalStateException("Cannot set CPU when Extender is not of type CPU");
    }
  }

  public PortData getPortData() {
    return getConfigDataManager().getPortData(this.port - 1);
  }

  public void setPortData(PortData portData) {
    setPort(null == portData ? 0 : portData.getOid() + 1);
  }

  public PortData getRdPortData() {
    return getConfigDataManager().getPortData(this.rdPort - 1);
  }

  public void setRdPortData(PortData portData) {
    setRdPort(null == portData ? 0 : portData.getOid() + 1);
  }

  /**
   * .
   */
  public void removeFromAssociatedCpuDatas(boolean commitDependencies, Threshold threshold) {
    CpuData cpuData = getCpuData();
    if (null != cpuData) {
      for (int i = 0; i < CaesarConstants.Extender.CPUCON; i++) {
        if (equals(cpuData.getExtenderData(i))) {
          cpuData.setThreshold(threshold);
          cpuData.setExtenderData(i, null);
          cpuData.setPorts(cpuData.getPorts() - 1);
          if (commitDependencies) {
            cpuData.commit(threshold);
          }
          Threshold oldThreshold = cpuData.getThreshold();
          cpuData.setThreshold(oldThreshold);
          break;
        }
      }
    }
  }

  /**
   * .
   */
  public void removeFromAssociatedConsoleDatas(boolean commitDependencies, Threshold threshold) {
    ConsoleData consoleData = getConsoleData();
    if (null != consoleData) {
      for (int i = 0; i < CaesarConstants.Extender.CPUCON; i++) {
        if (equals(consoleData.getExtenderData(i))) {
          consoleData.setThreshold(threshold);
          consoleData.setExtenderData(i, null);
          consoleData.setPorts(consoleData.getPorts() - 1);
          if (commitDependencies) {
            consoleData.commit(threshold);
          }
          Threshold oldThreshold = consoleData.getThreshold();
          consoleData.setThreshold(oldThreshold);
          break;
        }
      }
    }
  }

  @Override
  public void delete(boolean internalCommit) {
    setStatusDelete(true);
    if (isCpuType() || isUsbCpuType() || isCustCpuType() || isUniCpuType()) {
      removeFromAssociatedCpuDatas(internalCommit, getThreshold());
    } else if (isConType() || isUsbConType() || isCustConType() || isUniConType()) {
      removeFromAssociatedConsoleDatas(internalCommit, getThreshold());
    }
    initDefaults();
    if (internalCommit) {
      commit(getThreshold());
    }
  }

  @Override
  public void delete() {
    delete(true);
  }
}

