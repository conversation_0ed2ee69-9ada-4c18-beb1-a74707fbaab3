package com.mc.tool.caesar.vpm.pages.hotkeyconfiguration;

import javafx.scene.control.Skin;
import org.controlsfx.control.ListSelectionViewEx;

/**
 * .
 */
public class CopyListSelectionView<T> extends ListSelectionViewEx<T> {
  private int maxSize;

  /** Constructor. */
  public CopyListSelectionView(int maxSize) {
    super();
    this.maxSize = maxSize;
    setStyle("-fx-padding: 0px;");
  }

  @Override
  protected Skin<ListSelectionViewEx<T>> createDefaultSkin() {
    return new CopyListSelectionViewSkin<>(this, maxSize);
  }
}
