package com.mc.tool.caesar.vpm.gui.pages.extenderupdate;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.caesar.vpm.pages.extenderupdate.CaesarExtenderUpdatePage;
import com.mc.tool.caesar.vpm.pages.update.UpdateData;
import com.mc.tool.caesar.vpm.util.update.UpdateConstant;
import com.mc.tool.framework.utility.dialogues.FileDialogue;
import com.mc.tool.framework.utility.dialogues.FileDialogueFactory;
import com.mc.tool.framework.view.FrameView;
import java.io.File;
import java.io.IOException;
import java.util.function.Predicate;
import javafx.scene.control.TreeItem;
import javafx.scene.control.TreeTableView;
import javafx.stage.Stage;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * .
 */
@RunWith(MockitoJUnitRunner.class)
public class LoadTest extends AppGuiTestBase {

  private static final String UPDATE_RESOURCE_DIR = "com/mc/tool/caesar/vpm/update/";

  @Mock private FileDialogueFactory fileDialogueFactory;

  @Mock private FileDialogue fileDialogue;

  @Override
  public void beforeAll() {}

  @Override
  public void start(Stage stage) throws Exception {
    FrameView.setModule(
        binder -> binder.bind(FileDialogueFactory.class).toInstance(fileDialogueFactory));
    when(fileDialogueFactory.createFileDialogue()).thenReturn(fileDialogue);
    super.start(stage);
  }

  /**
   * .
   */
  public void testTemplate(
      String statusName, String swuName, Predicate<TreeItem<UpdateData>> itemTest) {
    try {
      clickOn("#" + CaesarExtenderUpdatePage.NAME);
      // 加载升级文件
      File updateFile = copyResourceToTemp(UPDATE_RESOURCE_DIR + swuName, "test", ".swu");
      when(fileDialogue.showOpenDialog(any())).thenReturn(updateFile);
      clickOn(GuiTestConstants.EXTENDER_LOAD_BUTTON);
      clickOn(GuiTestConstants.EXTENDER_SELECT_ALL_BUTTON);
      // 检查各行的选中情况
      TreeTableView<UpdateData> tableView =
          (TreeTableView)
              lookup(GuiTestConstants.EXTENDER_TABLE_VIEW).match((node) -> isShowing(node)).query();
      checkUpdateData(tableView.getRoot(), itemTest, 0);
      // 删除文件
      updateFile.delete();
      File tempFile = loadResourceStatus(UPDATE_RESOURCE_DIR + statusName);
      tempFile.delete();
      // 关闭页面
      clickOn(GuiTestConstants.BUTTON_LIST_MENU);
      clickOn(GuiTestConstants.MENU_CLOSE_ALL);
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testLoadTx() {
    testTemplate(
        "single_36_hw1.2_sample.status",
        "tx_hw1.2_sample.swu",
        (treeItem) -> {
          UpdateData updateData = treeItem.getValue();
          if (updateData.getType().equals(UpdateConstant.UPDATE_APP_TYPE)
              || updateData.getType().equals(UpdateConstant.UPDATE_FPGA_TYPE)
              || updateData.getType().equals(UpdateConstant.UPDATE_SYS_TYPE)) {
            if (treeItem.getParent() != null
                && treeItem
                    .getParent()
                    .getValue()
                    .getType()
                    .equals(UpdateConstant.EXTENDER_CPU_TYPE)) {
              Assert.assertFalse(updateData.getDisabled());
              Assert.assertTrue(updateData.getSelected());
            } else {
              Assert.assertFalse(updateData.getSelected());
              Assert.assertTrue(updateData.getDisabled());
            }
          }
          return true;
        });
  }

  @Test
  public void testLoadRx() {
    testTemplate(
        "single_36_hw1.2_sample.status",
        "rx_hw1.2_sample.swu",
        (treeItem) -> {
          UpdateData updateData = treeItem.getValue();
          if (updateData.getType().equals(UpdateConstant.UPDATE_APP_TYPE)
              || updateData.getType().equals(UpdateConstant.UPDATE_FPGA_TYPE)
              || updateData.getType().equals(UpdateConstant.UPDATE_SYS_TYPE)) {
            if (treeItem.getParent() != null
                && treeItem
                    .getParent()
                    .getValue()
                    .getType()
                    .equals(UpdateConstant.EXTENDER_CON_TYPE)) {
              Assert.assertFalse(updateData.getDisabled());
              Assert.assertTrue(updateData.getSelected());
            } else {
              Assert.assertFalse(updateData.getSelected());
              Assert.assertTrue(updateData.getDisabled());
            }
          }
          return true;
        });
  }

  @Test
  public void testLoadVp6() {
    testTemplate(
        "single_36_hw1.2_sample.status",
        "vp6_hw1.2_sample.swu",
        (treeItem) -> {
          UpdateData updateData = treeItem.getValue();
          if (updateData.getType().equals(UpdateConstant.UPDATE_APP_TYPE)
              || updateData.getType().equals(UpdateConstant.UPDATE_FPGA_TYPE)
              || updateData.getType().equals(UpdateConstant.UPDATE_SYS_TYPE)) {
            if (treeItem.getParent() != null
                && treeItem
                    .getParent()
                    .getValue()
                    .getType()
                    .equals(UpdateConstant.EXTENDER_VPCON_TYPE)
                && updateData.getType().equals(UpdateConstant.UPDATE_FPGA_TYPE)) {
              Assert.assertFalse(updateData.getDisabled());
              Assert.assertTrue(updateData.getSelected());
            } else {
              Assert.assertFalse(updateData.getSelected());
              Assert.assertTrue(updateData.getDisabled());
            }
          }
          return true;
        });
  }
}
