package com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard;

import com.mc.tool.framework.utility.UndecoratedWizard;
import com.mc.tool.framework.utility.UndecoratedWizardPane;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.control.CheckBox;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class WizardPrepare extends UndecoratedWizardPane implements Initializable {

  @FXML private CheckBox check1;
  @FXML private CheckBox check2;
  @FXML private CheckBox check3;

  /** . */
  public WizardPrepare() {
    FXMLLoader loader = new FXMLLoader();
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    loader.setLocation(
        classLoader.getResource(
            "com/mc/tool/caesar/vpm/pages/matrixgrid/configwizard/prepare_view.fxml"));
    loader.setResources(
        ResourceBundle.getBundle("com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.Bundle"));
    loader.setController(this);
    try {
      Node root = loader.load();
      setContent(root);
    } catch (IOException exception) {
      log.warn("Fail to load prepare_view.fxml!", exception);
    }
  }

  @Override
  public void onEnteringPage(UndecoratedWizard wizard) {
    wizard
        .invalidProperty()
        .bind(
            check1
                .selectedProperty()
                .and(check2.selectedProperty())
                .and(check3.selectedProperty())
                .not());
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {}
}
