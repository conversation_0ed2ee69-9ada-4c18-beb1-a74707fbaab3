package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import javax.validation.constraints.NotNull;
import lombok.Setter;

/**
 * 图层位置信息.
 */
@Setter
@JsonPropertyOrder({
    LayerWindow.JSON_PROPERTY_X,
    LayerWindow.JSON_PROPERTY_Y,
    LayerWindow.JSON_PROPERTY_WIDTH,
    LayerWindow.JSON_PROPERTY_HEIGHT
})
public class LayerWindow {
  public static final String JSON_PROPERTY_X = "x";
  private Integer xpos;

  public static final String JSON_PROPERTY_Y = "y";
  private Integer ypos;

  public static final String JSON_PROPERTY_WIDTH = "width";
  private Integer width;

  public static final String JSON_PROPERTY_HEIGHT = "height";
  private Integer height;

  public LayerWindow xpos(Integer xpos) {
    this.xpos = xpos;
    return this;
  }

  /**
   * 横坐标.
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_X)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getXpos() {
    return xpos;
  }


  public LayerWindow ypos(Integer ypos) {
    this.ypos = ypos;
    return this;
  }

  /**
   * 纵坐标.
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_Y)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getYpos() {
    return ypos;
  }


  public LayerWindow width(Integer width) {
    this.width = width;
    return this;
  }

  /**
   * 宽度.
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_WIDTH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getWidth() {
    return width;
  }


  public LayerWindow height(Integer height) {
    this.height = height;
    return this;
  }

  /**
   * 高度.
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_HEIGHT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getHeight() {
    return height;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LayerWindow layerWindow = (LayerWindow) o;
    return Objects.equals(this.xpos, layerWindow.xpos)
        && Objects.equals(this.ypos, layerWindow.ypos)
        && Objects.equals(this.width, layerWindow.width)
        && Objects.equals(this.height, layerWindow.height);
  }

  @Override
  public int hashCode() {
    return Objects.hash(xpos, ypos, width, height);
  }


  @Override
  public String toString() {
    return "LayerWindow {\n"
        + "    x: " + toIndentedString(xpos) + "\n"
        + "    y: " + toIndentedString(ypos) + "\n"
        + "    width: " + toIndentedString(width) + "\n"
        + "    height: " + toIndentedString(height) + "\n" + "}";
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
