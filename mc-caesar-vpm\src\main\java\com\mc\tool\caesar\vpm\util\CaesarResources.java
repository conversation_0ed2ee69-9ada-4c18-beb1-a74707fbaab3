package com.mc.tool.caesar.vpm.util;

import java.util.ResourceBundle;

/**
 * .
 */
public class CaesarResources {
  private static final String BASE_NAME = "com.mc.tool.caesar.vpm.i18n.common";

  public static String getString(String key) {
    return ResourceBundle.getBundle(BASE_NAME).getString(key);
  }

  public static ResourceBundle getResourceBundle() {
    return ResourceBundle.getBundle(BASE_NAME);
  }
}
