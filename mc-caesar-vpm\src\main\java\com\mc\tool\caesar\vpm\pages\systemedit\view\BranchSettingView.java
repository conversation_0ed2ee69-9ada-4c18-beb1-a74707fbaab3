package com.mc.tool.caesar.vpm.pages.systemedit.view;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.BranchData;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import java.util.Collections;
import javafx.beans.binding.Bindings;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.control.ComboBox;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.util.Callback;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class BranchSettingView extends TableView<BranchData> {
  private static final String DEMO_IP = "s0.0.0.0".substring(1);

  private CaesarDeviceController deviceController;

  private final TableColumn<BranchData, String> indexCol;
  private final TableColumn<BranchData, String> nameCol;
  private final TableColumn<BranchData, BranchData> speedCol;

  /**
   * 远程开关机.
   */
  public BranchSettingView() {
    indexCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.speed.index"));
    indexCol.prefWidthProperty().bind(widthProperty().multiply(0.1));
    indexCol.setId("index-col");
    nameCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.speed.name"));
    nameCol.prefWidthProperty().bind(widthProperty().multiply(0.1));
    nameCol.setId("name-col");
    speedCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.speed.speed"));
    speedCol.prefWidthProperty().bind(widthProperty().multiply(0.7));
    speedCol.setId("speed-col");

    getColumns().addAll(indexCol, nameCol, speedCol);

    indexCol.setCellFactory(new IndexCellFactory());
    indexCol.setStyle("-fx-alignment:CENTER");
    indexCol.setSortable(false);

    nameCol.setCellValueFactory(
        (cell) -> new SimpleStringProperty("IO Board " + (cell.getValue().getOid() + 1)));
    speedCol.setCellValueFactory(
        cellData -> new SimpleObjectProperty<>(cellData.getValue()));

    speedCol.setCellFactory(col -> new SpeedTableCell());
  }

  /**
   * .
   */
  public void setDeviceController(CaesarDeviceController deviceController) {
    this.deviceController = deviceController;

    final String[] updateData = {
        ModuleData.PROPERTY_STATUS,
        BranchData.PROPERTY_TYPE
    };

    CaesarSwitchDataModel model = this.deviceController.getDataModel();
    model.addPropertyChangeListener(updateData,
        evt -> PlatformUtility.runInFxThread(this::updateItems));

    PlatformUtility.runInFxThreadLater(this::updateItems);
  }

  /**
   * 刷新视图内容.
   */
  public void refreshData() {
    this.deviceController.submitAsync(() -> {
      try {
        this.deviceController.getDataModel().getController()
            .getBranchData(this.deviceController.getDataModel().getConfigData()
                .getBranchDatas());
      } catch (DeviceConnectionException e) {
        log.warn("Fail to get branch data", e);
      } catch (ConfigException e) {
        log.warn("Fail to get branch data", e);
      } catch (BusyException e) {
        log.warn("Fail to get branch data", e);
      }
    }).whenComplete((result, throwable) -> {
      if (throwable != null) {
        log.warn("Fail to get branch data", throwable);
      }
    });
  }

  private void updateItems() {
    ObservableList<BranchData> list = FXCollections.observableArrayList();
    for (BranchData branchData : deviceController.getDataModel().getConfigData().getBranchDatas()) {
      ModuleData moduleData = deviceController.getDataModel().getSwitchModuleData()
          .getModuleData(branchData.getOid() + 1);
      if (moduleData != null && moduleData.isStatusActive() && moduleData.isStatusAvailable()) {
        list.add(branchData);
      }
    }
    setItems(list);
    refresh();
  }

  static class IndexCellFactory
      implements Callback<
      TableColumn<BranchData, String>, TableCell<BranchData, String>> {

    @Override
    public TableCell<BranchData, String> call(
        TableColumn<BranchData, String> param) {
      TableCell<BranchData, String> cell = new TableCell<>();
      cell.textProperty()
          .bind(
              Bindings.createStringBinding(
                  () -> {
                    if (cell.isEmpty()) {
                      return null;
                    } else {
                      return String.format("%04d", cell.getIndex() + 1);
                    }
                  },
                  cell.indexProperty(),
                  cell.emptyProperty()));
      return cell;
    }
  }

  class SpeedTableCell extends TableCell<BranchData, BranchData> {
    final ComboBox<BranchData.BranchType> comboBox =
        new ComboBox<>(FXCollections.observableArrayList(
            BranchData.BranchType.SPEED_1G, BranchData.BranchType.SPEED_2_5G,
            BranchData.BranchType.SPEED_10G));

    {
      comboBox.setOnAction(event -> {
        if (getTableRow() == null || getTableRow().getItem() == null) {
          return;
        }
        BranchData data = (BranchData) getTableRow().getItem();
        data.setBranchType(comboBox.getValue());
        deviceController.submitAsync(() -> {
          try {
            deviceController.getDataModel().getController().setBranchData(
                Collections.singletonList(data));
          } catch (DeviceConnectionException | ConfigException | BusyException e) {
            log.warn("Fail to set branch data", e);
          }
        }).whenComplete((ret, throwable) -> {
          if (throwable != null) {
            log.warn("Fail to set branch data", throwable);
          }
          refresh();
        });
      });
    }

    @Override
    protected void updateItem(BranchData item, boolean empty) {
      super.updateItem(item, empty);
      if (item == null || empty) {
        setGraphic(null);
      } else {
        comboBox.setValue(item.getBranchType());
        if (deviceController.getDataModel().getSwitchModuleData().getModuleData(0).getPorts()
            == 36 && (item.getOid() == 0 || item.getOid() == 1)) {
          comboBox.getItems().remove(BranchData.BranchType.SPEED_10G);
        }
        setGraphic(comboBox);
      }
    }

  }
}
