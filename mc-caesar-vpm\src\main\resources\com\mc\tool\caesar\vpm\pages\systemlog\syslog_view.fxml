<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<fx:root id="main-container" stylesheets="@syslog_view.css" type="VBox"
  xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
  <children>
    <TableView fx:id="tableView" VBox.vgrow="ALWAYS">
      <columns>
        <TableColumn fx:id="checkBoxCol" minWidth="0.0" prefWidth="34.0"/>
        <TableColumn fx:id="nameCol" prefWidth="224.0" text="C1"/>
        <TableColumn fx:id="typeCol" minWidth="5.0" prefWidth="221.0" text="C2"/>
        <TableColumn fx:id="portCol" minWidth="0.0" prefWidth="213.0" text="C3"/>
      </columns>
    </TableView>
    <HBox id="tool-box">
      <children>
        <Button fx:id="downloadButton" mnemonicParsing="false" styleClass="common-button"
          text="Button" HBox.hgrow="ALWAYS">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </Button>
      </children>
    </HBox>
  </children>
</fx:root>
