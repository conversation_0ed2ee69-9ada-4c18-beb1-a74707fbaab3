/*
 * Javolution - Java(TM) Solution for Real-Time and Embedded Systems Copyright (C) 2012 - Javolution
 * (http://javolution.org/) All rights reserved.
 *
 * Permission to use, copy, modify, and distribute this software is freely granted, provided that
 * this notice is preserved.
 */

package com.mc.tool.caesar.api.io;

import java.io.CharConversionException;
import java.io.IOException;
import java.io.InputStream;
import java.io.Reader;

/**
 * <p>
 * A UTF-8 stream reader.
 * </p>
 *
 * <p>
 * This reader supports surrogate <code>char</code> pairs (representing characters in the range
 * [U+10000 .. U+10FFFF]). It can also be used to read characters unicodes (31 bits) directly (ref.
 * {@link #read()}).
 * </p>
 *
 * <p>
 * Each invocation of one of the <code>read()</code> methods may cause one or more bytes to be read
 * from the underlying byte-input stream. To enable the efficient conversion of bytes to characters,
 * more bytes may be read ahead from the underlying stream than are necessary to satisfy the current
 * read operation.
 * </p>
 *
 * <p>
 * Instances of this class can be reused for different input streams and can be part of a higher
 * level component (e.g. parser) in order to avoid dynamic buffer allocation when the input source
 * changes. Also wrapping using a <code>java.io.BufferedReader</code> is unnescessary as instances
 * of this class embed their own data buffers.
 * </p>
 *
 * <p>
 * Note: This reader is unsynchronized and does not test if the UTF-8 encoding is well-formed (e.g.
 * UTF-8 sequences longer than necessary to encode a character).
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">Jean-Marie Dautelle</a>
 * @version 2.0, December 9, 2004
 * @see Utf8StreamWriter
 */
public final class Utf8StreamReader extends Reader {

  /**
   * Holds the current input stream or <code>null</code> if closed.
   */
  private InputStream inputStream0;

  /**
   * Holds the start index.
   */
  private int start0;

  /**
   * Holds the end index.
   */
  private int end0;

  /**
   * Holds the bytes buffer.
   */
  private final byte[] bytes0;

  /**
   * Creates a UTF-8 reader having a byte buffer of moderate capacity (2048).
   */
  public Utf8StreamReader() {
    bytes0 = new byte[2048];
  }

  /**
   * Creates a UTF-8 reader having a byte buffer of moderate capacity (2048), initialized to read
   * with the specified InputStream.
   *
   * @param inputStream InputStream to Read From
   */
  public Utf8StreamReader(InputStream inputStream) {
    bytes0 = new byte[2048];
    inputStream0 = inputStream;
  }

  /**
   * Creates a UTF-8 reader having a byte buffer of specified capacity.
   *
   * @param capacity the capacity of the byte buffer.
   */
  public Utf8StreamReader(int capacity) {
    bytes0 = new byte[capacity];
  }

  /**
   * Creates a UTF-8 reader having a byte buffer of specified capacity, initialized to read with the
   * specified InputStream.
   *
   * @param inputStream InputStream to read
   * @param capacity    the capacity of the byte buffer.
   */
  public Utf8StreamReader(InputStream inputStream, int capacity) {
    bytes0 = new byte[capacity];
    inputStream0 = inputStream;
  }

  /**
   * Sets the input stream to use for reading until this reader is closed. For example:[code] Reader
   * reader = new UTF8StreamReader().setInput(inStream); [/code] is equivalent but reads twice as
   * fast as [code] Reader reader = new java.io.InputStreamReader(inStream, "UTF-8"); [/code]
   *
   * @param inStream the input stream.
   * @return this UTF-8 reader.
   * @throws IllegalStateException if this reader is being reused and it has not been {@link #close
   *                               closed} or {@link #reset reset}.
   */
  public Utf8StreamReader setInput(InputStream inStream) {
    if (inputStream0 != null) {
      throw new IllegalStateException("Reader not closed or reset");
    }
    inputStream0 = inStream;
    return this;
  }

  /**
   * Indicates if this stream is ready to be read.
   *
   * @return <code>true</code> if the next read() is guaranteed not to block for input; <code>false
   *     </code> otherwise.
   * @throws IOException if an I/O error occurs.
   */
  @Override
  public boolean ready() throws IOException {
    if (inputStream0 == null) {
      throw new IOException("Stream closed");
    }
    return (end0 - start0) > 0 || inputStream0.available() != 0;
  }

  /**
   * Closes and {@link #reset resets} this reader for reuse.
   *
   * @throws IOException if an I/O error occurs.
   */
  @Override
  public void close() throws IOException {
    if (inputStream0 != null) {
      inputStream0.close();
      reset();
    }
  }

  /**
   * Reads a single character. This method will block until a character is available, an I/O error
   * occurs or the end of the stream is reached.
   *
   * @return the 31-bits Unicode of the character read, or -1 if the end of the stream has been
   *     reached.
   * @throws IOException if an I/O error occurs.
   */
  @Override
  public int read() throws IOException {
    byte bb = bytes0[start0];
    return bb >= 0 && start0++ < end0 ? bb : read2();
  }

  /**
   * Reads characters into a portion of an array. This method will block until some input is
   * available, an I/O error occurs or the end of the stream is reached.
   *
   * <p>
   * Note: Characters between U+10000 and U+10FFFF are represented by surrogate pairs (two
   * <code>char</code>).
   * </p>
   *
   * @param cbuf the destination buffer.
   * @param off  the offset at which to start storing characters.
   * @param len  the maximum number of characters to read
   * @return the number of characters read, or -1 if the end of the stream has been reached
   * @throws IOException if an I/O error occurs.
   */
  @Override
  public int read(char[] cbuf, int off, int len) throws IOException {
    if (inputStream0 == null) {
      throw new IOException("No input stream or stream closed");
    }
    if (start0 >= end0) { // Fills buffer.
      start0 = 0;
      end0 = inputStream0.read(bytes0, 0, bytes0.length);
      if (end0 <= 0) { // Done.
        return end0;
      }
    }
    final int off_plus_len = off + len;
    for (int i = off; i < off_plus_len; ) {
      // assert(start0 < end0)
      byte bb = bytes0[start0];
      if (bb >= 0 && ++start0 < end0) {
        cbuf[i++] = (char) bb; // Most common case.
      } else if (bb < 0) {
        if (i < off_plus_len - 1) { // Up to two 'char' can be read.
          int code = read2();
          if (code < 0x10000) {
            cbuf[i++] = (char) code;
          } else if (code <= 0x10ffff) { // Surrogates.
            cbuf[i++] = (char) (((code - 0x10000) >> 10) + 0xd800);
            cbuf[i++] = (char) (((code - 0x10000) & 0x3ff) + 0xdc00);
          } else {
            throw new CharConversionException("Cannot convert U+" + Integer.toHexString(code)
                + " to char (code greater than U+10FFFF)");
          }
          if (start0 < end0) {
            continue;
          }
        }
        return i - off;
      } else { // End of buffer (start0 >= end0).
        cbuf[i++] = (char) bb;
        return i - off;
      }
    }
    return len;
  }

  /**
   * Reads characters into the specified appendable. This method will block until the end of the
   * stream is reached.
   *
   * @param dest the destination buffer.
   * @throws IOException if an I/O error occurs.
   */
  public void read(Appendable dest) throws IOException {
    if (inputStream0 == null) {
      throw new IOException("No input stream or stream closed");
    }
    while (true) {
      if (start0 >= end0) { // Fills buffer.
        start0 = 0;
        end0 = inputStream0.read(bytes0, 0, bytes0.length);
        if (end0 <= 0) { // Done.
          break;
        }
      }
      byte bb = bytes0[start0];
      if (bb >= 0) {
        dest.append((char) bb); // Most common case.
        start0++;
      } else {
        int code = read2();
        if (code < 0x10000) {
          dest.append((char) code);
        } else if (code <= 0x10ffff) { // Surrogates.
          dest.append((char) (((code - 0x10000) >> 10) + 0xd800));
          dest.append((char) (((code - 0x10000) & 0x3ff) + 0xdc00));
        } else {
          throw new CharConversionException("Cannot convert U+" + Integer.toHexString(code)
              + " to char (code greater than U+10FFFF)");
        }
      }
    }
  }

  // Reads one full character, blocks if necessary.
  private int read2() throws IOException {
    if (start0 < end0) {
      byte bb = bytes0[start0++];

      // Decodes UTF-8.
      if (bb >= 0 && moreBytes0 == 0) {
        // 0xxxxxxx
        return bb;
      } else if ((bb & 0xc0) == 0x80 && moreBytes0 != 0) {
        // 10xxxxxx (continuation byte)
        code0 = (code0 << 6) | (bb & 0x3f); // Adds 6 bits to code.
        if (--moreBytes0 == 0) {
          return code0;
        } else {
          return read2();
        }
      } else if ((bb & 0xe0) == 0xc0 && moreBytes0 == 0) {
        // 110xxxxx
        code0 = bb & 0x1f;
        moreBytes0 = 1;
        return read2();
      } else if ((bb & 0xf0) == 0xe0 && moreBytes0 == 0) {
        // 1110xxxx
        code0 = bb & 0x0f;
        moreBytes0 = 2;
        return read2();
      } else if ((bb & 0xf8) == 0xf0 && moreBytes0 == 0) {
        // 11110xxx
        code0 = bb & 0x07;
        moreBytes0 = 3;
        return read2();
      } else if ((bb & 0xfc) == 0xf8 && moreBytes0 == 0) {
        // 111110xx
        code0 = bb & 0x03;
        moreBytes0 = 4;
        return read2();
      } else if ((bb & 0xfe) == 0xfc && moreBytes0 == 0) {
        // 1111110x
        code0 = bb & 0x01;
        moreBytes0 = 5;
        return read2();
      } else {
        throw new CharConversionException("Invalid UTF-8 Encoding");
      }
    } else { // No more bytes in buffer.
      if (inputStream0 == null) {
        throw new IOException("No input stream or stream closed");
      }
      start0 = 0;
      end0 = inputStream0.read(bytes0, 0, bytes0.length);
      if (end0 > 0) {
        return read2(); // Continues.
      } else { // Done.
        if (moreBytes0 == 0) {
          return -1;
        } else { // Incomplete sequence.
          throw new CharConversionException("Unexpected end of stream");
        }
      }
    }
  }

  private int code0;

  private int moreBytes0;

  /**
   * .
   */
  @Override
  public void reset() {
    code0 = 0;
    end0 = 0;
    inputStream0 = null;
    moreBytes0 = 0;
    start0 = 0;
  }

  /**
   * setInputStream.
   *
   * @param inStream InputStream to read.
   * @return Reference to this UTF8StreamReader
   * @deprecated Replaced by {@link #setInput(InputStream)}
   */
  public Utf8StreamReader setInputStream(InputStream inStream) {
    return this.setInput(inStream);
  }
}
