package com.mc.tool.caesar.api.utils;

import com.mc.tool.caesar.api.datamodel.ExtenderData;

/**
 * 转换extender的rdport的值为integer.
 */
public final class RportTransformer implements ObjectTransformer {

  public static final RportTransformer INSTANCE = new RportTransformer();

  @Override
  public Object transform(Object value) {
    if (value instanceof ExtenderData) {
      ExtenderData extenderData = (ExtenderData) value;
      if (extenderData.isStatusRedundant()) {
        return ((ExtenderData) value).getRdPort();
      }
      return "-";
    }
    return value;
  }
}

