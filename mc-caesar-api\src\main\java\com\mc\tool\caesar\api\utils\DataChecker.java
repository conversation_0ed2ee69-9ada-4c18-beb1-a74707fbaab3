package com.mc.tool.caesar.api.utils;

import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.datamodel.MultiviewData;
import com.mc.tool.caesar.api.datamodel.PortData;
import java.text.MessageFormat;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * .
 */
public class DataChecker {

  private static final Logger LOG = Logger.getLogger(DataChecker.class.getName());

  /**
   * 检查跨屏数据.
   *
   * @param model         model
   * @param errorDataList 错误的跨屏数据
   * @return 错误个数
   */
  public static int checkMultiscreen(CaesarSwitchDataModel model,
      List<MultiScreenData> errorDataList, boolean verbose) {
    for (MultiScreenData screenData : model.getConfigDataManager().getActiveMultiScreen()) {
      if (screenData.getConInfos().stream().allMatch((item) -> item.getConId() <= 0)) {
        errorDataList.add(screenData);
        if (verbose) {
          LOG.log(Level.SEVERE, MessageFormat
              .format("Multiscreen data {0}[{1}]'s rx infos is empty!", screenData.getName(),
                  screenData.getOid()));
        }
      }
    }
    return errorDataList.size();
  }

  /**
   * 检查错误.
   *
   * @param model             配置数据.
   * @param errorPortList     返回的错误的端口列表.
   * @param errorExtenderList 返回的错误的外设列表.
   * @param errorCpuList      返回的错误的TX列表
   * @param errorConsoleList  返回的错误的RX列表
   * @param verbose           是否打印log.
   * @return 错误的数量.
   */
  public static int checkErrors(CaesarSwitchDataModel model, List<PortData> errorPortList,
      List<ExtenderData> errorExtenderList,
      List<CpuData> errorCpuList, List<ConsoleData> errorConsoleList, boolean verbose) {
    errorPortList.clear();
    errorExtenderList.clear();
    errorCpuList.clear();
    errorConsoleList.clear();
    Set<ExtenderData> hasPortExtenders = new HashSet<>();
    Set<CpuData> cpuDatas = new HashSet<>();
    Set<ConsoleData> consoleDatas = new HashSet<>();
    for (PortData portData : model.getConfigData().getPortDatas()) {
      // 只处理在线设备或者usb设备.
      if (!portData.isStatusAvailable() && (portData.getExtenderData() == null || !portData
          .getExtenderData().isUsbType())) {
        continue;
      }
      if (!checkPort(portData, verbose)) {
        errorPortList.add(portData);
      }

      ExtenderData extenderData = portData.getExtenderData();
      if (extenderData != null) {
        cpuDatas.add(extenderData.getCpuData());
        consoleDatas.add(extenderData.getConsoleData());
      }
      hasPortExtenders.add(portData.getExtenderData());
    }
    // 检查外设错误
    for (ExtenderData extenderData : model.getConfigDataManager()
        .getActiveExtenders()) {
      if (hasPortExtenders.contains(extenderData)) {
        continue;
      }
      cpuDatas.add(extenderData.getCpuData());
      consoleDatas.add(extenderData.getConsoleData());
      if (!model.isOnlyConfig() && extenderData.getPortData() != null
          && extenderData.getPortData().getExtenderData() != extenderData) {
        errorExtenderList.add(extenderData);
        ExtenderData otherExtenderData = extenderData.getPortData().getExtenderData();
        if (verbose) {
          LOG.log(Level.SEVERE,
              MessageFormat.format(
                  "EXT data is error for mismatch port. "
                      + "EXT {0}[{1}]''s port is {2} but port {3}''s EXT is {4}[{5}]",
                  extenderData.getName(), extenderData.getOid(), extenderData.getPort(),
                  extenderData.getPort(),
                  otherExtenderData == null ? "null" : otherExtenderData.getName(),
                  otherExtenderData == null ? -1 : otherExtenderData.getOid()));
        }
      } else if (!model.isOnlyConfig() && extenderData.getRdPortData() != null
          && extenderData.getRdPortData().getExtenderData() != extenderData) {
        errorExtenderList.add(extenderData);
        ExtenderData otherExtenderData = extenderData.getRdPortData().getExtenderData();
        if (verbose) {
          LOG.log(Level.SEVERE,
              MessageFormat.format(
                  "EXT data is error for mismatch port. "
                      + "EXT {0}[{1}]''s rdport is {2} but port {3}''s EXT is {4}[{5}]",
                  extenderData.getName(), extenderData.getOid(), extenderData.getRdPort(),
                  extenderData.getRdPort(),
                  otherExtenderData == null ? "null" : otherExtenderData.getName(),
                  otherExtenderData == null ? -1 : otherExtenderData.getOid()));
        }
      } else if (!checkExtender(extenderData, verbose)) {
        errorExtenderList.add(extenderData);
      }
    }

    // 检查TX错误
    for (CpuData cpuData : model.getConfigDataManager()
        .getActiveCpus()) {
      if (cpuDatas.contains(cpuData)) {
        continue;
      }

      boolean error = false;
      for (ExtenderData extenderData : cpuData.getExtenderDatas()) {
        if (extenderData.getCpuData() != cpuData) {
          error = true;
          if (verbose) {
            LOG.log(Level.SEVERE, MessageFormat.format(
                "TX data is error for mismatch EXT. TX {0}[{1}]''s EXT {2}[{3}]'' TX is {4}[{5}].",
                cpuData.getName(), cpuData.getOid(), extenderData.getName(), extenderData.getOid(),
                extenderData.getCpuData() == null ? "null" : extenderData.getCpuData().getName(),
                extenderData.getCpuData() == null ? -1 : extenderData.getCpuData().getOid()));
          }
        }
      }
      if (error) {
        errorCpuList.add(cpuData);
      } else {
        if (verbose) {
          LOG.log(Level.SEVERE, MessageFormat
              .format("TX {0}[{1}] data is probably error for not referenced by extender data.",
                  cpuData.getName(), cpuData.getOid()));
        }
      }
    }

    // 检查RX错误
    for (ConsoleData consoleData : model.getConfigDataManager().getActiveConsoles()) {
      if (consoleDatas.contains(consoleData)) {
        continue;
      }

      boolean error = false;
      for (ExtenderData extenderData : consoleData.getExtenderDatas()) {
        if (extenderData.getConsoleData() != consoleData) {
          error = true;
          if (verbose) {
            LOG.log(Level.SEVERE, MessageFormat
                .format(
                    "RX data is error for mismatch EXT. "
                        + " RX {0}[{1}]''s EXT {2}[{3}]'' RX is {4}[{5}].",
                    consoleData.getName(), consoleData.getOid(), extenderData.getName(),
                    extenderData.getOid(),
                    extenderData.getConsoleData() == null ? "null"
                        : extenderData.getConsoleData().getName(),
                    extenderData.getConsoleData() == null ? -1
                        : extenderData.getConsoleData().getOid()));
          }
        }
      }
      if (error) {
        errorConsoleList.add(consoleData);
      } else {
        if (verbose) {
          LOG.log(Level.SEVERE, MessageFormat
              .format("RX {0}[1] data is probably error for not referenced by extender data.",
                  consoleData.getName(), consoleData.getOid()));
        }
      }
    }
    // 检查RX的multiview索引
    for (ConsoleData consoleData : model.getConfigDataManager().getActiveConsoles()) {
      MultiviewData multiviewData = null;
      if (consoleData.getMultiviewIndex() != 0 && ((multiviewData =
          model.getConfigDataManager().getMultiviewData(consoleData.getMultiviewIndex() - 1))
          == null || multiviewData.getUsConIndex() != consoleData.getOid() + 1)) {
        errorConsoleList.add(consoleData);
        if (verbose) {
          LOG.log(Level.SEVERE, MessageFormat.format(
              "RX data is error for mismatch multiview. "
                  + "RX {0}[{1}]''s multiview is [{2}] but multiview''s RX is [{3}].",
              consoleData.getName(), consoleData.getOid(),
              consoleData.getMultiviewIndex() - 1,
              multiviewData == null ? "null" : multiviewData.getUsConIndex() - 1));
        }
      }
    }

    return errorPortList.size() + errorExtenderList.size() + errorCpuList.size()
        + errorConsoleList.size();
  }

  /**
   * 检查数据.
   *
   * @param portData 端口数据.
   * @return 如果没有问题，返回true
   */
  public static boolean checkPort(PortData portData, boolean verbose) {
    if (portData == null) {
      return true;
    }
    ExtenderData extenderData = portData.getExtenderData();
    if (extenderData == null) {
      return true;
    }
    if (extenderData.getPortData() != portData && extenderData.getRdPortData() != portData) {
      if (verbose) {
        LOG.log(Level.SEVERE,
            MessageFormat.format(
                "EXT data {3} is error for mismatch port. "
                    + "Port {0}''s EXT data''s port & rdport is {1} & {2}",
                portData.getOid() + 1, extenderData.getPort(), extenderData.getRdPort(),
                extenderData.getName()));
      }
      return false;
    } else if (!checkExtender(extenderData, verbose)) {
      return false;
    }
    return true;
  }

  /**
   * 检查外设.
   *
   * @param extenderData 外设数据.
   * @return 如果外设没有问题，返回true
   */
  private static boolean checkExtender(ExtenderData extenderData, boolean verbose) {
    if (extenderData.getCpuData() != null
        && extenderData.getCpuData().getExtenderData(Utilities.getExtenderIdx(extenderData))
        != extenderData) {
      ExtenderData mismatch = extenderData.getCpuData()
          .getExtenderData(Utilities.getExtenderIdx(extenderData));
      if (verbose) {
        LOG.log(Level.SEVERE, MessageFormat
            .format(
                "EXT data is error for mismatch TX. "
                    + "EXT(Port {0}/{1}) {2}[{3}]''s TX''s EXT is {4}[{5}].",
                extenderData.getPort(), extenderData.getRdPort(), extenderData.getName(),
                extenderData.getOid(),
                mismatch == null ? "null" : mismatch.getName(),
                mismatch == null ? -1 : mismatch.getOid()));
      }
      return false;
    } else if (extenderData.getConsoleData() != null
        && extenderData.getConsoleData().getExtenderData(Utilities.getExtenderIdx(extenderData))
        != extenderData) {
      ExtenderData mismatch = extenderData.getConsoleData()
          .getExtenderData(Utilities.getExtenderIdx(extenderData));
      if (verbose) {
        LOG.log(Level.SEVERE, MessageFormat.format(
            "EXT data is error for mismatch RX. "
                + "EXT(Port {0}/{1}) {2}[{3}]''s RX''s EXT is {4}[{5}].",
            extenderData.getPort(), extenderData.getRdPort(), extenderData.getName(),
            extenderData.getOid(),
            mismatch == null ? "null" : mismatch.getName(),
            mismatch == null ? -1 : mismatch.getOid()));
      }
      return false;
    }
    return true;
  }
}
