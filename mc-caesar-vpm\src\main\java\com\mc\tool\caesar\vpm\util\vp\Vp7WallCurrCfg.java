package com.mc.tool.caesar.vpm.util.vp;

import com.mc.tool.caesar.api.datamodel.vp.Vp7ConfigData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * VP7当前配置.
 */
public class Vp7WallCurrCfg {
  private Map<Integer, Vp7ConfigData> configData = new HashMap<>();
  private Map<Integer, Set<VpVideoPortInfo>> portInfos = new HashMap<>();

  /**
   * 设置VpCon的端口信息.
   */
  public void setVpconPortInfos(int vpconId, Set<VpVideoPortInfo> portInfos) {
    this.portInfos.put(vpconId, portInfos);
  }

  /**
   * 设置VpConfig数据.
   */
  public void setVpConfigData(int vpconId, Vp7ConfigData vpConfigData) {
    this.configData.put(vpconId, vpConfigData);
  }

  /**
   * 清除旧数据.
   */
  public void clearOldData() {
    portInfos.clear();
    configData.clear();
  }

  /**
   * 获取VpCon端口信息.
   */
  public Set<VpVideoPortInfo> getVpconPortInfos(int vpconId) {
    return portInfos.getOrDefault(vpconId, new HashSet<>());
  }

  /**
   * 获取VpCon的配置数据.
   */
  public Vp7ConfigData getVpconConfigData(int vpconId) {
    return configData.getOrDefault(vpconId, new Vp7ConfigData());
  }

  /**
   * 获取连接数据.
   */
  public List<Integer> getConnection(int vpconId) {
    List<Integer> result = new ArrayList<>(Vp7Constants.MAX_VIDEO_INPUT);
    for (int i = 0; i < Vp7Constants.MAX_VIDEO_INPUT; i++) {
      result.add(0); // Initialize with 0 (default)
    }

    Set<VpVideoPortInfo> portInfoSet = portInfos.get(vpconId);
    if (portInfoSet != null) {
      int maxPort = -1;
      for (VpVideoPortInfo item : portInfoSet) {
        if (item.portIndex >= 0 && item.portIndex < Vp7Constants.MAX_VIDEO_INPUT) {
          result.set(item.portIndex, item.txId);
          maxPort = Math.max(maxPort, item.portIndex);
        }
      }
      // Resize the list to the actual size
      while (result.size() > maxPort + 1) {
        result.remove(result.size() - 1);
      }
    }

    return result;
  }

  /**
   * 获取所有已使用的VpCon.
   */
  public List<Integer> getUsedVpCons() {
    List<Integer> result = new ArrayList<>();
    for (Map.Entry<Integer, Vp7ConfigData> entry : configData.entrySet()) {
      result.add(entry.getKey());
    }
    return result;
  }
}
