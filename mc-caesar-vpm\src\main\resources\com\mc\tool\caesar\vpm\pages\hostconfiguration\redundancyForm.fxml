<?xml version="1.0" encoding="UTF-8"?>

<?import com.dooapp.fxform.view.control.ConstraintLabel?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.control.CheckBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<VBox stylesheets="@networkForm.css" xmlns="http://javafx.com/javafx/8">
  <children>
    <GridPane prefWidth="700.0">
      <columnConstraints>
        <ColumnConstraints hgrow="SOMETIMES" maxWidth="387.0" minWidth="10.0" prefWidth="193.0"/>
        <ColumnConstraints hgrow="SOMETIMES" maxWidth="761.0" minWidth="294.0" prefWidth="507.0"/>
      </columnConstraints>
      <rowConstraints>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      </rowConstraints>
      <children>
        <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="1">
          <children>
            <CheckBox id="isRedundancy-form-editor" mnemonicParsing="false"/>
            <Label id="isRedundancy-form-tooltip" text="Label">
              <HBox.margin>
                <Insets/>
              </HBox.margin>
            </Label>
            <ConstraintLabel id="isRedundancy-form-constraint"/>
          </children>
        </HBox>
        <Label text="%redundancy.configuration"/>
        <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="1"
          GridPane.rowIndex="1">
          <children>
            <CheckBox id="isSynchronize-form-editor" mnemonicParsing="false"/>
            <Label id="isSynchronize-form-tooltip" text="Label"/>
            <ConstraintLabel id="isSynchronize-form-constraint"/>
          </children>
        </HBox>
        <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="1"
          GridPane.rowIndex="2">
          <children>
            <CheckBox id="isEchoOnly-form-editor" mnemonicParsing="false"/>
            <Label id="isEchoOnly-form-tooltip" text="Label"/>
            <ConstraintLabel id="isEchoOnly-form-constraint"/>
          </children>
        </HBox>
        <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="1"
          GridPane.rowIndex="3">
          <children>
            <CheckBox disable="true" mnemonicParsing="false" selected="true"/>
            <Label id="masterIp-form-label" text="Label"/>
            <TextField id="masterIp-form-editor"/>
            <ConstraintLabel id="masterIp-form-constraint"/>
          </children>
        </HBox>
        <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="1"
              GridPane.rowIndex="4">
          <children>
            <CheckBox id="isDefaultBackup-form-editor" mnemonicParsing="false"/>
            <Label id="isDefaultBackup-form-tooltip" text="Label"/>
            <ConstraintLabel id="isDefaultBackup-form-constraint"/>
          </children>
        </HBox>
        <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="1"
              GridPane.rowIndex="5">
          <children>
            <CheckBox disable="true" mnemonicParsing="false" selected="true"/>
            <Label id="virtualIp-form-label" text="Label"/>
            <TextField id="virtualIp-form-editor"/>
            <ConstraintLabel id="virtualIp-form-constraint"/>
          </children>
        </HBox>
        <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="1"
              GridPane.rowIndex="6">
          <children>
            <CheckBox disable="true" mnemonicParsing="false" selected="true"/>
            <Label id="virtualRouteId-form-label" text="Label"/>
            <TextField id="virtualRouteId-form-editor"/>
            <ConstraintLabel id="virtualRouteId-form-constraint"/>
          </children>
        </HBox>
      </children>
    </GridPane>
  </children>
</VBox>
