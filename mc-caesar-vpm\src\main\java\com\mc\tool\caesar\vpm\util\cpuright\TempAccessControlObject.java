package com.mc.tool.caesar.vpm.util.cpuright;

import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.datamodel.AccessControlObject;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.AdvancedBitSet;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import java.util.Arrays;
import java.util.Collection;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import lombok.Getter;

/**
 * .
 */
public class TempAccessControlObject implements AccessControlObject {
  @Getter private final AccessControlObject wrappedObeject;
  private BooleanProperty isChangeProperty = new SimpleBooleanProperty(false);
  private final AdvancedBitSet videoAccess;
  private final AdvancedBitSet noAccess;

  private final AdvancedBitSet usbNoAccess;

  private final CustomPropertyChangeSupport pcs;
  private final ConfigDataManager configDataManager;

  /**
   * Constructor.
   *
   * @param pcs property change support
   * @param configDataManager config data manager
   * @param object object
   */
  public TempAccessControlObject(
      CustomPropertyChangeSupport pcs,
      ConfigDataManager configDataManager,
      AccessControlObject object) {
    this.wrappedObeject = object;
    this.pcs = pcs;
    this.configDataManager = configDataManager;
    videoAccess = new AdvancedBitSet();
    noAccess = new AdvancedBitSet();
    usbNoAccess = new AdvancedBitSet();
    reload();
  }

  @Override
  public int getId() {
    return wrappedObeject.getId();
  }

  @Override
  public String getName() {
    return wrappedObeject.getName();
  }

  @Override
  public void setVideoAccess(CpuData cpuData, boolean locked) {
    if (null == cpuData) {
      return;
    }
    boolean oldValue = this.videoAccess.get(cpuData.getOid());
    this.videoAccess.set(cpuData.getOid(), locked);
    isChangeProperty.set(
        !Arrays.equals(noAccess.getBits(), wrappedObeject.getNoAccessBitSet().getBits())
            || !Arrays.equals(videoAccess.getBits(),
            wrappedObeject.getVideoAccessBitSet().getBits())
            ||
            !Arrays.equals(usbNoAccess.getBits(), wrappedObeject.getUsbNoAccessBitSet().getBits()));
    firePropertyChange(
        AccessControlObject.PROPERTY_VIDEO_ACCESS, oldValue, locked, cpuData.getOid());
  }

  @Override
  public boolean isVideoAccess(CpuData cpuData) {
    return null != cpuData && this.videoAccess.get(cpuData.getOid());
  }

  @Override
  public void setNoAccess(CpuData cpuData, boolean locked) {
    if (null == cpuData) {
      return;
    }
    boolean oldValue = this.noAccess.get(cpuData.getOid());
    this.noAccess.set(cpuData.getOid(), locked);
    isChangeProperty.set(
        !Arrays.equals(noAccess.getBits(), wrappedObeject.getNoAccessBitSet().getBits())
            || !Arrays.equals(videoAccess.getBits(),
            wrappedObeject.getVideoAccessBitSet().getBits())
            ||
            !Arrays.equals(usbNoAccess.getBits(), wrappedObeject.getUsbNoAccessBitSet().getBits()));
    firePropertyChange(AccessControlObject.PROPERTY_NO_ACCESS, oldValue, locked, cpuData.getOid());
  }

  @Override
  public boolean isNoAccess(CpuData cpuData) {
    return null != cpuData && this.noAccess.get(cpuData.getOid());
  }

  @Override
  public boolean isUsbNoAccess(CpuData cpuData) {
    return null != cpuData && this.usbNoAccess.get(cpuData.getOid());
  }

  @Override
  public void setUsbNoAccess(CpuData cpuData, boolean locked) {
    if (null == cpuData) {
      return;
    }
    boolean oldValue = this.usbNoAccess.get(cpuData.getOid());
    this.usbNoAccess.set(cpuData.getOid(), locked);
    isChangeProperty.set(
        !Arrays.equals(noAccess.getBits(), wrappedObeject.getNoAccessBitSet().getBits())
            || !Arrays.equals(videoAccess.getBits(), wrappedObeject.getVideoAccessBitSet().getBits())
            || !Arrays.equals(usbNoAccess.getBits(), wrappedObeject.getUsbNoAccessBitSet().getBits()));
    firePropertyChange(AccessControlObject.PROPERTY_USB_NO_ACCESS, oldValue, locked, cpuData.getOid());
  }

  @Override
  public Collection<CpuData> getVideoAccessCpuDatas() {
    return configDataManager.getCpuData(this.videoAccess.getBits());
  }

  @Override
  public Collection<CpuData> getNoAccessCpuDatas() {
    return configDataManager.getCpuData(this.noAccess.getBits());
  }

  @Override
  public void setThreshold(Threshold paramThreshold) {
    wrappedObeject.setThreshold(paramThreshold);
  }

  @Override
  public Threshold getThreshold() {
    return wrappedObeject.getThreshold();
  }

  /** 把修改应用到wrapped object. */
  public void commitData() {
    wrappedObeject.setVideoAccessBits(videoAccess.getBits());
    wrappedObeject.setNoAccessBits(noAccess.getBits());
    wrappedObeject.setUsbNoAccessBits(usbNoAccess.getBits());
  }

  /** 提交状态. */
  public void commitStatus() {
    isChangeProperty.set(false);
    firePropertyChange(AccessControlObject.PROPERTY_NO_ACCESS, null, null, null);
    firePropertyChange(AccessControlObject.PROPERTY_VIDEO_ACCESS, null, null, null);
    firePropertyChange(AccessControlObject.PROPERTY_USB_NO_ACCESS, null, null, null);
  }

  protected void reload() {
    videoAccess.setBits(wrappedObeject.getVideoAccessBitSet().getBits());
    noAccess.setBits(wrappedObeject.getNoAccessBitSet().getBits());
    usbNoAccess.setBits(wrappedObeject.getUsbNoAccessBitSet().getBits());
  }

  /** 删除所有的修改. */
  public void cancel() {
    reload();
    isChangeProperty.set(false);
    firePropertyChange(AccessControlObject.PROPERTY_NO_ACCESS, null, null, null);
    firePropertyChange(AccessControlObject.PROPERTY_VIDEO_ACCESS, null, null, null);
    firePropertyChange(AccessControlObject.PROPERTY_USB_NO_ACCESS, null, null, null);
  }

  public boolean isChange() {
    return isChangeProperty.get();
  }

  public BooleanProperty isChangeProperty() {
    return isChangeProperty;
  }

  @Override
  public AdvancedBitSet getVideoAccessBitSet() {
    return videoAccess;
  }

  @Override
  public void setVideoAccessBits(int... bits) {
    getVideoAccessBitSet().setBits(bits);
  }

  @Override
  public AdvancedBitSet getNoAccessBitSet() {
    return noAccess;
  }

  @Override
  public void setNoAccessBits(int... bits) {
    getNoAccessBitSet().setBits(bits);
  }

  @Override
  public AdvancedBitSet getUsbNoAccessBitSet() {
    return usbNoAccess;
  }

  @Override
  public void setUsbNoAccessBits(int... bits) {
    getUsbNoAccessBitSet().setBits(bits);
  }

  protected final void firePropertyChange(
      String propertyName, Object oldValue, Object newValue, int... indizes) {
    int oid = 0;
    if (null == indizes || 0 == indizes.length) {
      this.pcs.fireIndexedPropertyChange(propertyName, oid, oldValue, newValue);
    } else {
      int[] idx = new int[indizes.length + 1];
      idx[0] = oid;
      System.arraycopy(indizes, 0, idx, 1, indizes.length);

      this.pcs.fireMultiIndexedPropertyChange(propertyName, idx, oldValue, newValue);
    }
  }
}
