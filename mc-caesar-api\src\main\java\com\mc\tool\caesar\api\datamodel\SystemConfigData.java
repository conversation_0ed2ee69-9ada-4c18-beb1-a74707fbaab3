package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarConstants.System.Forcebits;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.Version;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.interfaces.CommitRollback;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/**
 * 全部的系统配置数据.
 */
public final class SystemConfigData extends AbstractData implements CaesarCommunicatable {

  public static final String PROPERTY_BASE = "SystemData.";
  public static final String FIELD_STATUS = "Status";
  public static final String PROPERTY_STATUS = "SystemData.Status";
  public static final String FIELD_NETWORK_DATA_PRESET1 = "NetworkDataPreset1";
  public static final String PROPERTY_NETWORK_DATA_PRESET1 = "SystemData.NetworkDataPreset1";
  public static final String FIELD_NETWORK_DATA_CURRENT1 = "NetworkDataCurrent1";
  public static final String PROPERTY_NETWORK_DATA_CURRENT1 = "SystemData.NetworkDataCurrent1";
  public static final String FIELD_NETWORK_DATA_PRESET2 = "NetworkDataPreset2";
  public static final String PROPERTY_NETWORK_DATA_PRESET2 = "SystemData.NetworkDataPreset2";
  public static final String FIELD_NETWORK_DATA_CURRENT2 = "NetworkDataCurrent2";
  public static final String PROPERTY_NETWORK_DATA_CURRENT2 = "SystemData.NetworkDataCurrent2";
  public static final String FIELD_SYSTEM_DATA = "SystemData";
  public static final String PROPERTY_SYSTEM_DATA = "SystemData.SystemData";
  public static final String FIELD_AUTOID_DATA = "AutoIdData";
  public static final String PROPERTY_AUTOID_DATA = "SystemData.AutoIdData";
  public static final String FIELD_ACCESS_DATA = "AccessData";
  public static final String PROPERTY_ACCESS_DATA = "SystemData.AccessData";
  public static final String FIELD_SWITCH_DATA = "SwitchData";
  public static final String PROPERTY_SWITCH_DATA = "SystemData.SwitchData";
  public static final String FIELD_FORCE_BITS = "ForceBits";
  public static final String PROPERTY_FORCE_BITS = "SystemData.ForceBits";
  public static final String FIELD_SYSLOG_DATA = "SyslogData";
  public static final String FIELD_SNMP_DATA = "SnmpData";
  public static final String FIELD_LDAP_DATA = "LdapData";
  public static final String FIELD_DISPLAY_DATA = "DisplayData";
  public static final String FIELD_SNTP_DATA = "SntpData";
  public static final String FIELD_INTERNAL_LOGLEVEL = "InternalLogLevel";
  public static final String PROPERTY_INTERNAL_LOGLEVEL = "SystemData.InternalLogLevel";
  public static final String PROPERTY_AUTO_REJECT_TIME = "SystemData.AutoRejectTime";
  public static final String FIELD_INTERNAL_LOGLEVEL_DEBUG = "InternalLogLevel.Debug";
  // Log Level 下的是否选中Debug
  public static final String PROPERTY_INTERNAL_LOGLEVEL_DEBUG = "SystemData.InternalLogLevel.Debug";
  // Log Level 下的是否选中Info
  public static final String FIELD_INTERNAL_LOGLEVEL_INFO = "InternalLogLevel.Info";
  // System Data 下的是否选中 General窗口标题下的 Info
  public static final String PROPERTY_INTERNAL_LOGLEVEL_INFO = "SystemData.InternalLogLevel.Info";
  // Log Level 下的是否选中Notice
  public static final String FIELD_INTERNAL_LOGLEVEL_NOTICE = "InternalLogLevel.Notice";
  public static final String PROPERTY_INTERNAL_LOGLEVEL_NOTICE =
      "SystemData.InternalLogLevel.Notice";
  public static final String FIELD_INTERNAL_LOGLEVEL_WARNING = "InternalLogLevel.Warning";
  // Log Level 下的是否选中 Warning
  public static final String PROPERTY_INTERNAL_LOGLEVEL_WARNING =
      "SystemData.InternalLogLevel.Warning";
  public static final String FIELD_INTERNAL_LOGLEVEL_ERROR = "InternalLogLevel.Error";
  // Log Level 下的是否选中Error
  public static final String PROPERTY_INTERNAL_LOGLEVEL_ERROR = "SystemData.InternalLogLevel.Error";
  public static final String FIELD_MULTICAST = "Multicast";
  public static final String PROPERTY_MULTICAST = "SystemData.Multicast";
  public static final String PROPERTY_DEFAULT_MODE = "SystemData.DefaultMode";

  public static final int RESERVE_COUNT = 16;
  public static final int RESERVE_COUNT2 = 42;

  @Getter
  @Expose
  private int status; // @unknown 估计已废
  @Getter
  @Expose
  private int forceBits; // 系统配置中的部分bool的配置值
  @Getter
  @Expose
  private final SystemData systemData;
  @Getter
  @Expose
  private final AutoIdData autoIdData;
  @Getter
  @Expose
  private final AccessData accessData;
  @Getter
  @Expose
  private final SwitchData switchData;
  @Getter
  @Expose
  private final MatrixGridData matrixGridData;
  @Getter
  @Expose
  private int internalLogLevel; // trace log level
  @Expose
  private int internalSysLevel;

  @Getter
  @Setter
  @Expose
  private int combinationLogin;
  @Getter
  @Expose
  private SyslogData syslogData;
  @Getter
  @Expose
  private SyslogData syslogData2;
  @Getter
  @Expose
  private SnmpData snmpData;
  @Getter
  @Expose
  private SnmpData snmpData2;
  @Getter
  @Expose
  private SntpData sntpData;
  @Expose
  private NetworkData[] networkDataPreset;
  @Expose
  private NetworkData[] networkDataCurrent;
  @Getter
  @Expose
  private EventSetup eventSetup;
  @Expose
  private byte[] multicast = new byte[4];
  @Getter
  @Setter
  @Expose
  private int autoRejectTime = 10;
  private byte[] sn = new byte[8];
  @Expose
  @Getter
  @Setter
  private int defaultMode; // RX无登录下,推送与抓取的默认模式,0表示不使用默认模式,1表示默认使用完全连接模式,2表示默认使用视频连接模式

  /**
   * .
   */
  public SystemConfigData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager,
      String fqn) {
    super(pcs, configDataManager, -1, fqn);

    this.systemData = new SystemData(pcs, configDataManager, fqn + "." + "SystemData", this);
    this.autoIdData = new AutoIdData(pcs, configDataManager, fqn + "." + "AutoIdData", this);
    this.accessData = new AccessData(pcs, configDataManager, fqn + "." + "AccessData", this);
    this.switchData = new SwitchData(pcs, configDataManager, fqn + "." + "SwitchData", this);
    this.matrixGridData =
        new MatrixGridData(pcs, configDataManager, fqn + "." + "SwitchData", this);
    this.syslogData = new SyslogData(pcs, configDataManager, 0, fqn + "." + "SyslogData");
    this.syslogData2 = new SyslogData(pcs, configDataManager, 1, fqn + "." + "SyslogData");
    this.snmpData = new SnmpData(pcs, configDataManager, 0, fqn + "." + "SnmpData");
    this.snmpData2 = new SnmpData(pcs, configDataManager, 1, fqn + "." + "SnmpData");
    this.sntpData = new SntpData(pcs, configDataManager, fqn + "." + "SntpData");
    this.eventSetup  = new EventSetup(pcs, configDataManager, fqn + "." + "EventSetup");

    final Map<String, Integer> forceBitMap = new HashMap<>();

    forceBitMap.put(SystemData.PROPERTY_FORCE_BITS_AUTO_SAVE, Forcebits.AUTOSAVE);
    forceBitMap.put(SystemData.PROPERTY_FORCE_BITS_COM_ECHO, Forcebits.COMECHO);
    forceBitMap.put(SystemData.PROPERTY_FORCE_BITS_LAN_ECHO, Forcebits.LANECHO);
    forceBitMap.put(SystemData.PROPERTY_FORCE_BITS_SLAVE, Forcebits.SLAVE);
    forceBitMap.put(SystemData.PROPERTY_FORCE_BITS_FORCE_PUSH_GET, Forcebits.FORCEPUSHGET);
    forceBitMap.put(SystemData.PROPERTY_FORCE_BITS_ECHOONLY, Forcebits.ECHOONLY);
    forceBitMap.put(SystemData.PROPERTY_FORCE_BITS_SYNCHRONIZE, Forcebits.SYNCHRONIZE);
    forceBitMap.put(SystemData.PROPERTY_FORCE_BITS_INVALID, Forcebits.INVALID);
    forceBitMap.put(SystemData.PROPERTY_FORCE_BITS_OLDECHO, Forcebits.OLDECHO);
    forceBitMap.put(SystemData.PROPERTY_FORCE_UART_ENABLE, Forcebits.UART_ENABLE);
    forceBitMap.put(SystemData.PROPERTY_FORCE_BITS_DEFAULT, Forcebits.DEFAULT);
    forceBitMap.put(MatrixGridData.PROPERTY_FORCE_BITS_GRID, Forcebits.GRID);

    forceBitMap.put(AutoIdData.PROPERTY_FORCE_BITS_AUTO_CONFIG, Forcebits.AUTOCONFIG);

    forceBitMap.put(AccessData.PROPERTY_FORCE_BITS_LOGIN, Forcebits.LOGIN);
    forceBitMap.put(AccessData.PROPERTY_FORCE_BITS_USERLOCK, Forcebits.USERLOCK);
    forceBitMap.put(AccessData.PROPERTY_FORCE_BITS_CONLOCK, Forcebits.CONLOCK);
    forceBitMap.put(AccessData.PROPERTY_FORCE_BITS_USERACCESS, Forcebits.USERACCESS);
    forceBitMap.put(AccessData.PROPERTY_FORCE_BITS_CONACCESS, Forcebits.CONACCESS);
    forceBitMap.put(AccessData.PROPERTY_FORCE_BITS_CPUDISCONNECT, Forcebits.CPUDISCONNECT);
    forceBitMap.put(AccessData.PROPERTY_FORCE_BITS_USERCONOR, Forcebits.USERCONOR);
    forceBitMap.put(AccessData.PROPERTY_FORCE_BITS_USERCONAND, Forcebits.USERCONAND);

    forceBitMap.put(SwitchData.PROPERTY_FORCE_BITS_AUTO_CONNECT, Forcebits.AUTOCONNECT);
    forceBitMap.put(SwitchData.PROPERTY_FORCE_BITS_CON_DISCONNECT, Forcebits.CONDISCONNECT);
    forceBitMap.put(SwitchData.PROPERTY_FORCE_BITS_CPU_CONNECT, Forcebits.CPUCONNECT);
    forceBitMap.put(SwitchData.PROPERTY_FORCE_BITS_CPU_WATCH, Forcebits.CPUWATCH);
    forceBitMap
        .put(SwitchData.PROPERTY_FORCE_BITS_KEYBOARD_MOUSE_CONNECT, Forcebits.KEYBOARD_MOUSE);
    forceBitMap.put(SwitchData.PROPERTY_FORCE_BITS_FKEYSINGLE, Forcebits.FKEYSINGLE);

    pcs.addPropertyChangeListener(SystemConfigData.PROPERTY_FORCE_BITS,
        new PropertyChangeListener() {
          @Override
          public void propertyChange(PropertyChangeEvent evt) {
            int oldValue = getValue(evt.getOldValue());
            int newValue = getValue(evt.getNewValue());
            for (Map.Entry<String, Integer> entry : forceBitMap.entrySet()) {
              SystemConfigData.this.firePropertyChange(entry.getKey(),
                  Utilities.areBitsSet(oldValue, entry.getValue()),
                  Utilities.areBitsSet(newValue, entry.getValue()));
            }
          }

          private int getValue(Object object) {
            if (object instanceof Number) {
              return ((Number) object).intValue();
            }
            return 0;
          }
        });
    final Map<String, Integer> internalLogLevelMap = new HashMap<>();
    internalLogLevelMap.put(SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL_DEBUG,
        CaesarConstants.Network.Loglevel.DEBUG);
    internalLogLevelMap.put(SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL_INFO,
        CaesarConstants.Network.Loglevel.INFO);
    internalLogLevelMap.put(SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL_NOTICE,
        CaesarConstants.Network.Loglevel.NOTICE);
    internalLogLevelMap.put(SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL_WARNING,
        CaesarConstants.Network.Loglevel.WARNING);
    internalLogLevelMap.put(SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL_ERROR,
        CaesarConstants.Network.Loglevel.ERROR);

    pcs.addPropertyChangeListener(SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL,
        new PropertyChangeListener() {
          @Override
          public void propertyChange(PropertyChangeEvent evt) {
            int oldValue = getValue(evt.getOldValue());
            int newValue = getValue(evt.getNewValue());
            for (Map.Entry<String, Integer> entry : internalLogLevelMap.entrySet()) {
              SystemConfigData.this.firePropertyChange(entry.getKey(),
                  Utilities.areBitsSet(oldValue, entry.getValue()),
                  Utilities.areBitsSet(newValue, entry.getValue()));
            }
          }

          private int getValue(Object object) {
            if (object instanceof Number) {
              return ((Number) object).intValue();
            }
            return 0;
          }
        });
    initCommitRollback();
  }

  @Override
  public void initDefaults() {
    this.systemData.initDefaults();
    this.autoIdData.initDefaults();
    this.accessData.initDefaults();
    this.switchData.initDefaults();
    this.matrixGridData.initDefaults();
    this.syslogData.initDefaults();
    this.syslogData2.initDefaults();
    this.snmpData.initDefaults();
    this.snmpData2.initDefaults();
    this.sntpData.initDefaults();
    this.eventSetup.initDefaults();
    initNetworkData();

    this.internalLogLevel = 0;
    setMulticast(new byte[]{-1, -1, -1, -1});
  }

  private void initNetworkData() {
    Version version = getConfigDataManager().getConfigMetaData().getUtilVersion();
    if (networkDataPreset == null || networkDataPreset.length != version.getNetworkCount()) {
      if (networkDataPreset != null) {
        for (NetworkData data : networkDataPreset) {
          if (data != null) {
            data.destroy();
          }
        }
      }
      if (networkDataCurrent != null) {
        for (NetworkData data : networkDataCurrent) {
          if (data != null) {
            data.destroy();
          }
        }
      }
      networkDataPreset = new NetworkData[version.getNetworkCount()];
      networkDataCurrent = new NetworkData[version.getNetworkCount()];
      for (int i = 0; i < version.getNetworkCount(); i++) {
        networkDataPreset[i] = new NetworkData(pcs, getConfigDataManager(), i * 2,
            getFqn() + "." + "NetworkDataPreset" + (i + 1), true);
        networkDataCurrent[i] = new NetworkData(pcs, getConfigDataManager(), i * 2 + 1,
            getFqn() + "." + "NetworkDataCurrent" + (i + 1), false);

        networkDataPreset[i].setDhcp(false);
        networkDataPreset[i].setApi(true);
        networkDataPreset[i].setFtp(true);
        networkDataPreset[i].setNetmask(new byte[]{-1, -1, -1, 0});
      }
    }
  }

  /**
   * 获取网络配置1.
   *
   * @return 网路配置1
   */
  public NetworkData getNetworkDataCurrent1() {
    if (this.networkDataCurrent != null && this.networkDataCurrent.length > 0) {
      return this.networkDataCurrent[0];
    } else {
      return null;
    }
  }

  /**
   * 获取网络配置1.
   *
   * @return 网路配置1
   */
  public NetworkData getNetworkDataPreset1() {
    if (this.networkDataPreset != null && this.networkDataPreset.length > 0) {
      return this.networkDataPreset[0];
    } else {
      return null;
    }
  }

  /**
   * 获取网络配置2.
   *
   * @return 网络配置2
   */
  public NetworkData getNetworkDataCurrent2() {
    if (this.networkDataCurrent != null && this.networkDataCurrent.length > 1) {
      return this.networkDataCurrent[1];
    } else {
      return null;
    }
  }

  /**
   * 获取网络配置2.
   *
   * @return 网络配置2
   */
  public NetworkData getNetworkDataPreset2() {
    if (this.networkDataPreset != null && this.networkDataPreset.length > 1) {
      return this.networkDataPreset[1];
    } else {
      return null;
    }
  }

  /**
   * 获取网络配置3.
   *
   * @return 网络配置3
   */
  public NetworkData getNetworkDataCurrent3() {
    if (this.networkDataCurrent != null && this.networkDataCurrent.length > 2) {
      return this.networkDataCurrent[2];
    } else {
      return null;
    }
  }

  /**
   * 获取网络配置3.
   *
   * @return 网络配置3
   */
  public NetworkData getNetworkDataPreset3() {
    if (this.networkDataPreset != null && this.networkDataPreset.length > 2) {
      return this.networkDataPreset[2];
    } else {
      return null;
    }
  }

  /**
   * 获取网络配置4.
   *
   * @return 网络配置4
   */
  public NetworkData getNetworkDataCurrent4() {
    if (this.networkDataCurrent != null && this.networkDataCurrent.length > 3) {
      return this.networkDataCurrent[3];
    } else {
      return null;
    }
  }

  /**
   * 获取网络配置4.
   *
   * @return 网络配置4
   */
  public NetworkData getNetworkDataPreset4() {
    if (this.networkDataPreset != null && this.networkDataPreset.length > 3) {
      return this.networkDataPreset[3];
    } else {
      return null;
    }
  }

  /**
   * .
   */
  public void setForceBits(int forceBits) {
    int oldValue = this.forceBits;
    this.forceBits = forceBits;
    firePropertyChange(SystemConfigData.PROPERTY_FORCE_BITS, oldValue, this.forceBits);
  }

  /**
   * 设置默认模式.
   */
  public void setDefaultMode(int defaultMode) {
    int oldValue = this.defaultMode;
    if (oldValue != defaultMode) {
      this.defaultMode = defaultMode;
      firePropertyChange(SystemConfigData.PROPERTY_DEFAULT_MODE, oldValue, this.defaultMode);
    }
  }

  /**
   * .
   */
  public void setStatus(int status) {
    int oldValue = this.status;
    this.status = status;
    firePropertyChange(SystemConfigData.PROPERTY_STATUS, oldValue, this.status);
  }

  public boolean isMasterCpu() {
    return Utilities.areBitsSet(this.status, CaesarConstants.System.Status.MASTERCPU);
  }

  public boolean isSlaveCpu() {
    return Utilities.areBitsSet(this.status, CaesarConstants.System.Status.SLAVECPU);
  }

  public boolean isPrimaryCpu() {
    return Utilities.areBitsSet(this.status, CaesarConstants.System.Status.PRIMARYCPU);
  }

  public boolean isSecondaryCpu() {
    return Utilities.areBitsSet(this.status, CaesarConstants.System.Status.SECONDARYCPU);
  }

  /**
   * .
   */
  public void setInternalLogLevel(int internalLogLevel) {
    int oldValue = this.internalLogLevel;
    this.internalLogLevel = internalLogLevel;
    firePropertyChange(SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL, oldValue, internalLogLevel);
  }

  public boolean isInternalLogLevelDebug() {
    return Utilities.areBitsSet(this.internalLogLevel, CaesarConstants.Network.Loglevel.DEBUG);
  }

  public void setInternalLogLevelDebug(boolean enabled) {
    setInternalLogLevel(
        Utilities.setBits(this.internalLogLevel, enabled, CaesarConstants.Network.Loglevel.DEBUG));
  }

  public boolean isInternalLogLevelInfo() {
    return Utilities.areBitsSet(this.internalLogLevel, CaesarConstants.Network.Loglevel.INFO);
  }

  public void setInternalLogLevelInfo(boolean enabled) {
    setInternalLogLevel(
        Utilities.setBits(this.internalLogLevel, enabled, CaesarConstants.Network.Loglevel.INFO));
  }

  public boolean isInternalLogLevelNotice() {
    return Utilities.areBitsSet(this.internalLogLevel, CaesarConstants.Network.Loglevel.NOTICE);
  }

  public void setInternalLogLevelNotice(boolean enabled) {
    setInternalLogLevel(
        Utilities.setBits(this.internalLogLevel, enabled, CaesarConstants.Network.Loglevel.NOTICE));
  }

  public boolean isInternalLogLevelWarning() {
    return Utilities.areBitsSet(this.internalLogLevel, CaesarConstants.Network.Loglevel.WARNING);
  }

  public void setInternalLogLevelWarning(boolean enabled) {
    setInternalLogLevel(Utilities
        .setBits(this.internalLogLevel, enabled, CaesarConstants.Network.Loglevel.WARNING));
  }

  public boolean isInternalLogLevelError() {
    return Utilities.areBitsSet(this.internalLogLevel, CaesarConstants.Network.Loglevel.ERROR);
  }

  public void setInternalLogLevelError(boolean enabled) {
    setInternalLogLevel(
        Utilities.setBits(this.internalLogLevel, enabled, CaesarConstants.Network.Loglevel.ERROR));
  }

  public boolean isFaceLogin() {
    return Utilities.areBitsSet(this.combinationLogin, CaesarConstants.LoginSetupType.FACE);
  }

  public void setFaceLogin(boolean enable) {
    setCombinationLogin(
        Utilities.setBits(this.combinationLogin, enable, CaesarConstants.LoginSetupType.FACE));
  }

  public boolean isPwdLogin() {
    return Utilities.areBitsSet(this.combinationLogin, CaesarConstants.LoginSetupType.PWD);
  }

  public void setPwdLogin(boolean enable) {
    setCombinationLogin(
        Utilities.setBits(this.combinationLogin, enable, CaesarConstants.LoginSetupType.PWD));
  }

  public boolean isMatrixGridEnabled() {
    return this.matrixGridData.isMatrixGridEnabled();
  }

  public byte[] getMulticast() {
    return Arrays.copyOf(this.multicast, this.multicast.length);
  }

  /**
   * .
   */
  public void setMulticast(byte[] multicast) {
    byte[] oldValue = this.multicast;
    this.multicast = Arrays.copyOf(multicast, multicast.length);
    firePropertyChange(SystemConfigData.PROPERTY_MULTICAST, oldValue, this.multicast);
  }

  public boolean isEnableRemoteLog() {
    return this.syslogData.isEnabled();
  }

  public void setEnabledRemoteLog(boolean enabled) {
    this.syslogData.setEnabled(enabled);
  }

  public boolean isEnableLocalLog() {
    return this.syslogData.isEnableLocalLog();
  }

  public void setEnableLocalLog(boolean enabled) {
    this.syslogData.setEnableLocalLog(enabled);
  }

  @Override
  protected Collection<? extends CommitRollback> getDependentCommitRollbacks() {
    return Arrays.asList(this.accessData, this.autoIdData, this.switchData, this.systemData,
        this.matrixGridData, this.syslogData, this.syslogData2, this.snmpData, this.snmpData2,
        this.sntpData);
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (SystemConfigData.PROPERTY_STATUS.equals(propertyName)) {
      setStatus((Integer) value);
    } else if (SystemConfigData.PROPERTY_FORCE_BITS.equals(propertyName)) {
      setForceBits((Integer) value);
    } else if (SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL.equals(propertyName)) {
      setInternalLogLevel((Integer) value);
    } else if (SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL_DEBUG.equals(propertyName)) {
      setInternalLogLevelDebug((Boolean) value);
    } else if (SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL_INFO.equals(propertyName)) {
      setInternalLogLevelInfo((Boolean) value);
    } else if (SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL_NOTICE.equals(propertyName)) {
      setInternalLogLevelNotice((Boolean) value);
    } else if (SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL_WARNING.equals(propertyName)) {
      setInternalLogLevelWarning((Boolean) value);
    } else if (SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL_ERROR.equals(propertyName)) {
      setInternalLogLevelError((Boolean) value);
    } else if (SystemConfigData.PROPERTY_MULTICAST.equals(propertyName)) {
      setMulticast((byte[]) value);
    }
  }

  @Override
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    cfgWriter.writeInteger(getStatus());
    cfgWriter.writeString(this.systemData.getName(), CaesarConstants.MATRIX_NAME_LEN);
    cfgWriter.writeString(this.systemData.getInfo(), CaesarConstants.MATRIX_INFO_LEN);
    cfgWriter.writeString(this.systemData.getDevice(), CaesarConstants.MATRIX_DEVICE_LEN);

    Version version = getConfigDataManager().getConfigMetaData().getUtilVersion();
    for (int j = 0; j < version.getNetworkCount(); j++) {
      networkDataPreset[j].writeData(cfgWriter);
      networkDataCurrent[j].writeData(cfgWriter);
      for (int i = this.systemData.getMasterIp().length; i > 0; i--) {
        cfgWriter.write4Byte(this.systemData.getMasterIp()[i - 1]);
      }
    }
    cfgWriter.writeInteger(this.forceBits);
    cfgWriter.writeInteger(this.accessData.getTimeoutDisplay());
    cfgWriter.writeInteger(this.switchData.getTimeoutDisconnect());
    cfgWriter.writeInteger(this.accessData.getTimeoutLogout());
    cfgWriter.writeInteger(this.switchData.getTimeoutShare());
    for (int idx = 0; idx < this.autoIdData.getAutoIdArray().length; idx++) {
      cfgWriter.writeInteger(this.autoIdData.getAutoId(idx));
    }
    cfgWriter.writeInteger(this.internalLogLevel);
    cfgWriter.writeInteger(this.internalSysLevel);
    cfgWriter.writeByteArray(new byte[RESERVE_COUNT]);
    getSntpData().writeData(cfgWriter);
    getSnmpData().writeData(cfgWriter);
    getSnmpData2().writeData(cfgWriter);
    cfgWriter.writeByteArray(sn);
    cfgWriter.writeByte((byte) autoRejectTime);
    getSyslogData().writeData(cfgWriter);
    getEventSetup().writeData(cfgWriter);
    cfgWriter.writeInteger(combinationLogin);
    cfgWriter.writeByte((byte) defaultMode);
    cfgWriter.writeByteArray(new byte[RESERVE_COUNT2]);
  }

  @Override
  public void readData(CfgReader cfgReader) throws ConfigException {
    int status = cfgReader.readInteger();
    setStatus(status);
    String name = cfgReader.readString(CaesarConstants.NAME_LEN);
    this.systemData.setName(name);
    String info = cfgReader.readString(CaesarConstants.MATRIX_INFO_LEN);
    this.systemData.setInfo(info);
    String device = cfgReader.readString(CaesarConstants.MATRIX_DEVICE_LEN);
    this.systemData.setDevice(device);
    if (!getConfigDataManager().getConfigMetaData().getUtilVersion().isConnectable()) {
      return;
    }

    initNetworkData();
    Version version = getConfigDataManager().getConfigMetaData().getUtilVersion();
    for (int i = 0; i < version.getNetworkCount(); i++) {
      networkDataPreset[i].readData(cfgReader);
      networkDataCurrent[i].readData(cfgReader);
      byte[] newMasterIp = new byte[this.systemData.getMasterIp().length];
      for (int idx = this.systemData.getMasterIp().length; idx > 0; idx--) {
        newMasterIp[idx - 1] = cfgReader.read4ByteValue();
      }
      this.systemData.setMasterIp(newMasterIp);
    }
    int forceBits = cfgReader.readInteger();
    setForceBits(forceBits);
    int timeoutDisplay = cfgReader.readInteger();
    this.accessData.setTimeoutDisplay(timeoutDisplay);
    int timeoutDisconnect = cfgReader.readInteger();
    this.switchData.setTimeoutDisconnect(timeoutDisconnect);
    int timeoutLogout = cfgReader.readInteger();
    this.accessData.setTimeoutLogout(timeoutLogout);
    int timeoutShare = cfgReader.readInteger();
    this.switchData.setTimeoutShare(timeoutShare);
    for (int idx = 0; idx < this.autoIdData.getAutoIdArray().length; idx++) {
      int autoId = cfgReader.readInteger();
      this.autoIdData.setAutoId(idx, autoId);
    }
    int internalLogLevel = cfgReader.readInteger();
    setInternalLogLevel(internalLogLevel);
    this.internalSysLevel = cfgReader.readInteger();

    cfgReader.readByteArray(RESERVE_COUNT);
    this.sntpData.readData(cfgReader);
    this.snmpData.readData(cfgReader);
    this.snmpData2.readData(cfgReader);
    sn = cfgReader.readByteArray(8);
    autoRejectTime = cfgReader.readByteValue();
    this.syslogData.readData(cfgReader);
    this.eventSetup.readData(cfgReader);
    combinationLogin = cfgReader.readInteger();
    defaultMode = cfgReader.readByteValue();
    cfgReader.readByteArray(RESERVE_COUNT2);
  }
}

