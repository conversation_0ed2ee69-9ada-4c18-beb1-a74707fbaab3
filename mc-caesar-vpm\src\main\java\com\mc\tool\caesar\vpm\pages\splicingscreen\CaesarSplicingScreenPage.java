package com.mc.tool.caesar.vpm.pages.splicingscreen;

import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.framework.interfaces.Page;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.fxml.FXMLLoader;
import javafx.scene.layout.Pane;
import lombok.extern.slf4j.Slf4j;

/**
 * CaesarSplicingScreenPage.
 */
@Slf4j
public final class CaesarSplicingScreenPage implements Page {
  public static final String NAME = "splicingscreen";
  private final CaesarSplicingScreenController controller;
  private FXMLLoader fxmlLoader;

  private final BooleanProperty visibility = new SimpleBooleanProperty(true);

  /**
   * Constructor.
   */
  public CaesarSplicingScreenPage(CaesarEntity entity) {
    URL location = getClass().getResource(
        "/com/mc/tool/caesar/vpm/pages/splicingscreen/splicingscreen_view.fxml");
    fxmlLoader = new FXMLLoader(location);
    fxmlLoader.setResources(
        ResourceBundle.getBundle("com.mc.tool.caesar.vpm.pages.splicingscreen.Bundle"));
    try {
      fxmlLoader.load();
    } catch (IOException exception) {
      log.warn("Fail to load splicingscreen_view.fxml", exception);
    }
    controller = fxmlLoader.getController();
    controller.setDeviceController(entity.getController());
  }

  @Override
  public String getTitle() {
    return Bundle.getMessage("splicingscreen_page.title");
  }

  @Override
  public String getName() {
    return NAME;
  }

  @Override
  public Pane getView() {
    return fxmlLoader.getRoot();
  }

  @Override
  public String getStyleClass() {
    return "caesar-splicingscreen-page";
  }

  @Override
  public BooleanProperty getVisibleProperty() {
    return visibility;
  }

  @Override
  public void showObject(Object object) {
  }

  @Override
  public void refresh() {
    controller.refresh();
  }

  @Override
  public void refreshOnShow() {
    controller.refresh();
  }
}
