package com.mc.tool.caesar.vpm.gui.pages.systemedit;

import com.mc.common.control.TextWrapper;
import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import javafx.scene.control.Button;
import javafx.scene.input.KeyCode;
import org.junit.Assert;
import org.junit.Test;

/**
 * .
 */
public class UsbTest extends AppGuiTestBase {

  @Test
  public void testUsbCreate() {
    createUsbGroupImpl(0);
    createUsbGroupImpl(1);
  }

  protected void createUsbGroupImpl(int typeIndex) {
    Object item = createUsbImpl(typeIndex, "300", 1);
    checkUncreatable("300", 1);
    Object newItem = createUsbImpl(typeIndex, "301", 1);
    Assert.assertNotEquals(item, newItem);

    for (int i = 0; i < 2; i++) {
      deleteUsbImpl((300 + i) + "");
    }
  }

  protected void checkUncreatable(String port, int index) {
    rightClickOn(GuiTestConstants.MATRIX_CELL);
    clickOn(GuiTestConstants.BUTTON_USB_BIND);
    clickOn(GuiTestConstants.TEXT_PORT_INPUT);
    type(KeyCode.BACK_SPACE, 5);
    write(port);
    clickOn(GuiTestConstants.COMBO_BINDING);
    for (int i = 0; i < index; i++) {
      type(KeyCode.DOWN);
    }
    type(KeyCode.ENTER);
    Assert.assertTrue(
        lookup(
                (n) ->
                    n instanceof Button
                        && n.isVisible()
                        && window(n) == targetWindow()
                        && ((Button) n).getText().equals(GuiTestConstants.BUTTON_APPLY))
            .queryButton()
            .disableProperty()
            .get());
    clickOn(GuiTestConstants.BUTTON_CANCEL);
  }

  protected void deleteUsbImpl(String port) {
    rightClickOn(port);
    clickOn(GuiTestConstants.MENU_DELETE);
    Assert.assertTrue(
        lookup((n) -> n instanceof TextWrapper && ((TextWrapper) n).getText().equals(port))
                .queryAll()
                .size()
            == 0);
  }
}
