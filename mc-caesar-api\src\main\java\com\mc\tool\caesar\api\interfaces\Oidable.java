package com.mc.tool.caesar.api.interfaces;

import java.util.Comparator;

/**
 * .
 */
public interface Oidable {

  Comparator<Oidable> COMPARATOR_OID = (left, right) -> {
    if (null == left) {
      if (null == right) {
        return 0;
      }
      return -1;
    }
    if (null == right) {
      return 1;
    }
    if (left.getClass().equals(right.getClass())) {
      return left.getOid() - right.getOid();
    }
    if (left.getClass().isAssignableFrom(right.getClass())) {
      return -1;
    }
    if (right.getClass().isAssignableFrom(left.getClass())) {
      return 1;
    }
    return left.getOid() - right.getOid();
  };

  int getOid();
}

