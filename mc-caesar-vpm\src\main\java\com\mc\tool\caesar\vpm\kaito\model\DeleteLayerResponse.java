package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import javax.annotation.Nullable;
import lombok.Setter;

/**
 * DeleteLayerResponse.
 */
@Setter
@JsonPropertyOrder({
    DeleteLayerResponse.JSON_PROPERTY_SEQ
})
public class DeleteLayerResponse {
  public static final String JSON_PROPERTY_SEQ = "seq";
  private Integer seq;


  public DeleteLayerResponse seq(Integer seq) {
    this.seq = seq;
    return this;
  }

  /**
   * 大屏修改序号.
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_SEQ)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getSeq() {
    return seq;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DeleteLayerResponse deleteLayerResponse = (DeleteLayerResponse) o;
    return Objects.equals(this.seq, deleteLayerResponse.seq);
  }

  @Override
  public int hashCode() {
    return Objects.hash(seq);
  }


  @Override
  public String toString() {
    return "DeleteLayerResponse {\n"
        + "    seq: " + toIndentedString(seq) + "\n" + "}";
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
