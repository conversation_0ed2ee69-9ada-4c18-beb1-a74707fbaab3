package com.mc.tool.caesar.vpm.pages.systemedit.datamodel;

import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.framework.systemedit.datamodel.MultimediaInterfaceType;

/**
 * .
 */
public class CaesarUsbRxTerminal extends CaesarTerminalBase {
  private ExtenderData extenderData;

  public CaesarUsbRxTerminal(ExtenderData extenderData) {
    setExtenderData(extenderData);
  }

  public CaesarUsbRxTerminal() {
    setExtenderData(null);
  }

  /**
   * 设置外设数据.
   *
   * @param extenderData 外设数据
   */
  public void setExtenderData(ExtenderData extenderData) {
    if (this.extenderData != null && this.extenderData.getNameProperty() != null) {
      nameProperty.unbindBidirectional(extenderData.getNameProperty());
    }
    this.extenderData = extenderData;
    multimediaInterfaceType.set(MultimediaInterfaceType.USB);
    if (extenderData != null) {
      port1ConnectedProperty.set(extenderData.getPort() != 0);
      port2ConnectedProperty.set(extenderData.getRdPort() != 0);
      targetDeviceConnected.set(true);
      nameProperty.bindBidirectional(extenderData.getNameProperty());
      idProperty.set(extenderData.getId());
    } else {
      idProperty.set(0);
    }

    onlineProperty.set(
        extenderData != null
            && extenderData.isStatusActive()
            && (extenderData.getPort() != 0 || extenderData.getRdPort() != 0));
  }

  @Override
  public ExtenderData getExtenderData() {
    return extenderData;
  }

  @Override
  public boolean isRx() {
    return true;
  }

  @Override
  public boolean isTx() {
    return false;
  }
}
