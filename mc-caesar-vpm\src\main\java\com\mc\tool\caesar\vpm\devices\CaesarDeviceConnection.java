package com.mc.tool.caesar.vpm.devices;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * CaesarDeviceConnection.
 */
@Setter
@Getter
@EqualsAndHashCode
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CaesarDeviceConnection {
  private int cpuExtenderId;
  private boolean isCpuRedundant;
  private int conExtenderId;
  private boolean isConRedundant;
  private int conChannel;
}
