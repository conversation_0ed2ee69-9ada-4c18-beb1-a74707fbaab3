package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * .
 */
public final class NetworkData extends AbstractData {

  public static final String PROPERTY_BASE = "SystemConfigData.NetworkData.";
  public static final String FIELD_ADDRESS = "Address";
  // General 的IP地址
  public static final String PROPERTY_ADDRESS = "SystemConfigData.NetworkData.Address";
  public static final String FIELD_SNMPAGENT_PORT = "SnmpAgentPort";
  public static final String PROPERTY_SNMPAGENT_PORT = "SystemConfigData.NetworkData.SnmpAgentPort";
  public static final String FIELD_NETMASK = "Netmask";
  // General 的子网掩码
  public static final String PROPERTY_NETMASK = "SystemConfigData.NetworkData.Netmask";
  public static final String FIELD_GATEWAY = "Gateway";
  // General 的网关
  public static final String PROPERTY_GATEWAY = "SystemConfigData.NetworkData.Gateway";
  public static final String FIELD_MACADDRESS = "MacAddress";
  // General 的 MAC地址
  public static final String PROPERTY_MACADDRESS = "SystemConfigData.NetworkData.MacAddress";
  public static final String FIELD_NETWORK_BITS = "NetworkBits";
  public static final String PROPERTY_NETWORK_BITS = "SystemConfigData.NetworkData.NetworkBits";
  // General的 DHCP
  public static final String PROPERTY_NETWORK_BITS_DHCP =
      "SystemConfigData.NetworkData.NetworkBits.Dhcp";
  // General的API服务
  public static final String PROPERTY_NETWORK_BITS_API =
      "SystemConfigData.NetworkData.NetworkBits.Api";
  // General的FTP服务器
  public static final String PROPERTY_NETWORK_BITS_FTP =
      "SystemConfigData.NetworkData.NetworkBits.Ftp";
  // SNMP的 SNMP代理
  public static final String PROPERTY_NETWORK_BITS_SNMP =
      "SystemConfigData.NetworkData.NetworkBits.Snmp";
  public static final String PROPERTY_NETWORK_BITS_SNTP =
      "SystemConfigData.NetworkData.NetworkBits.Sntp";
  public static final String PROPERTY_NETWORK_BITS_SYSLOG =
      "SystemConfigData.NetworkData.NetworkBits.Syslog";
  public static final String PROPERTY_NETWORK_BITS_LDAP =
      "SystemConfigData.NetworkData.NetworkBits.Ldap";
  @Expose
  private int networkBits;
  @Expose
  private byte[] address = new byte[4];
  @Expose
  private byte[] netmask = new byte[4];
  @Expose
  private byte[] gateway = new byte[4];
  @Expose
  private String macaddress;
  @Expose
  private int snmpAgentPort;

  private PropertyChangeListener propertyChangeListener = null;

  /**
   * .
   */
  public NetworkData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager, int oid,
      String fqn, boolean addNetworkBitsChangeListener) {
    super(pcs, configDataManager, oid, fqn);

    final Map<String, Integer> forceBitMap = new HashMap<>();
    forceBitMap.put(NetworkData.PROPERTY_NETWORK_BITS_DHCP, CaesarConstants.Network.Bits.DHCP);
    forceBitMap.put(NetworkData.PROPERTY_NETWORK_BITS_API, CaesarConstants.Network.Bits.API);
    forceBitMap.put(NetworkData.PROPERTY_NETWORK_BITS_FTP, CaesarConstants.Network.Bits.FTP);
    forceBitMap.put(NetworkData.PROPERTY_NETWORK_BITS_SNMP, CaesarConstants.Network.Bits.SNMP);
    forceBitMap.put(NetworkData.PROPERTY_NETWORK_BITS_SNTP, CaesarConstants.Network.Bits.SNTP);
    forceBitMap.put(NetworkData.PROPERTY_NETWORK_BITS_SYSLOG, CaesarConstants.Network.Bits.SYSLOG);
    forceBitMap.put(NetworkData.PROPERTY_NETWORK_BITS_LDAP, CaesarConstants.Network.Bits.LDAP);
    if (addNetworkBitsChangeListener) {
      pcs.addPropertyChangeListener(NetworkData.PROPERTY_NETWORK_BITS,
          propertyChangeListener = new PropertyChangeListener() {
            @Override
            public void propertyChange(PropertyChangeEvent evt) {
              int oldValue = getValue(evt.getOldValue());
              int newValue = getValue(evt.getNewValue());
              for (Map.Entry<String, Integer> entry : forceBitMap.entrySet()) {
                NetworkData.this.firePropertyChange(entry.getKey(),
                    Utilities.areBitsSet(oldValue, entry.getValue()),
                    Utilities.areBitsSet(newValue, entry.getValue()));
              }
            }

            private int getValue(Object object) {
              if (object instanceof Number) {
                return ((Number) object).intValue();
              }
              return 0;
            }
          });
    }
    initCommitRollback();
  }

  /**
   * 销毁数据.
   */
  public void destroy() {
    if (propertyChangeListener != null) {
      pcs.removePropertyChangeListener(propertyChangeListener);
    }
  }

  @Override
  public void initDefaults() {
    setDhcp(false);

    setApi(true);
    setFtp(true);
  }

  public int getNetworkBits() {
    return this.networkBits;
  }

  /**
   * .
   */
  public void setNetworkBits(int networkBits) {
    int oldValue = this.networkBits;
    this.networkBits = networkBits;
    firePropertyChange(NetworkData.PROPERTY_NETWORK_BITS, oldValue, this.networkBits);
  }

  public byte[] getAddress() {
    return Arrays.copyOf(this.address, this.address.length);
  }

  /**
   * .
   */
  public void setAddress(byte[] address) {
    byte[] oldValue = this.address;
    this.address = Arrays.copyOf(address, address.length);
    firePropertyChange(NetworkData.PROPERTY_ADDRESS, oldValue, this.address);
  }

  public int getSnmpAgentPort() {
    return this.snmpAgentPort;
  }

  /**
   * .
   */
  public void setSnmpAgentPort(int port) {
    int oldValue = this.snmpAgentPort;
    this.snmpAgentPort = port;
    firePropertyChange(NetworkData.PROPERTY_SNMPAGENT_PORT, oldValue, this.snmpAgentPort);
  }

  public boolean isDhcp() {
    return Utilities.areBitsSet(getNetworkBits(), CaesarConstants.Network.Bits.DHCP);
  }

  public void setDhcp(boolean enabled) {
    setNetworkBits(Utilities.setBits(this.networkBits, enabled, CaesarConstants.Network.Bits.DHCP));
  }

  public boolean isApi() {
    return Utilities.areBitsSet(getNetworkBits(), CaesarConstants.Network.Bits.API);
  }

  public void setApi(boolean enabled) {
    setNetworkBits(Utilities.setBits(this.networkBits, enabled, CaesarConstants.Network.Bits.API));
  }

  public boolean isFtp() {
    return Utilities.areBitsSet(getNetworkBits(), CaesarConstants.Network.Bits.FTP);
  }

  public void setFtp(boolean enabled) {
    setNetworkBits(Utilities.setBits(this.networkBits, enabled, CaesarConstants.Network.Bits.FTP));
  }

  public boolean isSnmp() {
    return Utilities.areBitsSet(getNetworkBits(), CaesarConstants.Network.Bits.SNMP);
  }

  public void setSnmp(boolean enabled) {
    setNetworkBits(Utilities.setBits(this.networkBits, enabled, CaesarConstants.Network.Bits.SNMP));
  }

  public boolean isSyslog() {
    return Utilities.areBitsSet(getNetworkBits(), CaesarConstants.Network.Bits.SYSLOG);
  }

  public void setSyslog(boolean enabled) {
    setNetworkBits(
        Utilities.setBits(this.networkBits, enabled, CaesarConstants.Network.Bits.SYSLOG));
  }

  public boolean isSntp() {
    return Utilities.areBitsSet(getNetworkBits(), CaesarConstants.Network.Bits.SNTP);
  }

  public void setSntp(boolean enabled) {
    setNetworkBits(Utilities.setBits(this.networkBits, enabled, CaesarConstants.Network.Bits.SNTP));
  }

  public boolean isLdap() {
    return Utilities.areBitsSet(getNetworkBits(), CaesarConstants.Network.Bits.LDAP);
  }

  public void setLdap(boolean enabled) {
    setNetworkBits(Utilities.setBits(this.networkBits, enabled, CaesarConstants.Network.Bits.LDAP));
  }

  public byte[] getGateway() {
    return Arrays.copyOf(this.gateway, this.gateway.length);
  }

  /**
   * .
   */
  public void setGateway(byte[] gateway) {
    byte[] oldValue = this.gateway;
    this.gateway = Arrays.copyOf(gateway, gateway.length);
    firePropertyChange(NetworkData.PROPERTY_GATEWAY, oldValue, this.gateway);
  }

  public byte[] getNetmask() {
    return Arrays.copyOf(this.netmask, this.netmask.length);
  }

  /**
   * .
   */
  public void setNetmask(byte[] netmask) {
    byte[] oldValue = this.netmask;
    this.netmask = Arrays.copyOf(netmask, netmask.length);
    firePropertyChange(NetworkData.PROPERTY_NETMASK, oldValue, this.netmask);
  }

  public String getMacAddress() {
    return this.macaddress;
  }

  /**
   * .
   */
  public void setMacAddress(String macaddress) {
    String oldValue = this.macaddress;
    this.macaddress = macaddress;
    firePropertyChange(NetworkData.PROPERTY_MACADDRESS, oldValue, this.macaddress);
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (NetworkData.PROPERTY_NETWORK_BITS_DHCP.equals(propertyName)) {
      setDhcp((Boolean) value);
    } else if (NetworkData.PROPERTY_NETWORK_BITS_API.equals(propertyName)) {
      setApi((Boolean) value);
    } else if (NetworkData.PROPERTY_NETWORK_BITS_FTP.equals(propertyName)) {
      setFtp((Boolean) value);
    } else if (NetworkData.PROPERTY_NETWORK_BITS_SNMP.equals(propertyName)) {
      setSnmp((Boolean) value);
    } else if (NetworkData.PROPERTY_NETWORK_BITS_SNTP.equals(propertyName)) {
      setSntp((Boolean) value);
    } else if (NetworkData.PROPERTY_NETWORK_BITS_SYSLOG.equals(propertyName)) {
      setSyslog((Boolean) value);
    } else if (NetworkData.PROPERTY_NETWORK_BITS_LDAP.equals(propertyName)) {
      setLdap((Boolean) value);
    } else if (NetworkData.PROPERTY_ADDRESS.equals(propertyName)) {
      setAddress((byte[]) value);
    } else if (NetworkData.PROPERTY_NETMASK.equals(propertyName)) {
      setNetmask((byte[]) value);
    } else if (NetworkData.PROPERTY_GATEWAY.equals(propertyName)) {
      setGateway((byte[]) value);
    } else if (NetworkData.PROPERTY_MACADDRESS.equals(propertyName)) {
      setMacAddress((String) value);
    }
  }

  /**
   * .
   */
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    cfgWriter.writeInteger(getNetworkBits());
    for (int idx = this.address.length; idx > 0; idx--) {
      cfgWriter.write4Byte(this.address[idx - 1]);
    }
    for (int idx = this.netmask.length; idx > 0; idx--) {
      cfgWriter.write4Byte(this.netmask[idx - 1]);
    }
    for (int idx = this.gateway.length; idx > 0; idx--) {
      cfgWriter.write4Byte(this.gateway[idx - 1]);
    }
  }

  /**
   * .
   */
  public void readData(CfgReader cfgReader) throws ConfigException {
    int networkBits = cfgReader.readInteger();
    setNetworkBits(networkBits);
    byte[] newAddress = new byte[this.address.length];
    for (int idx = newAddress.length; idx > 0; idx--) {
      newAddress[idx - 1] = cfgReader.read4ByteValue();
    }
    setAddress(newAddress);
    byte[] newNetmask = new byte[this.netmask.length];
    for (int idx = newNetmask.length; idx > 0; idx--) {
      newNetmask[idx - 1] = cfgReader.read4ByteValue();
    }
    setNetmask(newNetmask);
    byte[] newGateway = new byte[this.gateway.length];
    for (int idx = newGateway.length; idx > 0; idx--) {
      newGateway[idx - 1] = cfgReader.read4ByteValue();
    }
    setGateway(newGateway);
  }
}

