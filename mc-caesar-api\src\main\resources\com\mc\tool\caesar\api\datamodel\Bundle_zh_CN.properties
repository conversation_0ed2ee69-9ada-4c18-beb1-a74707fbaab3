ConfigData.checkArrays=\u9635\u5217\u5927\u5C0F\u65E0\u6548{0},\u5F00\u59CB\u91CD\u65B0\u542F\u52A8
ConfigData.warning.tooNew.message=<html>\r\n<b>\u914D\u7F6E\u6587\u4EF6\u51B2\u7A81\uFF01</b>\r\n<br><br>\r\n<table cellspacing='0' cellpadding='0'>\r\n    <tr>\r\n        <td width='200'>\u5F53\u524D\u6587\u4EF6\u7248\u672C\uFF1A</td>\r\n        <td><b>%s</b></td>\r\n    </tr>\r\n    <tr>\r\n        <td width='200'>\u5F53\u524D\u5DE5\u5177\u652F\u6301\u7684\u7248\u672C:</td>\r\n        <td><b>%s</b></td>\r\n    </tr>\r\n</table>\r\n<br>\u914D\u7F6E\u6587\u4EF6\u662F\u65B0\u7248\u7684\uFF0C\u4F60\u9700\u8981\u4E0B\u8F7D\u4E00\u4E2A\u66F4\u65B0\u7684\u7248\u672C:\r\n<br><br>\r\n<table cellspacing='0' cellpadding='0'>\r\n    <tr><td width='120'>\u6587\u4EF6\u7248\u672C V03.03\uFF1A</td><td>\u5DE5\u5177\u7248\u672C *******\u6216\u8005\u66F4\u65E7</td></tr>\r\n    <tr><td width='120'>\u6587\u4EF6\u7248\u672C V03.04\uFF1A</td><td>\u5DE5\u5177\u7248\u672C 0.0.0.6 </td></tr>\r\n</table>
ConfigData.warning.tooNew.title=\u914D\u7F6E\u6587\u4EF6\u51B2\u7A81
ConfigData.warning.unsupported.message=<html>\r\n<b>\u5F53\u524D\u5DE5\u5177\u4E0D\u652F\u6301\u6B64\u56FA\u4EF6\u7248\u672C.</b>\r\n<br><br>\r\n<table cellspacing='0' cellpadding='0'>\r\n    <tr>\r\n        <td width='200'>\u5F53\u524D\u56FA\u4EF6\u7248\u672C\uFF1A</td>\r\n        <td><b>&nbsp;&nbsp;&nbsp;%s</b></td>\r\n    </tr>\r\n    <tr>\r\n        <td width='240'>\u5F53\u524D\u5DE5\u5177\u652F\u6301\u7684\u7248\u672C\uFF1A</td>\r\n        <td><b>&gt;&nbsp;%s</b></td>\r\n    </tr>\r\n</table>\r\n<br>\u8BF7\u4F7F\u7528\u5DE5\u5177\u7248\u672C*******\u3002
ConfigData.warning.unsupported.title=\u4E0D\u652F\u6301\u7684\u56FA\u4EF6\u7248\u672C\uFF01
ConsoleData.CPU=CPU \u8FDE\u63A5
ConsoleData.CPU.Tooltip=CPU \u8FDE\u63A5
ConsoleData.ConsoleVirtual=\u64CD\u63A7\u53F0\u5206\u914D
ConsoleData.ConsoleVirtual.Tooltip=\u64CD\u63A7\u53F0\u5206\u914D
ConsoleData.ID=ID
ConsoleData.ID.Tooltip=ID
ConsoleData.Name=\u540D\u79F0
ConsoleData.Name.Tooltip=\u540D\u79F0
ConsoleData.RDCPU=\u7EA2\u8272. CPU \u8FDE\u63A5
ConsoleData.RDCPU.Tooltip=\u5197\u4F59 CPU \u8FDE\u63A5
ConsoleData.ScanTime=\u8F6E\u8BE2\u65F6\u95F4[sec]
ConsoleData.ScanTime.Tooltip=\u8F6E\u8BE2\u65F6\u95F4
ConsoleData.Status.AllowLogin=\u5141\u8BB8\u7528\u6237 ACL
ConsoleData.Status.AllowLogin.Tooltip=\u5141\u8BB8\u7528\u6237 ACL
ConsoleData.Status.AllowScan=\u5141\u8BB8CPU\u63A5\u5165\u7AEF\u8F6E\u8BE2
ConsoleData.Status.AllowScan.Tooltip=\u5141\u8BB8CPU\u63A5\u5165\u7AEF\u8F6E\u8BE2
ConsoleData.Status.ForceLogin=\u5F3A\u5236\u767B\u5F55
ConsoleData.Status.ForceLogin.Tooltip=\u5F3A\u5236\u767B\u5F55
ConsoleData.Status.ForceMacro=\u5C55\u793A\u5B8F\u5217\u8868
ConsoleData.Status.ForceMacro.Tooltip=\u5C55\u793A\u5B8F\u5217\u8868
ConsoleData.Status.ForceScan=\u5F3A\u5236CPU\u63A5\u5165\u7AEF\u8F6E\u8BE2
ConsoleData.Status.ForceScan.Tooltip=\u5F3A\u5236CPU\u63A5\u5165\u7AEF\u8F6E\u8BE2
ConsoleData.Status.LOSFrame=LOS\u67B6\u6784
ConsoleData.Status.LOSFrame.Tooltip=\u4E22\u5931\u4FE1\u53F7\u5E27
ConsoleData.Status.OsdDisabled=OSD\u5DF2\u7981\u7528
ConsoleData.Status.OsdDisabled.Tooltip=OSD\u5DF2\u7981\u7528
ConsoleData.Status.PortMode=\u7AEF\u53E3\u6A21\u5F0F
ConsoleData.Status.PortMode.Tooltip=\u64CD\u4F5C\u6A21\u5F0F\u5141\u8BB8\u624B\u52A8\u5207\u6362\u5230\u6BCF\u4E2A\u4E3B\u673A\u7AEF\u53E31-99.
ConsoleData.Status.Redundant=\u5173\u95ED\u5197\u4F59
ConsoleData.Status.Redundant.Tooltip=\u7981\u7528\u5197\u4F59CON\u6269\u5C55\u7AEF
ConsoleData.Status.Virtual=\u865A\u62DF\u8BBE\u5907
ConsoleData.Status.Virtual.Tooltip=\u865A\u62DF\u8BBE\u5907
ControlGroupData.Arrangement=\u6392\u5217
ControlGroupData.Arrangement.Tooltip=\u4E00\u884C\u56DB\u4E2A\u663E\u793A\u5668\u6216\u4E24\u884C\u6BCF\u884C\u4E24\u4E2A\u663E\u793A\u5668
ControlGroupData.Control1=\u63A7\u5236
ControlGroupData.Control1.Tooltip=\u63A7\u5236
ControlGroupData.Control2=\u63A7\u5236
ControlGroupData.Control2.Tooltip=\u63A7\u5236
ControlGroupData.Control3=\u63A7\u5236
ControlGroupData.Control3.Tooltip=\u63A7\u5236
ControlGroupData.Control4=\u63A7\u5236
ControlGroupData.Control4.Tooltip=\u63A7\u5236
ControlGroupData.ControlCon=\u63A7\u5236 CON
ControlGroupData.ControlCon.Tooltip=\u5F53\u524D\u63A7\u5236 CON
ControlGroupData.Enabled1=\u6FC0\u6D3B
ControlGroupData.Enabled1.Tooltip=\u6FC0\u6D3B
ControlGroupData.Enabled2=\u6FC0\u6D3B
ControlGroupData.Enabled2.Tooltip=\u6FC0\u6D3B
ControlGroupData.Enabled3=\u6FC0\u6D3B
ControlGroupData.Enabled3.Tooltip=\u6FC0\u6D3B
ControlGroupData.Enabled4=\u6FC0\u6D3B
ControlGroupData.Enabled4.Tooltip=\u6FC0\u6D3B
ControlGroupData.Frame1=\u63D0\u793A\u6846[\u79D2]
ControlGroupData.Frame1.Tooltip=\u63D0\u793A\u6846
ControlGroupData.Frame2=\u63D0\u793A\u6846[\u79D2]
ControlGroupData.Frame2.Tooltip=\u63D0\u793A\u6846
ControlGroupData.Frame3=\u63D0\u793A\u6846[\u79D2]
ControlGroupData.Frame3.Tooltip=\u63D0\u793A\u6846
ControlGroupData.Frame4=\u63D0\u793A\u6846[\u79D2]
ControlGroupData.Frame4.Tooltip=\u63D0\u793A\u6846
ControlGroupData.Manual=\u624B\u52A8
ControlGroupData.Manual.Tooltip=\u5207\u6362\u5230\u4F7F\u7528\u70ED\u952E\u7684\u624B\u52A8\u5207\u6362.<br>\u7981\u7528\u9F20\u6807\u81EA\u52A8\u6F2B\u6E38\u5207\u6362\u591A\u5C4F\u62D3\u5C55\u5668
ControlGroupData.Name1=\u540D\u79F0
ControlGroupData.Name1.Tooltip=\u540D\u79F0
ControlGroupData.Name2=\u540D\u79F0
ControlGroupData.Name2.Tooltip=\u540D\u79F0
ControlGroupData.Name3=\u540D\u79F0
ControlGroupData.Name3.Tooltip=\u540D\u79F0
ControlGroupData.Name4=\u540D\u79F0
ControlGroupData.Name4.Tooltip=\u540D\u79F0
ControlGroupData.Owner1=\u6240\u6709\u8005
ControlGroupData.Owner1.Tooltip=\u6240\u6709\u8005
ControlGroupData.Owner2=\u6240\u6709\u8005
ControlGroupData.Owner2.Tooltip=\u6240\u6709\u8005
ControlGroupData.Owner3=\u6240\u6709\u8005
ControlGroupData.Owner3.Tooltip=\u6240\u6709\u8005
ControlGroupData.Owner4=\u6240\u6709\u8005
ControlGroupData.Owner4.Tooltip=\u6240\u6709\u8005
CpuData.Console=CON\u8FDE\u63A5
CpuData.Console.Tooltip=CON\u8FDE\u63A5
CpuData.ID=ID
CpuData.ID.Tooltip=ID
CpuData.Name=\u540D\u79F0
CpuData.Name.Tooltip=\u540D\u79F0
CpuData.RealCpu=CPU \u5206\u914D
CpuData.RealCpu.Tooltip=CPU \u5206\u914D
CpuData.Status.AllowPrivate=\u5141\u8BB8\u79C1\u6709\u6A21\u5F0F
CpuData.Status.AllowPrivate.Tooltip=\u5141\u8BB8\u79C1\u6709\u6A21\u5F0F
CpuData.Status.FixFrame=Fix\u67B6\u6784
CpuData.Status.FixFrame.Tooltip=Fix\u67B6\u6784
CpuData.Status.ForcePrivate=\u5F3A\u5236\u79C1\u6709\u6A21\u5F0F
CpuData.Status.ForcePrivate.Tooltip=\u5F3A\u5236\u79C1\u6709\u6A21\u5F0F
CpuData.Status.Virtual=\u865A\u62DF\u8BBE\u5907
CpuData.Status.Virtual.Tooltip=\u865A\u62DF\u8BBE\u5907
DisplayData.C_Speed=\u53CC\u51FB\u65F6\u95F4[\u6BEB\u79D2]
DisplayData.C_Speed.Tooltip=\u8C03\u6574\u53CC\u51FB\u7684\u65F6\u95F4\u95F4\u9699
DisplayData.H_Speed=\u9F20\u6807\u6C34\u5E73\u901F\u5EA6[1/x]
DisplayData.H_Speed.Tooltip=\u8C03\u6574\u9F20\u6807\u6C34\u5E73\u901F\u5EA6
DisplayData.Keyboard=\u952E\u76D8\u5E03\u5C40
DisplayData.Keyboard.Tooltip=\u6839\u636E\u952E\u76D8\u8BBE\u7F6EOSD\u952E\u76D8\u5E03\u5C40
DisplayData.V_Speed=\u9F20\u6807\u5782\u76F4\u901F\u5EA6[1/x]
DisplayData.V_Speed.Tooltip=\u8C03\u6574\u9F20\u6807\u5782\u76F4\u901F\u5EA6
DisplayData.VideoMode=\u89C6\u9891\u6A21\u5F0F
DisplayData.VideoMode.Tooltip=\u89C6\u9891\u6A21\u5F0F
EdidData.Audio=Audio \u97F3\u9891
EdidData.Audio.Tooltip=Audio \u97F3\u9891
EdidData.EdidVersion=EDIE\u7248\u672C
EdidData.EdidVersion.Tooltip=EDIE\u7248\u672C
EdidData.HorizontalActivePixel=H.\u6709\u6548\u50CF\u7D20
EdidData.HorizontalActivePixel.Tooltip=\u6C34\u5E73\u6709\u6548\u50CF\u7D20
EdidData.HorizontalFrequency=H.\u9891\u7387[kHz]
EdidData.HorizontalFrequency.Tooltip=H.\u9891\u7387[kHz]
EdidData.ManufacturerID=\u5236\u4F5C\u5546\u540D\u79F0
EdidData.ManufacturerID.Tooltip=\u5236\u4F5C\u5546\u540D\u79F0
EdidData.ManufacturerProductCode=\u4EA7\u54C1\u7F16\u7801
EdidData.ManufacturerProductCode.Tooltip=\u4EA7\u54C1\u7F16\u7801
EdidData.ManufacturerWeek=\u5236\u4F5C\u5546\u5468\u671F
EdidData.ManufacturerWeek.Tooltip=\u5236\u4F5C\u5546\u5468\u671F
EdidData.ManufacturerYear=\u5236\u9020\u5546\u5E74\u4EFD
EdidData.ManufacturerYear.Tooltip=\u5236\u9020\u5546\u5E74\u4EFD
EdidData.PixelClock=\u50CF\u7D20\u65F6\u949F[Mhz]
EdidData.PixelClock.Tooltip=\u50CF\u7D20\u65F6\u949F
EdidData.SerialNumber=\u5E8F\u5217\u53F7
EdidData.SerialNumber.Tooltip=\u5E8F\u5217\u53F7
EdidData.State=EDID\u6821\u9A8C\u548C
EdidData.State.Invalid=\u65E0\u6548
EdidData.State.Reset=\u91CD\u7F6E
EdidData.State.Tooltip=EDID\u6821\u9A8C\u548C
EdidData.State.Valid=\u65E0\u6548
EdidData.VerticalBlankingPixel=V. \u6D3B\u52A8\u7EBF
EdidData.VerticalBlankingPixel.Tooltip=\u5782\u76F4\u6D3B\u52A8\u7EBF
EdidData.VerticalFrequency=V.\u9891\u7387[Hz]
EdidData.VerticalFrequency.Tooltip=V.\u9891\u7387
ExtenderData.Con=CON\u5206\u914D
ExtenderData.Con.Tooltip=CON\u5206\u914D
ExtenderData.Cpu=CPU \u5206\u914D
ExtenderData.Cpu.Tooltip=CPU\u5206\u914D
ExtenderData.CpuCon=CPU/CON\u5206\u914D
ExtenderData.CpuCon.Tooltip=CPU/CON\u5206\u914D
ExtenderData.ID=ID
ExtenderData.ID.Tooltip=ID
ExtenderData.Name=\u540D\u79F0
ExtenderData.Name.Tooltip=\u540D\u79F0
ExtenderData.Port=\u7AEF\u53E3
ExtenderData.Port.Tooltip=\u7AEF\u53E3
ExtenderData.RDPort=\u5197\u4F59\u7AEF\u53E3
ExtenderData.RDPort.Tooltip=\u5197\u4F59\u7AEF\u53E3
ExtenderData.Status.FixPort=\u56FA\u5B9A
ExtenderData.Status.FixPort.Tooltip=\u56FA\u5B9A
ExtenderData.Status.UsbDiskEnable=U\u76D8\u4F7F\u80FD
ExtenderData.Status.UsbDiskEnable.Tooltip=U\u76D8\u4F7F\u80FD
MatrixData.Active=\u6FC0\u6D3B
MatrixData.Device=\u8BBE\u5907
MatrixData.Ports=\u7AEF\u53E3
MultiScreenData.CtrlConId=\u63A7\u5236 CON
MultiScreenData.CtrlConId.Tooltip=\u63A7\u5236 CON
MultiScreenData.Frame=\u63D0\u793A\u6846
MultiScreenData.Frame.Tooltip=\u63D0\u793A\u6846\u65F6\u95F4
MultiScreenData.HorizontalNumber=\u5217
MultiScreenData.HorizontalNumber.Tooltip=\u5217
MultiScreenData.Name=\u540D\u79F0
MultiScreenData.Name.Tooltip=\u591A\u5C4F\u63A7\u5236\u7EC4\u7684\u540D\u79F0
MultiScreenData.Type=\u6A21\u5F0F
MultiScreenData.Type.Tooltip=\u8DE8\u5C4F\u7C7B\u578B 0\uFF1A\u9F20\u6807\u8DE8\u5C4F  1\uFF1A\u624B\u52A8\u8DE8\u5C4F
MultiScreenData.VerticalNumber=\u884C
MultiScreenData.VerticalNumber.Tooltip=\u884C
OsdData.Col=\u6C34\u5E73\u4F4D\u7F6E [10 px]
OsdData.Col.Tooltip=OSD\u6C34\u5E73\u4F4D\u7F6E. \u8303\u56F4: -128 ... +127
OsdData.OsdPosition=OSD \u4F4D\u7F6E\u9884\u8BBE
OsdData.OsdPosition.Tooltip=OSD\u4F4D\u7F6E\u9884\u8BBE
OsdData.Preview=\u9884\u8BBE
OsdData.Preview.Tooltip=\u9884\u8BBE
OsdData.Row=\u5782\u76F4\u4F4D\u7F6E [10 px]
OsdData.Row.Tooltip=OSD\u5782\u76F4\u4F4D\u7F6E. \u8303\u56F4: -128 ... +127
OsdData.Status.Active=\u542F\u7528\u8FDE\u63A5\u4FE1\u606F\u5C55\u793A
OsdData.Status.Active.Tooltip=\u542F\u7528\u8FDE\u63A5\u4FE1\u606F\u663E\u793A\u5F53\u524D\u8FDE\u63A5\u72B6\u6001
OsdData.Status.Select=\u542F\u7528mini OSD\u83DC\u5355
OsdData.Status.Select.Tooltip=<html>\u5F53\u6267\u884C\u5BC6\u94A5\u5E8F\u5217\u6253\u5F00OSD\u65F6,<br>\u7528\u4E8E\u5207\u6362\u7684CPU\u63A5\u5165\u7AEF\u5C06\u7ED3\u679C\u663E\u793A\u5728CON\u7BA1\u63A7\u7AEFOSD\u7684\u9009\u62E9\u5217\u8868\u3002
OsdData.Status.Update=\u542F\u7528\u8FDE\u63A5\u4FE1\u606F\u66F4\u65B0
OsdData.Status.Update.Tooltip=\u8FDE\u63A5\u5347\u7EA7\u5F53\u6DE1\u5165CON\u7BA1\u63A7\u7AEF\u7684OSD
OsdData.Timeout=\u5C55\u793A\u65F6\u95F4[sec]
OsdData.Timeout.Tooltip=OSD\u6E10\u53D8\u7684\u6301\u7EED\u65F6\u95F4\uFF080 =\u65E0\u9650\u5236\uFF09
SystemConfigData.AccessData.ForceBits.ConAccess=\u5141\u8BB8\u65B0\u7684CON\u7BA1\u63A7\u7AEF
SystemConfigData.AccessData.ForceBits.ConAccess.Tooltip=\u5F00\u542F\u65B0\u5EFA\u7684CON\u5BF9\u6240\u6709CPU\u7684\u6743\u9650
SystemConfigData.AccessData.ForceBits.ConLock=\u5141\u8BB8\u64CD\u63A7\u53F0ACL
SystemConfigData.AccessData.ForceBits.ConLock.Tooltip=\u5F00\u542FCON\u5BF9\u4ED6\u6240\u6709\u6709\u8BBF\u95EE\u6743\u7684CPU\u5217\u8868\u7684\u6743\u9650
SystemConfigData.AccessData.ForceBits.CpuDisconnct=\u81EA\u52A8\u65AD\u5F00
SystemConfigData.AccessData.ForceBits.CpuDisconnct.Tooltip=\u4ECE\u5F53\u524D\u7684CPU\u63A5\u5165\u7AEF\u6253\u5F00OSD\u4ECE\u800C\u65AD\u5F00\u64CD\u63A7\u53F0
SystemConfigData.AccessData.ForceBits.Login=\u5F3A\u5236\u7528\u6237\u767B\u5F55
SystemConfigData.AccessData.ForceBits.Login.Tooltip=\u8981\u6C42\u7528\u6237\u767B\u5F55\u8FDB\u5165OSD
SystemConfigData.AccessData.ForceBits.UserAccess=\u5141\u8BB8\u65B0\u7528\u6237
SystemConfigData.AccessData.ForceBits.UserAccess.Tooltip=\u5F00\u542F\u65B0\u5EFA\u7684\u7528\u6237\u5BF9\u6240\u6709CPU\u7684\u6743\u9650
SystemConfigData.AccessData.ForceBits.UserConAnd=AND \u7528\u6237/CON \u8BBF\u95EE\u63A7\u5236\u5217\u8868
SystemConfigData.AccessData.ForceBits.UserConAnd.Tooltip=AND \u7528\u6237\u548C CON \u8BBF\u95EE\u63A7\u5236\u5217\u8868 (\u51CF\u5C11\u8BBF\u95EE)
SystemConfigData.AccessData.ForceBits.UserConOr=OR\u7528\u6237/CON \u8BBF\u95EE\u63A7\u5236\u5217\u8868
SystemConfigData.AccessData.ForceBits.UserConOr.Tooltip=AND \u7528\u6237 \u548C CON \u8BBF\u95EE\u63A7\u5236\u5217\u8868 (\u589E\u52A0\u8BBF\u95EE)
SystemConfigData.AccessData.ForceBits.UserLock=\u5141\u8BB8\u7528\u6237ACL
SystemConfigData.AccessData.ForceBits.UserLock.Tooltip=\u5F00\u542F\u7528\u6237\u5BF9\u4ED6\u6240\u6709\u6709\u63A7\u5236\u6743\u7684CPU\u5217\u8868\u7684\u6743\u9650
SystemConfigData.AccessData.TimeoutDisplay=OSD\u8D85\u65F6[\u79D2]
SystemConfigData.AccessData.TimeoutDisplay.Tooltip=\u6307\u5B9A\u4E0D\u6D3B\u52A8\u65F6\u81EA\u52A8\u9000\u51FAOSD(0 = \u672A\u6FC0\u6D3B\u7684)
SystemConfigData.AccessData.TimeoutLogout=\u81EA\u52A8\u9000\u51FA[\u5206\u949F]
SystemConfigData.AccessData.TimeoutLogout.Tooltip=\u6307\u5B9A\u4E0D\u6D3B\u52A8\u65F6\u7528\u6237\u81EA\u52A8\u9000\u51FA(0= \u672A\u6FC0\u6D3B\u7684 -1=\u4E0D\u53D7\u9650\u5236\u7684)
SystemConfigData.AutoIdData.AutoId.RealConId=\u771F\u5B9E\u7684CON\u7BA1\u63A7\u7AEFID
SystemConfigData.AutoIdData.AutoId.RealConId.Tooltip=\u8D77\u59CBID\u4E3A\u771F\u5B9E\u7684CON\u7BA1\u63A7\u7AEF\u81EA\u52A8\u5B9A\u4E49
SystemConfigData.AutoIdData.AutoId.RealCpuId=\u771F\u5B9E\u7684CPU\u63A5\u5165\u7AEFID
SystemConfigData.AutoIdData.AutoId.RealCpuId.Tooltip=\u8D77\u59CBID\u4E3A\u771F\u5B9E\u7684CPU\u63A5\u5165\u7AEF\u81EA\u52A8\u5B9A\u4E49
SystemConfigData.AutoIdData.AutoId.VirtualConId=\u865A\u62DF\u7684CON\u7BA1\u63A7\u7AEFID
SystemConfigData.AutoIdData.AutoId.VirtualConId.Tooltip=\u8D77\u59CBID\u4E3A\u865A\u62DF\u7684CON\u7BA1\u63A7\u7AEF\u81EA\u52A8\u5B9A\u4E49
SystemConfigData.AutoIdData.AutoId.VirtualCpuId=\u865A\u62DF\u7684CPU\u63A5\u5165\u7AEFID
SystemConfigData.AutoIdData.AutoId.VirtualCpuId.Tooltip=\u8D77\u59CBID\u4E3A\u865A\u62DF\u7684CPU\u63A5\u5165\u7AEF\u81EA\u52A8\u5B9A\u4E49
SystemConfigData.AutoIdData.ForceBits.AutoConfig=\u5FAA\u5E8F\u81EA\u52A8\u914D\u7F6E
SystemConfigData.AutoIdData.ForceBits.AutoConfig.Tooltip=\u5B9A\u4E49\u65B0\u7684EXT\u5355\u5143\u81F3\u65B0\u7684CPU\u63A5\u5165\u7AEF\u6216CON\u7BA1\u63A7\u7AEF
SystemConfigData.LdapData.Address=LDAP\u670D\u52A1\u5668
SystemConfigData.LdapData.Address.Tooltip=LDAP \u670D\u52A1\u5668
SystemConfigData.LdapData.BaseDN=\u57FA\u672CDN
SystemConfigData.LdapData.BaseDN.Tooltip=\u4F8B\u5B50: ou=\u7528\u6237,dc=\u57DF\u540D,dc=\u7F51\u7EDC
SystemConfigData.LdapData.Port=\u7AEF\u53E3
SystemConfigData.LdapData.Port.Tooltip=\u7AEF\u53E3
SystemConfigData.MatrixGridData.ForceBits.Grid=\u5141\u8BB8\u4E3B\u673A\u7EA7\u8054
SystemConfigData.MatrixGridData.ForceBits.Grid.Tooltip=\u542F\u7528/\u7981\u7528\u77E9\u9635\u7EA7\u8054\u9700\u8981\u91CD\u65B0\u542F\u52A8
SystemConfigData.NetworkData.Address=IP\u5730\u5740
SystemConfigData.NetworkData.Address.Tooltip=IP\u5730\u5740
SystemConfigData.NetworkData.Gateway=\u7F51\u5173
SystemConfigData.NetworkData.Gateway.Tooltip=\u7F51\u5173
SystemConfigData.NetworkData.MacAddress=MAC\u5730\u5740
SystemConfigData.NetworkData.MacAddress.Tooltip=Mac\u5730\u5740
SystemConfigData.NetworkData.Netmask=\u5B50\u7F51\u63A9\u7801
SystemConfigData.NetworkData.Netmask.Tooltip=\u5B50\u7F51\u63A9\u7801
SystemConfigData.NetworkData.NetworkBits.Api=API\u670D\u52A1 
SystemConfigData.NetworkData.NetworkBits.Api.Tooltip=\u5F00\u542FAPI\u670D\u52A1(\u7AEF\u53E3\u53F7:5555)
SystemConfigData.NetworkData.NetworkBits.Dhcp=DHCP
SystemConfigData.NetworkData.NetworkBits.Dhcp.Tooltip=\u901A\u8FC7DHCP\u670D\u52A1\u5668\u81EA\u52A8\u914D\u7F6E\u7F51\u7EDC\u53C2\u6570
SystemConfigData.NetworkData.NetworkBits.Ftp=FTP\u670D\u52A1
SystemConfigData.NetworkData.NetworkBits.Ftp.Tooltip=\u542F\u7528FTP\u670D\u52A1\u4F5C\u4E3A\u914D\u7F6E\u6587\u4EF6\u8F6C\u79FB
SystemConfigData.NetworkData.NetworkBits.Ldap=LDAP
SystemConfigData.NetworkData.NetworkBits.Ldap.Tooltip=\u5141\u8BB8 LDAP
SystemConfigData.NetworkData.NetworkBits.Snmp=SNMP\u4EE3\u7406
SystemConfigData.NetworkData.NetworkBits.Snmp.Tooltip=\u542F\u7528SNMP\u4EE3\u7406GET\u8BF7\u6C42\u548C\u9677\u9631\u3002
SystemConfigData.NetworkData.NetworkBits.Sntp=SNTP
SystemConfigData.NetworkData.NetworkBits.Sntp.Tooltip=\u5141\u8BB8\u7F51\u7EDC\u65F6\u95F4\u670D\u52A1\u540C\u6B65
SystemConfigData.NetworkData.NetworkBits.Syslog=\u7CFB\u7EDF\u65E5\u5FD7
SystemConfigData.NetworkData.NetworkBits.Syslog.Tooltip=\u5141\u8BB8\u72B6\u6001\u62A5\u544A\u7CFB\u7EDF\u65E5\u5FD7\u6D88\u606F
SystemConfigData.NetworkData.SnmpAgentPort=\u7AEF\u53E3
SystemConfigData.NetworkData.SnmpAgentPort.Tooltip=\u7AEF\u53E3
SystemConfigData.SnmpData.Address=SNMP\u670D\u52A1
SystemConfigData.SnmpData.Address.Tooltip=SNMP\u670D\u52A1
SystemConfigData.SnmpData.Port=\u7AEF\u53E3
SystemConfigData.SnmpData.Port.Tooltip=\u7AEF\u53E3
SystemConfigData.SnmpData.SnmpBits.FanTray1=\u98CE\u6247\u69FD1
SystemConfigData.SnmpData.SnmpBits.FanTray1.Tooltip=\u5173\u4E8E\u98CE\u6247\u69FD1\u72B6\u6001\u901A\u77E5
SystemConfigData.SnmpData.SnmpBits.FanTray2=\u98CE\u6247\u69FD2
SystemConfigData.SnmpData.SnmpBits.FanTray2.Tooltip=\u5173\u4E8E\u98CE\u6247\u69FD2\u72B6\u6001\u7684\u901A\u77E5
SystemConfigData.SnmpData.SnmpBits.InsertBoard=\u63D2\u5165I/O\u677F\u5361
SystemConfigData.SnmpData.SnmpBits.InsertBoard.Tooltip=\u5173\u4E8E\u4E00\u4E2A\u65B0\u7684I/O\u677F\u5361\u63D2\u5165\u5230\u63D2\u69FD\u7684\u901A\u77E5
SystemConfigData.SnmpData.SnmpBits.InsertExtender=\u63D2\u5165\u6269\u5C55\u5668
SystemConfigData.SnmpData.SnmpBits.InsertExtender.Tooltip=\u5173\u4E8E\u65B0\u8FDE\u63A5\u6269\u5C55\u5668\u5230\u4E3B\u673A\u7684\u901A\u77E5
SystemConfigData.SnmpData.SnmpBits.InvalidBoard=\u65E0\u6548I/O\u677F\u5361
SystemConfigData.SnmpData.SnmpBits.InvalidBoard.Tooltip=\u5173\u4E8E\u975E\u6B63\u5E38\u5DE5\u4F5C\u7684I/O\u677F\u5361\u7684\u901A\u77E5
SystemConfigData.SnmpData.SnmpBits.PowerSupply1=\u7535\u6E90\u4F9B\u5E94\u56681
SystemConfigData.SnmpData.SnmpBits.PowerSupply1.Tooltip=\u5173\u4E8E\u7535\u6E90\u4F9B\u5E94\u56681\u72B6\u6001\u7684\u901A\u77E5
SystemConfigData.SnmpData.SnmpBits.PowerSupply2=\u7535\u6E90\u4F9B\u5E94\u56682
SystemConfigData.SnmpData.SnmpBits.PowerSupply2.Tooltip=\u5173\u4E8E\u7535\u6E90\u4F9B\u5E94\u56682\u72B6\u6001\u7684\u901A\u77E5
SystemConfigData.SnmpData.SnmpBits.PowerSupply3=\u7535\u6E90\u4F9B\u5E94\u56683
SystemConfigData.SnmpData.SnmpBits.PowerSupply3.Tooltip=\u5173\u4E8E\u7535\u6E90\u4F9B\u5E94\u56683\u72B6\u6001\u7684\u901A\u77E5
SystemConfigData.SnmpData.SnmpBits.RemoveBoard=\u79FB\u9664I/O\u677F\u5361
SystemConfigData.SnmpData.SnmpBits.RemoveBoard.Tooltip=\u5173\u4E8E\u4ECE\u5361\u69FD\u4E0A\u79FB\u9664I/O\u677F\u5361\u7684\u901A\u77E5
SystemConfigData.SnmpData.SnmpBits.RemoveExtener=\u79FB\u9664\u6269\u5C55\u5668
SystemConfigData.SnmpData.SnmpBits.RemoveExtener.Tooltip=\u5173\u4E8E\u4ECE\u4E3B\u673A\u4E0A\u79FB\u9664\u6269\u5C55\u5668\u7684\u901A\u77E5
SystemConfigData.SnmpData.SnmpBits.Status=\u72B6\u6001
SystemConfigData.SnmpData.SnmpBits.Status.Tooltip=\u5173\u4E8E\u4E3B\u673A\u72B6\u6001\u7684\u901A\u77E5
SystemConfigData.SnmpData.SnmpBits.SwitchCommand=\u5207\u6362\u547D\u4EE4
SystemConfigData.SnmpData.SnmpBits.SwitchCommand.Tooltip=\u5173\u4E8E\u4E3B\u673A\u8FDB\u884C\u5207\u6362\u64CD\u4F5C\u7684\u901A\u77E5
SystemConfigData.SnmpData.SnmpBits.Temperature=\u6E29\u5EA6
SystemConfigData.SnmpData.SnmpBits.Temperature.Tooltip=\u5173\u4E8E\u4E3B\u673A\u5185\u90E8\u6E29\u5EA6\u7684\u901A\u77E5
SystemConfigData.SnmpData.SnmpBits.Trap=\u5141\u8BB8\u8FC7\u6EE4
SystemConfigData.SnmpData.SnmpBits.Trap.Tooltip=\u5141\u8BB8\u8FC7\u6EE4
SystemConfigData.SntpData.Address=SNTP \u670D\u52A1\u5668
SystemConfigData.SntpData.Address.Tooltip=SNTP\u670D\u52A1\u5668
SystemConfigData.SntpData.RealTimeClock=\u65E5\u671F\u548C\u65F6\u95F4
SystemConfigData.SntpData.RealTimeClock.Tooltip=\u65E5\u671F\u548C\u5B9E\u65F6\u65F6\u949F\u7684\u65F6\u95F4
SystemConfigData.SntpData.TimeZone=\u65F6\u533A
SystemConfigData.SntpData.TimeZone.Tooltip=\u65F6\u533A
SystemConfigData.SwitchData.ForceBits.AutoConnect=CPU\u63A5\u5165\u7AEF\u81EA\u52A8\u8FDE\u63A5
SystemConfigData.SwitchData.ForceBits.AutoConnect.Tooltip=\u5141\u8BB8\u901A\u8FC7\u9F20\u6807\u6216\u952E\u76D8\u8FDE\u63A5\u5230\u4E0B\u4E00\u4E2A\u6709\u6548\u7684CPU\u63A5\u5165\u7AEF
SystemConfigData.SwitchData.ForceBits.ConDisconnect=\u5F3A\u5236\u65AD\u5F00
SystemConfigData.SwitchData.ForceBits.ConDisconnect.Tooltip=\u5F3A\u5236\u6267\u884C\u64CD\u4F5C\u6A21\u5F0F\u8BBF\u95EECPU\u63A5\u5165\u7AEF\uFF0C\u5176\u4ED6\u63A7\u5236\u53F0\u65AD\u5F00\u8FDE\u63A5
SystemConfigData.SwitchData.ForceBits.CpuConnect=\u5F3A\u5236\u8FDE\u63A5
SystemConfigData.SwitchData.ForceBits.CpuConnect.Tooltip=\u5F3A\u5236\u6267\u884C\u64CD\u4F5C\u6A21\u5F0F\u8BBF\u95EECPU\u63A5\u5165\u7AEF\uFF0C\u5176\u4ED6\u63A7\u5236\u53F0\u4FDD\u7559\u89C6\u9891\u8FDE\u63A5
SystemConfigData.SwitchData.ForceBits.CpuWatch=\u5141\u8BB8\u89C6\u9891\u5171\u4EAB
SystemConfigData.SwitchData.ForceBits.CpuWatch.Tooltip=\u5141\u8BB8\u5171\u4EAB\u89C6\u9891\u767B\u5F55\u81F3CPU\u63A5\u5165\u7AEF
SystemConfigData.SwitchData.ForceBits.FKeySingle=\u5355\u6B65\u9AA4\u5B8F\u63A7\u5236
SystemConfigData.SwitchData.ForceBits.FKeySingle.Tooltip=\u5355\u6B65\u9AA4\u6267\u884C\u5B8F\u63A7\u5236
SystemConfigData.SwitchData.ForceBits.KeyboardConnect=\u952E\u76D8\u9F20\u6807\u8FDE\u63A5
SystemConfigData.SwitchData.ForceBits.KeyboardConnect.Tooltip=\u5141\u8BB8\u901A\u8FC7\u952E\u76D8\u9F20\u6807\u6FC0\u6D3BCPU\u63A5\u5165\u7AEF\u63A7\u5236\u8981\u6C42
SystemConfigData.SwitchData.ForceBits.MouseConnect=\u9F20\u6807\u8FDE\u63A5
SystemConfigData.SwitchData.ForceBits.MouseConnect.Tooltip=\u5141\u8BB8\u901A\u8FC7\u9F20\u6807\u6FC0\u6D3BCPU\u63A5\u5165\u7AEF\u63A7\u5236\u8981\u6C42
SystemConfigData.SwitchData.TimeoutDisconnect=CPU\u63A5\u5165\u7AEF\u8D85\u65F6[\u5206\u949F]
SystemConfigData.SwitchData.TimeoutDisconnect.Tooltip=\u6307\u5B9A\u4E0D\u6D3B\u52A8\u671F\u95F4\u5728\u5F53\u524DCPU\u540E,CPU\u5C06\u81EA\u52A8\u65AD\u5F00\u8FDE\u63A5(0 =\u672A\u6FC0\u6D3B\u7684)
SystemConfigData.SwitchData.TimeoutShare=\u91CA\u653E\u65F6\u95F4[\u79D2]
SystemConfigData.SwitchData.TimeoutShare.Tooltip=\u6307\u5B9A\u4E0D\u6D3B\u52A8\u671F\u95F4\u4ECE\u53E6\u4E00\u4E2A\u63A7\u5236\u53F0\u63A5\u53D7CPU\u63A7\u5236\u7684\u8981\u6C42
SystemConfigData.SyslogData.Address=\u4FE1\u606F\u65E5\u5FD7\u670D\u52A1
SystemConfigData.SyslogData.Address.Tooltip=\u4FE1\u606F\u65E5\u5FD7\u670D\u52A1
SystemConfigData.SyslogData.Facility=\u8BBE\u5907
SystemConfigData.SyslogData.Facility.Tooltip=\u7CFB\u7EDF\u65E5\u5FD7\u8BBE\u5907
SystemConfigData.SyslogData.Port=\u7AEF\u53E3
SystemConfigData.SyslogData.Port.Tooltip=\u7CFB\u7EDF\u65E5\u5FD7\u7AEF\u53E3
SystemConfigData.SyslogData.SysLevel=\u65E5\u5FD7\u7EA7\u522B
SystemConfigData.SyslogData.SysLevel.Debug=\u8C03\u8BD5
SystemConfigData.SyslogData.SysLevel.Debug.Tooltip=\u8C03\u8BD5
SystemConfigData.SyslogData.SysLevel.Error=\u9519\u8BEF
SystemConfigData.SyslogData.SysLevel.Error.Tooltip=\u9519\u8BEF
SystemConfigData.SyslogData.SysLevel.Info=\u4FE1\u606F
SystemConfigData.SyslogData.SysLevel.Info.Tooltip=\u4FE1\u606F
SystemConfigData.SyslogData.SysLevel.Notice=\u901A\u77E5
SystemConfigData.SyslogData.SysLevel.Notice.Tooltip=\u901A\u77E5
SystemConfigData.SyslogData.SysLevel.Tooltip=\u65E5\u5FD7\u7EA7\u522B
SystemConfigData.SyslogData.SysLevel.Warning=\u8B66\u544A
SystemConfigData.SyslogData.SysLevel.Warning.Tooltip=\u8B66\u544A
SystemConfigData.SyslogData.Syslog.Enabled=\u5141\u8BB8\u7CFB\u7EDF\u65E5\u5FD7
SystemConfigData.SyslogData.Syslog.Enabled.Tooltip=\u542F\u7528\u72B6\u6001\u62A5\u544A\u7CFB\u7EDF\u65E5\u5FD7\u6D88\u606F
SystemConfigData.SystemData.Device=\u8BBE\u5907
SystemConfigData.SystemData.Device.Tooltip=\u7F51\u7EDC\u73AF\u5883\u7684\u4E3B\u673A\u540D\u79F0\uFF08\u63A8\u8350\u5B57\u7B26\uFF1Aa-z, A-Z, 0-9, -\uFF09
SystemConfigData.SystemData.ForceBits.AutoSave=\u81EA\u52A8\u4FDD\u5B58
SystemConfigData.SystemData.ForceBits.AutoSave.Tooltip=\u81EA\u52A8\u4FDD\u5B58\u4E3B\u673A\u72B6\u6001
SystemConfigData.SystemData.ForceBits.ComEcho=\u5141\u8BB8COM\u6570\u636E\u56DE\u4F20
SystemConfigData.SystemData.ForceBits.ComEcho.Tooltip=\u901A\u8FC7\u901A\u8BAF\u7AEF\u53E3\u56DE\u4F20\u6240\u6709\u5207\u6362\u547D\u4EE4
SystemConfigData.SystemData.ForceBits.Default=\u52A0\u8F7D\u9ED8\u8BA4
SystemConfigData.SystemData.ForceBits.Default.Tooltip=\u5F53\u6267\u884C\u4E00\u4E2A\u51B7\u542F\u52A8\u6216\u91CD\u542F\u4E3B\u673A,\u603B\u662F\u6FC0\u6D3B\u9ED8\u8BA4\u914D\u7F6E\u5B58\u50A8
SystemConfigData.SystemData.ForceBits.EchoOnly=\u53EA\u56DE\u4F20
SystemConfigData.SystemData.ForceBits.EchoOnly.Tooltip=\u53EA\u540C\u6B65\u4E3B\u673A\u7684\u56DE\u4F20\u4FE1\u606F
SystemConfigData.SystemData.ForceBits.Invalid=\u65E0\u6548\u7684I/O\u677F\u5361
SystemConfigData.SystemData.ForceBits.Invalid.Tooltip=\u64CD\u4F5C\u8FC7\u7A0B\u4E2D\u5FC5\u987B\u5173\u95ED\uFF0C\u4EC5\u5141\u8BB8\u5728\u4E3B\u673A\u5347\u7EA7
SystemConfigData.SystemData.ForceBits.LanEcho=\u5141\u8BB8LAN\u53E3\u6570\u636E\u56DE\u4F20
SystemConfigData.SystemData.ForceBits.LanEcho.Tooltip=\u901A\u8FC7\u7F51\u7EDC\u7AEF\u53E3\u56DE\u4F20\u6240\u6709\u5207\u6362\u547D\u4EE4
SystemConfigData.SystemData.ForceBits.OldEcho=\u5141\u8BB8\u65E7\u683C\u5F0F\u56DE\u4F20
SystemConfigData.SystemData.ForceBits.OldEcho.Tooltip=\u56DE\u4F20\u65E7\u683C\u5F0F\u7684\u5185\u90E8\u5207\u6362\u547D\u4EE4
SystemConfigData.SystemData.ForceBits.OnlineConfig=\u5728\u7EBF\u914D\u7F6E
SystemConfigData.SystemData.ForceBits.OnlineConfig.Tooltip=\u5728\u7BA1\u7406\u8F6F\u4EF6\u6FC0\u6D3B\u5728\u7EBF\u914D\u7F6E
SystemConfigData.SystemData.ForceBits.Redundancy=\u5141\u8BB8\u5197\u4F59
SystemConfigData.SystemData.ForceBits.Redundancy.Tooltip=\u542F\u7528\u5197\u4F59\u6269\u5C55\u5668\u81EA\u52A8\u5207\u6362
SystemConfigData.SystemData.ForceBits.RemoveSlave=\u79FB\u9664I/O\u677F\u5361 
SystemConfigData.SystemData.ForceBits.RemoveSlave.Tooltip=\u5F53\u4E22\u5931\u7B2C\u4E8C\u5F20CPU\u63A7\u5236\u677F\u5361\uFF08576\uFF09\uFF0C\u79FB\u9664 I/O \u677F\u5361
SystemConfigData.SystemData.ForceBits.Slave=\u5B50\u4E3B\u673A
SystemConfigData.SystemData.ForceBits.Slave.Tooltip=\u5141\u8BB8\u5728\u7EA7\u8054\u73AF\u5883\u4E0B\u70ED\u952E\u63A7\u5236
SystemConfigData.SystemData.ForceBits.Synchronize=\u540C\u6B65
SystemConfigData.SystemData.ForceBits.Synchronize.Tooltip=\u4E3B\u673A\u4E0E\u4E3B\u7528\u4E3B\u673A\u540C\u6B65
SystemConfigData.SystemData.ForceBits.UartEnable=\u4F7F\u80FD\u7B2C\u4E09\u65B9\u4E32\u53E3
SystemConfigData.SystemData.ForceBits.UartEnable.Tooltip=\u4F7F\u80FD\u7B2C\u4E09\u65B9\u4E32\u53E3
SystemConfigData.SystemData.Info=\u4FE1\u606F
SystemConfigData.SystemData.Info.Tooltip=\u5F53\u524D\u4E3B\u673A\u914D\u7F6E\u7684\u63CF\u8FF0
SystemConfigData.SystemData.MasterIP=\u4E3B\u7528\u4E3B\u673AIP\u5730\u5740
SystemConfigData.SystemData.MasterIP.Tooltip=\u8BBE\u7F6E\u4E3B\u7528\u4E3B\u673A\u7684\u7F51\u7EDC\u5730\u5740
SystemConfigData.SystemData.Name=\u540D\u79F0
SystemConfigData.SystemData.Name.Tooltip=\u5F53\u524D\u4E3B\u673A\u914D\u7F6E\u7684\u540D\u79F0
SystemData.InternalLogLevel=\u65E5\u5FD7\u7EA7\u522B
SystemData.InternalLogLevel.Debug=\u8C03\u8BD5
SystemData.InternalLogLevel.Debug.Tooltip=\u8C03\u8BD5
SystemData.InternalLogLevel.Error=\u9519\u8BEF
SystemData.InternalLogLevel.Error.Tooltip=\u9519\u8BEF
SystemData.InternalLogLevel.Info=\u4FE1\u606F
SystemData.InternalLogLevel.Info.Tooltip=\u4FE1\u606F
SystemData.InternalLogLevel.Notice=\u901A\u77E5
SystemData.InternalLogLevel.Notice.Tooltip=\u901A\u77E5
SystemData.InternalLogLevel.Tooltip=\u65E5\u5FD7\u7EA7\u522B
SystemData.InternalLogLevel.Warning=\u8B66\u544A
SystemData.InternalLogLevel.Warning.Tooltip=\u8B66\u544A
SystemData.Multicast=\u7EC4\u64AD
SystemData.Multicast.Tooltip=\u7EA7\u8054\u7EC4\u64AD\u6216\u8005\u5E7F\u64AD (***************)
UserData.FullName=\u5168\u540D
UserData.FullName.Tooltip=\u540D\u5B57\u59D3\u6C0F
UserData.ID=ID
UserData.ID.Tooltip=ID
UserData.Name=\u540D\u5B57
UserData.Name.Tooltip=\u540D\u5B57
UserData.Password=\u5BC6\u7801
UserData.Password.Tooltip=\u5BC6\u7801
UserData.Rights.ADMIN=\u7BA1\u7406\u5458
UserData.Rights.ADMIN.Tooltip=\u7BA1\u7406\u5458
UserData.Rights.FTP=FTP
UserData.Rights.FTP.Tooltip=FTP
UserData.Rights.LDAP=LDAP \u7528\u6237
UserData.Rights.LDAP.Tooltip=LDAP\u7528\u6237
UserData.Rights.POWER=\u6743\u9650\u7528\u6237
UserData.Rights.POWER.Tooltip=\u6743\u9650\u7528\u6237
UserData.Rights.SUPER=\u8D85\u7EA7\u7528\u6237
UserData.Rights.SUPER.Tooltip=\u8D85\u7EA7\u7528\u6237
UserGroupData.ID=ID
UserGroupData.ID.Tooltip=ID
UserGroupData.Name=\u540D\u5B57
UserGroupData.Name.Tooltip=\u540D\u5B57

