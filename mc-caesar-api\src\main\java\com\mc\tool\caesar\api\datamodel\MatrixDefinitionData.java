package com.mc.tool.caesar.api.datamodel;

import com.mc.tool.caesar.api.CaesarConstants;

/**
 * .
 */
public class MatrixDefinitionData {

  private int id;
  private String device;
  private String address;
  private CaesarConstants.CpuType cpuType;
  private int ports;
  private int firstModule;
  private int lastModule;
  private final int firstPort;
  private final int lastPort;
  private final boolean isGridEnabled;

  /**
   * .
   */
  public MatrixDefinitionData(int id, String device, String address, int ports, int firstModule,
      int lastModule, int firstPort, int lastPort, boolean isGridEnabled) {
    this.id = id;
    this.device = device;
    this.address = address;
    this.ports = ports;
    this.cpuType = CaesarConstants.CpuType.DEFAULT;
    this.firstModule = firstModule;
    this.lastModule = lastModule;
    this.firstPort = firstPort;
    this.lastPort = lastPort;
    this.isGridEnabled = isGridEnabled;
  }

  public int getId() {
    return this.id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public String getDevice() {
    return this.device;
  }

  public void setDevice(String device) {
    this.device = device;
  }

  public String getAddress() {
    return this.address;
  }

  public void setAddress(String address) {
    this.address = address;
  }

  public CaesarConstants.CpuType getCpuType() {
    return this.cpuType;
  }

  public void setCpuType(CaesarConstants.CpuType cpuType) {
    this.cpuType = cpuType;
  }

  public int getPorts() {
    return this.ports;
  }

  public void setPorts(int ports) {
    this.ports = ports;
  }

  public int getFirstModule() {
    return this.firstModule;
  }

  public void setFirstModule(int firstModule) {
    this.firstModule = firstModule;
  }

  public int getLastModule() {
    return this.lastModule;
  }

  public void setLastModule(int lastModule) {
    this.lastModule = lastModule;
  }

  public int getFirstPort() {
    return firstPort;
  }

  public int getLastPort() {
    return lastPort;
  }

  public boolean isGridEnabled() {
    return isGridEnabled;
  }
}

