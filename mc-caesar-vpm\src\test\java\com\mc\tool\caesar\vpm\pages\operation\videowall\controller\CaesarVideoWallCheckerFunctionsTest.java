package com.mc.tool.caesar.vpm.pages.operation.videowall.controller;

import com.mc.tool.caesar.vpm.pages.operation.videowall.vp.CaesarVpMatrix;
import com.mc.tool.caesar.vpm.util.VideoWallGenerator;
import com.mc.tool.caesar.vpm.util.VideoWallGenerator.StatusConfig;
import com.mc.tool.framework.operation.videowall.controller.VideoWallChecker;
import com.mc.tool.framework.operation.videowall.controller.VideoWallChecker.VideoWallError;
import java.util.Collection;
import javafx.beans.property.SimpleObjectProperty;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * .
 */
public class CaesarVideoWallCheckerFunctionsTest {

  @Before
  public void before() {
    System.setProperty("no-fx-mode", "true");
    System.setProperty("generator-mode", "true");
  }

  protected Collection<VideoWallError> getWallErrors(String configPath) {
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    VideoWallGenerator videoWallGenerator =
        VideoWallGenerator.createVideoWallGeneratorFromResource(classLoader, configPath, VideoWallGenerator.VideoWallType.VP6);

    VideoWallChecker checker = new VideoWallChecker();
    CaesarVideoWallCheckerFunctions
        .registerFunctions(
            new SimpleObjectProperty<>(new CaesarVpMatrix(videoWallGenerator.getController())),
            checker);
    return checker.check(videoWallGenerator.getVideoWallData());
  }

  protected Collection<VideoWallError> getWallErrors(StatusConfig statusConfig, String configPath) {
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    VideoWallGenerator videoWallGenerator =
        VideoWallGenerator.createVideoWallGeneratorFromResource(
            classLoader, configPath, statusConfig);

    VideoWallChecker checker = new VideoWallChecker();
    CaesarVideoWallCheckerFunctions
        .registerFunctions(
            new SimpleObjectProperty<>(new CaesarVpMatrix(videoWallGenerator.getController())),
            checker);
    return checker.check(videoWallGenerator.getVideoWallData());
  }

  @Test
  public void testFull() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/common/full.json";
    Collection<VideoWallError> error = getWallErrors(path);
    Assert.assertEquals(0, error.size());
  }

  @Test
  public void test2kMoreLayer() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/common/2k_more_layer.json";
    Collection<VideoWallError> error = getWallErrors(path);
    Assert.assertEquals(1, error.size());
  }

  @Test
  public void test2kMoreLayerWithBgimg() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/common/2k_more_layer_with_bgimg.json";
    Collection<VideoWallError> error = getWallErrors(path);
    Assert.assertEquals(1, error.size());
  }

  @Test
  public void test2kMoreLayerWithUnRegularBgimg() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/common/2k_more_layer_with_unregular_bgimg.json";
    Collection<VideoWallError> error = getWallErrors(path);
    Assert.assertEquals(8, error.size());
  }

  @Test
  public void test2kMoreWindow() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/common/2k_more_window.json";
    Collection<VideoWallError> error = getWallErrors(path);
    Assert.assertEquals(4, error.size());
  }

  @Test
  public void test2kMoreEmptyWindow() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/common/2k_more_empty_window.json";
    Collection<VideoWallError> error = getWallErrors(path);
    Assert.assertEquals(4, error.size());
  }

  @Test
  public void test2kGlobalMoreLayer() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/common/2k_global_more_layer.json";
    Collection<VideoWallError> error = getWallErrors(path);
    Assert.assertEquals(4, error.size());
  }

  @Test
  public void test4kfull() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/common/4k_full.json";
    Collection<VideoWallError> error = getWallErrors(path);
    Assert.assertEquals(0, error.size());
  }

  @Test
  public void test4kMoreLayer() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/common/4k_more_layer.json";
    Collection<VideoWallError> error = getWallErrors(path);
    Assert.assertEquals(1, error.size());
  }

  @Test
  public void test4kMoreLayerWithBgimg() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/common/4k_more_layer_with_bgimg.json";
    Collection<VideoWallError> error = getWallErrors(path);
    Assert.assertEquals(1, error.size());
  }

  @Test
  public void test4kMoreWindow() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/common/4k_more_window.json";
    Collection<VideoWallError> error = getWallErrors(path);
    Assert.assertEquals(4, error.size());
  }

  @Test
  public void test4kGlobalMoreLayer() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/common/4k_global_more_layer.json";
    Collection<VideoWallError> error = getWallErrors(path);
    Assert.assertEquals(4, error.size());
  }

  @Test
  public void testSingle4kSinglePort() {
    String path = "com/mc/tool/caesar/vpm/videowall/unit/common/4k_full.json";
    StatusConfig statusConfig = new StatusConfig();
    statusConfig.vp6PortSize = 1;
    Collection<VideoWallError> error = getWallErrors(statusConfig, path);
    Assert.assertEquals(0, error.size());
  }
}
