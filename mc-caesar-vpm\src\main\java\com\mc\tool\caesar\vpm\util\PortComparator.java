package com.mc.tool.caesar.vpm.util;

import com.mc.tool.caesar.api.datamodel.ExtenderData;
import java.io.Serializable;
import java.util.Comparator;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class PortComparator implements Comparator<String>, Serializable {
  private static final long serialVersionUID = 6106269076155338055L;

  @Override
  public int compare(String o1, String o2) {
    Pair<Integer, Integer> first = new Pair<>(0, 0);
    Pair<Integer, Integer> second = new Pair<>(0, 0);
    try {
      first = ExtenderData.parsePortProperty(o1);
      second = ExtenderData.parsePortProperty(o2);
    } catch (Exception exc) {
      log.debug("Error input!", o1);
    }
    if (first.getKey() < second.getKey()) {
      return -1;
    } else if (first.getKey() > second.getKey()) {
      return 1;
    } else {
      return first.getValue().compareTo(second.getValue());
    }
  }
}
