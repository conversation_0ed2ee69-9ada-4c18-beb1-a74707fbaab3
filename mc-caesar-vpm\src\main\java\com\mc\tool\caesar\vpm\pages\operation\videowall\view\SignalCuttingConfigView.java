package com.mc.tool.caesar.vpm.pages.operation.videowall.view;

import java.io.IOException;
import java.util.ResourceBundle;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 *
 * <AUTHOR>
 */

@Slf4j
public class SignalCuttingConfigView {
  private Node root = null;

  private SignalCuttingConfigViewController presenter;

  /**
   * config view.
   *
   * @param resWidth  分辨率宽度
   * @param resHeight 分辨率高度
   * @param left      初始左裁剪
   * @param top       初始上裁剪
   * @param right     初始右裁剪
   * @param bottom    初始下裁剪
   */
  public SignalCuttingConfigView(String name, int resWidth, int resHeight, int left, int top, int right,
                                 int bottom) {
    FXMLLoader loader =
        new FXMLLoader(
            Thread.currentThread()
                .getContextClassLoader()
                .getResource(
                    "com/mc/tool/caesar/vpm/pages/operation/videowall/view/"
                        + "signalcuttingconfig.fxml"));
    presenter =
        new SignalCuttingConfigViewController(name, resWidth, resHeight, left, top, right, bottom);
    loader.setController(presenter);
    loader.setResources(
        ResourceBundle.getBundle("com/mc/tool/caesar/vpm/pages/operation/videowall/view/Bundle"));
    try {
      root = loader.load();
    } catch (IOException ex) {
      log.warn("Fail to load list_view.fxml!", ex);
    }
  }

  Node getView() {
    return root;
  }

  SignalCuttingConfigViewController getPresenter() {
    return presenter;
  }


}
