package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import lombok.Setter;

/**
 * DecodeCardUpdateInfo.
 */
@Setter
@JsonPropertyOrder({
    DecodeCardUpdateInfo.JSON_PROPERTY_INPUT_ID,
    DecodeCardUpdateInfo.JSON_PROPERTY_SLOT_ID,
    DecodeCardUpdateInfo.JSON_PROPERTY_SN
})
public class DecodeCardUpdateInfo {
  public static final String JSON_PROPERTY_INPUT_ID = "inputId";
  private Integer inputId;

  public static final String JSON_PROPERTY_SLOT_ID = "slotId";
  private Integer slotId;

  public static final String JSON_PROPERTY_SN = "sn";
  private String sn;

  /**
   * inputId.
   */
  public DecodeCardUpdateInfo inputId(Integer inputId) {
    this.inputId = inputId;
    return this;
  }

  /**
   * Get inputId.
   **/
  @JsonProperty(JSON_PROPERTY_INPUT_ID)
  @JsonInclude()
  public Integer getInputId() {
    return inputId;
  }

  /**
   * slotId.
   */
  public DecodeCardUpdateInfo slotId(Integer slotId) {
    this.slotId = slotId;
    return this;
  }

  /**
   * Get slotId.
   **/
  @JsonProperty(JSON_PROPERTY_SLOT_ID)
  @JsonInclude()
  public Integer getSlotId() {
    return slotId;
  }

  /**
   * sn.
   */
  public DecodeCardUpdateInfo sn(String sn) {
    this.sn = sn;
    return this;
  }

  /**
   * Get sn.
   **/
  @JsonProperty(JSON_PROPERTY_SN)
  @JsonInclude()
  public String getSn() {
    return sn;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DecodeCardUpdateInfo decodeCardUpdateInfo = (DecodeCardUpdateInfo) o;
    return Objects.equals(this.inputId, decodeCardUpdateInfo.inputId)
        && Objects.equals(this.slotId, decodeCardUpdateInfo.slotId)
        && Objects.equals(this.sn, decodeCardUpdateInfo.sn);
  }

  @Override
  public int hashCode() {
    return Objects.hash(inputId, slotId, sn);
  }


  @Override
  public String toString() {
    return "DecodeCardUpdateInfo {\n"
        + "    inputId: " + toIndentedString(inputId) + "\n"
        + "    slotId: " + toIndentedString(slotId) + "\n"
        + "    sn: " + toIndentedString(sn) + "\n" + "}";
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
