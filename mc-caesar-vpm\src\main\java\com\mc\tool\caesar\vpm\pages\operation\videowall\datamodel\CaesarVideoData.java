package com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel;

import static com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoData.SourceType.GB28181;

import com.google.common.base.Strings;
import com.mc.tool.framework.operation.videowall.datamodel.VideoData;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.LongProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.ReadOnlyStringProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleLongProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.beans.value.ChangeListener;
import lombok.Getter;

/**
 * .
 */
public class CaesarVideoData extends VideoData {
  /**
   * 视频源类型枚举.
   */
  public enum SourceType {
    NORMAL(0),
    GB28181(1);

    private int value;

    public int getValue() {
      return value;
    }

    SourceType(int value) {
      this.value = value;
    }

    /**
     * 从数值获取类型枚举.
     */
    public static SourceType fromValue(int value) {
      for (SourceType type : values()) {
        if (type.value == value) {
          return type;
        }
      }
      return NORMAL;
    }

  }

  /**
   * GB28181视频源数据.
   */
  public static class Gb28181SourceData {
    /**
     * GB28181视频源状态.
     */
    public enum Status {
      OK(0),
      NO_RESOURCE(1),
      REQUESTING(2),
      DECODE_ERROR(3);

      private int value;

      public int getValue() {
        return value;
      }

      Status(int value) {
        this.value = value;
      }

      /**
       * 从数值获取状态枚举.
       */
      public static Status fromValue(int value) {
        for (Status status : values()) {
          if (status.value == value) {
            return status;
          }
        }
        return NO_RESOURCE;
      }
    }

    @Getter
    private final StringProperty id = new SimpleStringProperty("");
    @Getter
    private final IntegerProperty gridIndex = new SimpleIntegerProperty(0);
    @Getter
    private final ObjectProperty<Status> status = new SimpleObjectProperty<>(Status.NO_RESOURCE);
    @Getter
    private final LongProperty syncVersion = new SimpleLongProperty(0);
  }

  @Getter
  private IntegerProperty sourceIndex = new SimpleIntegerProperty(0);
  @Getter
  private IntegerProperty cropIndex = new SimpleIntegerProperty(0);
  @Getter
  private StringProperty cropName = new SimpleStringProperty("");
  // 用于输出音频的RX ID,0表示无输出
  @Getter
  private IntegerProperty audioRx = new SimpleIntegerProperty(0);
  @Getter
  private IntegerProperty audioSeq = new SimpleIntegerProperty(0);
  @Getter
  private ObjectProperty<SourceType> sourceType = new SimpleObjectProperty<>(SourceType.NORMAL);

  @Getter
  private ObjectProperty<Gb28181SourceData> gb28181SourceData = new SimpleObjectProperty<>(new Gb28181SourceData());

  private ChangeListener<?> sourceChangeListener;

  public CaesarVideoData() {
  }

  @Override
  public void copyTo(VideoObject videoData) {
    super.copyTo(videoData);
    if (videoData instanceof CaesarVideoData) {
      CaesarVideoData target = (CaesarVideoData) videoData;
      target.getSourceIndex().set(sourceIndex.get());
      target.getCropIndex().set(cropIndex.get());
      target.getCropName().set(cropName.get());
      target.getAudioRx().set(audioRx.get());
      target.getAudioSeq().set(audioSeq.get());
      target.getSourceType().set(sourceType.get());
      switch (sourceType.get()) {
        case GB28181:
          Gb28181SourceData gbData = new Gb28181SourceData();
          Gb28181SourceData gb28181SourceData = this.gb28181SourceData.get();
          if (gb28181SourceData != null) {
            gbData.getId().set(gb28181SourceData.getId().get());
            gbData.getGridIndex().set(gb28181SourceData.getGridIndex().get());
            gbData.getStatus().set(gb28181SourceData.getStatus().get());
            gbData.getSyncVersion().set(gb28181SourceData.getSyncVersion().get());
          }
          target.getGb28181SourceData().set(gbData);
          break;
        default:
          // 清空GB28181源数据
          target.getGb28181SourceData().set(null);
          break;
      }
    }
  }

  @Override
  public ReadOnlyStringProperty getNameWithSource() {
    if (sourceChangeListener == null) {
      sourceChangeListener =
          weakAdapter.wrap(
              (obs, oldVal, newVal) -> {
                if (nameWithSource != null) {
                  nameWithSource.set(createNameWithSource());
                }
              });
      sourceIndex.addListener((ChangeListener<Number>) sourceChangeListener);
      cropIndex.addListener((ChangeListener<Number>) sourceChangeListener);
      cropName.addListener((ChangeListener<String>) sourceChangeListener);
      audioRx.addListener((ChangeListener<Number>) sourceChangeListener);
    }
    return super.getNameWithSource();
  }

  @Override
  protected String createNameWithSource() {
    String sourceFullName = getSourceFullName();
    StringBuilder sb = new StringBuilder();
    sb.append(name.get());
    sb.append("[").append(sourceFullName).append("]");
    if (audioRx.get() > 0) {
      sb.append(String.format("<audio_%d>", audioRx.get()));
    }
    if (sourceType.get() == GB28181 && gb28181SourceData.get() != null) {
      Gb28181SourceData data = gb28181SourceData.get();
      sb.append(String.format("%n<GB@%d:v%d:%s:%s>", data.gridIndex.get(),
          data.getSyncVersion().get(), data.getStatus().get().toString(), data.getId().get()));
    }
    return sb.toString();
  }

  protected String getSourceFullName() {
    if (source.get() == null) {
      return "";
    } else {
      return String.format("%s%s%s", source.get().getName(),
          source.get().canSeperate() ? "(" + (getSourceIndex().get() + 1) + ")" : "",
          cropIndex.get() > 0 ? "@" + (Strings.isNullOrEmpty(cropName.get()) ? "(empty)" : cropName.get()) : "");
    }
  }
}
