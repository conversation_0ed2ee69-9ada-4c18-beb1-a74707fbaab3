package com.mc.tool.caesar.api.version.v5x;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.MonitorDecoderInfo;
import com.mc.tool.caesar.api.datamodel.ScreenInfo;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.version.ApiDataConverter;
import java.util.Arrays;

/**
 * .
 */
public class CpuDataConverter5x implements ApiDataConverter<CpuData> {

  private static final int HOST_LENGTH = 4;

  private final int dataSize;

  public CpuDataConverter5x(int dataSize) {
    this.dataSize = dataSize;
  }

  @Override
  public void readData(CpuData data, CfgReader cfgReader) throws ConfigException {
    final int startAvailable = cfgReader.available();
    String name = cfgReader.readString(CaesarConstants.NAME_LEN);
    data.setName(name);
    int status = cfgReader.readInteger();
    data.setStatus(status);
    int console = cfgReader.read2ByteValue();
    data.setConsole(console);
    int realCpu = cfgReader.read2ByteValue();
    data.setRealCpu(realCpu);
    int id = cfgReader.read2ByteValue();
    data.setId(id);

    int ctrlUserIndex = cfgReader.read2ByteValue();
    data.setCtrlUserIndex(ctrlUserIndex);

    int ports = cfgReader.read2ByteValue();
    data.setPorts(ports);
    // extender size
    for (int idx = 0; idx < CaesarConstants.Extender.CPUCON; idx++) {
      int extender = cfgReader.read2ByteValue();
      data.setExtender(idx, extender & 0xFFFF);
      data.setCpuIndex(idx, extender & 0xFFFF0000);
    }

    data.getScreenOffsetConfig().setTotalResolutionX(cfgReader.read2ByteValue());
    data.getScreenOffsetConfig().setTotalResolutionY(cfgReader.read2ByteValue());
    int offset = cfgReader.readByteValue();
    data.getScreenOffsetConfig().setActiveOffset(offset & 1);
    data.getScreenOffsetConfig().setArrangement((offset >> 1) & 1);
    data.getScreenOffsetConfig().setActiveMonitorDecoder((offset >> 2) & 1);
    if (data.getScreenOffsetConfig().getActiveMonitorDecoder() > 0) {
      // 4个字节的监控解码器IP地址
      byte[] monitorDecoderIp = cfgReader.readByteArray(HOST_LENGTH);
      data.getScreenOffsetConfig().getMonitorDecoderInfo().setIp(monitorDecoderIp);
      // 2个字节的监控解码器端口
      int monitorDecoderPort = cfgReader.read2ByteValue();
      data.getScreenOffsetConfig().getMonitorDecoderInfo().setPort(monitorDecoderPort);
      // 1个字节的网格类型
      int gridTypeValue = cfgReader.readByteValue();
      data.getScreenOffsetConfig().getMonitorDecoderInfo()
          .setDefaultGridType(MonitorDecoderInfo.GridType.valueOf(gridTypeValue));
      // 9个字节预留
      cfgReader.readByteArray(9);
    } else {
      setScreenInfo(data.getScreenOffsetConfig().getScreenInfo1(), cfgReader);
      setScreenInfo(data.getScreenOffsetConfig().getScreenInfo2(), cfgReader);
    }
    int groupIndex = cfgReader.read2ByteValue();
    data.setTxGroupIndex(groupIndex);

    int pduOpenMode = cfgReader.readByteValue();
    data.setOpenMode(pduOpenMode);
    int pduPort = cfgReader.readByteValue();
    data.setPduPort(pduPort);
    byte[] pduIp = cfgReader.readByteArray(HOST_LENGTH);
    data.setPduIp(pduIp);

    int endAvailable = cfgReader.available();
    int readedSize = startAvailable - endAvailable;
    int reserveSize = dataSize - readedSize;
    cfgReader.readByteArray(reserveSize);
  }

  private void setScreenInfo(ScreenInfo screenInfo, CfgReader cfgReader) throws ConfigException {
    screenInfo.setHorizontalResolution(cfgReader.read2ByteValue());
    screenInfo.setVerticalResolution(cfgReader.read2ByteValue());
    screenInfo.setOffsetX(cfgReader.read2ByteValue());
    screenInfo.setOffsetY(cfgReader.read2ByteValue());
  }

  @Override
  public void writeData(CpuData data, CfgWriter cfgWriter) throws ConfigException {
    final long startSize = cfgWriter.getSize();
    cfgWriter.writeString(data.getName(), CaesarConstants.NAME_LEN);
    cfgWriter.writeInteger(data.getStatus());
    cfgWriter.write2ByteSmallEndian(data.getConsole());
    cfgWriter.write2ByteSmallEndian(data.getRealCpu());
    cfgWriter.write2ByteSmallEndian(data.getId());
    cfgWriter.write2ByteSmallEndian(data.getCtrlUserIndex());
    cfgWriter.write2ByteSmallEndian(data.getPorts());
    for (int idx = 0; idx < CaesarConstants.Extender.CPUCON; idx++) {
      int value = data.getCpuIndex(idx);
      value |= data.getExtender(idx);
      cfgWriter.write2ByteSmallEndian(value);
    }

    cfgWriter.write2ByteSmallEndian(data.getScreenOffsetConfig().getTotalResolutionX());
    cfgWriter.write2ByteSmallEndian(data.getScreenOffsetConfig().getTotalResolutionY());
    int flags = (data.getScreenOffsetConfig().getActiveOffset() & 0x01)
        | ((data.getScreenOffsetConfig().getArrangement()  & 0x01) << 1)
        | ((data.getScreenOffsetConfig().getActiveMonitorDecoder() & 0x01) << 2);
    cfgWriter.writeByte((byte) flags);
    if (data.getScreenOffsetConfig().getActiveOffset() == 0 && data.getScreenOffsetConfig().getActiveMonitorDecoder() > 0) {
      byte[] ip = data.getScreenOffsetConfig().getMonitorDecoderInfo().getIp();
      if (ip.length < HOST_LENGTH) {
        ip = new byte[HOST_LENGTH];
      } else {
        ip = Arrays.copyOf(ip, HOST_LENGTH);
      }
      cfgWriter.writeByteArray(ip);
      cfgWriter.write2ByteSmallEndian(data.getScreenOffsetConfig().getMonitorDecoderInfo().getPort());
      cfgWriter.writeByte((byte) data.getScreenOffsetConfig().getMonitorDecoderInfo().getDefaultGridType().getValue());
      cfgWriter.writeByteArray(new byte[9]); // 9个字节预留
    } else {
      writeScreenInfoData(data.getScreenOffsetConfig().getScreenInfo1(), cfgWriter);
      writeScreenInfoData(data.getScreenOffsetConfig().getScreenInfo2(), cfgWriter);
    }
    cfgWriter.write2ByteSmallEndian(data.getTxGroupIndex());

    cfgWriter.writeByte((byte) data.getOpenMode());
    cfgWriter.writeByte((byte) data.getPduPort());
    cfgWriter.writeByteArray(
        Arrays.equals(data.getPduIp(), new byte[0]) ? new byte[] {0, 0, 0, 0} : data.getPduIp());

    long endSize = cfgWriter.getSize();
    int writedSize = (int) (endSize - startSize);
    int reservedSize = dataSize - writedSize;
    cfgWriter.writeByteArray(new byte[reservedSize]);
  }

  private void writeScreenInfoData(ScreenInfo screenInfo, CfgWriter cfgWriter)
      throws ConfigException {
    cfgWriter.write2ByteSmallEndian(screenInfo.getHorizontalResolution());
    cfgWriter.write2ByteSmallEndian(screenInfo.getVerticalResolution());
    cfgWriter.write2ByteSmallEndian(screenInfo.getOffsetX());
    cfgWriter.write2ByteSmallEndian(screenInfo.getOffsetY());
  }
}
