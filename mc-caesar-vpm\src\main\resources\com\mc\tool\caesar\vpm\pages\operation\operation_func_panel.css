#root {
  -fx-background-color: #ffffff;
}

#show-btn-container {
  -fx-border-width: 0 0 0 1;
  -fx-border-color: #cccccc;
}

#show-btn-sub-container {
  -fx-background-color: #e6e6e6;
}


#show-property-btn {
  -fx-graphic: url("show_property.png");
}

.seperator {
  -fx-background-color: #cccccc;
}

#icon-btn {
  -fx-graphic: url("icon_btn.png");
}

#property-btn {
  -fx-graphic: url("property_btn.png");
}

.right-panel {
  -fx-padding: 0 0 0 0;
}

.right-title {
  -fx-background-color: #e6e6e6;
  -fx-alignment: center-left;
  -fx-padding: 0 0 0 5;
}

#hide-btn-container {
  -fx-padding: 0;
}

#hide-property-btn {
  -fx-graphic: url("hide_property.png");
}

#hide-property-btn {
  -fx-graphic: url("hide_property.png");
}

#header-toolbox {
  -fx-spacing: 30;
  -fx-padding: 0 10 0 0;
}

#function-title {
  -fx-label-padding: 0 0 0 9;
}

#seperator {
  -fx-background-color: #cccccc;
}

#body-pane-main {
  -fx-padding: 10 0 10 10;
}

#function-source {
  -fx-border-width: 1px;
  -fx-border-color: #cccccc;
}

#function-content {
  -fx-border-width: 1 0 1 0;
  -fx-border-color: #cccccc;
}

.list-cell {
  -fx-cell-size: 32;
  -fx-background-color: white;
}

.list-cell:selected {
  -fx-background-color: #e6e6e6;
  -fx-text-fill: black;
}