package com.mc.tool.caesar.vpm.pages.splicingscreen;

import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import javafx.util.StringConverter;

/**
 * ConsoleDataStringConverter.
 */
public class ConsoleDataStringConverter extends StringConverter<ConsoleData>  {

  @Override
  public String toString(ConsoleData object) {
    if (object == null) {
      return "";
    }
    ExtenderData extenderData = object.getExtenderData(0);
    if (extenderData != null) {
      return String.format("%s(%s)", object.getName(), extenderData.getExtenderStatusInfo()
          .getVideoResolutionType().getText());
    }
    return object.getName();
  }

  @Override
  public ConsoleData fromString(String string) {
    return null;
  }
}
