package com.mc.tool.caesar.vpm.view;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConfigDataModel;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.DemoSwitchDataModel;
import com.mc.tool.caesar.api.Version;
import com.mc.tool.caesar.api.datamodel.ConfigData.IoMode;
import com.mc.tool.caesar.api.datamodel.LoginResponse;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgError;
import com.mc.tool.caesar.api.utils.FileTransfer;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.activateconfig.ActivateConfigMetaCdm;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.ConfigListUtility;
import com.mc.tool.caesar.vpm.util.ConnectDeviceUtility;
import com.mc.tool.caesar.vpm.util.ReconnectDevicesUtility;
import com.mc.tool.caesar.vpm.util.VpmVersionUtility;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.UndecoratedAlert;
import com.mc.tool.framework.utility.UndecoratedWizard;
import com.mc.tool.framework.utility.UndecoratedWizardPane;
import com.mc.tool.framework.utility.dialogues.FileDialogue;
import com.mc.tool.framework.utility.dialogues.FileDialogueFactory;
import com.mc.tool.framework.utility.dialogues.FileDialogueManager;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.ref.WeakReference;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;
import java.util.concurrent.ExecutionException;
import javafx.application.Platform;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.control.CheckBox;
import javafx.scene.control.Label;
import javafx.scene.control.PasswordField;
import javafx.scene.control.SelectionMode;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextField;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import javafx.stage.FileChooser.ExtensionFilter;
import javafx.stage.Window;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class ConfigListView implements Initializable {

  @FXML private TableView<ActivateConfigMetaCdm> tableView;
  @FXML private TableColumn<ActivateConfigMetaCdm, String> indexCol;
  @FXML private TableColumn<ActivateConfigMetaCdm, String> fileCol;
  @FXML private TableColumn<ActivateConfigMetaCdm, String> nameCol;
  @FXML private TableColumn<ActivateConfigMetaCdm, String> infoCol;
  @FXML private TableColumn<ActivateConfigMetaCdm, String> addressCol;
  @FXML private TableColumn<ActivateConfigMetaCdm, String> versionCol;
  @FXML private Button btn;
  @FXML private CheckBox activateCkb;

  private static ObservableList<ActivateConfigMetaCdm> activateConfigList =
      FXCollections.observableArrayList();

  private static BooleanProperty isDownload = new SimpleBooleanProperty();
  private static BooleanProperty isSelected = new SimpleBooleanProperty(false);
  private static CaesarSwitchDataModel model;
  private static StringProperty addressString = new SimpleStringProperty();
  private static StringProperty userString = new SimpleStringProperty();
  private static StringProperty passwordString = new SimpleStringProperty();

  private CaesarDeviceController deviceController;
  private WeakReference<Entity> entityRef;
  private Node root = null;
  private BooleanProperty updatingProperty = new SimpleBooleanProperty(false);

  /** Constructor. */
  public ConfigListView(Entity entity, CaesarDeviceController controller, Boolean is) {
    deviceController = controller;
    isDownload.set(is);
    entityRef = new WeakReference<>(entity);
    FXMLLoader loader =
        new FXMLLoader(
            Thread.currentThread()
                .getContextClassLoader()
                .getResource("com/mc/tool/caesar/vpm/list_view.fxml"));
    loader.setController(this);
    loader.setResources(CaesarI18nCommonResource.getResourceBundle());
    try {
      root = loader.load();
    } catch (IOException ex) {
      log.warn("Fail to load list_view.fxml!", ex);
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {

    indexCol.setCellFactory(col -> new IndexCell());
    fileCol.setCellValueFactory(item -> item.getValue().getFilenameProperty());
    nameCol.setCellValueFactory(item -> item.getValue().getNameProperty());
    infoCol.setCellValueFactory(item -> item.getValue().getInfoProperty());
    addressCol.setCellValueFactory(item -> item.getValue().getAddressProperty());
    versionCol.setCellValueFactory(item -> item.getValue().getVersionProperty());

    activateCkb.managedProperty().bind(activateCkb.visibleProperty());
    activateCkb.visibleProperty().bind(isDownload.not());

    activateCkb
        .selectedProperty()
        .addListener(
            (observable, oldValue, newValue) -> {
              if (newValue) {
                Platform.runLater(
                    () -> {
                      UndecoratedAlert alert = new UndecoratedAlert(AlertExType.CONFIRMATION);
                      if (activateCkb != null && activateCkb.getScene() != null) {
                        alert.initOwner(activateCkb.getScene().getWindow());
                      }
                      alert.setTitle(
                          CaesarI18nCommonResource.getString(
                              "save_config.page3.activate_alert.title"));
                      alert.setHeaderText(null);
                      alert.setContentText(
                          CaesarI18nCommonResource.getString(
                              "save_config.page3.activate_alert.text"));
                      alert
                          .showAndWait()
                          .ifPresent(
                              (result) -> {
                                if (result.equals(ButtonType.CANCEL)) {
                                  activateCkb.setSelected(false);
                                }
                              });
                    });
              }
            });

    tableView.getSelectionModel().setSelectionMode(SelectionMode.SINGLE);
    isSelected.bind(tableView.getSelectionModel().selectedItemProperty().isNotNull());
    if (isDownload.get()) {
      btn.setText(CaesarI18nCommonResource.getString("save_config.page3.downloadBtn"));
    } else {
      btn.setText(CaesarI18nCommonResource.getString("save_config.page3.uploadBtn"));
    }
    btn.disableProperty().bind(isSelected.not().or(updatingProperty));
    btn.setOnAction(
        (event) -> {
          if (isDownload.get()) {
            FileDialogueFactory factory =
                InjectorProvider.getInjector().getInstance(FileDialogueFactory.class);
            Window owner = null;
            if (tableView != null && tableView.getScene() != null) {
              owner = tableView.getScene().getWindow();
            }
            FileDialogue fileDialogue = factory.createFileDialogue("CONFIG");
            fileDialogue.addExtensionFilter(new ExtensionFilter("Config file", "*.cfgx"));
            File filepath = fileDialogue.showSaveDialog(owner);
            if (filepath != null) {
              // Save the selected directory for future use
              FileDialogueManager fileDialogueManager =
                  InjectorProvider.getInjector().getInstance(FileDialogueManager.class);
              fileDialogueManager.handleFileDialogueResult(fileDialogue, filepath);
              final String filePath = filepath.getPath();
              ActivateConfigMetaCdm item = tableView.getSelectionModel().getSelectedItem();
              if (item == null) {
                log.warn("Selected item is null! selected is {}!", isSelected.get());
              }
              updatingProperty.set(true);
              deviceController
                  .submitAsync(() -> saveConfig(model, filePath, item.getFileType()))
                  .whenComplete(
                      (result, throwable) -> {
                        if (throwable != null) {
                          log.warn("Fail to save config!", throwable);
                        }
                        PlatformUtility.runInFxThread(() -> updatingProperty.set(false));
                      });
            }
          } else {
            ActivateConfigMetaCdm item = tableView.getSelectionModel().getSelectedItem();
            updatingProperty.set(true);
            deviceController
                .submitAsync(
                    () -> uploadConfig(deviceController.getDataModel(), model, item.getFileType()))
                .whenComplete(
                    (result, throwable) -> {
                      if (throwable != null) {
                        log.warn("Fail to upload config!", throwable);
                      }
                      PlatformUtility.runInFxThread(() -> updatingProperty.set(false));
                    });
          }
        });
    tableView.setItems(activateConfigList);
  }

  public Node getView() {
    return root;
  }

  /** 下载/上传配置. */
  public static void show(Entity entity, CaesarDeviceController controller, Boolean isDownload) {
    ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    UndecoratedWizard wizard;
    if (isDownload) {
      wizard =
          new UndecoratedWizard(
              app.getMainWindow(),
              CaesarI18nCommonResource.getString("save_config.download_title"));
    } else {
      wizard =
          new UndecoratedWizard(
              app.getMainWindow(), CaesarI18nCommonResource.getString("save_config.upload_title"));
    }
    ConfigListView view = new ConfigListView(entity, controller, isDownload);

    UndecoratedWizardPane page1 =
        new UndecoratedWizardPane() {
          {
            Label tips1 = new Label(CaesarI18nCommonResource.getString("save_config.page1.tips1"));
            Label tips2 = new Label(CaesarI18nCommonResource.getString("save_config.page1.tips2"));
            Label tips3 = new Label();
            if (isDownload) {
              tips3.setText(CaesarI18nCommonResource.getString("save_config.page1.tips3"));
            } else {
              tips3.setText(CaesarI18nCommonResource.getString("save_config.page1.tips4"));
            }
            setContent(new VBox(tips1, tips2, tips3));
          }

          @Override
          public void onEnteringPage(UndecoratedWizard wizard) {
            ((Button) lookupButton(UndecoratedWizard.buttonPrevious))
                .setText(CaesarI18nCommonResource.getString("wizard_pane.previous_btn"));
            ((Button) lookupButton(UndecoratedWizard.buttonNext))
                .setText(CaesarI18nCommonResource.getString("wizard_pane.next_btn"));
            wizard.invalidProperty().unbind();
            wizard.setInvalid(false);
          }
        };

    UndecoratedWizardPane page2 =
        new UndecoratedWizardPane() {
          BooleanProperty isInvalid = new SimpleBooleanProperty();

          {
            GridPane page1Grid = new GridPane();
            page1Grid.setVgap(10);
            page1Grid.setHgap(10);

            page1Grid.add(
                new Label(CaesarI18nCommonResource.getString("save_config.page2.address")), 0, 0);
            TextField txIp = createTextField("address");
            String currentIp =
                controller
                        .getDataModel()
                        .getConfigData()
                        .getSystemConfigData()
                        .getNetworkDataPreset1()
                        .isDhcp()
                    ? IpUtil.getAddressString(
                        controller
                            .getDataModel()
                            .getConfigData()
                            .getSystemConfigData()
                            .getNetworkDataCurrent1()
                            .getAddress())
                    : IpUtil.getAddressString(
                        controller
                            .getDataModel()
                            .getConfigData()
                            .getSystemConfigData()
                            .getNetworkDataPreset1()
                            .getAddress());
            txIp.setText(currentIp);
            addressString.bind(txIp.textProperty());
            page1Grid.add(txIp, 1, 0);

            page1Grid.add(
                new Label(CaesarI18nCommonResource.getString("save_config.page2.user")), 0, 1);
            TextField txUser = createTextField("user");
            userString.bind(txUser.textProperty());
            page1Grid.add(txUser, 1, 1);

            page1Grid.add(
                new Label(CaesarI18nCommonResource.getString("save_config.page2.password")), 0, 2);
            PasswordField txPassword = new PasswordField();
            txPassword.setId("password");
            passwordString.bind(txPassword.textProperty());
            page1Grid.add(txPassword, 1, 2);

            Label errorMsg = new Label();
            errorMsg.setTextFill(Color.RED);
            errorMsg.setWrapText(true);
            errorMsg.setMaxWidth(200);
            Button btn = new Button(CaesarI18nCommonResource.getString("save_config.page2.verify"));
            HBox hbox = new HBox(btn, errorMsg);
            hbox.setSpacing(10);
            hbox.setAlignment(Pos.CENTER_LEFT);
            btn.setOnAction(
                (event) -> {
                  // demo登录
                  if (txIp.getText().equals(ConnectDeviceUtility.DEMO_IP)) {
                    try {
                      controller
                          .submit(
                              () -> {
                                model = new DemoSwitchDataModel(false);
                                model.initDefaults();
                              })
                          .get();
                    } catch (ExecutionException | InterruptedException exception) {
                      log.warn("Fail to create model.");
                    }
                    isInvalid.set(false);
                    return;
                  }
                  btn.setDisable(true);
                  controller.execute(
                      () -> {
                        String msg = "";
                        boolean invalid = false;
                        try {
                          if (model != null) {
                            model.closeConnection();
                          }
                          model = new CaesarSwitchDataModel();
                          model.setConnection(txIp.getText());
                          if (!model.userExists(txUser.getText())) {
                            invalid = true;
                            msg =
                                CaesarI18nCommonResource.getString(
                                    "save_config.page2.errorMsg.text5");
                            log.warn("user not exist!");
                            return;
                          }
                          if (!model.isAdminUser(txUser.getText())) {
                            invalid = true;
                            msg =
                                CaesarI18nCommonResource.getString(
                                    "save_config.page2.errorMsg.text1");
                            log.warn("The user has no admin rights. Upload not allowed.");
                            return;
                          }

                          // 账号密码验证
                          byte[] username = new byte[CaesarConstants.NAME_BYTE_LEN];
                          byte[] user;
                          user = txUser.getText().getBytes(StandardCharsets.UTF_8);
                          System.arraycopy(user, 0, username, 0, user.length);
                          byte[] password = new byte[20];
                          byte[] pwd;
                          pwd = txPassword.getText().getBytes(StandardCharsets.UTF_8);
                          System.arraycopy(pwd, 0, password, 0, pwd.length);
                          LoginResponse lr = model.login(username, password, VpmVersionUtility.makeVpmVersion());
                          if (lr.getStatus() != LoginResponse.LoginStatus.SUCCESS) {
                            invalid = true;
                            msg =
                                CaesarI18nCommonResource.getString(
                                    "save_config.page2.errorMsg.text5");
                            log.warn("User name or password error!");
                          }
                        } catch (ConfigException ex) {
                          invalid = true;
                          if (ex.getError() == CfgError.USER_ERROR) {
                            log.warn("user not exist", ex);
                            msg =
                                CaesarI18nCommonResource.getString(
                                    "save_config.page2.errorMsg.text1");
                          }
                          msg = "connect failed";
                          log.warn("connect failed", ex);
                          msg =
                              CaesarI18nCommonResource.getString(
                                  "save_config.page2.errorMsg.text3");
                        } catch (BusyException ex) {
                          invalid = true;
                          log.warn("Matrix busy", ex);
                          msg =
                              CaesarI18nCommonResource.getString(
                                  "save_config.page2.errorMsg.text4");
                        } finally {
                          final boolean finalInvalid = invalid;
                          final String finalMsg = msg;
                          Platform.runLater(
                              () -> {
                                isInvalid.set(finalInvalid);
                                errorMsg.setText(finalMsg);
                                btn.setDisable(false);
                              });
                        }
                      });
                });
            page1Grid.add(hbox, 1, 3);
            page1Grid.setMaxSize(400, 300);

            setGraphic(null);
            setContent(page1Grid);
          }

          @Override
          public void onEnteringPage(UndecoratedWizard wizard) {
            ((Button) lookupButton(UndecoratedWizard.buttonPrevious))
                .setText(CaesarI18nCommonResource.getString("wizard_pane.previous_btn"));
            ((Button) lookupButton(UndecoratedWizard.buttonNext))
                .setText(CaesarI18nCommonResource.getString("wizard_pane.next_btn"));
            isInvalid.set(true);
            wizard.invalidProperty().unbind();
            wizard.invalidProperty().bind(isInvalid);
          }
        };

    UndecoratedWizardPane page3 =
        new UndecoratedWizardPane() {
          {
            setContent(view.getView());
          }

          @Override
          public void onEnteringPage(UndecoratedWizard wizard) {
            wizard.invalidProperty().bind(view.updatingProperty);
            controller.execute(ConfigListView::listImpl);
          }
        };

    wizard.setFlow(new UndecoratedWizard.LinearFlow(page1, page2, page3));
    wizard
        .showAndWait()
        .ifPresent(
            (result) -> {
              if (result.equals(ButtonType.FINISH) && model != null) {
                model.closeConnection();
              }
            });
  }

  private static TextField createTextField(String id) {
    TextField textField = new TextField();
    textField.setId(id);
    GridPane.setHgrow(textField, Priority.ALWAYS);
    return textField;
  }

  private static class IndexCell extends TableCell<ActivateConfigMetaCdm, String> {
    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);

      if (!empty) {
        int rowIndex = this.getIndex() + 1;
        this.setText(String.valueOf(rowIndex));
      }
    }
  }

  private static void listImpl() {
    int type;
    InputStream is;

    List<ActivateConfigMetaCdm> newList = new ArrayList<>();
    for (String configName : CaesarConstants.CONFIG_NAMES) {
      // Fix bug #1374:避免主机的数据错误导致后续的配置读取失败的问题.
      CaesarConfigDataModel activeConfigModel = new CaesarConfigDataModel();
      try {
        type = getFileType(configName);
        byte[] bytes = FileTransfer.read(model.getController(), type);
        is = new ByteArrayInputStream(bytes);

        boolean error;
        int tryCounter = 0;
        do {
          try {
            tryCounter++;
            error = false;
            activeConfigModel.initDefaults();
            activeConfigModel.readData(is, IoMode.Available);
          } catch (ConfigException ex) {
            log.warn("Failed to read activeConfig data!");
            error = true;
          }
        } while (error && tryCounter < 5);
        if (error) {
          newList.add(
              new ActivateConfigMetaCdm("无效配置", "无效配置", "s0.0.0.0".substring(1), "", "", 0));
        } else {
          // 保存配置的基本信息
          String filename = configName + CaesarConstants.CFG_FILE_EXTENSION;
          String name =
              activeConfigModel.getConfigData().getSystemConfigData().getSystemData().getName();
          String info =
              activeConfigModel.getConfigData().getSystemConfigData().getSystemData().getInfo();
          byte[] address =
              activeConfigModel
                  .getConfigData()
                  .getSystemConfigData()
                  .getNetworkDataPreset1()
                  .getAddress();
          Boolean isdhcp =
              activeConfigModel
                  .getConfigData()
                  .getSystemConfigData()
                  .getNetworkDataPreset1()
                  .isDhcp();
          int version = activeConfigModel.getConfigData().getConfigMetaData().getVersion();
          newList.add(
              new ActivateConfigMetaCdm(
                  filename,
                  name,
                  info,
                  dhcpRow(isdhcp, address),
                  Version.getVersion(version).getName(),
                  type));
        }

      } catch (ConfigException | BusyException | IOException ex) {
        log.warn("Failed to initialize activeConfig list!", ex);
      }
    }
    PlatformUtility.runInFxThread(
        () -> {
          // 复制列表，避免选中的项消失
          int originalSize = activateConfigList.size();
          int copySize;
          if (originalSize > newList.size()) {
            copySize = newList.size();
            activateConfigList.remove(newList.size(), originalSize);
          } else if (originalSize < newList.size()) {
            copySize = originalSize;
            activateConfigList.addAll(newList.subList(originalSize, newList.size()));
          } else {
            copySize = newList.size();
          }
          for (int i = 0; i < copySize; i++) {
            newList.get(i).copyTo(activateConfigList.get(i));
          }
        });
  }

  private static int getFileType(String configname) {
    int index = 1;
    for (String config : CaesarConstants.CONFIG_NAMES) {
      if (config.equals(configname)) {
        return index;
      }
      index++;
    }
    return 0;
  }

  private static String dhcpRow(Boolean isDhcp, byte[] address) {
    if (isDhcp) {
      return "";
    }
    return IpUtil.getAddressString(address);
  }

  private void saveConfig(CaesarSwitchDataModel model, String path, int fileType) {
    log.info(String.format("User(%s) save config, fileType:%d.", deviceController.getLoginUser(), fileType));
    byte[] configData = null;
    byte[] vpdata = null;
    try {
      configData = FileTransfer.read(model.getController(), fileType);
      vpdata = FileTransfer.read(model.getController(), fileType | 0xf0);
    } catch (IOException | ConfigException | BusyException exception) {
      log.warn("Fail to read config data!", exception);
    }
    if (configData != null && ConfigListUtility.saveConfigFile(path, configData, vpdata)) {
      listImpl();
      Platform.runLater(
          () -> {
            UndecoratedAlert alert = new UndecoratedAlert(AlertExType.INFORMATION);
            if (tableView != null && tableView.getScene() != null) {
              alert.initOwner(tableView.getScene().getWindow());
            }
            alert.setTitle(CaesarI18nCommonResource.getString("save_config.alert.title"));
            alert.setHeaderText(null);
            alert.setContentText(
                CaesarI18nCommonResource.getString("save_config.alert.download_complete"));
            alert.showAndWait();
          });
    } else {
      Platform.runLater(
          () -> {
            UndecoratedAlert alert = new UndecoratedAlert(AlertExType.INFORMATION);
            if (tableView != null && tableView.getScene() != null) {
              alert.initOwner(tableView.getScene().getWindow());
            }
            alert.setTitle(CaesarI18nCommonResource.getString("save_config.alert.title"));
            alert.setHeaderText(null);
            alert.setContentText(
                CaesarI18nCommonResource.getString("save_config.alert.download_failed"));
            alert.showAndWait();
          });
    }
  }

  private void uploadConfig(
      CaesarSwitchDataModel source, CaesarSwitchDataModel target, int fileType) {
    log.info(String.format("User(%s) upload config, fileType:%d.", deviceController.getLoginUser(), fileType));
    if (uploadImpl(source, target, fileType)) {
      listImpl();
      Platform.runLater(
          () -> {
            UndecoratedAlert alert = new UndecoratedAlert(AlertExType.INFORMATION);
            if (tableView != null && tableView.getScene() != null) {
              alert.initOwner(tableView.getScene().getWindow());
            }
            alert.setTitle(CaesarI18nCommonResource.getString("save_config.alert.title"));
            alert.setHeaderText(null);
            alert.setContentText(
                CaesarI18nCommonResource.getString("save_config.alert.upload_complete"));
            alert.showAndWait();
            if (activateCkb.isSelected()) {
              deviceController.execute(
                  () -> {
                    activateConfig(fileType - 1);
                    if (!target.isDemo()
                        && Utilities.getIpFromModel(model)
                            .equals(Utilities.getIpFromModel(target))) {
                      ApplicationBase app =
                          InjectorProvider.getInjector().getInstance(ApplicationBase.class);
                      ReconnectDevicesUtility.waitingForReconnection(
                          app, entityRef.get(), deviceController, false);
                    } else if (!Utilities.getIpFromModel(model)
                        .equals(Utilities.getIpFromModel(target))) {
                      Platform.runLater(
                          () -> {
                            UndecoratedAlert demoAlert =
                                new UndecoratedAlert(AlertExType.INFORMATION);
                            if (tableView != null && tableView.getScene() != null) {
                              demoAlert.initOwner(tableView.getScene().getWindow());
                            }
                            demoAlert.setTitle(
                                CaesarI18nCommonResource.getString("alert.reboot_alert.title"));
                            demoAlert.setHeaderText(null);
                            demoAlert.setContentText(
                                CaesarI18nCommonResource.getString("alert.reboot_alert.text")
                                    + Utilities.getIpFromModel(target));
                            demoAlert.showAndWait();
                          });
                    }
                  });
            }
          });
    } else {
      Platform.runLater(
          () -> {
            UndecoratedAlert alert = new UndecoratedAlert(AlertExType.INFORMATION);
            if (tableView != null && tableView.getScene() != null) {
              alert.initOwner(tableView.getScene().getWindow());
            }
            alert.setTitle(CaesarI18nCommonResource.getString("save_config.alert.title"));
            alert.setHeaderText(null);
            alert.setContentText(
                CaesarI18nCommonResource.getString("save_config.alert.upload_failed"));
            alert.showAndWait();
          });
    }
  }

  private boolean uploadImpl(
      CaesarSwitchDataModel source, CaesarSwitchDataModel target, int fileType) {
    source.getDbDataModel().reloadAll();
    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    try {
      source.writeData(baos, IoMode.Available);
      FileTransfer.write(
          target.getController(), fileType, new ByteArrayInputStream(baos.toByteArray()));
    } catch (ConfigException | BusyException | IOException exception) {
      log.warn("Fail to upload config file!", exception);
      return false;
    }

    String tempPath = System.getProperty("java.io.tmpdir");
    String zipFilePath = tempPath + "temp.zip";
    if (!ConfigListUtility.saveExtraConfig(source, zipFilePath)) {
      // 为了兼容旧设备，即使上传额外配置数据不成功也是可以的.
      return true;
    }
    // 上传额外配置数据
    try (FileInputStream fis = new FileInputStream(zipFilePath)) {
      FileTransfer.write(target.getController(), fileType | 0xf0, fis);
    } catch (IOException | ConfigException | BusyException exception) {
      log.warn("Fail write extra conf data to device for {} | 0xf0!", fileType, exception);
    }

    return true;
  }

  private void activateConfig(int configNr) {
    try {
      model.activateConfig(configNr);
    } catch (ConfigException | BusyException ex) {
      log.warn("Activate error!", ex);
    }
  }
}
