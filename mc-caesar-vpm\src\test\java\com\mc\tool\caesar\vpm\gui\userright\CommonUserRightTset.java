package com.mc.tool.caesar.vpm.gui.userright;

import static org.mockito.Mockito.when;

import com.mc.common.control.IpInputInfo;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.device.FileUserInfoFactory;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.caesar.vpm.pages.operation.CaesarOperationPageNew;
import com.mc.tool.framework.view.FrameView;
import java.io.File;
import java.io.IOException;
import java.util.Optional;
import javafx.scene.Node;
import javafx.stage.Stage;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.testfx.util.NodeQueryUtils;

/**
 * .
 */
@RunWith(MockitoJUnitRunner.class)
public class CommonUserRightTset extends AppGuiTestBase {

  @Mock private FileUserInfoFactory fileUserInfoFactory;

  @Mock private IpInputInfo info;

  @Override
  public void beforeAll() {}

  @Override
  public void start(Stage stage) throws Exception {
    FrameView.setModule(
        binder -> binder.bind(FileUserInfoFactory.class).toInstance(fileUserInfoFactory));
    when(fileUserInfoFactory.createInputInfo()).thenReturn(info);
    super.start(stage);
  }

  @Test
  public void testVideoRights() {
    for (int testIndex = 0; testIndex < VideoWallGroupData.GROUP_COUNT; testIndex++) {
      when(info.getUser()).thenReturn("testrights" + testIndex);
      try {
        clickOn(GuiTestConstants.RESTORE_GRAPH);

        clickOn("#" + CaesarOperationPageNew.NAME);
        for (int i = 0; i < VideoWallGroupData.GROUP_COUNT; i++) {
          clickOn(GuiTestConstants.FUNC_COMBOBOX);
          clickOn("VideoWall" + (i + 1));
          boolean hasRight = i == testIndex;
          // 检查按钮是否存在
          Optional<Node> result =
              lookup(GuiTestConstants.VIDEO_WALL_PREWINDOW_BTN)
                  .match(NodeQueryUtils.isVisible())
                  .tryQuery();
          Assert.assertTrue(hasRight ^ !result.isPresent());
          result =
              lookup(GuiTestConstants.VIDEO_WALL_SAVE_BTN)
                  .match(NodeQueryUtils.isVisible())
                  .tryQuery();
          Assert.assertTrue(hasRight ^ !result.isPresent());
          result =
              lookup(GuiTestConstants.VIDEO_WALL_SAVE_AS_BTN)
                  .match(NodeQueryUtils.isVisible())
                  .tryQuery();
          Assert.assertTrue(hasRight ^ !result.isPresent());
          // 检查属性框
          Node node = lookup(GuiTestConstants.VIDEO_WALL_VIDEO_LAYOUT_PROPERTY).query();
          Assert.assertFalse(hasRight ^ !node.isDisabled());
          node = lookup(GuiTestConstants.VIDEO_WALL_CONFIG_PROPERTY).query();
          Assert.assertFalse(hasRight ^ !node.isDisabled());

          clickOn(GuiTestConstants.VIDEO_WALL_SCREEN_BTN);
          // 检查按钮
          result =
              lookup(GuiTestConstants.VIDEO_WALL_AUTO_ARRANGE_BTN)
                  .match(NodeQueryUtils.isVisible())
                  .tryQuery();
          Assert.assertTrue(hasRight ^ !result.isPresent());
          // 检查属性框
          node = lookup(GuiTestConstants.VIDEO_WALL_LAYOUT_PROPERTY).query();
          Assert.assertFalse(hasRight ^ !node.isDisabled());
          node = lookup(GuiTestConstants.VIDEO_WALL_OUTPUT_PROPERTY).query();
          Assert.assertFalse(hasRight ^ !node.isDisabled());
          clickOn(GuiTestConstants.VIDEO_WALL_SCREEN_BTN);
        }
        File tempFile = loadResourceStatus("com/mc/tool/caesar/vpm/96_hw1.2_rightstest.status");
        tempFile.delete();
      } catch (IOException exception) {
        exception.printStackTrace();
        Assert.fail("Expect no exception!");
      }
    }
  }
}
