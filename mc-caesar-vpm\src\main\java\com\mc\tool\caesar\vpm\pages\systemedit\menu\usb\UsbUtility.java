package com.mc.tool.caesar.vpm.pages.systemedit.menu.usb;

import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;

/**
 * .
 */
public class UsbUtility {
  /**
   * 获取可绑定usb的cpu列表.
   *
   * @param dataManager data manager
   * @return cpu列表
   */
  public static Collection<CpuData> getBindableCpus(
      ConfigDataManager dataManager, CaesarMatrix matrix) {
    List<CpuData> result = new ArrayList<>(dataManager.getActiveCpus());
    int first = matrix.getMatrixData().getFirstPort();
    int last = matrix.getMatrixData().getLastPort();
    result.removeIf(
        cpu ->
            (cpu.getExtenderData(0).getPort() < first || cpu.getExtenderData(0).getPort() > last)
                && (cpu.getExtenderData(0).getRdPort() < first
                    || cpu.getExtenderData(0).getRdPort() > last));
    result.removeIf(cpu -> cpu.getExtenderData(1) != null);
    result.sort(Comparator.comparing(CpuData::getName));
    return result;
  }

  /**
   * 获取可绑定usb的console列表.
   *
   * @param dataManager data manager
   * @return console列表
   */
  public static Collection<ConsoleData> getBindableConsoles(
      ConfigDataManager dataManager, CaesarMatrix matrix) {
    List<ConsoleData> result = new ArrayList<>(dataManager.getActiveConsolesWithoutVpcon());
    int first = matrix.getMatrixData().getFirstPort();
    int last = matrix.getMatrixData().getLastPort();
    result.removeIf(
            con -> con.getExtender(0) == 0
                    || (con.getExtenderData(0).getPort() < first || con.getExtenderData(0).getPort() > last)
                    && (con.getExtenderData(0).getRdPort() < first
                    || con.getExtenderData(0).getRdPort() > last));
    result.removeIf(con -> con.getExtenderData(1) != null);
    result.sort(Comparator.comparing(ConsoleData::getName));
    return result;
  }
}
