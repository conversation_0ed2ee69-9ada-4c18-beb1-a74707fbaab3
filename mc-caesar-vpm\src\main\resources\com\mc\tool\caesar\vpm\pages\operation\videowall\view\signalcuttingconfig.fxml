<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<GridPane alignment="CENTER" xmlns="http://javafx.com/javafx/17.0.2-ea" xmlns:fx="http://javafx.com/fxml/1">
    <columnConstraints>
        <ColumnConstraints hgrow="SOMETIMES" maxWidth="70.0" minWidth="70.0"/>
        <ColumnConstraints hgrow="SOMETIMES" maxWidth="200.0" minWidth="200.0"/>
        <ColumnConstraints hgrow="SOMETIMES" maxWidth="70.0" minWidth="70.0"/>
    </columnConstraints>
    <rowConstraints>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
        <RowConstraints vgrow="SOMETIMES"/>
        <RowConstraints maxHeight="80.0" minHeight="80.0" vgrow="SOMETIMES"/>
        <RowConstraints vgrow="SOMETIMES"/>
    </rowConstraints>
    <children>
        <HBox alignment="CENTER" prefHeight="100.0" prefWidth="200.0" GridPane.columnSpan="3"
              GridPane.halignment="CENTER" GridPane.valignment="CENTER">
            <children>
                <Label text="%name"/>
                <TextField fx:id="name" maxWidth="80.0"/>
            </children>
        </HBox>
        <HBox alignment="CENTER" prefHeight="100.0" prefWidth="200.0" GridPane.columnSpan="3"
              GridPane.halignment="CENTER" GridPane.valignment="CENTER" GridPane.rowIndex="1">
            <children>
                <Label text="%resolution"/>
                <TextField fx:id="resWidth" maxWidth="80.0"/>
                <Label text="x"/>
                <TextField fx:id="resHeight" maxWidth="80.0"/>
            </children>
        </HBox>
        <VBox alignment="BOTTOM_CENTER" GridPane.columnIndex="1" GridPane.rowIndex="2">
            <children>
                <Label alignment="TOP_LEFT" text="%top"/>
                <TextField fx:id="top" alignment="TOP_LEFT" maxWidth="80.0"/>
            </children>
        </VBox>
        <VBox alignment="CENTER_RIGHT" prefHeight="200.0" prefWidth="100.0" GridPane.rowIndex="3">
            <children>
                <Label text="%left"/>
                <TextField fx:id="left"/>
            </children>
        </VBox>
        <VBox alignment="CENTER_LEFT" prefHeight="200.0" prefWidth="100.0" GridPane.columnIndex="2"
              GridPane.rowIndex="3">
            <children>
                <Label text="%right"/>
                <TextField fx:id="right"/>
            </children>
        </VBox>
        <VBox alignment="TOP_CENTER" GridPane.columnIndex="1" GridPane.rowIndex="4">
            <children>
                <Label text="%bottom"/>
                <TextField fx:id="bottom" maxWidth="80.0"/>
            </children>
        </VBox>
        <Pane fx:id="oldScreen" maxHeight="70.0" maxWidth="190.0"
              style="-fx-border-color: black; -fx-border-width: 0.5; -fx-border-style: dashed;" GridPane.columnIndex="1"
              GridPane.halignment="CENTER" GridPane.rowIndex="3">
            <children>
                <Region fx:id="newScreen" style="-fx-border-width: 1; -fx-border-color: black;"/>
            </children>
        </Pane>
    </children>
</GridPane>
