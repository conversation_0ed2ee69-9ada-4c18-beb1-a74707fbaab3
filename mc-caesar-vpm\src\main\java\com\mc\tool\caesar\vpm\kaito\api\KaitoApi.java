package com.mc.tool.caesar.vpm.kaito.api;

import com.mc.tool.caesar.vpm.kaito.ApiClient;
import com.mc.tool.caesar.vpm.kaito.EncodingUtils;
import com.mc.tool.caesar.vpm.kaito.model.CreateLayerResponse;
import com.mc.tool.caesar.vpm.kaito.model.CreateVideoWallGroupInfo;
import com.mc.tool.caesar.vpm.kaito.model.CreateVideoWallGroupResponse;
import com.mc.tool.caesar.vpm.kaito.model.DecodeCard;
import com.mc.tool.caesar.vpm.kaito.model.DecodeCardUpdateInfo;
import com.mc.tool.caesar.vpm.kaito.model.DecodeGroup;
import com.mc.tool.caesar.vpm.kaito.model.DecodeGroupUpdateInfo;
import com.mc.tool.caesar.vpm.kaito.model.DeleteLayerResponse;
import com.mc.tool.caesar.vpm.kaito.model.InputCard;
import com.mc.tool.caesar.vpm.kaito.model.IpcInfo;
import com.mc.tool.caesar.vpm.kaito.model.LayerWindow;
import com.mc.tool.caesar.vpm.kaito.model.UpdateLayerResponse;
import com.mc.tool.caesar.vpm.kaito.model.UpdateLayerSourceRequest;
import com.mc.tool.caesar.vpm.kaito.model.UpdateLayerZorderRequest;
import com.mc.tool.caesar.vpm.kaito.model.UpdateLayerZorderResponse;
import com.mc.tool.caesar.vpm.kaito.model.VideoWall;
import com.mc.tool.caesar.vpm.kaito.model.VideoWallGroup;
import com.mc.tool.caesar.vpm.kaito.model.VideoWallLayer;
import feign.Headers;
import feign.Param;
import feign.QueryMap;
import feign.RequestLine;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * KaitoApi.
 *
 * @update 2024-03-15
 */
public interface KaitoApi extends ApiClient.Api {


  /**
   * 获取可用解码卡
   * 获取全光拼接组可用的未加入解码组的解码卡.
   *
   * @param videowallGroupId 全光拼接屏组的ID (required)
   * @return List&lt;DecodeCard&gt;
   */
  @RequestLine("GET /kaito/decode-card/available/{videowallGroupId}")
  @Headers({
      "Accept: application/json",
  })
  List<DecodeCard> kaitoDecodeCardAvailableVideowallGroupIdGet(
      @Param("videowallGroupId") Integer videowallGroupId);

  /**
   * 获取所有解码卡.
   *
   * @param videowallGroupId (required)
   * @return List&lt;DecodeCard&gt;
   */
  @RequestLine("GET /kaito/decode-card/list/{videowallGroupId}")
  @Headers({
      "Accept: application/json",
  })
  List<DecodeCard> kaitoDecodeCardListVideowallGroupIdGet(
      @Param("videowallGroupId") Integer videowallGroupId);

  /**
   * 删除解码卡.
   *
   * @param videowallGroupId (required)
   * @param decodeCardId     (required)
   * @return Object
   */
  @RequestLine("DELETE /kaito/decode-card/{videowallGroupId}/{decodeCardId}")
  @Headers({
      "Accept: application/json",
  })
  Object kaitoDecodeCardVideowallGroupIdDecodeCardIdDelete(
      @Param("videowallGroupId") Integer videowallGroupId,
      @Param("decodeCardId") Integer decodeCardId);

  /**
   * 添加或者更新解码卡.
   *
   * @param videowallGroupId     (required)
   * @param decodeCardId         (required)
   * @param decodeCardUpdateInfo (optional)
   * @return DecodeCard
   */
  @RequestLine("POST /kaito/decode-card/{videowallGroupId}/{decodeCardId}")
  @Headers({
      "Content-Type: application/json",
      "Accept: application/json",
  })
  DecodeCard kaitoDecodeCardVideowallGroupIdDecodeCardIdPost(
      @Param("videowallGroupId") Integer videowallGroupId,
      @Param("decodeCardId") Integer decodeCardId, DecodeCardUpdateInfo decodeCardUpdateInfo);


  /**
   * 获取全光拼接屏组内的所有解码组信息.
   *
   * @param videowallGroupId 全光拼接屏组的ID (required)
   * @return List&lt;DecodeGroup&gt;
   */
  @RequestLine("GET /kaito/decode-group/list/{videowallGroupId}")
  @Headers({
      "Accept: application/json",
  })
  List<DecodeGroup> kaitoDecodeGroupListVideowallGroupIdGet(
      @Param("videowallGroupId") Integer videowallGroupId);

  /**
   * 删除解码组.
   *
   * @param videowallGroupId 全光拼接屏组的ID (required)
   * @param decodeGroupId    (required)
   */
  @RequestLine("DELETE /kaito/decode-group/{videowallGroupId}/{decodeGroupId}")
  @Headers({
      "Content-Type: application/json",
      "Accept: application/json",
  })
  Object kaitoDecodeGroupVideowallGroupIdDecodeGroupIdDelete(
      @Param("videowallGroupId") Integer videowallGroupId,
      @Param("decodeGroupId") Integer decodeGroupId);

  /**
   * 更新解码组.
   *
   * @param videowallGroupId      全光拼接屏组的ID (required)
   * @param decodeGroupId         (required)
   * @param decodeGroupUpdateInfo (optional)
   * @return DecodeGroup
   */
  @RequestLine("POST /kaito/decode-group/{videowallGroupId}/{decodeGroupId}")
  @Headers({
      "Content-Type: application/json",
      "Accept: application/json",
  })
  DecodeGroup kaitoDecodeGroupVideowallGroupIdDecodeGroupIdPost(
      @Param("videowallGroupId") Integer videowallGroupId,
      @Param("decodeGroupId") String decodeGroupId, DecodeGroupUpdateInfo decodeGroupUpdateInfo);

  /**
   * 添加解码组到全光拼接屏组.
   *
   * @param videowallGroupId 全光拼接屏组的ID (required)
   * @param decodeGroup      (optional)
   * @return DecodeGroup
   */
  @RequestLine("PUT /kaito/decode-group/{videowallGroupId}")
  @Headers({
      "Content-Type: application/json",
      "Accept: application/json",
  })
  DecodeGroup kaitoDecodeGroupVideowallGroupIdPut(
      @Param("videowallGroupId") Integer videowallGroupId, DecodeGroupUpdateInfo decodeGroup);

  /**
   * 获取所有的输入卡.
   *
   * @param videowallGroupId (required)
   * @return List&lt;InputCard&gt;
   */
  @RequestLine("GET /kaito/input-card/list/{videowallGroupId}")
  @Headers({
      "Accept: application/json",
  })
  List<InputCard> kaitoInputCardListVideowallGroupIdGet(
      @Param("videowallGroupId") Integer videowallGroupId);

  /**
   * 获取IPC列表.
   *
   * @param videowallGroupId (required)
   * @return List&lt;IpcInfo&gt;
   */
  @RequestLine("GET /kaito/ipc/list/{videowallGroupId}")
  @Headers({
      "Accept: application/json",
  })
  List<IpcInfo> kaitoIpcListVideowallGroupIdGet(
      @Param("videowallGroupId") Integer videowallGroupId);

  /**
   * 清空图层.
   *
   * @param videowallGroupId (required)
   * @param videowallId      (required)
   * @return Object
   */
  @RequestLine("POST /kaito/videowall/clear/{videowallGroupId}/{videowallId}")
  @Headers({
      "Accept: application/json",
  })
  Object kaitoVideowallClearVideowallGroupIdVideowallIdPost(
      @Param("videowallGroupId") Integer videowallGroupId,
      @Param("videowallId") String videowallId);

  /**
   * 删除全光拼接屏组.
   *
   * @param id 全光拼接屏组的ID (required)
   * @return Object
   */
  @RequestLine("DELETE /kaito/videowall-group/{id}")
  @Headers({
      "Accept: application/json",
  })
  Object kaitoVideowallGroupIdDelete(@Param("id") Integer id);

  /**
   * 获取单个全光拼接屏组信息
   * 获取全光拼接屏组信息.
   *
   * @param id 全光拼接屏组的ID (required)
   * @return List&lt;VideoWallGroup&gt;
   */
  @RequestLine("GET /kaito/videowall-group/{id}")
  @Headers({
      "Accept: application/json",
  })
  VideoWallGroup kaitoVideowallGroupIdGet(@Param("id") Integer id);

  /**
   * 获取全光拼接屏信息.
   *
   * @param groupId 全光拼接屏组的ID (required)
   * @param wallId  全光拼接屏ID (required)
   * @return VideoWall
   */
  @RequestLine("GET /kaito/videowall/{groupId}/{wallId}")
  @Headers({
      "Accept: application/json",
  })
  VideoWall kaitoVideowallGroupIdWallIdGet(@Param("groupId") Integer groupId,
                                           @Param("wallId") Integer wallId);

  /**
   * 创建一个或者多个图层.
   *
   * @param groupId        全光拼接屏组的ID (required)
   * @param wallId         全光拼接屏ID (required)
   * @param user           (optional)
   * @param address        (optional)
   * @param videoWallLayer (optional)
   * @return InlineResponse2001
   */
  @RequestLine("PUT /kaito/videowall/{groupId}/{wallId}/layer?user={user}&address={address}")
  @Headers({
      "Content-Type: application/json",
      "Accept: application/json",
  })
  CreateLayerResponse kaitoVideowallGroupIdWallIdLayerPut(@Param("groupId") Integer groupId,
                                                          @Param("wallId") Integer wallId,
                                                          @Param("user") String user,
                                                          @Param("address") String address,
                                                          List<VideoWallLayer> videoWallLayer);

  /**
   * 创建一个或者多个图层.
   * Note, this is equivalent to the other <code>kaitoVideowallGroupIdWallIdLayerPut</code> method,
   * but with the query parameters collected into a single Map parameter. This
   * is convenient for services with optional query parameters, especially when
   * used with the {@link KaitoVideowallGroupIdWallIdLayerPutQueryParams} class that allows for
   * building up this map in a fluent style.
   *
   * @param groupId        全光拼接屏组的ID (required)
   * @param wallId         全光拼接屏ID (required)
   * @param videoWallLayer (optional)
   * @param queryParams    Map of query parameters as name-value pairs
   *                       <p>The following elements may be specified in the query map:</p>
   *                       <ul>
   *                       <li>user -  (optional)</li>
   *                       <li>address -  (optional)</li>
   *                       </ul>
   * @return InlineResponse2001
   */
  @RequestLine("PUT /kaito/videowall/{groupId}/{wallId}/layer?user={user}&address={address}")
  @Headers({
      "Content-Type: application/json",
      "Accept: application/json",
  })
  CreateLayerResponse kaitoVideowallGroupIdWallIdLayerPut(@Param("groupId") Integer groupId,
                                                          @Param("wallId") Integer wallId,
                                                          List<VideoWallLayer> videoWallLayer,
                                                          @QueryMap(encoded = true)
                                                          Map<String, Object> queryParams);

  /**
   * A convenience class for generating query parameters for the
   * <code>kaitoVideowallGroupIdWallIdLayerPut</code> method in a fluent style.
   */
  class KaitoVideowallGroupIdWallIdLayerPutQueryParams
      extends HashMap<String, Object> {
    private static final long serialVersionUID = 362498820763181269L;

    public KaitoVideowallGroupIdWallIdLayerPutQueryParams user(final String value) {
      put("user", EncodingUtils.encode(value));
      return this;
    }

    public KaitoVideowallGroupIdWallIdLayerPutQueryParams address(final String value) {
      put("address", EncodingUtils.encode(value));
      return this;
    }
  }

  @RequestLine("GET /kaito/videowall-group/list")
  @Headers({
      "Accept: application/json",
  })
  List<VideoWallGroup> kaitoVideowallGroupListGet();

  /**
   * 添加全光拼接屏组.
   *
   * @param createVideoWallGroupInfo (optional)
   * @return CreateVideoWallGroupResponse
   */
  @RequestLine("PUT /kaito/videowall-group")
  @Headers({
      "Content-Type: application/json",
      "Accept: application/json",
  })
  CreateVideoWallGroupResponse kaitoVideowallGroupPut(
      CreateVideoWallGroupInfo createVideoWallGroupInfo);

  /**
   * 删除图层.
   *
   * @param groupId 全光拼接屏组的ID (required)
   * @param wallId  全光拼接屏ID (required)
   * @param layerId 图层ID (required)
   */
  @RequestLine("DELETE /kaito/videowall/layer/{groupId}/{wallId}/{layerId}?user={user}&address={address}")
  @Headers({
      "Content-Type: application/json",
      "Accept: application/json",
  })
  DeleteLayerResponse kaitoVideowallLayerGroupIdWallIdLayerIdDelete(
      @Param("groupId") Integer groupId, @Param("wallId") Integer wallId,
      @Param("layerId") Integer layerId, @Param("user") String user,
      @Param("address") String address, Object body);

  /**
   * 删除图层.
   * Note, this is equivalent to the other <code>kaitoVideowallLayerGroupIdWallIdLayerIdDelete</code> method,
   * but with the query parameters collected into a single Map parameter. This
   * is convenient for services with optional query parameters, especially when
   * used with the {@link KaitoVideowallLayerGroupIdWallIdLayerIdDeleteQueryParams} class that allows for
   * building up this map in a fluent style.
   */
  @RequestLine("DELETE /kaito/videowall/layer/{groupId}/{wallId}/{layerId}?user={user}&address={address}")
  @Headers({
      "Content-Type: application/json",
      "Accept: application/json",
  })
  DeleteLayerResponse kaitoVideowallLayerGroupIdWallIdLayerIdDelete(
      @Param("groupId") Integer groupId, @Param("wallId") Integer wallId,
      @Param("layerId") Integer layerId, Object body,
      @QueryMap(encoded = true) Map<String, Object> queryParams);

  /**
   * A convenience class for generating query parameters for the
   * <code>kaitoVideowallLayerGroupIdWallIdLayerIdDelete</code> method in a fluent style.
   */
  class KaitoVideowallLayerGroupIdWallIdLayerIdDeleteQueryParams
      extends HashMap<String, Object> {
    private static final long serialVersionUID = 362498820763181268L;

    public KaitoVideowallLayerGroupIdWallIdLayerIdDeleteQueryParams user(final String value) {
      put("user", EncodingUtils.encode(value));
      return this;
    }

    public KaitoVideowallLayerGroupIdWallIdLayerIdDeleteQueryParams address(final String value) {
      put("address", EncodingUtils.encode(value));
      return this;
    }
  }

  /**
   * 更新图层信号源.
   *
   * @param groupId 全光拼接屏组的ID (required)
   * @param wallId  全光拼接屏ID (required)
   * @param layerId 图层ID (required)
   * @param user    (optional)
   * @param address (optional)
   * @return InlineResponse2002
   */
  @RequestLine("POST /kaito/videowall/layer/source/{groupId}/{wallId}/{layerId}?user={user}&address={address}")
  @Headers({
      "Content-Type: application/json",
      "Accept: application/json",
  })
  UpdateLayerResponse kaitoVideowallLayerSourceGroupIdWallIdLayerIdPost(
      @Param("groupId") Integer groupId, @Param("wallId") Integer wallId,
      @Param("layerId") Integer layerId, @Param("user") String user,
      @Param("address") String address, UpdateLayerSourceRequest updateLayerSourceRequest);

  /**
   * 更新图层信号源
   * 更新图层信号源
   * Note, this is equivalent to the other <code>kaitoVideowallLayerSourceGroupIdWallIdLayerIdPost</code> method,
   * but with the query parameters collected into a single Map parameter. This
   * is convenient for services with optional query parameters, especially when
   * used with the {@link KaitoVideowallLayerSourceGroupIdWallIdLayerIdPostQueryParams} class that allows for
   * building up this map in a fluent style.
   *
   * @param groupId     全光拼接屏组的ID (required)
   * @param wallId      全光拼接屏ID (required)
   * @param layerId     图层ID (required)
   * @param requestBody (optional)
   * @param queryParams Map of query parameters as name-value pairs
   *                    <p>The following elements may be specified in the query map:</p>
   *                    <ul>
   *                    <li>user -  (optional)</li>
   *                    <li>address -  (optional)</li>
   *                    </ul>
   * @return InlineResponse2002
   */
  @RequestLine("POST /kaito/videowall/layer/source/{groupId}/{wallId}/{layerId}?user={user}&address={address}")
  @Headers({
      "Content-Type: application/json",
      "Accept: application/json",
  })
  UpdateLayerResponse kaitoVideowallLayerSourceGroupIdWallIdLayerIdPost(
      @Param("groupId") Integer groupId, @Param("wallId") Integer wallId,
      @Param("layerId") Integer layerId, List<Object> requestBody,
      @QueryMap(encoded = true) Map<String, Object> queryParams);

  /**
   * A convenience class for generating query parameters for the
   * <code>kaitoVideowallLayerSourceGroupIdWallIdLayerIdPost</code> method in a fluent style.
   */
  class KaitoVideowallLayerSourceGroupIdWallIdLayerIdPostQueryParams
      extends HashMap<String, Object> {
    private static final long serialVersionUID = 362498820763181266L;

    public KaitoVideowallLayerSourceGroupIdWallIdLayerIdPostQueryParams user(final String value) {
      put("user", EncodingUtils.encode(value));
      return this;
    }

    public KaitoVideowallLayerSourceGroupIdWallIdLayerIdPostQueryParams address(
        final String value) {
      put("address", EncodingUtils.encode(value));
      return this;
    }
  }

  /**
   * 更新图层位置.
   *
   * @param groupId     全光拼接屏组的ID (required)
   * @param wallId      全光拼接屏ID (required)
   * @param layerId     图层ID (required)
   * @param user        (optional)
   * @param address     (optional)
   * @param layerWindow (optional)
   * @return InlineResponse2002
   */
  @RequestLine("POST /kaito/videowall/layer/window/{groupId}/{wallId}/{layerId}?user={user}&address={address}")
  @Headers({
      "Content-Type: application/json",
      "Accept: application/json",
  })
  UpdateLayerResponse kaitoVideowallLayerWindowGroupIdWallIdLayerIdPost(
      @Param("groupId") Integer groupId, @Param("wallId") Integer wallId,
      @Param("layerId") Integer layerId, @Param("user") String user,
      @Param("address") String address, LayerWindow layerWindow);

  /**
   * 更新图层位置
   * 更新图层位置
   * Note, this is equivalent to the other <code>kaitoVideowallLayerWindowGroupIdWallIdLayerIdPost</code> method,
   * but with the query parameters collected into a single Map parameter. This
   * is convenient for services with optional query parameters, especially when
   * used with the {@link KaitoVideowallLayerWindowGroupIdWallIdLayerIdPostQueryParams} class that allows for
   * building up this map in a fluent style.
   *
   * @param groupId     全光拼接屏组的ID (required)
   * @param wallId      全光拼接屏ID (required)
   * @param layerId     图层ID (required)
   * @param layerWindow (optional)
   * @param queryParams Map of query parameters as name-value pairs
   *                    <p>The following elements may be specified in the query map:</p>
   *                    <ul>
   *                    <li>user -  (optional)</li>
   *                    <li>address -  (optional)</li>
   *                    </ul>
   * @return InlineResponse2002
   */
  @RequestLine("POST /kaito/videowall/layer/window/{groupId}/{wallId}/{layerId}?user={user}&address={address}")
  @Headers({
      "Content-Type: application/json",
      "Accept: application/json",
  })
  UpdateLayerResponse kaitoVideowallLayerWindowGroupIdWallIdLayerIdPost(
      @Param("groupId") Integer groupId, @Param("wallId") Integer wallId,
      @Param("layerId") Integer layerId, LayerWindow layerWindow,
      @QueryMap(encoded = true) Map<String, Object> queryParams);

  /**
   * A convenience class for generating query parameters for the
   * <code>kaitoVideowallLayerWindowGroupIdWallIdLayerIdPost</code> method in a fluent style.
   */
  class KaitoVideowallLayerWindowGroupIdWallIdLayerIdPostQueryParams
      extends HashMap<String, Object> {
    private static final long serialVersionUID = 362498820763181267L;

    public KaitoVideowallLayerWindowGroupIdWallIdLayerIdPostQueryParams user(final String value) {
      put("user", EncodingUtils.encode(value));
      return this;
    }

    public KaitoVideowallLayerWindowGroupIdWallIdLayerIdPostQueryParams address(
        final String value) {
      put("address", EncodingUtils.encode(value));
      return this;
    }
  }

  /**
   * 更新图层上下层.
   *
   * @param groupId                  全光拼接屏组的ID (required)
   * @param wallId                   全光拼接屏ID (required)
   * @param layerId                  图层ID (required)
   * @param user                     (optional)
   * @param address                  (optional)
   * @param updateLayerZorderRequest (optional)
   * @return InlineResponse2003
   */
  @RequestLine("POST /kaito/videowall/layer/zorder/{groupId}/{wallId}/{layerId}?user={user}&address={address}")
  @Headers({
      "Content-Type: application/json",
      "Accept: application/json",
  })
  UpdateLayerZorderResponse kaitoVideowallLayerZorderGroupIdWallIdLayerIdPost(
      @Param("groupId") Integer groupId, @Param("wallId") Integer wallId,
      @Param("layerId") Integer layerId, @Param("user") String user,
      @Param("address") String address, UpdateLayerZorderRequest updateLayerZorderRequest);

  /**
   * 更新图层上下层
   * 更新图层上下层
   * Note, this is equivalent to the other <code>kaitoVideowallLayerZorderGroupIdWallIdLayerIdPost</code> method,
   * but with the query parameters collected into a single Map parameter. This
   * is convenient for services with optional query parameters, especially when
   * used with the {@link KaitoVideowallLayerZorderGroupIdWallIdLayerIdPostQueryParams} class that allows for
   * building up this map in a fluent style.
   *
   * @param groupId                  全光拼接屏组的ID (required)
   * @param wallId                   全光拼接屏ID (required)
   * @param layerId                  图层ID (required)
   * @param updateLayerZorderRequest (optional)
   * @param queryParams              Map of query parameters as name-value pairs
   *                                 <p>The following elements may be specified in the query map:</p>
   *                                 <ul>
   *                                 <li>user -  (optional)</li>
   *                                 <li>address -  (optional)</li>
   *                                 </ul>
   * @return InlineResponse2003
   */
  @RequestLine("POST /kaito/videowall/layer/zorder/{groupId}/{wallId}/{layerId}?user={user}&address={address}")
  @Headers({
      "Content-Type: application/json",
      "Accept: application/json",
  })
  UpdateLayerZorderResponse kaitoVideowallLayerZorderGroupIdWallIdLayerIdPost(
      @Param("groupId") Integer groupId, @Param("wallId") Integer wallId,
      @Param("layerId") Integer layerId, UpdateLayerZorderRequest updateLayerZorderRequest,
      @QueryMap(encoded = true) Map<String, Object> queryParams);

  /**
   * A convenience class for generating query parameters for the
   * <code>kaitoVideowallLayerZorderGroupIdWallIdLayerIdPost</code> method in a fluent style.
   */
  class KaitoVideowallLayerZorderGroupIdWallIdLayerIdPostQueryParams
      extends HashMap<String, Object> {
    private static final long serialVersionUID = 362498820763181264L;

    public KaitoVideowallLayerZorderGroupIdWallIdLayerIdPostQueryParams user(final String value) {
      put("user", EncodingUtils.encode(value));
      return this;
    }

    public KaitoVideowallLayerZorderGroupIdWallIdLayerIdPostQueryParams address(
        final String value) {
      put("address", EncodingUtils.encode(value));
      return this;
    }
  }
}
