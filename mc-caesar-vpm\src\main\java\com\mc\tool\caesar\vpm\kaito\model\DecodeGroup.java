package com.mc.tool.caesar.vpm.kaito.model;

import com.mc.tool.caesar.vpm.pages.splicingscreen.TreeViewItem;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 解码组.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class DecodeGroup implements TreeViewItem {
  /**
   * 解码组ID.
   */
  private Integer id;
  /**
   * 解码组名称.
   */
  private String name;
  /**
   * TX权限数组的base64编码.
   **/
  private String rights;
  /**
   * 解码卡是否可裁剪.
   */
  private boolean croppable;

  private List<DecodeCard> cards = null;

  @Override
  public String cellToString() {
    return String.format("[%d]%s", this.getId(), this.getName());
  }
}
