package com.mc.tool.caesar.api.utils;

import com.mc.tool.caesar.api.exception.ConfigException;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.CharacterCodingException;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;


/**
 * .
 */
public final class CfgReader {

  private static final Logger LOG = Logger.getLogger(CfgReader.class.getName());
  private final byte[] data;
  private int index;
  private final Charset charset = StandardCharsets.UTF_8;
  private final CharsetDecoder decoder = this.charset.newDecoder();
  private static final byte[] RAW =
      new byte[]{43, 12, -53, 37, -73, -26, 77, -24, -26, -57, 22, 112, 32, 0, -106, 53};

  private CfgReader(byte[] data) {
    this.data = data;
    this.index = 0;
  }

  /**
   * CfgReader.
   *
   * @param is         输入字节流.
   * @param secureMode 是否有加密
   * @throws ConfigException ConfigException
   */
  public CfgReader(InputStream is, boolean secureMode) throws ConfigException {
    this(secureMode ? CfgReader.secureReadAll(is) : CfgReader.readAll(is));
  }

  public CfgReader(InputStream is) {
    this(CfgReader.readAll(is));
  }

  public int size() {
    return this.data.length;
  }

  public int available() {
    return this.data.length - this.index;
  }

  public String readString(int count) throws ConfigException {
    return this.readString(count, 4);
  }

  /**
   * .
   *
   * @param count    count
   * @param junkData junkData
   * @throws ConfigException ConfigException
   */
  public String readString(int count, int junkData) throws ConfigException {
    try {
      boolean finished = false;
      byte[] data = new byte[count];
      int dataCount = 0;
      block6:
      for (int i = 0; i < count + junkData; ++i) {
        int cc = this.read();
        switch (cc) {
          case -1: {
            throw new ConfigException(CfgError.EOF);
          }
          case 0: {
            finished = true;
            continue block6;
          }
          default: {
            if (finished || i >= count) {
              continue block6;
            }
            data[i] = (byte) cc;
            ++dataCount;
          }
        }
      }

      return this.decoder.decode(ByteBuffer.wrap(data, 0, dataCount)).toString();
    } catch (CharacterCodingException ex) {
      LOG.log(Level.WARNING, "Read string fail!", ex);
      return "";
    } catch (Exception ex) {
      LOG.log(Level.WARNING, "Read string fail!", ex);
      return "";
    }
  }


  public boolean readBoolean() throws ConfigException {
    return 0 != (1 & this.readInteger());
  }

  public byte[] readByteArray() {
    return this.readByteArray(this.data.length - this.index);
  }

  /**
   * WARNING - Removed try catching itself - possible behaviour change.
   */
  public byte[] readByteArray(int maxSize) {
    if (maxSize <= 0) {
      return new byte[0];
    }
    int dataLength = Math.min(this.data.length, this.index + maxSize);
    try {
      byte[] arrby = Arrays.copyOfRange(this.data, this.index, dataLength);
      return arrby;
    } finally {
      this.index = dataLength;
    }
  }

  /**
   * 读取加密后的数据.
   *
   * @throws ConfigException ConfigException
   */
  public byte[] readSecureByteArray() throws ConfigException {
    return secureTransfer(readByteArray());
  }

  public synchronized int readByteValue() throws ConfigException {
    int b0 = this.read();
    return b0;
  }

  /**
   * read2ByteValue.
   */
  public synchronized int read2ByteValue() throws ConfigException {
    int b0 = this.read();
    int b1 = this.read();
    return b0 + (b1 << 8);
  }

  public byte read4ByteValue() throws ConfigException {
    int intValue = this.readInteger();
    return (byte) (255 & intValue);
  }

  /**
   * readInteger.
   */
  public synchronized int readInteger() throws ConfigException {
    int b0 = this.read();
    int b1 = this.read();
    int b2 = this.read();
    int b3 = this.read();
    return b0 + (b1 << 8) + (b2 << 16) + (b3 << 24);
  }

  public int readInteger(int minValue, int maxValue) throws ConfigException {
    return Math.min(maxValue, Math.max(minValue, this.readInteger()));
  }

  /**
   * 读取一个long值.
   *
   * @return long值.
   */
  public synchronized long readLong() throws ConfigException {
    return ByteBuffer.wrap(readByteArray(8)).order(ByteOrder.LITTLE_ENDIAN).getLong();
  }

  /**
   * read short.
   *
   * @return short数值.
   * @throws ConfigException 读取失败
   */
  public synchronized short readShort() throws ConfigException {
    int b0 = this.read();
    int b1 = this.read();
    return (short) ((b1 << 8) + b0);
  }

  /**
   * readEnum.
   */
  public int readEnum(int maxEnum) throws ConfigException {
    int value = this.readInteger();
    if (value < 0) {
      throw new ConfigException(CfgError.ENUM);
    }
    if (value >= maxEnum) {
      value = 0;
    }
    return Math.min(value, maxEnum - 1);
  }

  private int read() throws ConfigException {
    if (this.index < this.data.length) {
      return 255 & this.data[this.index++];
    }
    throw new ConfigException(CfgError.EOF);
  }

  /**
   * WARNING - Removed try catching itself - possible behaviour change.
   */
  private static byte[] readAll(InputStream is) {
    ByteArrayOutputStream baos = new ByteArrayOutputStream(10240);
    byte[] buffer = new byte[10240];
    try {
      int read;
      do {
        if ((read = is.read(buffer)) <= 0) {
          continue;
        }
        baos.write(buffer, 0, read);
      } while (read >= 0);
    } catch (IOException ioe) {
      Logger.getLogger(CfgReader.class.toString()).log(Level.SEVERE, ioe.getMessage(), ioe);
    }
    return baos.toByteArray();
  }

  /**
   * 加密数据.
   *
   * @param data data
   * @throws ConfigException          ConfigException
   * @throws IOException              IOException
   * @throws NoSuchPaddingException   NoSuchPaddingException
   * @throws NoSuchAlgorithmException NoSuchAlgorithmException
   */
  private static byte[] secureTransfer(byte[] data) throws ConfigException {
    ByteArrayOutputStream baos = new ByteArrayOutputStream(10240);
    baos.write(255);
    baos.write(255);
    try {
      SecretKeySpec skeySpec = new SecretKeySpec(RAW, "AES");
      Cipher cipher;
      cipher = Cipher.getInstance("AES");
      cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
      data = cipher.doFinal(data);
      baos.write(data);
    } catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidKeyException
        | IllegalBlockSizeException | BadPaddingException | IOException ex) {
      throw new ConfigException(CfgError.IO);
    }

    return baos.toByteArray();
  }

  /**
   * WARNING - Removed try catching itself - possible behaviour change.
   */
  private static byte[] secureReadAll(InputStream is) throws ConfigException {
    ByteArrayOutputStream baos = new ByteArrayOutputStream(10240);
    byte[] buffer = new byte[10240];

    boolean encrypted = false;
    try {
      int read;
      int b0 = is.read();
      int b1 = is.read();
      if (b0 == 255 && b1 == 255) {
        encrypted = true;
      } else {
        baos.write(b0);
        baos.write(b1);
      }
      do {
        if ((read = is.read(buffer)) <= 0) {
          continue;
        }
        baos.write(buffer, 0, read);
      } while (read >= 0);
    } catch (IOException ioe) {
      Logger.getLogger(CfgReader.class.toString()).log(Level.SEVERE, ioe.getMessage(), ioe);
    }
    byte[] data = baos.toByteArray();
    if (encrypted) {
      try {
        SecretKeySpec skeySpec = new SecretKeySpec(RAW, "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec);
        data = cipher.doFinal(data);
      } catch (NoSuchAlgorithmException | IllegalBlockSizeException
          | BadPaddingException | InvalidKeyException | NoSuchPaddingException ex) {
        throw new ConfigException(CfgError.IO);
      }
    }
    return data;
  }
}
