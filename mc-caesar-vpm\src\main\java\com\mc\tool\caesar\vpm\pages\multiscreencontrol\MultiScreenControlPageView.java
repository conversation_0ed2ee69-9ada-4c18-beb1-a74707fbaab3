package com.mc.tool.caesar.vpm.pages.multiscreencontrol;

import com.mc.tool.framework.utility.InjectorProvider;
import java.io.IOException;
import java.net.URL;
import javafx.fxml.FXMLLoader;
import javafx.scene.layout.VBox;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class MultiScreenControlPageView extends VBox {

  @Getter private MultiScreenControlPageController controller;

  /** Contructor. */
  public MultiScreenControlPageView() {
    try {
      controller =
          InjectorProvider.getInjector().getInstance(MultiScreenControlPageController.class);
      URL location =
          getClass()
              .getResource(
                  "/com/mc/tool/caesar/vpm/pages/multiscreencontrol/multiscreencontrol_view.fxml");
      FXMLLoader loader = new FXMLLoader(location);
      loader.setController(controller);
      loader.setRoot(this);
      loader.load();
    } catch (IOException exc) {
      log.warn("Can not load multiscreencontrol_view.fxml", exc);
    }
  }
}
