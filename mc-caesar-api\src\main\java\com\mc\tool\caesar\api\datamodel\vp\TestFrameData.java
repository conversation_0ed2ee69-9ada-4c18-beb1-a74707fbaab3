package com.mc.tool.caesar.api.datamodel.vp;

import com.mc.tool.caesar.api.io.NormalStruct;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

/**
 * 测试画面配置,VP7大屏使用.
 */
@SuppressFBWarnings("URF_UNREAD_PUBLIC_OR_PROTECTED_FIELD")
public class TestFrameData extends NormalStruct {
  public Unsigned8 mode = new Unsigned8();
  public Unsigned32 color = new Unsigned32();
  public Unsigned8 speed = new Unsigned8();
  public Unsigned8 alpha = new Unsigned8();
  public Unsigned8[] reserved = array(new Unsigned8[1]);

  public void setMode(short mode) {
    this.mode.set(mode);
  }

  public void setColor(long color) {
    this.color.set(color);
  }

  public void setSpeed(short speed) {
    this.speed.set(speed);
  }

  public short getMode() {
    return mode.get();
  }

  public long getColor() {
    return color.get();
  }

  public short getSpeed() {
    return speed.get();
  }

  public void setAlpha(short alpha) {
    this.alpha.set(alpha);
  }

  public short getAlpha() {
    return alpha.get();
  }
}
