package com.mc.tool.caesar.api.exception;

import java.io.IOException;

/**
 * .
 */
public class UnexpectedResponseException extends IOException {

  /**
   * .
   */
  private static final long serialVersionUID = 1L;
  private final int unexpectedByte;

  /**
   * .
   */
  public UnexpectedResponseException(int unexpectedByte) {
    super(String.format("%02X was received but was not expected!", unexpectedByte));
    this.unexpectedByte = unexpectedByte;
  }

  /**
   * .
   */
  public UnexpectedResponseException(int expectedByte, int readByte) {
    super(String.format("%02X was expected but %02X was received!", expectedByte, readByte));
    this.unexpectedByte = readByte;
  }

  public int getUnexpectedByte() {
    return this.unexpectedByte;
  }
}

