package com.mc.tool.caesar.vpm.util.macrokey.datamodel;

import com.mc.tool.caesar.api.CaesarConstants.FunctionKey.Cmd.Command;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public class MacroKeyItem {
  @Getter @Setter
  private ObjectProperty<Command> cmd = new SimpleObjectProperty<>(Command.NoFunction);

  @Getter @Setter private ObjectProperty<MacroParam> param1 = new SimpleObjectProperty<>();

  @Getter @Setter private ObjectProperty<MacroParam> param2 = new SimpleObjectProperty<>();

  @Getter @Setter private int functionKeyId = -1;

  public MacroKeyItem() {}

  /** 删除宏信息. */
  public void delete() {
    cmd.set(Command.NoFunction);
    param1.set(null);
    param2.set(null);
  }

  /** 判断数据是否一致. */
  public boolean isEqual(Object obj) {
    // 不重载equals的原因是，避免tableview显示时更新混乱的问题
    if (obj instanceof MacroKeyItem) {
      MacroKeyItem item = (MacroKeyItem) obj;
      return cmd.get() == item.getCmd().get()
          && (param1.get() == item.getParam1().get()
              || param1.get() != null && param1.get().equals(item.getParam1().get()))
          && (param2.get() == item.getParam2().get()
              || param2.get() != null && param2.get().equals(item.getParam2().get()));
    } else {
      return false;
    }
  }
}
