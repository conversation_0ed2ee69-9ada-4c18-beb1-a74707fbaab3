package com.mc.tool.caesar.vpm.pages.operation.controller;

import com.mc.common.util.WeakAdapter;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.layout.HBox;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import lombok.Getter;
import lombok.Setter;
import org.controlsfx.control.MasterDetailPane;

/**
 * .
 */
public class CaesarOperationFuncController implements Initializable {

  @FXML private MasterDetailPane masterDetailPane;
  @FXML private VBox showBtnContainer;

  @FXML private HBox toolBoxContainer;
  @FXML private VBox sourceListContainer;
  @FXML @Getter private StackPane functionViewContainer;
  @FXML private VBox propertyContainer;

  @Getter @Setter
  protected ObservableList<Node> toolBoxContent = FXCollections.observableArrayList();

  @Getter @Setter
  protected ObservableList<Node> sourceListContent = FXCollections.observableArrayList();

  @Getter protected ObjectProperty<Node> funcViewContent = new SimpleObjectProperty<>();

  @Getter @Setter
  protected ObservableList<Node> propertyContent = FXCollections.observableArrayList();

  protected WeakAdapter weakAdapter = new WeakAdapter();

  /** Constructor. */
  public CaesarOperationFuncController() {
    toolBoxContent.addListener(
        weakAdapter.wrap(
            (ListChangeListener<Node>)
                (change) -> toolBoxContainer.getChildren().setAll(toolBoxContent)));

    sourceListContent.addListener(
        weakAdapter.wrap(
            (ListChangeListener<Node>)
                (change) -> sourceListContainer.getChildren().setAll(sourceListContent)));

    funcViewContent.addListener(
        weakAdapter.wrap(
            (obs, oldVal, newVal) ->
                functionViewContainer.getChildren().setAll(funcViewContent.get())));

    propertyContent.addListener(
        weakAdapter.wrap(
            (ListChangeListener<Node>)
                (change) -> propertyContainer.getChildren().setAll(propertyContent)));
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    showBtnContainer.managedProperty().bind(showBtnContainer.visibleProperty());
    showBtnContainer.visibleProperty().bind(masterDetailPane.showDetailNodeProperty().not());
  }

  @FXML
  public void onShowProperty() {
    masterDetailPane.setShowDetailNode(true);
  }

  @FXML
  public void onHideProperty() {
    masterDetailPane.setShowDetailNode(false);
  }
}
