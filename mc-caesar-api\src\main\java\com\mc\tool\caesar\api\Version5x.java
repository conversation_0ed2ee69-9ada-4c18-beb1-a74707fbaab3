package com.mc.tool.caesar.api;

import com.mc.tool.caesar.api.datamodel.BranchData;
import com.mc.tool.caesar.api.datamodel.ConfigMetaData;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.api.datamodel.MatrixData;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.datamodel.MultiviewData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.datamodel.SourceCropData;
import com.mc.tool.caesar.api.datamodel.SystemConfigData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.datamodel.UserGroupData;
import com.mc.tool.caesar.api.version.v3x.ExtenderDataConverter3x;
import com.mc.tool.caesar.api.version.v4x.FunctionKeyDataConverter4x;
import com.mc.tool.caesar.api.version.v4x.GroupDataConverter4x;
import com.mc.tool.caesar.api.version.v5x.ConsoleDataConverter5x;
import com.mc.tool.caesar.api.version.v5x.CpuDataConverter5x;
import com.mc.tool.caesar.api.version.v5x.MultiScreenDataConverter5x;
import com.mc.tool.caesar.api.version.v5x.UserDataConverter5x;
import java.util.Map;

/**
 * .
 */
public class Version5x extends Version50 {

  public static final int VERSION_VALUE = 0x50000;
  public static final int CONFIG_DATA_SIZE = 3602298;
  public static final int META_DATA_SIZE = 0xa4;
  public static final int SYSTEM_DATA_SIZE = 0x358;
  public static final int CPU_DATA_SIZE = 0x68;
  public static final int CON_DATA_SIZE = 0x2a2;
  public static final int EXT_DATA_SIZE = 0xab;
  public static final int USER_DATA_SIZE = 0x3cb;
  public static final int USER_GROUP_DATA_SIZE = 0x262;
  public static final int MULTI_SCREEN_DATA_SIZE = 0x9e;
  public static final int MACRO_DATA_SIZE = 0x6b;
  public static final int TX_GROUP_SIZE = 0x2e;
  public static final int MATRIX_DATA_SIZE = 0x52;
  public static final int BRANCH_DATA_SIZE = 0x9;
  public static final int PORT_DATA_SIZE = 0x14;
  public static final int BOARD_DATA_SIZE = 0x35;
  public static final int MULTIVIEW_DATA_SIZE = 0x2d;
  public static final int SOURCE_CROP_DATA_SIZE = 0x3c;

  public static final int MAX_USER_NUMBER = 256;
  public static final int MAX_USER_GROUP_NUMBER = 64;
  public static final int MAX_MULTI_SCREEN_GROUP_NUMBER = 150;
  public static final int MAX_EXT_NUMBER = 4000;
  public static final int MAX_CON_NUMBER = 2000;
  public static final int MAX_CPU_NUMBER = 2000;
  public static final int MAX_PORT_NUMBER = 6096;
  public static final int MAX_MACROT_NUMBER = 8192;
  public static final int MAX_TX_GROUP_NUMBER = 500;
  public static final int MAX_BRANCH_NUMBER = 64;
  public static final int MAX_MATRIX_GRID_NUMBER = 16;
  public static final int MAX_MULTIVIEW_NUMBER = 250;
  public static final int MAX_SOURCE_CROP_NUMBER = 256;

  public static final int SYSTEMCONFIGDATA_OFFSET = 0xa4;
  public static final int CPUDATA_OFFSET = 0x3fc;
  public static final int CONDATA_OFFSET = 0x3307c;
  public static final int EXTDATA_OFFSET = 0x17c21c;
  public static final int USERDATA_OFFSET = 0x2231fc;
  public static final int USERGROUPDATA_OFFSET = 0x25fcfc;
  public static final int MULTISCREENDATA_OFFSET = 0x26957c;
  public static final int MACRODATA_OFFSET = 0x26f210;
  public static final int TX_GROUP_DATA_OFFSET = 0x345730;
  public static final int BRANCH_DATA_OFFSET = 0x34b108;
  public static final int MATRIXGRIDDATA_OFFSET = 0x345210;
  public static final int PORTDATA_OFFSET = 0x351b3a;
  public static final int MULTIVIEW_DATA_OFFSET = 0x34b348;
  public static final int SOURCE_CROP_DATA_OFFSET = 0x34df3a;

  public static final int MAX_MULTIVIEW_SOURCE = 8;


  protected Version5x() {
    Map<Class, Integer> v5x = classSizes;
    v5x.clear();
    v5x.put(ConfigMetaData.class, META_DATA_SIZE);
    v5x.put(SystemConfigData.class, SYSTEM_DATA_SIZE);
    v5x.put(CpuData.class, CPU_DATA_SIZE);
    v5x.put(ConsoleData.class, CON_DATA_SIZE);
    v5x.put(ExtenderData.class, EXT_DATA_SIZE);
    v5x.put(UserData.class, USER_DATA_SIZE);
    v5x.put(UserGroupData.class, USER_GROUP_DATA_SIZE);
    v5x.put(MultiScreenData.class, MULTI_SCREEN_DATA_SIZE);
    v5x.put(FunctionKeyData.class, MACRO_DATA_SIZE);
    v5x.put(MatrixData.class, MATRIX_DATA_SIZE);
    v5x.put(PortData.class, PORT_DATA_SIZE);
    v5x.put(ModuleData.class, BOARD_DATA_SIZE);
    v5x.put(TxRxGroupData.class, TX_GROUP_SIZE);
    v5x.put(BranchData.class, BRANCH_DATA_SIZE);
    v5x.put(MultiviewData.class, MULTIVIEW_DATA_SIZE);
    v5x.put(SourceCropData.class, SOURCE_CROP_DATA_SIZE);

    v5x = classCounts;
    v5x.clear();
    v5x.put(CpuData.class, MAX_CPU_NUMBER);
    v5x.put(ConsoleData.class, MAX_CON_NUMBER);
    v5x.put(ExtenderData.class, MAX_EXT_NUMBER);
    v5x.put(UserData.class, MAX_USER_NUMBER);
    v5x.put(UserGroupData.class, MAX_USER_GROUP_NUMBER);
    v5x.put(MultiScreenData.class, MAX_MULTI_SCREEN_GROUP_NUMBER);
    v5x.put(FunctionKeyData.class, MAX_MACROT_NUMBER);
    v5x.put(MatrixData.class, MAX_MATRIX_GRID_NUMBER);
    v5x.put(PortData.class, MAX_PORT_NUMBER);
    v5x.put(TxRxGroupData.class, MAX_TX_GROUP_NUMBER);
    v5x.put(BranchData.class, MAX_BRANCH_NUMBER);
    v5x.put(MultiviewData.class, MAX_MULTIVIEW_NUMBER);
    v5x.put(SourceCropData.class, MAX_SOURCE_CROP_NUMBER);

    v5x = classOffsets;
    v5x.clear();
    v5x.put(SystemConfigData.class, SYSTEMCONFIGDATA_OFFSET);
    v5x.put(CpuData.class, CPUDATA_OFFSET);
    v5x.put(ConsoleData.class, CONDATA_OFFSET);
    v5x.put(ExtenderData.class, EXTDATA_OFFSET);
    v5x.put(UserData.class, USERDATA_OFFSET);
    v5x.put(UserGroupData.class, USERGROUPDATA_OFFSET);
    v5x.put(MultiScreenData.class, MULTISCREENDATA_OFFSET);
    v5x.put(FunctionKeyData.class, MACRODATA_OFFSET);
    v5x.put(MatrixData.class, MATRIXGRIDDATA_OFFSET);
    v5x.put(PortData.class, PORTDATA_OFFSET);
    v5x.put(TxRxGroupData.class, TX_GROUP_DATA_OFFSET);
    v5x.put(BranchData.class, BRANCH_DATA_OFFSET);
    v5x.put(MultiviewData.class, MULTIVIEW_DATA_OFFSET);
    v5x.put(SourceCropData.class, SOURCE_CROP_DATA_OFFSET);

    classConverter.put(FunctionKeyData.class,
        new FunctionKeyDataConverter4x(getClassSize(FunctionKeyData.class)));
    classConverter
        .put(ExtenderData.class, new ExtenderDataConverter3x(getClassSize(ExtenderData.class)));
    classConverter.put(CpuData.class, new CpuDataConverter5x(getClassSize(CpuData.class)));
    classConverter.put(MultiScreenData.class, new MultiScreenDataConverter5x());
    classConverter
        .put(TxRxGroupData.class, new GroupDataConverter4x(getClassSize(TxRxGroupData.class)));
    classConverter.put(ConsoleData.class, new ConsoleDataConverter5x());
    classConverter.put(UserData.class, new UserDataConverter5x(getClassSize(UserData.class)));
  }

  @Override
  public String getName() {
    return "DKM5";
  }

  @Override
  public int getVersionValue() {
    return VERSION_VALUE;
  }

  @Override
  public int getMetaDataSize() {
    return META_DATA_SIZE;
  }


  @Override
  public int getConfigDataSize() {
    return CONFIG_DATA_SIZE;
  }

  @Override
  public boolean supportCropTx() {
    return true;
  }

  @Override
  public boolean hasBranchData() {
    return true;
  }

  @Override
  public boolean hasMultiviewData() {
    return true;
  }

  @Override
  public boolean hasSourceCropData() {
    return true;
  }

  @Override
  public boolean hasExtArgHighCompressRatio() {
    return true;
  }

  @Override
  public boolean hasExtArgOsdMenu() {
    return true;
  }

  @Override
  public boolean hasExtArgIcronEnable() {
    return true;
  }

  @Override
  public boolean hasExtArgUartBaudrate() {
    return true;
  }

  @Override
  public boolean hasExtArgDoubledpEnable() {
    return true;
  }
}
