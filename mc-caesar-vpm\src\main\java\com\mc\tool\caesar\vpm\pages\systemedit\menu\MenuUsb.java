package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.common.util.PlatformUtility;
import com.mc.common.validation.constraints.BlackIntegerList;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarData2VisualDataModel;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.usb.UsbBindingConfigBean;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.usb.UsbUtility;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.utility.FormDialog;
import com.mc.tool.framework.utility.SimpleFxFormValidator;
import com.mc.tool.framework.utility.UndecoratedAlert;
import java.lang.annotation.ElementType;
import java.util.Optional;
import javafx.collections.FXCollections;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.ButtonType;
import javafx.scene.control.ComboBox;
import javafx.scene.control.MenuItem;
import javax.validation.Configuration;
import javax.validation.Validation;
import javax.validation.ValidationException;
import javax.validation.ValidatorFactory;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.HibernateValidatorConfiguration;
import org.hibernate.validator.cfg.ConstraintMapping;
import org.hibernate.validator.cfg.GenericConstraintDef;
import org.hibernate.validator.cfg.defs.MaxDef;
import org.hibernate.validator.cfg.defs.MinDef;

/**
 * .
 */
@Slf4j
public class MenuUsb extends MenuItem {
  private final SystemEditControllable controllable;
  private final CaesarDeviceController deviceController;
  private final CaesarData2VisualDataModel converter;
  private final CaesarMatrix matrix;

  /**
   * 绑定USB端口菜单.
   *
   * @param controllable 系统编辑控制
   * @param matrix matrix
   * @param converter converter
   * @param deviceController 设备管理器
   */
  public MenuUsb(
      SystemEditControllable controllable,
      CaesarMatrix matrix,
      CaesarData2VisualDataModel converter,
      CaesarDeviceController deviceController) {
    this.controllable = controllable;
    this.matrix = matrix;
    this.converter = converter;
    this.deviceController = deviceController;
    setText(Bundle.NbBundle.getMessage("menu.usb"));
    setOnAction((event) -> onAction());
  }

  private void onAction() {
    UsbBindingConfigBean bean = new UsbBindingConfigBean();
    FormDialog<UsbBindingConfigBean> form =
        new FormDialog<>(
            bean,
            CaesarI18nCommonResource.getResourceBundle("usb_binding_bean"),
            getParentPopup().getOwnerWindow());
    form.setTitle(Bundle.NbBundle.getMessage("menu.usb.dialog_title"));

    ConfigDataManager dataManager = deviceController.getDataModel().getConfigDataManager();
    // 初始化扩展器列表
    ComboBox<CpuData> comboBox = form.getElementControl("binding", ComboBox.class);
    if (comboBox != null) {
      comboBox.setItems(
          FXCollections.observableArrayList(UsbUtility.getBindableCpus(dataManager, matrix)));
    }
    // 更新扩展器列表
    form.getBufferedElement("type")
        .addListener(
            (obs, oldVal, newVal) -> {
              ComboBox bindingCombox = form.getElementControl("binding", ComboBox.class);
              if (bindingCombox != null) {
                if (newVal == UsbBindingConfigBean.UsbType.USB_TX) {
                  bindingCombox.setItems(
                      FXCollections.observableArrayList(
                          UsbUtility.getBindableCpus(dataManager, matrix)));
                } else {
                  bindingCombox.setItems(
                      FXCollections.observableArrayList(
                          UsbUtility.getBindableConsoles(dataManager, matrix)));
                }
              }
            });
    // 限制端口范围

    form.getFxform()
        .setFxFormValidator(
            new PortValidater(
                matrix.getMatrixData().getFirstPort(),
                matrix.getMatrixData().getLastPort(),
                Utilities.getUsedPorts(deviceController.getDataModel()).stream()
                    .mapToInt(i -> i)
                    .toArray()));

    Optional<ButtonType> result = form.showAndWaitWithCommit();
    if (result.isPresent() && result.get() == ButtonType.APPLY) {
      addUsbBinding(deviceController.getDataModel(), bean);
    }
  }

  protected void addUsbBinding(CaesarSwitchDataModel model, UsbBindingConfigBean config) {
    ExtenderData extenderData = model.getConfigDataManager().getFreeExtender();
    if (extenderData == null) {
      UndecoratedAlert alert = new UndecoratedAlert(AlertExType.WARNING);
      alert.initOwner(getParentPopup().getOwnerWindow());
      alert.setContentText(Bundle.NbBundle.getMessage("menu.usb.create_extender_fail"));
      alert.showAndWait();
      return;
    }

    deviceController
        .submitAsync(() -> deviceController.addUsbBinding(model, config, extenderData))
        .whenComplete(
            (result, throwable) -> {
              if (throwable != null) {
                log.warn("Fail to add usbBinding!", throwable);
                return;
              }
              PlatformUtility.runInFxThread(
                  () -> {
                    converter.loadData(matrix, true);
                    controllable.updateModelToGraph();
                  });
            });
  }

  static class PortValidater extends SimpleFxFormValidator {
    private final int[] usedPorts;
    private final int maxPort;
    private final int minPort;

    public PortValidater(int minPort, int maxPort, int[] usedPorts) {
      this.minPort = minPort;
      this.maxPort = maxPort;
      this.usedPorts = usedPorts;
    }

    @Override
    protected void init() {
      if (init) {
        return;
      }
      try {
        Configuration<?> configuration = Validation.byDefaultProvider().configure();
        if (configuration instanceof HibernateValidatorConfiguration) {
          HibernateValidatorConfiguration hiConfig =
              (HibernateValidatorConfiguration) configuration;
          ConstraintMapping mapping = hiConfig.createConstraintMapping();
          mapping
              .type(UsbBindingConfigBean.class)
              .property("port", ElementType.METHOD)
              .constraint(
                  new GenericConstraintDef<>(BlackIntegerList.class)
                      .param("blackItems", usedPorts)
                      .message(Bundle.NbBundle.getMessage("menu.usb.port_used")))
              .constraint(new MinDef().value(minPort))
              .constraint(new MaxDef().value(maxPort));

          hiConfig.addMapping(mapping);
        }
        ValidatorFactory newFactory = configuration.buildValidatorFactory();
        validator = newFactory.getValidator();
        messageInterpolator = newFactory.getMessageInterpolator();
      } catch (ValidationException exception) {
        // validation is not activated, since no implementation has been provided
        log.warn("Validation disabled", exception);
      }
      init = true;
    }
  }
}
