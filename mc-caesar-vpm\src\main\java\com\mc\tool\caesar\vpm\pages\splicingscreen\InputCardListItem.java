package com.mc.tool.caesar.vpm.pages.splicingscreen;

import com.mc.tool.caesar.vpm.kaito.model.DecodeCard;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * RxListItem.
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class InputCardListItem extends DecodeCard {
  private int videowallGroupId;

  /**
   * Constructor.
   */
  public InputCardListItem(DecodeCard decodeCard, int videowallGroupId) {
    super(decodeCard.getRxId(), decodeCard.getName(), decodeCard.getPort(), decodeCard.getSn(),
        decodeCard.getType(), decodeCard.getInputId(), decodeCard.getSlotId());
    this.videowallGroupId = videowallGroupId;
  }
}
