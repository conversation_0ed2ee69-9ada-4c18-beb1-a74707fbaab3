package com.mc.tool.caesar.api.utils;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarConstants.Extender;
import com.mc.tool.caesar.api.CaesarConstants.Module.Type;
import com.mc.tool.caesar.api.CaesarCpuTypeProperty;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.ConfigData;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.MatrixData;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.MultiviewData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.datamodel.VersionSet;
import com.mc.tool.caesar.api.interfaces.SwitchModuleData;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public class SampleModelCreater {

  public static final int ID_MASK = 0xfff0000;

  /**
   * 创建模型数据.
   *
   * @param model 模型
   * @param info  信息
   */
  public static void createDataModel(CaesarSwitchDataModel model, SampleModelInfo info,
      int extenderOffset) {
    int count = createSampleConfigData(model.getConfigData(), info, extenderOffset);
    createSampleModuleData(model.getSwitchModuleData(), model.getConfigData().getGridData(), info,
        0, 0);
    createSamplePortData(model, count);
    model.getConfigData().readVpcon();
  }

  /**
   * 创建端口数据.
   *
   * @param model 模型
   */
  public static void createSamplePortData(CaesarSwitchDataModel model, int count) {
    for (int i = 0; i < count; i++) {
      if (!model.getConfigData().getExtenderData(i).isStatusActive()) {
        continue;
      }
      PortData portData = model.getConfigData().getPortData(i);
      portData.setStatusActive(true);
      portData.setStatusAvailable(true);
      portData.setExtender(i + 1); // extender的oid + 1
    }
  }

  /**
   * 创建模块数据 与 矩阵数据.
   *
   * @param switchModuleData 模块数据.
   * @param matrixData       矩阵数据
   * @param info             信息
   * @param moduleOffset     模块偏移
   * @param portOffset       端口偏移
   */
  public static void createSampleModuleData(SwitchModuleData switchModuleData,
      MatrixData matrixData, SampleModelInfo info, int moduleOffset, int portOffset) {
    int ports = info.getPorts();
    if (ports == 0) {
      return;
    }

    ModuleData cpuModule = switchModuleData.getModuleData(0);
    cpuModule.setStatusActive(true);
    cpuModule.setStatusAvailable(true);
    cpuModule.setPorts(ports);
    cpuModule.setType(info.getMatrixType().getValue());
    cpuModule.setVersion(info.matVersion.copy());

    CaesarCpuTypeProperty property = CaesarCpuTypeProperty
        .getProperty(info.getMatrixType().getValue());
    int moduleIndex = 1;
    final int portsSave = ports;
    while (ports > 0) {
      int ioPorts = Math.min(ports, property.getPortsPerIo());
      ModuleData moduleData = switchModuleData.getModuleData(moduleIndex++);
      moduleData.setStatusActive(true);
      moduleData.setStatusAvailable(true);
      moduleData.setType(Type.IO_SPF.getValue());
      moduleData.setPorts(ioPorts);
      moduleData.setVersion(info.ioVersion.copy());
      ports -= ioPorts;
    }
    matrixData.setFirstModule(moduleOffset + 1);
    matrixData.setLastModule(moduleOffset + moduleIndex - 1);
    matrixData.setFirstPort(portOffset + 1);
    matrixData.setLastPort(portOffset + portsSave - 1);
  }

  /**
   * 创建配置数据.
   *
   * @param configData 配置数据.
   * @param info       信息
   * @return 最大的extender索引
   */
  public static int createSampleConfigData(ConfigData configData, SampleModelInfo info,
      int extenderOffset) {

    configData.getSystemConfigData().getNetworkDataPreset1()
        .setAddress(IpUtil.getAddressByte(info.ip));
    configData.getSystemConfigData().getNetworkDataPreset1()
        .setGateway(IpUtil.getAddressByte(info.gateway));
    int extenderIndex = extenderOffset;
    int consoleIndex = 0;
    for (int i = 0; i < info.getRxCount(); i++) {
      createConsoleData(configData, extenderIndex, consoleIndex, extenderIndex,
          info.rxVersion.copy(), false, false, false);
      extenderIndex++;
      consoleIndex++;
    }

    for (int i = 0; i < info.getMicInConCount(); i++) {
      createConsoleData(configData, extenderIndex, consoleIndex, extenderIndex,
          info.rxVersion.copy(), false, false, true);
      extenderIndex++;
      consoleIndex++;
    }

    for (int i = 0; i < info.getTxCount(); i++) {
      createCpuData(configData, extenderIndex, i, extenderIndex, info.txVersion.copy());
      extenderIndex++;
    }

    for (int i = 0; i < info.getVp6Count(); i++) {
      int newExtenderIndex = (int) (Math.ceil(extenderIndex / 8.0) * 8);
      for (int j = 0; j < info.getVp6PortSize(); j++) {
        createConsoleData(configData, newExtenderIndex + j, consoleIndex++, newExtenderIndex + j,
            info.vp6Version.copy(),
            true, false, false);
      }
      extenderIndex = newExtenderIndex + 8;
    }

    for (int i = 0; i < info.getVp7Count(); i++) {
      createConsoleData(configData, extenderIndex, consoleIndex, extenderIndex,
          info.rxVersion.copy(), false, true, false);
      extenderIndex++;
      consoleIndex++;
    }

    for (int i = 0; i < info.getNoFirstPortVp6Count(); i++) {
      int newExtenderIndex = (int) (Math.ceil(extenderIndex / 8.0) * 8);
      for (int j = 1; j < 8; j++) {
        createConsoleData(configData, newExtenderIndex + j, consoleIndex++, newExtenderIndex + j,
            info.vp6Version.copy(),
            true, false, false);
      }
      extenderIndex = newExtenderIndex + 8;
    }
    return extenderIndex;
  }

  private static void createConsoleData(ConfigData configData, int extenderIndex, int consoleIndex,
      int portIndex,
      VersionSet version, boolean isVp6, boolean isVp7, boolean hasMicIn) {
    ExtenderData extenderData = configData.getExtenderData(extenderIndex);
    extenderData.setStatusActive(true);
    extenderData.setStatusOnline(true);
    extenderData.setPort(portIndex + 1);
    extenderData.setId(extenderIndex | 0xfff0000);
    extenderData.setType(Extender.Type.CON);
    extenderData.setVersion(version);
    extenderData.setName("EXT_" + extenderIndex);
    if (hasMicIn) {
      extenderData.getExtenderStatusInfo().getHwSpecial().setLineIn(true);
      extenderData.getExtenderStatusInfo().getHwSpecial().setMicIn(true);
    }

    ConsoleData consoleData = configData.getConsoleData(consoleIndex);
    consoleData.setStatusActive(true);
    if (isVp6) {
      consoleData.setStatus(consoleData.getStatus() | CaesarConstants.Console.Status.VP6_DEVICE);
    }
    consoleData.setExtenderData(0, extenderData);
    consoleData.setPorts(consoleData.getPorts() + 1);
    consoleData.setId(configData.getConfigDataManager().getAutoIdRealCon());
    consoleData.setName("CON_" + consoleIndex);

    if (isVp7) {
      consoleData.setName("VP7_" + consoleIndex);
      consoleData.setStatus(consoleData.getStatus() | CaesarConstants.Console.Status.VP7_DEVICE);
      extenderData.getExtenderStatusInfo().setSpecialExtType(Extender.SpecialExtenderType.HW_VP7);
      MultiviewData multiviewData = null;
      for (MultiviewData item : configData.getMultiviewDatas()) {
        if (item.getUsConIndex() == 0) {
          multiviewData = item;
          break;
        }
      }
      if (multiviewData != null) {
        multiviewData.setUsConIndex(consoleIndex + 1);
        consoleData.setMultiviewIndex(multiviewData.getOid() + 1);
      }
    }
    extenderData.setConsoleData(consoleData);
  }

  private static void createCpuData(ConfigData configData, int extenderIndex, int cpuIndex,
      int portIndex,
      VersionSet version) {
    ExtenderData extenderData = configData.getExtenderData(extenderIndex);
    extenderData.setStatusActive(true);
    extenderData.setStatusOnline(true);
    extenderData.setPort(portIndex + 1);
    extenderData.setId(extenderIndex | ID_MASK);
    extenderData.setType(Extender.Type.CPU);
    extenderData.setVersion(version);
    extenderData.setName("EXT_" + extenderIndex);

    CpuData cpuData = configData.getCpuData(cpuIndex);
    cpuData.setStatusActive(true);
    cpuData.setExtenderData(0, extenderData);
    cpuData.setPorts(cpuData.getPorts() + 1);
    cpuData.setId(configData.getConfigDataManager().getAutoIdRealCpu());
    cpuData.setName("CPU_" + cpuIndex);

    extenderData.setCpuData(cpuData);
  }

  /**
   * .
   */
  @Getter
  public static class SampleModelInfo {

    @Setter
    private int txCount;
    @Setter
    private int rxCount;
    @Setter
    private int vp6Count;
    @Setter
    private int vp6PortSize = 8;
    @Setter
    private int noFirstPortVp6Count;
    @Setter
    private int vp7Count;
    @Setter
    private int micInConCount;
    @Setter
    private Type matrixType;
    @Setter
    private int ports;

    @Setter
    private String deviceName;

    @Setter
    private String ip = "s192.168.10.10".substring(1);
    @Setter
    private String gateway = "s192.168.10.1".substring(1);

    private final VersionSet matVersion = new VersionSet();
    private final VersionSet ioVersion = new VersionSet();
    private final VersionSet txVersion = new VersionSet();
    private final VersionSet rxVersion = new VersionSet();
    private final VersionSet vp6Version = new VersionSet();
  }
}
