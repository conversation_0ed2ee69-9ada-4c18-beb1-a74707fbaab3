<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ListView?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>
<fx:root stylesheets="@extenderosdupdate_view.css" type="javafx.scene.layout.VBox"
  xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
  <HBox alignment="CENTER_RIGHT" minHeight="40" prefHeight="40">
  </HBox>
  <Region minHeight="1" styleClass="seperator"/>
  <VBox id="main-container" VBox.Vgrow="ALWAYS">
    <TableView fx:id="tableView" VBox.Vgrow="ALWAYS" xmlns:fx="http://javafx.com/fxml">
      <columns>
        <TableColumn fx:id="nameCol" prefWidth="150" text="%ExtenderOsdUpdate.nameCol.text"/>
        <TableColumn fx:id="deviceCol" prefWidth="75.0" text="%ExtenderOsdUpdate.deviceCol.text"/>
        <TableColumn fx:id="portCol" prefWidth="40" text="%ExtenderOsdUpdate.portCol.text"/>
        <TableColumn fx:id="serialCol" prefWidth="80.0" text="%ExtenderOsdUpdate.serialCol.text"/>
        <TableColumn fx:id="currentMd5Col" prefWidth="81.0"
          text="%ExtenderOsdUpdate.currentMd5Col.text"/>
        <TableColumn fx:id="updateMd5Col" prefWidth="80"
          text="%ExtenderOsdUpdate.updateMd5Col.text"/>
        <TableColumn fx:id="updateCol" prefWidth="120" text="%ExtenderOsdUpdate.updateDate.text"/>
        <TableColumn fx:id="progressCol" prefWidth="120"
          text="%ExtenderOsdUpdate.progressCol.text"/>
      </columns>
      <columnResizePolicy>
        <TableView fx:constant="CONSTRAINED_RESIZE_POLICY"/>
      </columnResizePolicy>
      <VBox.margin>
        <Insets/>
      </VBox.margin>
    </TableView>
    <HBox id="tool-box">
      <Button fx:id="loadButton" styleClass="common-button" text="%ExtenderOsdUpdate.load_file"
        onAction="#onLoad"/>
      <Label fx:id="updateFilePathLabel" prefWidth="400">
        <font>
          <Font size="17.0"/>
        </font>
      </Label>
      <Region HBox.hgrow="ALWAYS"/>
      <Button fx:id="selectAllButton" styleClass="common-button"
        text="%ExtenderOsdUpdate.seleteAllButton.text" onAction="#onSelectAll">
      </Button>
      <Button fx:id="updateButton" styleClass="common-button" text="%ExtenderOsdUpdate.update_btn"
        onAction="#onUpdate">
      </Button>
      <Button fx:id="cancelButton" styleClass="common-button" text="%ExtenderOsdUpdate.cancel_btn"
        onAction="#onCancel">
      </Button>
    </HBox>
    <HBox id="update-log-title-box">
      <Label id="update-log-title" text="%update_log"/>
    </HBox>

    <ListView fx:id="loglist" prefHeight="200">
    </ListView>
  </VBox>
</fx:root>
