package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import javax.validation.constraints.NotNull;
import lombok.Setter;

/**
 * 图层信号源.
 */
@Setter
@JsonPropertyOrder({
    LayerSource.JSON_PROPERTY_SOURCE_TYPE,
    LayerSource.JSON_PROPERTY_SOURCE_ID,
    LayerSource.JSON_PROPERTY_STREAM_ID
})
public class LayerSource {
  public static final String JSON_PROPERTY_SOURCE_TYPE = "sourceType";
  private Integer sourceType;

  public static final String JSON_PROPERTY_SOURCE_ID = "sourceId";
  private Integer sourceId;

  public static final String JSON_PROPERTY_STREAM_ID = "streamId";
  private Integer streamId;

  /**
   * sourceType.
   */
  public LayerSource sourceType(Integer sourceType) {
    this.sourceType = sourceType;
    return this;
  }

  /**
   * 信号源类型，0表示无源，1表示诺瓦视频卡信号，2表示诺瓦IPC信号，3表示凯中TX信号.
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_SOURCE_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getSourceType() {
    return sourceType;
  }


  /**
   * sourceId.
   */
  public LayerSource sourceId(Integer sourceId) {
    this.sourceId = sourceId;
    return this;
  }

  /**
   * 信号源ID.
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_SOURCE_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getSourceId() {
    return sourceId;
  }


  /**
   * streamId.
   */
  public LayerSource streamId(Integer streamId) {
    this.streamId = streamId;
    return this;
  }

  /**
   * 流ID，IPC使用.
   **/
  @JsonProperty(JSON_PROPERTY_STREAM_ID)
  @JsonInclude()
  public Integer getStreamId() {
    return streamId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LayerSource layerSource = (LayerSource) o;
    return Objects.equals(this.sourceType, layerSource.sourceType)
        && Objects.equals(this.sourceId, layerSource.sourceId)
        && Objects.equals(this.streamId, layerSource.streamId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(sourceType, sourceId, streamId);
  }


  @Override
  public String toString() {
    return "LayerSource {\n"
        + "    sourceType: " + toIndentedString(sourceType) + "\n"
        + "    sourceId: " + toIndentedString(sourceId) + "\n"
        + "    streamId: " + toIndentedString(streamId) + "\n"
        + "}";
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
