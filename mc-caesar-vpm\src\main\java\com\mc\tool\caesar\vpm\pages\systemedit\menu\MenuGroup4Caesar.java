package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.graph.interfaces.CellSkin;
import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupDataType;
import com.mc.tool.caesar.vpm.pages.systemedit.controller.CaesarSystemEditController;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.AudioGroup;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.CpuGroup;
import com.mc.tool.caesar.vpm.util.CaesarNamePredicate;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.menu.MenuGroup;
import com.mc.tool.framework.systemedit.menu.predicate.MenuPredicateBinding;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javafx.beans.binding.BooleanBinding;
import javafx.scene.control.MenuItem;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class MenuGroup4Caesar extends MenuItem {

  private final SystemEditControllable controllable;
  private TxRxGroupData unActiveTxGroup;

  /** Constructor. */
  public MenuGroup4Caesar(SystemEditControllable controllable) {
    this.controllable = controllable;
    this.setText(I18nUtility.getI18nBundle("systemedit").getString("menu.group"));
    this.setOnAction(event -> onAction());

    this.disableProperty().bind(getMenuDisableBinding(controllable));
  }

  /**
   * 获取菜单是否应该禁掉的binding.
   *
   * @param controllable controllable
   * @return binding
   */
  public BooleanBinding getMenuDisableBinding(SystemEditControllable controllable) {
    MenuPredicateBinding binding = MenuGroup.getMenuDisableBinding(controllable);
    binding.addSingleSelectionPredicate(
        (node) -> node instanceof CaesarCpuTerminal && !(node.getParent() instanceof CaesarMatrix));
    binding.addSingleSelectionPredicate((node) -> node instanceof CpuGroup);
    if (controllable instanceof CaesarSystemEditController
        && ((CaesarSystemEditController) controllable)
        .getDeviceController()
        .getDataModel()
        .getConfigMetaData()
        .getUtilVersion()
        .hasTxRxGroupData()) {
      unActiveTxGroup =
          ((CaesarSystemEditController) controllable)
              .getDeviceController()
              .getDataModel()
              .getConfigDataManager()
              .getUnActiveTxRxGroup();
      binding.addSingleSelectionPredicate(
          (node) -> node instanceof CaesarCpuTerminal && unActiveTxGroup == null);
      binding.addSingleSelectionPredicate(
          node -> node instanceof CaesarConTerminal && node.getParent() instanceof AudioGroup);
    }

    return binding;
  }

  private void onAction() {
    Collection<CellSkin> skins = controllable.getGraph().getSelectionModel().getSelectedCellSkin();
    List<VisualEditNode> nodeList = new ArrayList<>();
    for (CellSkin skin : skins) {
      if (skin.getCell().getBindedObject() instanceof VisualEditNode) {
        nodeList.add((VisualEditNode) skin.getCell().getBindedObject());
      } else {
        log.warn(
            "Cell's bindedobject is not VisualEditNode but {}", skin.getCell().getBindedObject());
      }
    }
    if (controllable instanceof CaesarSystemEditController) {
      if (((CaesarSystemEditController) controllable)
          .getDeviceController()
          .getDataModel()
          .getConfigMetaData()
          .getUtilVersion()
          .hasTxRxGroupData()) {
        if (nodeList.stream().allMatch(item -> item instanceof CaesarCpuTerminal)) {
          List<String> existGroupNames =
              ((CaesarSystemEditController) controllable)
                      .getDeviceController()
                      .getDataModel()
                      .getConfigDataManager()
                      .getActiveTxRxGroups()
                      .stream()
                      .map(TxRxGroupData::getName)
                      .collect(Collectors.toList());
          Predicate<String> predicate = new CaesarNamePredicate(existGroupNames);
          String groupName = "Group";
          ApplicationBase applicationBase =
              InjectorProvider.getInjector().getInstance(ApplicationBase.class);
          Optional<String> result =
              ViewUtility.getNameFromDialog(
                  applicationBase.getMainWindow(), groupName, null, predicate);
          if (result.isPresent()) {
            groupName = result.get();
          } else {
            return;
          }
          CpuGroup cpuGroup =
              controllable.addGroup(
                  groupName, CpuGroup.class, nodeList.toArray(new VisualEditNode[0]));
          cpuGroup.setTxRxGroupData(unActiveTxGroup);
          String finalGroupName = groupName;
          ((CaesarSystemEditController) controllable)
              .getDeviceController()
              .execute(
                  () -> {
                    for (VisualEditNode node : nodeList) {
                      if (node instanceof CaesarCpuTerminal) {
                        CaesarCpuTerminal cpuTerminal = (CaesarCpuTerminal) node;
                        cpuTerminal.getCpuData().setTxGroupIndex(unActiveTxGroup.getOid() + 1);
                      }
                    }
                    unActiveTxGroup.setName(finalGroupName);
                    unActiveTxGroup.setType(TxRxGroupDataType.TX);
                    ((CaesarSystemEditController) controllable)
                        .getDeviceController()
                        .addTxRxGroupData(unActiveTxGroup);
                  });
        } else {
          controllable.addGroup(
              null, VisualEditGroup.class, nodeList.toArray(new VisualEditNode[0]));
        }
      } else {
        controllable.addGroup(null, VisualEditGroup.class, nodeList.toArray(new VisualEditNode[0]));
      }
    }
  }
}
