package com.mc.tool.caesar.api;

import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.locks.ReentrantLock;

/**
 * .
 *
 * @brief 负责创建与销毁SwitchDataModel
 */
public class CaesarSwitchDataModelManager {

  private static CaesarSwitchDataModelManager INSTANCE = new CaesarSwitchDataModelManager();
  private static Map<String, CaesarSwitchDataModel> modelMap = new HashMap<>();
  private static Map<String, Set<String>> modelBinding = new HashMap<>();
  private final ReentrantLock lock = new ReentrantLock(true);

  public static CaesarSwitchDataModelManager getInstance() {
    return INSTANCE;
  }

  /**
   * .
   */
  public CaesarSwitchDataModel getModel(String hostname, boolean isDemo)
      throws ConfigException, BusyException {
    CaesarSwitchDataModel model;
    lock.lock();
    try {
      model = modelMap.get(hostname);
      if (model == null) {
        model = isDemo ? new DemoSwitchDataModel() : new CaesarSwitchDataModel();
        model.setConnection(hostname, true);
        modelMap.put(hostname, model);
      }
    } finally {
      lock.unlock();
    }

    return model;
  }

  /**
   * 删除无用的models.
   */
  protected void cleanModels() {
    // TODO
  }

  /**
   * 绑定主用的ip与辅用的ip.
   *
   * @param main 主用ip
   * @param sub  辅用ip
   */
  public void bind(String main, String sub) {
    lock.lock();
    try {
      Set<String> bindings;
      if (modelBinding.containsKey(sub)) {
        bindings = modelBinding.get(sub);
      } else {
        bindings = new HashSet<>();
      }
      bindings.add(main);
      modelBinding.put(sub, bindings);
    } finally {
      lock.unlock();
    }
  }

  /**
   * 解除绑定。如果辅用ip没有其他主用ip绑定，就删除.
   *
   * @param main 主用Ip
   * @param sub  辅用ip
   * @return 删除了的model，如果不用删除，返回null
   */
  public CaesarSwitchDataModel unbind(String main, String sub) {
    lock.lock();
    try {
      Set<String> bindings;
      if (modelBinding.containsKey(sub)) {
        bindings = modelBinding.get(sub);
        bindings.remove(main);
      } else {
        bindings = new HashSet<>();
      }
      if (bindings.isEmpty()) {
        modelBinding.remove(sub);
        return remove(sub);
      } else {
        return null;
      }
    } finally {
      lock.unlock();
    }
  }

  /**
   * .
   */
  public CaesarSwitchDataModel remove(String hostname) {
    lock.lock();
    try {
      return modelMap.remove(hostname);
    } finally {
      lock.unlock();
    }

  }


}

