package com.mc.tool.caesar.vpm.util.vp;

import com.mc.tool.caesar.api.datamodel.vp.Vp6ConfigData;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * Vp6WallCurrCfg.
 */
public class Vp6WallCurrCfg {
  private Map<Integer, Vp6ConfigData> configData = new HashMap<>();
  private Map<Integer, Collection<VpVideoPortInfo>> portInfos = new HashMap<>();

  /**
   * 设置VpCon的端口信息.
   */
  public void setVpconPortInfos(int vpconId, Collection<VpVideoPortInfo> portInfos) {
    this.portInfos.put(vpconId, portInfos);
  }

  /**
   * 设置VpConfig数据.
   */
  public void setVpConfigData(int vpconId, Vp6ConfigData vp6ConfigData) {
    this.configData.put(vpconId, vp6ConfigData);
  }

  /**
   * 清除旧数据.
   */
  public void clearOldData() {
    portInfos.clear();
    configData.clear();
  }

  /**
   * 获取VpCon端口信息.
   */
  public Collection<VpVideoPortInfo> getVpconPortInfos(int vpconId) {
    return portInfos.getOrDefault(vpconId, new HashSet<>());
  }

  /**
   * 获取VpCon的配置数据.
   */
  public Vp6ConfigData getVpconConfigData(int vpconId) {
    return configData.getOrDefault(vpconId, new Vp6ConfigData());
  }

  /**
   * 获取所有已使用的VpCon.
   */
  public List<Integer> getUsedVpCons() {
    List<Integer> result = new ArrayList<>();
    for (Map.Entry<Integer, Vp6ConfigData> entry : configData.entrySet()) {
      result.add(entry.getKey());
    }
    return result;
  }
}