package com.mc.tool.caesar.vpm.pages.systemedit.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.mc.tool.caesar.api.Version;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.ExtenderInfo;
import com.mc.tool.caesar.api.datamodel.ExtenderInfo.ExtenderType;
import com.mc.tool.caesar.api.datamodel.ExtenderInfo.ImportResult;
import com.mc.tool.caesar.api.datamodel.OpticalModuleInfo;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarTerminalBase;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbRxTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbTxTerminal;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.CaesarNamePredicate;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarExtenderExportHelper {

  private static final String EXPORT_ENCODING = "utf-8";
  private static final boolean EXPORT_WITH_BOM = true;
  private static final int EXPORT_COLUMN_CNT = 6;
  private static final String EXPORT_FORMAT = "%d,%s,%s,%s,%s,%d%n";

  /**
   * 导入外设信息.
   *
   * @param systemEditModel model
   * @param deviceController device controller
   * @param file 要导入的文件
   * @return 导入结果，导入成功，返回true
   */
  public static ExtenderInfo.ImportResult importExtenderInfoImpl(
      VisualEditModel systemEditModel, CaesarDeviceController deviceController, File file) {
    try (FileInputStream fis = new FileInputStream(file);
        InputStreamReader isr = new InputStreamReader(fis, EXPORT_ENCODING);
        BufferedReader bufferedReader = new BufferedReader(isr)) {
      // read header
      bufferedReader.readLine();
      // 解析文件
      String line;
      List<String> errorLines = new ArrayList<>();
      Map<Long, ExtenderInfo> extenderInfoMap = new HashMap<>();
      while ((line = bufferedReader.readLine()) != null) {
        if (line.trim().isEmpty()) {
          continue;
        }
        String[] items = line.split(",");
        if (items.length != EXPORT_COLUMN_CNT) {
          errorLines.add(line);
          continue;
        }
        ExtenderInfo extenderInfo = new ExtenderInfo();
        try {
          extenderInfo.setId(Integer.parseInt(items[0]));
          extenderInfo.setName(items[1]);
          extenderInfo.setPort(items[2]);
          extenderInfo.setType(ExtenderInfo.ExtenderType.valueOf(items[3]));
          extenderInfo.setSerial(items[4]);
          extenderInfo.setExtraid(Long.parseLong(items[5]));
          extenderInfoMap.put(extenderInfo.getExtraid(), extenderInfo);
        } catch (NumberFormatException exception) {
          errorLines.add(line);
        }
      }
      // 检查格式错误
      if (errorLines.size() > 0) {
        log.warn("Error import format!");
        for (String errorLine : errorLines) {
          log.warn(errorLine);
        }
        return ExtenderInfo.ImportResult.FORMAT_ERROR;
      }
      // 检查信息配对
      Map<Long, ExtenderData> extenderDataMap = new HashMap<>();
      for (ExtenderData extenderData :
          deviceController.getDataModel().getConfigDataManager().getActiveExtenders()) {
        extenderDataMap.put((long) extenderData.getId(), extenderData);
      }

      if (!checkImportType(extenderInfoMap, extenderDataMap)) {
        return ExtenderInfo.ImportResult.TYPE_ERROR;
      }
      // 检查名称长度
      if (!checkImportNameLen(extenderInfoMap)) {
        return ImportResult.NAME_ERROR;
      }

      // 检查ID重复与范围
      if (!checkImportId(
          extenderInfoMap,
          extenderDataMap,
          deviceController.getDataModel().getConfigMetaData().getUtilVersion())) {
        return ImportResult.ID_ERROR;
      }

      // 导入
      try {
        deviceController
            .submit(
                () -> {
                  try {
                    deviceController.getDataModel().sendExtData(extenderInfoMap.values());
                  } catch (DeviceConnectionException | BusyException exc) {
                    log.warn("Fail to send Ext import data!", exc);
                  }
                })
            .get();
      } catch (ExecutionException | InterruptedException exception) {
        log.warn("Fail to send Ext import data!", exception);
      }
      return ImportResult.OK;
    } catch (IOException | RuntimeException exception) {
      log.warn("Fail to import extender info!", exception);
      return ImportResult.UNKNOWN_ERROR;
    }
  }

  private static boolean checkImportId(
      Map<Long, ExtenderInfo> extenderInfoMap,
      Map<Long, ExtenderData> extenderDataMap,
      Version version) {
    List<ExtenderInfo> idErrorItems = new ArrayList<>();
    List<ExtenderInfo> idRepeatItems = new ArrayList<>();
    Set<Integer> unimportId = new HashSet<>();
    Map<Integer, Set<ExtenderInfo>> importId = new HashMap<>();
    // 统计没有导入的extender的id
    for (Map.Entry<Long, ExtenderData> entry : extenderDataMap.entrySet()) {
      if (extenderInfoMap.containsKey(entry.getKey())) {
        continue;
      }
      ExtenderData extenderData = entry.getValue();
      if (extenderData.isUsbCpuType() || extenderData.isUsbConType()) {
        continue;
      }
      if (extenderData.isCpuType() && extenderData.getCpuData() != null) {
        unimportId.add(extenderData.getCpuData().getId());
      } else if (extenderData.isConType() && extenderData.getConsoleData() != null) {
        unimportId.add(extenderData.getConsoleData().getId());
      }
    }
    // 统计导入的id
    for (Map.Entry<Long, ExtenderInfo> entry : extenderInfoMap.entrySet()) {
      ExtenderInfo info = entry.getValue();
      // 虚拟的外设没有id
      if (info.getType() == ExtenderType.USB) {
        continue;
      }
      if ((info.getType() == ExtenderType.RX || info.getType() == ExtenderType.VP)
          && (info.getId() < version.getMinConId() || info.getId() > version.getMaxConId())) {
        idErrorItems.add(info);
      } else if (info.getType() == ExtenderType.TX
          && (info.getId() < version.getMinCpuId() || info.getId() > version.getMaxCpuId())) {
        idErrorItems.add(info);
      } else if (unimportId.contains(info.getId())) {
        idRepeatItems.add(info);
      } else {
        Set<ExtenderInfo> set = importId.get(info.getId());
        if (set == null) {
          set = new HashSet<>();
        }
        set.add(info);
        importId.put(info.getId(), set);
      }
    }

    if (idErrorItems.size() > 0) {
      log.warn("Error import ids.");
      for (ExtenderInfo info : idErrorItems) {
        log.warn(formatExtenderInfo(info));
      }
    }
    if (idRepeatItems.size() > 0) {
      log.warn("Import exist ids!");
      for (ExtenderInfo info : idRepeatItems) {
        log.warn(formatExtenderInfo(info));
      }
    }

    int innerRepeatCount = 0;
    for (Set<ExtenderInfo> set : importId.values()) {
      if (set.size() > 1) {
        innerRepeatCount++;
        log.warn("Import repeat ids!");
        for (ExtenderInfo info : set) {
          log.warn(formatExtenderInfo(info));
        }
      }
    }

    return idErrorItems.size() == 0 && idRepeatItems.size() == 0 && innerRepeatCount == 0;
  }

  private static boolean checkImportNameLen(Map<Long, ExtenderInfo> extenderInfoMap) {
    List<ExtenderInfo> nameErrorItems = new ArrayList<>();
    for (Map.Entry<Long, ExtenderInfo> entry : extenderInfoMap.entrySet()) {
      ExtenderInfo info = entry.getValue();
      if (info.getName() == null
          || info.getName().isEmpty()
          || !new CaesarNamePredicate().test(info.getName())) {
        nameErrorItems.add(info);
      }
    }

    if (nameErrorItems.size() > 0) {
      log.warn("Import error names!");
      for (ExtenderInfo info : nameErrorItems) {
        log.warn(formatExtenderInfo(info));
      }
    }

    return nameErrorItems.size() == 0;
  }

  private static boolean checkImportType(
      Map<Long, ExtenderInfo> extenderInfoMap, Map<Long, ExtenderData> extenderDataMap) {
    List<ExtenderInfo> notFindItems = new ArrayList<>();
    List<ExtenderInfo> typeErrorItems = new ArrayList<>();
    for (Map.Entry<Long, ExtenderInfo> entry : extenderInfoMap.entrySet()) {
      ExtenderData extenderData = extenderDataMap.get(entry.getKey());
      if (extenderData == null) {
        notFindItems.add(entry.getValue());
      } else {
        ExtenderInfo info = entry.getValue();
        if (info.getType() == null) {
          typeErrorItems.add(info);
        } else {
          ExtenderType type = null;
          if (extenderData.isUsbConType() || extenderData.isUsbCpuType()) {
            type = ExtenderType.USB;
          } else if (extenderData.isVpConType()) {
            type = ExtenderType.VP;
          } else if (extenderData.isConType()) {
            type = ExtenderType.RX;
          } else if (extenderData.isCpuType()) {
            type = ExtenderType.TX;
          }
          if (type != info.getType()) {
            typeErrorItems.add(info);
          }
        }
      }
    }

    if (notFindItems.size() > 0) {
      log.warn("Import not exist extender!");
      for (ExtenderInfo info : notFindItems) {
        log.warn(formatExtenderInfo(info));
      }
    }
    if (typeErrorItems.size() > 0) {
      log.warn("Import mismatch type extender!");
      for (ExtenderInfo info : typeErrorItems) {
        log.warn(formatExtenderInfo(info));
      }
    }

    return notFindItems.size() == 0 && typeErrorItems.size() == 0;
  }

  /**
   * 导出外设信息.
   *
   * @param systemEditModel model
   * @param file 导出的位置
   * @return 如果导出成功，返回true
   */
  public static boolean exportExtenderInfoImpl(VisualEditModel systemEditModel, File file) {
    try (FileOutputStream fos = new FileOutputStream(file)) {

      final String header = "id,name,port,type,serial,extraid\n";
      if (EXPORT_WITH_BOM) {
        fos.write(new byte[] {(byte) 0xef, (byte) 0xbb, (byte) 0xbf});
      }
      fos.write(header.getBytes(EXPORT_ENCODING));
      for (VisualEditTerminal terminal : systemEditModel.getAllTerminals()) {
        if (!(terminal instanceof CaesarTerminalBase)) {
          continue;
        }
        CaesarTerminalBase terminalBase = (CaesarTerminalBase) terminal;
        // 忽略离线设备
        if (!terminalBase.getPort1ConnectedProperty().get()
            && !terminalBase.getPort2ConnectedProperty().get()) {
          continue;
        }
        ExtenderData extenderData = terminalBase.getExtenderData();
        if (extenderData == null) {
          continue;
        }

        ExtenderInfo extenderInfo = new ExtenderInfo();
        extenderInfo.setName(terminal.getName());
        extenderInfo.setPort(extenderData.getPortProperty().get());
        extenderInfo.setSerial(
            extenderData.getSerial() == null ? "" : extenderData.getSerial().trim());
        extenderInfo.setExtraid(extenderData.getId());
        if (terminalBase instanceof CaesarCpuTerminal) {
          CaesarCpuTerminal cpuTerminal = (CaesarCpuTerminal) terminalBase;
          extenderInfo.setType(ExtenderType.TX);
          extenderInfo.setId(cpuTerminal.getCpuData().getId());
        } else if (terminalBase instanceof CaesarConTerminal) {
          CaesarConTerminal conTerminal = (CaesarConTerminal) terminalBase;
          if (conTerminal.isVp()) {
            extenderInfo.setType(ExtenderType.VP);
          } else {
            extenderInfo.setType(ExtenderType.RX);
          }
          extenderInfo.setId(conTerminal.getConsoleData().getId());
        } else if (terminalBase instanceof CaesarUsbRxTerminal
            || terminalBase instanceof CaesarUsbTxTerminal) {
          extenderInfo.setType(ExtenderType.USB);
          extenderInfo.setId(0);
        } else {
          log.warn("Error terminal type:{}", terminalBase.getClass().getName());
          continue;
        }

        String content = formatExtenderInfo(extenderInfo);
        fos.write(content.getBytes(EXPORT_ENCODING));
      }
      return true;
    } catch (IOException | RuntimeException exception) {
      log.warn("Fail to export extender info!", exception);
      return false;
    }
  }

  private static String formatExtenderInfo(ExtenderInfo extenderInfo) {
    return String.format(
        EXPORT_FORMAT,
        extenderInfo.getId(),
        extenderInfo.getName(),
        extenderInfo.getPort(),
        extenderInfo.getType(),
        extenderInfo.getSerial(),
        extenderInfo.getExtraid());
  }

  /**
   * 获取导入结果的错误信息.
   *
   * @param result 导入结果
   * @return 错误信息
   */
  public static String getErrorMessage(ImportResult result) {
    switch (result) {
      case FORMAT_ERROR:
        return CaesarI18nCommonResource.getString("systemedit.importerror.formaterror");
      case TYPE_ERROR:
        return CaesarI18nCommonResource.getString("systemedit.importerror.typeerror");
      case NAME_ERROR:
        return CaesarI18nCommonResource.getString("systemedit.importerror.nameerror");
      case ID_ERROR:
        return CaesarI18nCommonResource.getString("systemedit.importerror.iderror");
      case UNKNOWN_ERROR:
        return CaesarI18nCommonResource.getString("systemedit.importerror.unknownerror");
      default:
        return "";
    }
  }

  /** 导出光模信息. */
  public static boolean exportOpticalModuleInfoImpl(
      CaesarDeviceController deviceController, File file) {
    try {
      List<OpticalModuleInfo> infos =
          deviceController
              .submit(() -> deviceController.getDataModel().getOpticalModulesInfo())
              .get();
      EasyExcel.write(file.getAbsolutePath(), OpticalModuleInfo.class)
          .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
          .sheet()
          .doWrite(infos);
      return true;
    } catch (Exception ex) {
      log.error(ex.getCause().getMessage(), ex);
      return false;
    }
  }
}
