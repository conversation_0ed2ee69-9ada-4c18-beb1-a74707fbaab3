package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import javax.validation.constraints.NotNull;
import lombok.Setter;

/**
 * KVM系统的RX信息.
 */
@Setter
@JsonPropertyOrder({
    KvmRx.JSON_PROPERTY_ID,
    KvmRx.JSON_PROPERTY_NAME
})
public class KvmRx {
  public static final String JSON_PROPERTY_ID = "id";
  private Integer id;

  public static final String JSON_PROPERTY_NAME = "name";
  private String name;


  public KvmRx id(Integer id) {
    this.id = id;
    return this;
  }

  /**
   * RX的ID.
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude()
  public Integer getId() {
    return id;
  }


  public KvmRx name(String name) {
    this.name = name;
    return this;
  }

  /**
   * RX的名称.
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_NAME)
  @JsonInclude()
  public String getName() {
    return name;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    KvmRx kvmRx = (KvmRx) o;
    return Objects.equals(this.id, kvmRx.id)
        && Objects.equals(this.name, kvmRx.name);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, name);
  }


  @Override
  public String toString() {
    return "KvmRx {\n"
        + "    id: " + toIndentedString(id) + "\n"
        + "    name: " + toIndentedString(name) + "\n"
        + "}";
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
