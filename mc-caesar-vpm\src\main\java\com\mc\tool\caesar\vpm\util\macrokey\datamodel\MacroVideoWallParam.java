package com.mc.tool.caesar.vpm.util.macrokey.datamodel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import lombok.Data;

/**
 * .
 */
@Data
public class MacroVideoWallParam implements MacroParam {
  private List<MacroParam> children = new ArrayList<>();
  private int index;
  private String name;

  MacroVideoWallParam(int index, String name) {
    this.index = index;
    this.name = name;
  }

  @Override
  public String getFullName() {
    return name;
  }

  @Override
  public Collection<MacroParam> getChildren() {
    return children;
  }

  @Override
  public void addChild(MacroParam param) {
    children.add(param);
  }

  @Override
  public void removeChild(MacroParam param) {
    children.remove(param);
  }

  @Override
  public boolean hasOid() {
    return false;
  }

  @Override
  public int getOid() {
    return -1;
  }
}
