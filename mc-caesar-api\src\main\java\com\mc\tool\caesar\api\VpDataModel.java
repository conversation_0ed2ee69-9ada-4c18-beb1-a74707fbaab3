package com.mc.tool.caesar.api;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ScenarioData;
import com.mc.tool.caesar.api.datamodel.ScenarioDescribeData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData.GlobalData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData.VideoWallData;
import com.mc.tool.caesar.api.datamodel.vp.Vp6OutputData;
import com.mc.tool.caesar.api.datamodel.vp.VpConConfigData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.utils.CfgError;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.locks.Lock;
import java.util.logging.Level;
import java.util.logging.Logger;
import javafx.util.Pair;

/**
 * .
 */
public class VpDataModel {

  private static final Logger LOG = Logger.getLogger(VpDataModel.class.getName());
  private final CustomPropertyChangeSupport pcs;
  protected final CaesarSwitchDataModel model;
  private final Lock lock;
  private final Runnable requireSaveRunnable;
  @Expose
  protected final VideoWallGroupData videoWallGroupData = new VideoWallGroupData();
  @Expose
  protected final ScenarioData[] scenarioDatas = new ScenarioData[CaesarConstants.SCENARIO_SIZE];

  /**
   * VPDataModel.
   */
  public VpDataModel(CustomPropertyChangeSupport pcs, CaesarSwitchDataModel model, Lock lock,
      Runnable requireSaveRunnable) {
    this.pcs = pcs;
    this.model = model;
    this.lock = lock;
    this.requireSaveRunnable = requireSaveRunnable;
  }

  private CaesarController getController() {
    return model.getController();
  }

  protected CustomPropertyChangeSupport getChangeSupport() {
    return pcs;
  }

  private void requireSave() {
    requireSaveRunnable.run();
  }

  /**
   * 发送视频墙的备份数据.
   *
   * @param index         视频墙的索引
   * @param videoWallData 视频墙的数据
   */
  public void sendVideoWallData(int index, VideoWallData videoWallData) throws BusyException {
    if (index < 0 || index >= VideoWallGroupData.GROUP_COUNT) {
      LOG.log(Level.SEVERE, "Error video wall index while sending video wall data:" + index + "!");
      return;
    }
    if (videoWallData == null) {
      LOG.log(Level.SEVERE, "Video wall data is null!");
      return;
    }
    try {
      ByteArrayOutputStream baos = new ByteArrayOutputStream();
      videoWallData.write(baos);
      ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
      videoWallGroupData.getData(index).read(bais);

      int originOffset = index * VideoWallGroupData.VIDEOWALL_DATA_SIZE;
      int[] baseDataRanges = videoWallData.getBaseDataRange();
      int[] validDataRanges = videoWallData.getValidDataRange();
      if (validDataRanges.length % 2 != 0 || baseDataRanges.length % 2 != 0) {
        LOG.log(Level.WARNING, "sendVideoWallData:VideoWallData's valid data range is error",
            new Object());
      }

      byte[] data = baos.toByteArray();
      for (int i = 0; i < baseDataRanges.length; i += 2) {
        getController().setVpBackup(originOffset + baseDataRanges[i],
            Arrays.copyOfRange(data, baseDataRanges[i], baseDataRanges[i] + baseDataRanges[i + 1]));
      }
      for (int i = 0; i < validDataRanges.length; i += 2) {
        getController().setVpBackup(originOffset + validDataRanges[i], Arrays.copyOfRange(data,
            validDataRanges[i], validDataRanges[i] + validDataRanges[i + 1]));
      }
      requireSave();
    } catch (ConfigException | IOException ex) {
      LOG.log(Level.WARNING, null, ex);
    }
  }

  /**
   * sendVideoWallGlobalData.
   */
  public void sendVideoWallGlobalData() {

    try {
      GlobalData globalData = videoWallGroupData.getGlobalData();
      int offset = VideoWallGroupData.VIDEOWALL_DATA_SIZE * VideoWallGroupData.GROUP_COUNT;
      ByteArrayOutputStream baos = new ByteArrayOutputStream();
      globalData.write(baos);

      getController().setVpBackup(offset, baos.toByteArray());
    } catch (IOException | ConfigException | BusyException ex) {
      LOG.log(Level.SEVERE, "send video wall global data error", ex);
    }
  }

  /**
   * 获取视频墙的数据.
   */
  public VideoWallData getVideoWallData(int index) {
    return videoWallGroupData.getData(index);
  }

  /**
   * 获取全部视频墙.
   *
   * @return 全部视频墙
   */
  public VideoWallData[] getAllVideoWalls() {
    VideoWallData[] result = new VideoWallData[VideoWallGroupData.GROUP_COUNT];
    for (int i = 0; i < VideoWallGroupData.GROUP_COUNT; i++) {
      result[i] = videoWallGroupData.getData(i);
    }
    return result;
  }

  /**
   * 获取视频墙组的数据.
   */
  public VideoWallGroupData getVideoWallGroupData() {
    return videoWallGroupData;
  }


  /**
   * 通过索引设置预案数据.
   */
  public void setScenarioDataByIndex(int index, ScenarioData data) {
    scenarioDatas[index] = data;
  }

  /**
   * 获取非空的预案所有数据.
   */
  public ScenarioData[] getScenarioDatas() {
    List<ScenarioData> dataList = new ArrayList<>(Arrays.asList(scenarioDatas));
    dataList.removeAll(Collections.singleton(null));
    return dataList.toArray(new ScenarioData[0]);
  }

  /**
   * 获取视频墙非空的预案.
   *
   * @param videoWallIndex 视频墙索引.
   * @return 预案数据
   */
  public ScenarioData[] getVideoWallScenarios(int videoWallIndex) {
    int scenarioBegin =
        videoWallIndex * (CaesarConstants.SCENARIO_SIZE / VideoWallGroupData.GROUP_COUNT);
    int scenarioEnd =
        scenarioBegin + CaesarConstants.SCENARIO_SIZE / VideoWallGroupData.GROUP_COUNT;
    List<ScenarioData> dataList = new ArrayList<>(Arrays.asList(scenarioDatas));
    dataList = new ArrayList<>(dataList.subList(scenarioBegin, scenarioEnd));
    dataList.removeAll(Collections.singleton(null));
    return dataList.toArray(new ScenarioData[0]);
  }

  /**
   * 获取指定索引的预案数据.
   *
   * @param index 索引
   * @return 预案数据.
   */
  public ScenarioData getScenarioData(int index) {
    if (index < 0 || index >= scenarioDatas.length) {
      return null;
    }
    return scenarioDatas[index];
  }

  /**
   * 加载预案的数据.
   */
  public void reloadScenarioDatas() {
    for (int i = 0; i < CaesarConstants.SCENARIO_SIZE; i++) {
      scenarioDatas[i] = null;
    }
    try {
      Collection<ScenarioDescribeData> datas = getController().getScenarioList();
      for (ScenarioDescribeData data : datas) {
        int index = data.getIndex();
        if (index < 0 || index >= CaesarConstants.SCENARIO_SIZE) {
          LOG.warning("scenario's describe data's index is error");
          continue;
        }

        byte[] readedData = getController().getScenarioData(index, CaesarConstants.Scenario.BACKUP);
        ScenarioData scenarioData = new ScenarioData();
        scenarioData.setIndex(index);
        scenarioData.readData(new CfgReader(new ByteArrayInputStream(readedData)), model);
        scenarioDatas[index] = scenarioData;
      }
      getChangeSupport().firePropertyChange(ScenarioData.PROPERTY_DATA_CHANGED, 0, 1);
    } catch (ConfigException | BusyException | IOException ex) {
      LOG.log(Level.SEVERE, "reload scenario data fail", ex);
    } catch (Exception ex) {
      LOG.log(Level.SEVERE, "reload scenario data fail", ex);
    }
  }

  /**
   * 获取空闲的预案数据.
   *
   * @return 空闲的预案数据.
   */
  public ScenarioData getFreeScenarioData(int videoWallIndex) {
    if (videoWallIndex < 0 || videoWallIndex >= VideoWallGroupData.GROUP_COUNT) {
      return null;
    }
    for (int i =
        videoWallIndex * VideoWallGroupData.VIDEOWALL_SCENARIO_COUNT; i < (videoWallIndex + 1)
        * VideoWallGroupData.VIDEOWALL_SCENARIO_COUNT; i++) {
      if (scenarioDatas[i] == null) {
        ScenarioData newData = new ScenarioData();
        newData.setIndex(i);

        VideoWallData videoWallData = new VideoWallData();
        newData.setVideoWallData(videoWallData);
        scenarioDatas[i] = newData;
        return newData;
      }
    }
    return null;
  }

  /**
   * 删除创建出来但是没有发送给设备的预案.
   *
   * @param data 预案数据
   */
  public void removeFreeScenarioData(ScenarioData data) {
    if (data.getIndex() < 0 || data.getIndex() >= scenarioDatas.length) {
      return;
    }
    scenarioDatas[data.getIndex()] = null;
  }

  /**
   * 添加一个预案.
   */
  public void sendScenarioData(ScenarioData scenarioData) {
    if (scenarioData == null || scenarioData.getVideoWallData() == null
        || scenarioData.getConnections() == null || scenarioData.getConfigs() == null) {
      return;
    }
    try {
      // 找一个可用的索引
      int index = -1;
      if (scenarioDatas[scenarioData.getIndex()] != scenarioData) {
        for (int i = 0; i < scenarioDatas.length; i++) {
          if (scenarioDatas[i] == null) {
            index = i;
            break;
          }
        }
      } else {
        index = scenarioData.getIndex();
      }

      if (index >= 0) {
        scenarioDatas[index] = scenarioData;
        scenarioData.setIndex(index);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        CfgWriter writer = new CfgWriter(baos);
        scenarioData.writeData(writer);
        getController().setScenarioData(index, baos.toByteArray());

        getChangeSupport().firePropertyChange(ScenarioData.PROPERTY_DATA_CHANGED, 0, 1);
      }
    } catch (ConfigException | IOException | BusyException ex) {
      LOG.log(Level.SEVERE, "send scenario data fail", ex);
    }
  }

  /**
   * 添加一个预案.
   */
  public void sendScenarioData(int index, ScenarioData scenarioData) {
    if (scenarioData == null || scenarioData.getVideoWallData() == null
        || scenarioData.getConnections() == null || scenarioData.getConfigs() == null) {
      return;
    }
    try {
      ByteArrayOutputStream baos = new ByteArrayOutputStream();
      CfgWriter writer = new CfgWriter(baos);
      scenarioData.writeData(writer);
      getController().setScenarioData(index, baos.toByteArray());
      getChangeSupport().firePropertyChange(ScenarioData.PROPERTY_DATA_CHANGED, 0, 1);
    } catch (ConfigException | IOException | BusyException ex) {
      LOG.log(Level.SEVERE, "send scenario data fail", ex);
    }
  }

  /**
   * 删除一个预案.
   */
  public void removeScenarioData(ScenarioData data) {
    if (data == null) {
      return;
    }
    removeScenarioData(data.getIndex());
  }

  /**
   * 删除一个预案.
   */
  public void removeScenarioData(int index) {
    if (index < 0 || index >= scenarioDatas.length) {
      return;
    }
    if (scenarioDatas[index] == null) {
      return;
    }

    try {
      scenarioDatas[index] = null;
      getController().deleteScenarioData(index);
      getChangeSupport().firePropertyChange(ScenarioData.PROPERTY_DATA_CHANGED, 0, 1);
    } catch (DeviceConnectionException | ConfigException | BusyException ex) {
      LOG.log(Level.SEVERE, "remove scenario data fail", ex);
    }
  }

  /**
   * startScenarioLocal.
   */
  public void startScenarioLocal(int index) {
    try {
      byte[] datas = model.getController().getScenarioData(index, 0x03);
      ScenarioData scenario = new ScenarioData();
      scenario.setIndex(0);
      scenario.readData(new CfgReader(new ByteArrayInputStream(datas)), model);
      System.out.println(scenario.getConfigName());

      Map<ConsoleData, CpuData> conns = scenario.getConnections();
      System.out.println("connections:");
      for (Entry<ConsoleData, CpuData> entry : conns.entrySet()) {
        CpuData cpuData = entry.getValue();
        System.out.println(entry.getKey() == null ? "null" : entry.getKey().getName());
        System.out.println(cpuData == null ? "null" : cpuData.getName());
      }

      System.out.println();
      System.out.println("configuration");
      Map<ConsoleData, Pair<VpConConfigData, Vp6OutputData>> configs = scenario.getConfigs();
      for (Entry<ConsoleData, Pair<VpConConfigData, Vp6OutputData>> entry : configs.entrySet()) {
        VpConConfigData config = entry.getValue().getKey();
        System.out.println(entry.getKey() == null ? "null" : entry.getKey().getName());
        if (config != null) {
          config.print();
        }
      }

      // do connnections
      Map<ConsoleData, CpuData> connBlock = new HashMap<>();
      Map<ConsoleData, CpuData> disconnBlock = new HashMap<>();
      for (Entry<ConsoleData, CpuData> entry : conns.entrySet()) {
        CpuData cpuData = entry.getValue();
        if (cpuData == null) {
          disconnBlock.put(entry.getKey(), null);
        } else {
          connBlock.put(entry.getKey(), cpuData);
        }
      }

      if (!connBlock.isEmpty()) {
        model.sendCpuConsoleBlock(new HashMap<>(), connBlock);
      }
      // config
      for (Entry<ConsoleData, Pair<VpConConfigData, Vp6OutputData>> entry : configs.entrySet()) {
        VpConsoleData vpConsoleData = new VpConsoleData(pcs, model.getConfigDataManager(), 0,
            "VpconsoleData", entry.getKey().getExtenderData(0).getVpType());
        vpConsoleData.setInPort(0, entry.getKey());
        vpConsoleData.setConfigData(entry.getValue().getKey());
        model.sendVpconOutputData(vpConsoleData, entry.getValue().getValue());
        model.sendVpconConfigData(vpConsoleData);
      }

      // disconnect
      if (!disconnBlock.isEmpty()) {
        model.getController().setCpuConsoleConnectionBlock(disconnBlock);
      }

    } catch (ConfigException | BusyException | IOException ex) {
      LOG.log(Level.SEVERE, "start scenario fail", ex);
    }
  }

  /**
   * 刷新视频墙组的数据.
   */
  public void reloadVideoWallGroup() throws BusyException, ConfigException {
    for (int i = 0; i < VideoWallGroupData.GROUP_COUNT; i++) {
      reloadVideoWallData(i);
    }

    try {
      byte[] bytes = getController().getVpBackup(
          VideoWallGroupData.VIDEOWALL_DATA_SIZE * VideoWallGroupData.GROUP_COUNT,
          videoWallGroupData.getGlobalData().size());
      videoWallGroupData.getGlobalData().read(new ByteArrayInputStream(bytes));
    } catch (IOException ex) {
      LOG.log(Level.SEVERE, "reload video wall global data error", ex);
    }
  }

  /**
   * 加载视频墙的数据.
   */
  public void reloadVideoWallData(int index) throws BusyException, ConfigException {
    VideoWallData result = videoWallGroupData.getData(index);
    if (result == null) {
      return;
    }
    this.lock.lock();
    try {
      int originOffset = index * VideoWallGroupData.VIDEOWALL_DATA_SIZE;
      // 读基本数据
      byte[] bytes = new byte[result.size()];
      int[] baseDataRange = result.getBaseDataRange();
      for (int i = 0; i < baseDataRange.length; i += 2) {
        byte[] tempData =
            getController().getVpBackup(originOffset + baseDataRange[i], baseDataRange[i + 1]);
        System.arraycopy(tempData, 0, bytes, baseDataRange[i], baseDataRange[i + 1]);
      }

      result.read(new ByteArrayInputStream(bytes));
      LOG.info("Video wall " + index + " data:");
      result.printBaseInfo();
      //如果数据无效，清空数据，直接返回
      if (!result.isDataValid()) {
        LOG.warning("Video wall " + index + "'s data is invalid");
        result.reset();
        return;
      }
      // 读变长数据
      int[] validDataRanges = result.getValidDataRange();
      if (validDataRanges.length > 0) {
        for (int i = 0; i < validDataRanges.length; i += 2) {
          byte[] tempData = getController().getVpBackup(originOffset + validDataRanges[i],
              validDataRanges[i + 1]);
          System.arraycopy(tempData, 0, bytes, validDataRanges[i], validDataRanges[i + 1]);
        }
        result.read(new ByteArrayInputStream(bytes));
      }

    } catch (DeviceConnectionException ex) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, ex);
    } catch (ConfigException | IOException ex) {
      result.reset();
      LOG.log(Level.WARNING, null, ex);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * 是否能够再添加预案.
   */
  public boolean isCanAddScenario() {
    for (ScenarioData data : scenarioDatas) {
      if (data == null) {
        return true;
      }
    }
    return false;
  }
}
