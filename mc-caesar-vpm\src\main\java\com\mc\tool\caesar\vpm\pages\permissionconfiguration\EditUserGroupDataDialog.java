package com.mc.tool.caesar.vpm.pages.permissionconfiguration;

import com.mc.common.beans.SimpleBooleanBinding;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.datamodel.UserGroupData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.CaesarNamePredicate;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.utility.UndecoratedDialog;
import java.io.IOException;
import java.net.URL;
import java.util.Collection;
import java.util.ResourceBundle;
import java.util.function.Predicate;
import javafx.beans.binding.Bindings;
import javafx.beans.binding.BooleanBinding;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.control.ButtonBar.ButtonData;
import javafx.scene.control.ButtonType;
import javafx.scene.control.DialogPaneEx;
import javafx.scene.control.Label;
import javafx.scene.control.TextField;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class EditUserGroupDataDialog extends UndecoratedDialog<String>
    implements Initializable, ViewControllable {

  @FXML private Label idLabel;
  @FXML private Label nameLabel;
  @FXML private TextField idField;
  @FXML private TextField nameField;

  private UserGroupData userGroupData;
  private Boolean isEditMode;
  private CaesarDeviceController deviceController = null;
  private Collection<UserGroupData> userGroupDatas;
  private BooleanProperty isNameIllegal = new SimpleBooleanProperty(false);

  private WeakAdapter weakAdapter = new WeakAdapter();

  /** 编辑用户组数据对话框. */
  public EditUserGroupDataDialog(UserGroupData userGroupData, Boolean isEditMode) {
    this.userGroupData = userGroupData;
    this.isEditMode = isEditMode;
    URL location =
        getClass()
            .getResource(
                "/com/mc/tool/caesar/vpm/pages/permissionconfiguration/"
                    + "EditUserGroupDataDialog_view.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    loader.setController(this);
    try {
      getDialogPane().setContent(loader.load());
    } catch (IOException ex) {
      log.warn("Can not load EditUserGroupDataDialog_view.fxml", ex);
    }

    idField.setText(String.valueOf(userGroupData.getId()));
    nameField.setText(String.format("NewUserGroup%d", userGroupData.getId()));
    if (isEditMode) {
      nameField.setText(userGroupData.getName());
    }

    initImpl();

    setResultConverter(
        (dialogButton) -> {
          ButtonData data = dialogButton == null ? null : dialogButton.getButtonData();
          if (data == ButtonData.OK_DONE) {
            return nameField.getText();
          } else {
            return null;
          }
        });
    Predicate<String> predicate = new CaesarNamePredicate();
    Node node = getDialogPane().lookupButton(ButtonType.OK);
    node.disableProperty()
        .bind(
            Bindings.createBooleanBinding(
                () -> !predicate.test(nameField.getText()), nameField.textProperty()));
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
    } else {
      log.warn("Error device controller type : {} !", deviceController.getClass().getName());
      return;
    }
    userGroupDatas =
        this.deviceController.getDataModel().getConfigDataManager().getActiveUserGroups();
    if (isEditMode) {
      userGroupDatas.remove(userGroupData);
    }
  }

  private void initImpl() {
    final DialogPaneEx dialogPane = getDialogPane();
    dialogPane.getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);
    BooleanBinding validationBinding = new SimpleBooleanBinding(false);
    validationBinding = validationBinding.or(isNameIllegal);
    dialogPane.lookupButton(ButtonType.OK).disableProperty().bind(validationBinding);
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    idLabel.setText("ID");
    nameLabel.setText(CaesarI18nCommonResource.getString("user_group.name"));

    nameField
        .textProperty()
        .addListener(
            weakAdapter.wrap(
                (observable, oldValue, newValue) -> {
                  if (newValue != null) {
                    isNameIllegal.set(false);
                    if (userGroupDatas != null) {
                      for (UserGroupData groupData : userGroupDatas) {
                        if (groupData.getName().equals(newValue)) {
                          isNameIllegal.set(true);
                          break;
                        }
                      }
                    }
                  }
                }));
  }

  @Override
  public DeviceControllable getDeviceController() {
    return null;
  }
}
