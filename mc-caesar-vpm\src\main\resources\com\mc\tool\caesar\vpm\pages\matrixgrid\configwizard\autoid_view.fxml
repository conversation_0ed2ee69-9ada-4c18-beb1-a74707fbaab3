<?xml version="1.0" encoding="UTF-8"?>

<?import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridWizardIndicator?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<VBox stylesheets="@autoid_view.css" xmlns="http://javafx.com/javafx/8"
  xmlns:fx="http://javafx.com/fxml/1"
  prefWidth="700" prefHeight="464" id="main-container">
  <children>
    <MatrixGridWizardIndicator currentIndex="4"/>
    <VBox id="sub-container">
      <Region prefHeight="45"/>
      <Label text="%autoid.info1" wrapText="true"/>
      <Label text="%autoid.info2" wrapText="true"/>
      <Label text="%autoid.info3" wrapText="true"/>
      <Region prefHeight="10"/>
      <TableView prefHeight="149" fx:id="tableView">
        <columns>
          <TableColumn fx:id="categoryCol" text="%autoid.table.category"/>
          <TableColumn fx:id="nameCol" text="%autoid.table.name"/>
          <TableColumn fx:id="ipCol" text="%autoid.table.ip"/>
          <TableColumn fx:id="autoId" text="%autoid.table.autoid"/>
        </columns>
      </TableView>
    </VBox>

  </children>
</VBox>
