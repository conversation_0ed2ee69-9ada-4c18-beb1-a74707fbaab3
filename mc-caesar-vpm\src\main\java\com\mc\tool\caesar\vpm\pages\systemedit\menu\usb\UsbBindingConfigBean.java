package com.mc.tool.caesar.vpm.pages.systemedit.menu.usb;

import com.dooapp.fxform.annotation.FormFactory;
import com.mc.tool.caesar.api.interfaces.DataObject;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleObjectProperty;
import javax.validation.constraints.NotNull;

/**
 * .
 */
public class UsbBindingConfigBean {
  public ObjectProperty<UsbType> type = new SimpleObjectProperty<>(UsbType.USB_TX);
  public IntegerProperty port = new SimpleIntegerProperty(0);

  @FormFactory(DataObjectFieldFactory.class)
  public ObjectProperty<DataObject> binding = new SimpleObjectProperty<>(null);

  @NotNull
  public Integer getPort() {
    return port.get();
  }

  @NotNull
  public DataObject getBinding() {
    return binding.get();
  }

  /**
   * .
   */
  public enum UsbType {
    USB_TX("USB2.0 TX"),
    USB_RX("USB2.0 RX");

    private String value;

    UsbType(String str) {
      this.value = str;
    }

    @Override
    public String toString() {
      return value;
    }
  }
}
