package com.mc.tool.caesar.api;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.datamodel.AbstractData;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.interfaces.SwitchModuleData;
import com.mc.tool.caesar.api.utils.ArrayIterable;
import com.mc.tool.caesar.api.utils.BitFieldEntry;
import com.mc.tool.caesar.api.utils.BitFieldPropertyChangeListener;
import com.mc.tool.caesar.api.utils.CfgError;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;

/**
 * .
 */
public class CaesarSwitchModuleData extends AbstractData implements SwitchModuleData {

  public static final String PROPERTY_BASE = "SwitchDataModel.SwitchModuleData.";
  public static final String PROPERTY_MODULE_DATA = "SwitchDataModel.SwitchModuleData.ModuleData";
  private static final int RELOAD_DELAY = 2000;
  private final transient CaesarSwitchDataModel switchDataModel;
  @Expose
  private final ModuleData[] moduleDatas;
  private long lastReloadModules1Timestamp = 0L;
  private long lastReloadModules2Timestamp = 0L;

  /**
   * .
   */
  public CaesarSwitchModuleData(CustomPropertyChangeSupport pcs,
      CaesarSwitchDataModel switchDataModel) {
    super(pcs, switchDataModel.getConfigDataManager(), -1, "SwitchDataModel.SwitchModuleData");
    this.switchDataModel = switchDataModel;
    this.moduleDatas = new ModuleData[255];
    for (int i = 0; i < this.moduleDatas.length; i++) {
      this.moduleDatas[i] = new ModuleData(pcs, null, i);
      this.moduleDatas[i].initDefaults();
    }
    for (Map.Entry<String, Collection<BitFieldEntry>> entry : ModuleData.BIT_FIELD_MAP.entrySet()) {
      if (null != entry.getValue() && !entry.getValue().isEmpty()) {
        pcs.addPropertyChangeListener(entry.getKey(),
            new BitFieldPropertyChangeListener(pcs, entry.getValue()));
      }
    }
    initCommitRollback();
  }

  @Override
  public void initDefaults() {
    super.initDefaults();
    for (ModuleData moduleData : this.moduleDatas) {
      moduleData.initDefaults();
    }
  }

  @Override
  public ModuleData getModuleData(int idx) {
    return this.moduleDatas[idx];
  }

  @Override
  public void reloadModules() throws ConfigException, BusyException {
    reloadModules(getModuleDatas());
  }

  @Override
  public void reloadModules(Iterable<ModuleData> moduleDatas)
      throws ConfigException, BusyException {
    if (this.lastReloadModules1Timestamp + RELOAD_DELAY > System.currentTimeMillis()) {
      return;
    }
    try {
      this.switchDataModel.getController().getModuleData(moduleDatas);
      this.lastReloadModules1Timestamp = System.currentTimeMillis();
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    }
  }

  @Override
  public void reloadModules(Iterable<ModuleData> requestedDatas, Iterable<ModuleData> allDatas)
      throws ConfigException, BusyException {
    if (this.lastReloadModules2Timestamp + RELOAD_DELAY > System.currentTimeMillis()) {
      return;
    }
    try {
      this.switchDataModel.getController().getModuleData(requestedDatas, allDatas);
      this.lastReloadModules2Timestamp = System.currentTimeMillis();
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    }
  }

  @Override
  public void requestPorts() throws ConfigException, BusyException {
    requestPorts(this.switchDataModel.getConfigData().getPortDatas());
  }

  @Override
  public void requestPorts(int... oids) throws ConfigException, BusyException {
    requestPorts(this.switchDataModel.getConfigDataManager().getPortData(oids));
  }

  @Override
  public void requestPorts(int from, int to) throws ConfigException, BusyException {
    Collection<PortData> portDatas = new ArrayList<>();
    for (int idx = from; idx <= to; idx++) {
      portDatas.add(this.switchDataModel.getConfigDataManager().getPortData(idx));
    }
    requestPorts(portDatas);
  }

  @Override
  public void requestPorts(Iterable<PortData> portDatas) throws ConfigException, BusyException {
    try {
      this.switchDataModel.getController().getPortData(portDatas);
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    }
  }

  @Override
  public Iterable<ModuleData> getModuleDatas() {
    return new ArrayIterable<>(this.moduleDatas);
  }
}

