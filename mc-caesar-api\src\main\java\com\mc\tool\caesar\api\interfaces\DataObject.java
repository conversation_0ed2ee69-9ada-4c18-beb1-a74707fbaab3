package com.mc.tool.caesar.api.interfaces;

import com.mc.tool.caesar.api.utils.ObjectTransformer;
import java.util.concurrent.atomic.AtomicReference;

/**
 * .
 */
public interface DataObject extends Idable, Nameable {

  void delete();

  void delete(boolean paramBoolean);

  /**
   * .
   */
  class IdNameObjectTransformer implements ObjectTransformer {

    private final String format;
    private final AtomicReference<Object> alternateValue;

    public IdNameObjectTransformer(int idLength, String delim) {
      this(idLength, delim, null);
    }

    public IdNameObjectTransformer(int idLength, String delim, Object alternateValue) {
      this(idLength, delim, new AtomicReference<>(alternateValue));
    }

    private IdNameObjectTransformer(int idLength, String delim,
        AtomicReference<Object> alternateValue) {
      this.format =
          '%' + (idLength >= 0 ? "0" + idLength : "") + "d" + (null == delim ? "" : delim) + "%s";
      this.alternateValue = alternateValue;
    }

    @Override
    public Object transform(Object value) {
      if (value instanceof DataObject) {
        DataObject dataObject = (DataObject) value;
        if (dataObject.getName().isEmpty()) {
          return "";
        }
        return String.format(this.format, dataObject.getId(), dataObject.getName());
      }
      if (null == this.alternateValue) {
        return value;
      }
      return this.alternateValue.get();
    }
  }

}
