package com.mc.tool.caesar.api.version.v2x;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.version.ApiDataConverter;

/**
 * .
 */
public class MultiScreenDataConverter2x implements ApiDataConverter<MultiScreenData> {

  public static final int RESERVE1_CNT = 10;

  @Override
  public void readData(MultiScreenData data, CfgReader cfgReader) throws ConfigException {
    data.setStatus(cfgReader.readByteValue());
    data.setName(cfgReader.readString(CaesarConstants.NAME_LEN));
    data.setType(cfgReader.readByteValue());
    data.setVerticalNumber(cfgReader.readByteValue());
    data.setHorizontalNumber(cfgReader.readByteValue());
    data.setCtrlConId(cfgReader.read2ByteValue());
    data.setCurrentConId(cfgReader.read2ByteValue());
    data.setUsFrame(cfgReader.read2ByteValue());
    for (MultiScreenData.MultiScreenConInfo info : data.getConInfos()) {
      info.readData(cfgReader);
    }
    cfgReader.readByteArray(RESERVE1_CNT);
  }

  @Override
  public void writeData(MultiScreenData data, CfgWriter cfgWriter) throws ConfigException {
    cfgWriter.writeByte((byte) data.getStatus());
    cfgWriter.writeString(data.getName(), CaesarConstants.NAME_LEN);
    cfgWriter.writeByte((byte) data.getType());
    cfgWriter.writeByte((byte) data.getVerticalNumber());
    cfgWriter.writeByte((byte) data.getHorizontalNumber());
    cfgWriter.write2ByteSmallEndian(data.getCtrlConId());
    if (data.getCurrentConId() == 0) {
      cfgWriter.write2ByteSmallEndian(data.getCtrlConId());
    } else {
      cfgWriter.write2ByteSmallEndian(data.getCurrentConId());
    }
    cfgWriter.write2ByteSmallEndian(data.getUsFrameProperty().get());
    for (MultiScreenData.MultiScreenConInfo info : data.getConInfos()) {
      info.writeData(cfgWriter);
    }

    cfgWriter.writeByteArray(new byte[RESERVE1_CNT]);
  }
}
