package com.mc.tool.caesar.api;

import com.mc.tool.caesar.api.datamodel.ConfigMetaData;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.api.datamodel.MatrixData;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.datamodel.SystemConfigData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.datamodel.UserGroupData;
import com.mc.tool.caesar.api.version.v2x.ConsoleDataConverter2x;
import com.mc.tool.caesar.api.version.v2x.CpuDataConverter2x;
import com.mc.tool.caesar.api.version.v2x.ExtenderDataConverter2x;
import com.mc.tool.caesar.api.version.v2x.FunctionKeyDataConverter2x;
import com.mc.tool.caesar.api.version.v2x.MultiScreenDataConverter2x;
import com.mc.tool.caesar.api.version.v2x.UserDataConverter2x;
import java.util.Map;

/**
 * .
 */
public class Version2x extends Version {

  public static final int VERSION_VALUE = 0x20000;
  public static final int CONFIG_DATA_SIZE = 529276;

  public static final int META_DATA_SIZE = 0x74;
  public static final int SYSTEM_DATA_SIZE = 0x1f0;
  public static final int CPU_DATA_SIZE = 0x68;
  public static final int CON_DATA_SIZE = 0x12e;
  public static final int EXT_DATA_SIZE = 0x5a;
  public static final int USER_DATA_SIZE = 0x139;
  public static final int USER_GROUP_DATA_SIZE = 0xee;
  public static final int MULTI_SCREEN_DATA_SIZE = 0x5e;
  public static final int MACRO_DATA_SIZE = 0xa;
  public static final int MATRIX_DATA_SIZE = 0x52;
  public static final int PORT_DATA_SIZE = 0x14;
  public static final int BOARD_DATA_SIZE = 0x35;

  public static final int MAX_USER_NUMBER = 256;
  public static final int MAX_USER_GROUP_NUMBER = 64;
  public static final int MAX_MULTI_SCREEN_GROUP_NUMBER = 100;
  public static final int MAX_EXT_NUMBER = 1024;
  public static final int MAX_CON_NUMBER = 512;
  public static final int MAX_CPU_NUMBER = 512;
  public static final int MAX_PORT_NUMBER = 2032;
  public static final int MAX_MACROT_NUMBER = 8192;
  public static final int MAX_MATRIX_GRID_NUMBER = 16;

  public static final int SYSTEMCONFIGDATA_OFFSET = 0x74;
  public static final int CPUDATA_OFFSET = 0x264;
  public static final int CONDATA_OFFSET = 0xd264;
  public static final int EXTDATA_OFFSET = 0x32e64;
  public static final int USERDATA_OFFSET = 0x49664;
  public static final int USERGROUPDATA_OFFSET = 0x5cf64;
  public static final int MULTISCREENDATA_OFFSET = 0x60ae4;
  public static final int MACRODATA_OFFSET = 0x62f9c;
  public static final int MATRIXGRIDDATA_OFFSET = 0x76f9c;
  public static final int PORTDATA_OFFSET = 0x774bc;

  protected Version2x() {
    Map<Class, Integer> v2x = classSizes;
    v2x.clear();
    v2x.put(ConfigMetaData.class, META_DATA_SIZE);
    v2x.put(SystemConfigData.class, SYSTEM_DATA_SIZE);
    v2x.put(CpuData.class, CPU_DATA_SIZE);
    v2x.put(ConsoleData.class, CON_DATA_SIZE);
    v2x.put(ExtenderData.class, EXT_DATA_SIZE);
    v2x.put(UserData.class, USER_DATA_SIZE);
    v2x.put(UserGroupData.class, USER_GROUP_DATA_SIZE);
    v2x.put(MultiScreenData.class, MULTI_SCREEN_DATA_SIZE);
    v2x.put(FunctionKeyData.class, MACRO_DATA_SIZE);
    v2x.put(MatrixData.class, MATRIX_DATA_SIZE);
    v2x.put(PortData.class, PORT_DATA_SIZE);
    v2x.put(ModuleData.class, BOARD_DATA_SIZE);

    v2x = classCounts;
    v2x.clear();
    v2x.put(CpuData.class, MAX_CPU_NUMBER);
    v2x.put(ConsoleData.class, MAX_CON_NUMBER);
    v2x.put(ExtenderData.class, MAX_EXT_NUMBER);
    v2x.put(UserData.class, MAX_USER_NUMBER);
    v2x.put(UserGroupData.class, MAX_USER_GROUP_NUMBER);
    v2x.put(MultiScreenData.class, MAX_MULTI_SCREEN_GROUP_NUMBER);
    v2x.put(FunctionKeyData.class, MAX_MACROT_NUMBER);
    v2x.put(MatrixData.class, MAX_MATRIX_GRID_NUMBER);
    v2x.put(PortData.class, MAX_PORT_NUMBER);

    v2x = classOffsets;
    v2x.clear();
    v2x.put(SystemConfigData.class, SYSTEMCONFIGDATA_OFFSET);
    v2x.put(CpuData.class, CPUDATA_OFFSET);
    v2x.put(ConsoleData.class, CONDATA_OFFSET);
    v2x.put(ExtenderData.class, EXTDATA_OFFSET);
    v2x.put(UserData.class, USERDATA_OFFSET);
    v2x.put(UserGroupData.class, USERGROUPDATA_OFFSET);
    v2x.put(MultiScreenData.class, MULTISCREENDATA_OFFSET);
    v2x.put(FunctionKeyData.class, MACRODATA_OFFSET);
    v2x.put(MatrixData.class, MATRIXGRIDDATA_OFFSET);
    v2x.put(PortData.class, PORTDATA_OFFSET);

    classConverter.put(FunctionKeyData.class,
        new FunctionKeyDataConverter2x(getClassSize(FunctionKeyData.class)));
    classConverter
        .put(ExtenderData.class, new ExtenderDataConverter2x(getClassSize(ExtenderData.class)));
    classConverter.put(CpuData.class, new CpuDataConverter2x(getClassSize(CpuData.class)));
    classConverter.put(MultiScreenData.class, new MultiScreenDataConverter2x());
    classConverter.put(ConsoleData.class, new ConsoleDataConverter2x());
    classConverter.put(UserData.class, new UserDataConverter2x(getClassSize(UserData.class)));
  }

  @Override
  public String getName() {
    return "DKM2";
  }

  @Override
  public int getVersionValue() {
    return VERSION_VALUE;
  }

  @Override
  public int getMetaDataSize() {
    return META_DATA_SIZE;
  }

  @Override
  public boolean isConnectable() {
    return true;
  }

  @Override
  public boolean hasMatrixData() {
    return true;
  }

  @Override
  public int getModuleCount() {
    return 255;
  }

  @Override
  public boolean hasLdap() {
    return true;
  }

  @Override
  public boolean hasSnmpServer2() {
    return true;
  }

  @Override
  public boolean hasSyslogServer2() {
    return true;
  }

  @Override
  public boolean isSyslogEnableSwitchInSyslogData() {
    return true;
  }

  @Override
  public boolean isComplicatedSyslogData() {
    return true;
  }

  @Override
  public boolean hasSnmpPortOption() {
    return true;
  }

  @Override
  public boolean hasSntpPortOption() {
    return true;
  }

  @Override
  public int getConfigDataSize() {
    return CONFIG_DATA_SIZE;
  }

  @Override
  public int getMaxExtNumber() {
    return MAX_EXT_NUMBER;
  }
}