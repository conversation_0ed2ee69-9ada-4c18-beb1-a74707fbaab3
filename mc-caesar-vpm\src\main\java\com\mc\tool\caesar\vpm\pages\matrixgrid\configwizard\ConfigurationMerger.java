package com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard;

import com.google.common.collect.Sets;
import com.mc.tool.caesar.api.CaesarConfigDataModel;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarConstants.FunctionKey.Cmd.Command;
import com.mc.tool.caesar.api.CaesarConstants.MultiScreen;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData.MultiScreenConInfo;
import com.mc.tool.caesar.api.datamodel.ScenarioData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData.VideoWallData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.framework.utility.ViewLogger;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

/** 合并matrix的配置. */
public class ConfigurationMerger {

  private final List<ExtenderData> extenderIgnoreList = new ArrayList<>();
  private final List<CpuData> cpuIgnoreList = new ArrayList<>();
  private final List<ConsoleData> consoleIgnoreList = new ArrayList<>();
  private final List<UserData> userIgnoreList = new ArrayList<>();
  private final Map<Integer, Integer> extenderIdMapper = new HashMap<>();
  private final Map<Integer, Integer> consoleIdMapper = new HashMap<>();
  private final Map<Integer, Integer> userIdMapper = new HashMap<>();
  private final Map<Integer, Integer> cpuIdMapper = new HashMap<>();
  private final Map<Integer, Integer> txGroupIndexMapper = new HashMap<>();
  private boolean keepId;
  private ViewLogger logger;

  /**
   * 合并.
   *
   * @param destinationModel 目标model
   * @param sourceModel 源model
   * @param keepId 是否保留id
   * @param portOffset 端口偏移
   * @param logger 日志
   * @return 如果合并成功，返回true
   */
  public boolean merge(
      CaesarSwitchDataModel destinationModel,
      CaesarSwitchDataModel sourceModel,
      boolean keepId,
      int portOffset,
      ViewLogger logger) {
    boolean valid = true;
    this.keepId = keepId;
    this.logger = logger;

    this.extenderIgnoreList.clear();
    this.cpuIgnoreList.clear();
    this.consoleIgnoreList.clear();
    this.extenderIdMapper.clear();
    this.consoleIdMapper.clear();
    this.userIdMapper.clear();
    this.cpuIdMapper.clear();
    try {
      mergeExtenders(destinationModel, sourceModel);
      mergeTxGroup(destinationModel, sourceModel);
      if (!mergeCpus(destinationModel, sourceModel)) {
        logger.error("An error occurred. Merge is incomplete.");
        return false;
      }
      mergeUsers(destinationModel, sourceModel);
      if (!mergeConsoles(destinationModel, sourceModel)) {
        logger.error("An error occurred. Merge is incomplete.");
        return false;
      }
      mergeMacros(destinationModel, sourceModel);

      mergeMultiscreens(destinationModel, sourceModel);
      mergeVideoWall(destinationModel, sourceModel);
    } catch (Exception ex) {
      logger.error("An error occurred. Merge is incomplete.", ex);
      valid = false;
    }
    return valid;
  }

  private void mergeExtenders(
      CaesarSwitchDataModel destinationModel, CaesarSwitchDataModel sourceModel) {
    logger.info("Merging extenders.");
    for (ExtenderData selectedExtender : sourceModel.getConfigData().getExtenderDatas()) {
      if (selectedExtender.isStatusActive()) {
        ExtenderData exisitingExtender =
            destinationModel.getConfigDataManager().getExtenderDataById(selectedExtender.getId());
        if (exisitingExtender != null) {
          this.extenderIgnoreList.add(selectedExtender);
          logger.info(
              MessageFormat.format(
                  "Extender {0} already exists and will not be added to the merged configuration",
                  selectedExtender.getId()));
        } else {
          ExtenderData extenderData =
              destinationModel.getConfigData().getConfigDataManager().getFreeExtender();

          extenderData.setName(selectedExtender.getName());
          extenderData.setId(selectedExtender.getId());
          extenderData.setCpuCon(selectedExtender.getCpuCon());
          extenderData.setStatus(selectedExtender.getStatus());
          extenderData.setType(selectedExtender.getType());
        }
      }
    }
  }

  private void mergeTxGroup(
      CaesarSwitchDataModel destinationModel, CaesarSwitchDataModel sourceModel) {
    List<String> existName =
        destinationModel.getConfigDataManager().getActiveTxRxGroups().stream()
            .map(TxRxGroupData::getName)
            .collect(Collectors.toList());
    for (TxRxGroupData activeTxGroup : sourceModel.getConfigDataManager().getActiveTxRxGroups()) {
      TxRxGroupData destUnActive = destinationModel.getConfigDataManager().getUnActiveTxRxGroup();
      if (destUnActive != null) {
        txGroupIndexMapper.put(activeTxGroup.getOid(), destUnActive.getOid());
        String uniqueName = getUniqueName(existName, activeTxGroup.getName());
        existName.add(uniqueName);
        destUnActive.setName(uniqueName);
        destUnActive.setStatusActive(true);
      } else {
        break;
      }
    }
  }

  private String getUniqueName(List<String> existName, String name) {
    if (existName.contains(name)) {
      String newName = name + "_" + new Random().nextInt(100);
      if (newName.length() < CaesarConstants.NAME_LEN) {
        return newName;
      }
    }
    return name;
  }

  private boolean mergeCpus(
      CaesarSwitchDataModel destinationModel, CaesarSwitchDataModel sourceModel) {
    logger.info("Merging TX Devices.");
    for (CpuData selectedCpu : sourceModel.getConfigData().getCpuDatas()) {
      try {
        boolean skip = skipDevice(selectedCpu, true);
        if (skip) {
          this.cpuIgnoreList.add(selectedCpu);
        }
        if (selectedCpu.isStatusActive() && !skip && !selectedCpu.isVirtual()) {
          CpuData cpuData = destinationModel.getConfigDataManager().getFreeCpu();
          if (this.keepId) {
            cpuData.setId(selectedCpu.getId());
          } else {
            cpuData.setId(destinationModel.getConfigDataManager().getAutoIdRealCpu());
          }
          cpuData.setName(selectedCpu.getName());
          if (selectedCpu.getId() != cpuData.getId()) {
            logger.info(
                MessageFormat.format(
                    "Changed TX Device ID, {0} {1} to {2} {3}",
                    selectedCpu.getId(),
                    selectedCpu.getName(),
                    cpuData.getId(),
                    cpuData.getName()));
          }
          for (int i = 0; i < CaesarConstants.Extender.CPUCON; i++) {
            ExtenderData remoteExtenderData = selectedCpu.getExtenderData(i);
            if (remoteExtenderData != null) {
              if (this.extenderIgnoreList.contains(remoteExtenderData)) {
                logger.info(
                    MessageFormat.format(
                        "Extender {0} exists in a different device. "
                            + "Removing extender from device {1}.",
                        remoteExtenderData.getId(), cpuData.getName()));
              } else {
                int id = remoteExtenderData.getId();
                if (this.extenderIdMapper.containsKey(remoteExtenderData.getId())) {
                  id = this.extenderIdMapper.get(remoteExtenderData.getId());
                }
                ExtenderData extenderData =
                    destinationModel.getConfigDataManager().getExtenderDataById(id);
                cpuData.setExtenderData(i, extenderData);
                cpuData.setPorts(cpuData.getPorts() + 1);

                extenderData.setCpuData(cpuData);
              }
            }
          }
          this.cpuIdMapper.put(selectedCpu.getId(), cpuData.getId());

          cpuData.setStatus(selectedCpu.getStatus());
          cpuData.setTxGroupIndex(
              txGroupIndexMapper.getOrDefault(
                  selectedCpu.getTxGroupIndex(), selectedCpu.getTxGroupIndex()));
        }
      } catch (RuntimeException ex) {
        logger.error(
            MessageFormat.format(
                "Cannot merge TX Device ID {0} {1}", selectedCpu.getId(), selectedCpu.getName()));
        return false;
      }
    }
    return true;
  }

  private boolean mergeConsoles(
      CaesarSwitchDataModel destinationModel, CaesarSwitchDataModel sourceModel) {
    logger.info("Merging RX Devices.");
    for (ConsoleData selectedConsole : sourceModel.getConfigData().getConsoleDatas()) {
      try {
        boolean skip = skipDevice(selectedConsole, true);
        if (skip) {
          this.consoleIgnoreList.add(selectedConsole);
        }
        if (selectedConsole.isStatusActive() && !skip) {
          ConsoleData consoleData =
              destinationModel.getConfigData().getConfigDataManager().getFreeConsole();
          consoleData.setInitMode(true);
          if (this.keepId) {
            consoleData.setId(selectedConsole.getId());
          } else {
            consoleData.setId(destinationModel.getConfigDataManager().getAutoIdRealCon());
          }
          consoleData.setName(selectedConsole.getName());
          if (selectedConsole.getId() != consoleData.getId()) {
            logger.info(
                MessageFormat.format(
                    "Changed RX Device ID, {0} {1} to {2} {3}",
                    selectedConsole.getId(),
                    selectedConsole.getName(),
                    consoleData.getId(),
                    consoleData.getName()));
          }
          this.consoleIdMapper.put(selectedConsole.getId(), consoleData.getId());

          consoleData.setPorts(consoleData.getPorts());
          for (int i = 0; i < CaesarConstants.Extender.CPUCON; i++) {
            ExtenderData remoteExtenderData = selectedConsole.getExtenderData(i);
            if (remoteExtenderData != null) {
              if (this.extenderIgnoreList.contains(remoteExtenderData)) {
                logger.info(
                    MessageFormat.format(
                        "Extender {0} exists in a different device. "
                            + "Removing extender from device {1}",
                        remoteExtenderData.getId(), consoleData.getName()));
              } else {
                int id = remoteExtenderData.getId();
                if (this.extenderIdMapper.containsKey(remoteExtenderData.getId())) {
                  id = this.extenderIdMapper.get(remoteExtenderData.getId());
                }
                ExtenderData extenderData =
                    destinationModel.getConfigDataManager().getExtenderDataById(id);
                consoleData.setExtenderData(i, extenderData);
                consoleData.setPorts(consoleData.getPorts() + 1);

                extenderData.setConsoleData(consoleData);
              }
            }
          }
          int oldUserId = selectedConsole.getUser();
          if (this.userIdMapper.containsKey(oldUserId)) {
            int newUserId = this.userIdMapper.get(oldUserId);
            consoleData.setUser(newUserId);
          }
          consoleData.setScanTime(selectedConsole.getScanTime());
          consoleData.setStatus(selectedConsole.getStatus());
          consoleData.setSuspendTime(selectedConsole.getSuspendTime());

          CpuData oldCpuData = selectedConsole.getCpuData();
          if (oldCpuData != null) {
            int oldCpuId = oldCpuData.getId();
            if (this.cpuIgnoreList.contains(oldCpuData)) {
              logger.info(
                  MessageFormat.format(
                      "Device ({0}) is missing, removing connection between {0} and {1}",
                      oldCpuData.getName(), consoleData.getName()));
            } else {
              int newCpuId = this.cpuIdMapper.get(oldCpuId);
              CpuData newCpuData = destinationModel.getConfigDataManager().getCpuData4Id(newCpuId);
              consoleData.setCpuData(newCpuData);
              if (selectedConsole.equals(oldCpuData.getConsoleData())) {
                newCpuData.setConsoleData(consoleData);
              }
            }
          }
          CpuData oldRcpuData = selectedConsole.getRdCpuData();
          if (oldRcpuData != null) {
            int oldRcpuId = oldRcpuData.getId();
            if (this.cpuIgnoreList.contains(oldRcpuData)) {
              logger.info(
                  MessageFormat.format(
                      "Device ({0}) is missing, removing connection between {0} and {1}",
                      oldRcpuData.getName(), consoleData.getName()));
            } else {
              int newCpuId = this.cpuIdMapper.get(oldRcpuId);
              CpuData newRcpuData = destinationModel.getConfigDataManager().getCpuData4Id(newCpuId);
              consoleData.setRdCpuData(newRcpuData);
              if (selectedConsole.equals(oldRcpuData.getConsoleData())) {
                newRcpuData.setConsoleData(consoleData);
              }
            }
          }
          Collection<CpuData> videoAccessCpuData = selectedConsole.getVideoAccessCpuDatas();
          Collection<CpuData> noAccessCpuData = selectedConsole.getNoAccessCpuDatas();
          Collection<CpuData> fullAccessCpuData =
              sourceModel.getConfigDataManager().getActiveCpus();
          fullAccessCpuData.removeAll(videoAccessCpuData);
          fullAccessCpuData.removeAll(noAccessCpuData);

          Collection<CpuData> newVideoAccessCpuData = new ArrayList<>();
          for (CpuData cpuData : videoAccessCpuData) {
            if (cpuData.getId() > 0 && !this.cpuIgnoreList.contains(cpuData)) {
              int newId = this.cpuIdMapper.get(cpuData.getId());
              CpuData newCpuData = destinationModel.getConfigDataManager().getCpuData4Id(newId);
              newVideoAccessCpuData.add(newCpuData);
            }
          }
          consoleData.setVideoAccessCpus(newVideoAccessCpuData);

          Collection<CpuData> newNoAccessCpuData =
              destinationModel.getConfigDataManager().getCpus();
          newNoAccessCpuData.removeAll(newVideoAccessCpuData);

          Collection<CpuData> newFullAccessCpuData = new ArrayList<>();
          for (CpuData cpuData : fullAccessCpuData) {
            if (cpuData.getId() > 0 && !this.cpuIgnoreList.contains(cpuData)) {
              int newId = this.cpuIdMapper.get(cpuData.getId());
              CpuData newCpuData = destinationModel.getConfigDataManager().getCpuData4Id(newId);
              newFullAccessCpuData.add(newCpuData);
            }
          }
          newNoAccessCpuData.removeAll(newFullAccessCpuData);

          consoleData.setNoAccessCpus(newNoAccessCpuData);
          for (int i = 0; i < CaesarConstants.Cpu.FAVORITE; i++) {
            CpuData oldFavorite = selectedConsole.getFavoriteData(i);
            if (oldFavorite != null) {
              if (this.cpuIgnoreList.contains(oldFavorite)) {
                logger.info(
                    MessageFormat.format(
                        "Device ({0}) is missing, removing device from favorite list of {1}",
                        oldFavorite.getName(), consoleData.getName()));
              } else {
                int newId = this.cpuIdMapper.get(oldFavorite.getId());
                consoleData.setFavoriteData(
                    i, destinationModel.getConfigDataManager().getCpuData4Id(newId));
              }
            }
          }
          consoleData.setInitMode(false);
        }
      } catch (RuntimeException ex) {
        logger.error(
            MessageFormat.format(
                "Cannot merge RX Device ID {0} {1}",
                selectedConsole.getId(), selectedConsole.getName()),
            ex);
        return false;
      }
    }
    return true;
  }

  private void mergeUsers(
      CaesarConfigDataModel destinationModel, CaesarConfigDataModel sourceModel) {
    List<UserData> newUsers = new ArrayList<>();

    logger.info("Merging User.");
    for (UserData selectedUser : sourceModel.getConfigData().getUserDatas()) {
      UserData existingUser = null;
      for (UserData userData : destinationModel.getConfigData().getUserDatas()) {
        if (userData.isStatusActive() && userData.getName().equals(selectedUser.getName())) {
          logger.info(
              MessageFormat.format(
                  "User {0} already exists, TX Access Rights will be merged, "
                      + "favorites and macros will be ignored.",
                  userData.getName()));
          this.userIgnoreList.add(selectedUser);
          existingUser = userData;
          break;
        }
      }
      if (selectedUser.isStatusActive()) {
        UserData userData;
        if (existingUser != null) {
          userData = existingUser;
        } else {
          userData = destinationModel.getConfigData().getConfigDataManager().getFreeUser();
        }
        this.userIdMapper.put(selectedUser.getId(), userData.getId());

        userData.setInitMode(true);
        if (existingUser == null) {
          userData.setName(selectedUser.getName());
          userData.setPassword(selectedUser.getPassword());
          userData.setRights(selectedUser.getRights());
          userData.setStatus(selectedUser.getStatus());
        }
        Collection<CpuData> videoAccessCpuData = selectedUser.getVideoAccessCpuDatas();
        Collection<CpuData> noAccessCpuData = selectedUser.getNoAccessCpuDatas();
        Collection<CpuData> fullAccessCpuData = sourceModel.getConfigDataManager().getActiveCpus();
        fullAccessCpuData.removeAll(videoAccessCpuData);
        fullAccessCpuData.removeAll(noAccessCpuData);

        Collection<CpuData> newVideoAccessCpuData = new ArrayList<>();
        for (CpuData cpuData : videoAccessCpuData) {
          if (cpuData.getId() > 0 && !this.cpuIgnoreList.contains(cpuData)) {
            int newId = this.cpuIdMapper.get(cpuData.getId());
            CpuData newCpuData = destinationModel.getConfigDataManager().getCpuData4Id(newId);
            newVideoAccessCpuData.add(newCpuData);
          }
        }
        userData.setLockControlCpus(newVideoAccessCpuData);

        Collection<CpuData> newNoAccessCpuData = destinationModel.getConfigDataManager().getCpus();
        newNoAccessCpuData.removeAll(newVideoAccessCpuData);

        Collection<CpuData> newFullAccessCpuData = new ArrayList<>();
        for (CpuData cpuData : fullAccessCpuData) {
          if (cpuData.getId() > 0 && !this.cpuIgnoreList.contains(cpuData)) {
            System.out.println(cpuData.getId());
            int newId = this.cpuIdMapper.get(cpuData.getId());
            CpuData newCpuData = destinationModel.getConfigDataManager().getCpuData4Id(newId);
            newFullAccessCpuData.add(newCpuData);
          }
        }
        newNoAccessCpuData.removeAll(newFullAccessCpuData);

        userData.setLockVideoCpus(newNoAccessCpuData);
        if (existingUser == null) {
          for (int i = 0; i < CaesarConstants.Cpu.FAVORITE; i++) {
            CpuData oldFavorite = selectedUser.getFavoriteData(i);
            if (oldFavorite != null) {
              if (this.cpuIgnoreList.contains(oldFavorite)) {
                logger.info(
                    MessageFormat.format(
                        "Device ({0}) is missing, removing device from favorite list of {1}",
                        oldFavorite.getName(), userData.getName()));
              } else {
                int newId = this.cpuIdMapper.get(oldFavorite.getId());
                userData.setFavoriteData(
                    i, destinationModel.getConfigDataManager().getCpuData4Id(newId));
              }
            }
          }
        }
        userData.setInitMode(false);

        newUsers.add(userData);
      }
    }
  }

  private void mergeMacros(
      CaesarConfigDataModel destinationModel, CaesarConfigDataModel sourceModel) {
    logger.info("Merging Macros.");

    List<FunctionKeyData> newFunctionKeys = new ArrayList<>();
    for (FunctionKeyData functionKeyData : sourceModel.getConfigData().getFunctionKeyDatas()) {
      int oldIndex = functionKeyData.getHostSubscript();
      if (oldIndex != 0) {
        boolean isConsole = functionKeyData.isConsole();
        CaesarConstants.FunctionKey.Cmd.Command command =
            Command.valueOf(functionKeyData.getMacroCmd());
        int p1 = functionKeyData.getIntParam(0);
        int p2 = functionKeyData.getIntParam(1);
        int fkey = functionKeyData.getHotkey();
        int pos = functionKeyData.getKeyline();

        int newIndex = -1;
        if (isConsole) {
          ConsoleData oldConsoleData =
              sourceModel.getConfigDataManager().getConsoleData(oldIndex - 1);
          if (this.consoleIgnoreList.contains(oldConsoleData)) {
            continue;
          }
          if (oldConsoleData != null) {
            newIndex = getNewConsoleDataIndex(destinationModel, oldConsoleData) + 1;
          }
          if (newIndex == 0) {
            if (oldConsoleData == null) {
              continue;
            }
            logger.info(
                MessageFormat.format(
                    "RX Device {0} has not been added, macros will be ignored.",
                    oldConsoleData.getName()));
            continue;
          }
        } else {
          UserData oldUserData = sourceModel.getConfigDataManager().getUserData(oldIndex - 1);
          if (this.userIgnoreList.contains(oldUserData)) {
            continue;
          }
          if (oldUserData != null) {
            newIndex = getNewUserDataIndex(oldUserData);
          }
          if (newIndex == -1) {
            if (oldUserData == null) {
              continue;
            }
            logger.warn(
                MessageFormat.format(
                    "User {0} has not been added, macros will be ignored.", oldUserData.getName()));
            continue;
          }
        }
        int newP1 = -1;
        int newP2 = -1;
        if (command == CaesarConstants.FunctionKey.Cmd.Command.Connect
            || command == CaesarConstants.FunctionKey.Cmd.Command.ConnectVideo) {
          ConsoleData consoleData = sourceModel.getConfigDataManager().getConsoleData(p1 - 1);
          CpuData cpuData = sourceModel.getConfigDataManager().getCpuData(p2 - 1);
          if (consoleData != null
              && (consoleData.isStatusActive() || consoleData.isStatusNewData())) {
            if (this.consoleIgnoreList.contains(consoleData)) {
              String param1 = consoleData.getName();
              String param2 = cpuData != null ? cpuData.getName() : "";
              addIgnoreMessage(
                  destinationModel.getConfigDataManager(),
                  isConsole,
                  newIndex,
                  fkey,
                  pos,
                  command,
                  param1,
                  param2);
              continue;
            }
            newP1 = getNewConsoleDataIndex(destinationModel, consoleData) + 1;
          }
          if (cpuData != null && (cpuData.isStatusActive() || cpuData.isStatusNewData())) {
            if (this.cpuIgnoreList.contains(cpuData)) {
              String param1 = consoleData != null ? consoleData.getName() : "";
              String param2 = cpuData.getName();
              addIgnoreMessage(
                  destinationModel.getConfigDataManager(),
                  isConsole,
                  newIndex,
                  fkey,
                  pos,
                  command,
                  param1,
                  param2);
              continue;
            }
            newP2 = getNewCpuDataIndex(destinationModel, cpuData) + 1;
          }
        } else if (command == CaesarConstants.FunctionKey.Cmd.Command.Disconnect
            || command == CaesarConstants.FunctionKey.Cmd.Command.Get
            || command == CaesarConstants.FunctionKey.Cmd.Command.GetVideo
            || command == CaesarConstants.FunctionKey.Cmd.Command.Push
            || command == CaesarConstants.FunctionKey.Cmd.Command.PushVideo) {
          ConsoleData consoleData = sourceModel.getConfigDataManager().getConsoleData(p1 - 1);
          if (consoleData != null
              && (consoleData.isStatusActive() || consoleData.isStatusNewData())) {
            if (this.consoleIgnoreList.contains(consoleData)) {
              String param1 = consoleData.getName();
              addIgnoreMessage(
                  destinationModel.getConfigDataManager(),
                  isConsole,
                  newIndex,
                  fkey,
                  pos,
                  command,
                  param1,
                  "");
              continue;
            }
            newP1 = getNewConsoleDataIndex(destinationModel, consoleData) + 1;
          }
        } else if (command == Command.PushVideoWallScenarioWindow) {
          CpuData cpuData = sourceModel.getConfigDataManager().getCpuData(p2 - 1);
          if (cpuData != null && (cpuData.isStatusActive() || cpuData.isStatusNewData())) {
            if (this.cpuIgnoreList.contains(cpuData)) {
              String param2 = cpuData.getName();
              addIgnoreMessage(
                  destinationModel.getConfigDataManager(),
                  isConsole,
                  newIndex,
                  fkey,
                  pos,
                  command,
                  functionKeyData.getIntParam(2) + "",
                  param2);
              continue;
            }
            newP2 = getNewCpuDataIndex(destinationModel, cpuData) + 1;
          }
        }
        FunctionKeyData fkd = destinationModel.getConfigDataManager().getFreeFunctionKeyData();
        fkd.readData(functionKeyData);
        fkd.setHostSubscript(newIndex);
        fkd.setIntParam(0, newP1);
        fkd.setIntParam(1, newP2);
        newFunctionKeys.add(fkd);
      }
    }
  }

  private void mergeMultiscreens(
      CaesarConfigDataModel destinationModel, CaesarConfigDataModel sourceModel) {
    logger.info("Merging multiscreens.");
    Set<Integer> destMultiScreenConId = new HashSet<>();
    // 提取所有跨屏用到的con id
    Optional<List<Integer>> all =
        destinationModel.getConfigDataManager().getActiveMultiScreen().stream()
            .map(
                (item) ->
                    item.getConInfos().stream()
                        .map(MultiScreenConInfo::getConId)
                        .filter(conId -> conId > 0)
                        .collect(Collectors.toList()))
            .reduce(
                (left, right) -> {
                  ArrayList<Integer> result = new ArrayList<>();
                  result.addAll(left);
                  result.addAll(right);
                  return result;
                });
    all.ifPresent(destMultiScreenConId::addAll);
    // 合并
    for (MultiScreenData screen : sourceModel.getConfigDataManager().getActiveMultiScreen()) {
      if (screen.getConInfos().size() <= 0
          || screen.getConInfos().stream().allMatch((item) -> item.getConId() <= 0)) {
        logger.warn("Multiscreen " + screen.getName() + " is ignored!");
        continue;
      }
      // 检查是否跟之前的跨屏冲突，还有id是否都为0
      Set<Integer> ids = new HashSet<>();
      for (int i = 0; i < MultiScreen.MAX_CON; i++) {
        MultiScreenConInfo info = screen.getConInfo(i);
        if (info.getId() > 0) {
          // 查找新的con
          ConsoleData consoleData =
              sourceModel.getConfigDataManager().getConsoleData4Id(info.getId());
          if (consoleIgnoreList.contains(consoleData)) {
            continue;
          }
          int newConIndex = getNewConsoleDataIndex(destinationModel, consoleData);
          if (newConIndex >= 0) {
            ConsoleData newConsoleData =
                destinationModel.getConfigData().getConsoleData(newConIndex);
            ids.add(newConsoleData.getId());
          }
        }
      }

      if (Sets.intersection(destMultiScreenConId, ids).size() > 0
          || ids.size() == 0
          || ids.size() == 1 && ids.iterator().next() == 0) {
        logger.warn("Multiscreen " + screen.getName() + " is ignored!");
        continue;
      }
      destMultiScreenConId.addAll(ids);
      //
      MultiScreenData destScreen = destinationModel.getConfigDataManager().getFreeMultiScreenData();
      if (destScreen == null) {
        logger.warn("Multiscreen " + screen.getName() + " is ignored!");
        continue;
      }

      destScreen.setName(screen.getName());
      destScreen.setStatus(screen.getStatus());
      destScreen.setType(screen.getType());
      destScreen.setHorizontalNumber(screen.getHorizontalNumber());
      destScreen.setVerticalNumber(screen.getVerticalNumber());
      destScreen.setUsFrame(screen.getUsFrame());
      destScreen.setCurrentConId(0);
      destScreen.setCtrlConId(0);
      // 复制con信息
      for (int i = 0; i < MultiScreen.MAX_CON; i++) {
        MultiScreenConInfo destInfo = destScreen.getConInfo(i);
        MultiScreenConInfo info = screen.getConInfo(i);
        if (info.getId() > 0) {
          // 查找新的con
          ConsoleData consoleData =
              sourceModel.getConfigDataManager().getConsoleData4Id(info.getId());
          if (consoleIgnoreList.contains(consoleData)) {
            continue;
          }
          int newConIndex = getNewConsoleDataIndex(destinationModel, consoleData);
          if (newConIndex >= 0) {
            ConsoleData newConsoleData =
                destinationModel.getConfigData().getConsoleData(newConIndex);
            // 设置跨屏的con信息
            int newConId = newConsoleData.getId();
            destInfo.setId(newConId);
            if (screen.getCurrentConId() == info.getId()) {
              destScreen.setCurrentConId(newConId);
            }
            if (screen.getCtrlConId() == info.getId()) {
              destScreen.setCtrlConId(newConId);
            }
            // 设置con的跨屏信息
            newConsoleData.setMultiScreenIndex(destScreen.getOid() + 1);
          } else {
            logger.warn(
                "Fail to find new RX info for "
                    + consoleData.getName()
                    + " in multi screen "
                    + screen.getName()
                    + "!");
          }
        }
      }
    }
  }

  private void mergeVideoWall(
      CaesarSwitchDataModel destinationModel, CaesarSwitchDataModel sourceModel) {
    logger.info("Merging VideoWall.");
    int destinationVideoWallCount =
        destinationModel.getVpDataModel().getVideoWallGroupData().getValidDataCount();
    try {
      sourceModel.getVpDataModel().reloadVideoWallGroup();
      sourceModel.getVpDataModel().reloadScenarioDatas();
    } catch (BusyException | ConfigException ex) {
      logger.error("Fail to reload videoWallDatas or ScenarioDatas", ex);
    }
    int sourceVideoWallCount =
        sourceModel.getVpDataModel().getVideoWallGroupData().getValidDataCount();
    int maxMergeCount =
        Math.min(VideoWallGroupData.GROUP_COUNT - destinationVideoWallCount, sourceVideoWallCount);
    int mergeIndex = 0;
    try {
      for (int sourceIndex = 0; sourceIndex < VideoWallGroupData.GROUP_COUNT; sourceIndex++) {
        if (mergeIndex == maxMergeCount) {
          break;
        }
        if (!sourceModel
            .getVpDataModel()
            .getVideoWallGroupData()
            .getData(sourceIndex)
            .isDataValid()) {
          continue;
        }
        mergeIndex++;
        VideoWallData cloneVideoWallData =
            (VideoWallData)
                sourceModel.getVpDataModel().getVideoWallGroupData().getData(sourceIndex).clone();
        checkVideoDataValid(cloneVideoWallData, extenderIgnoreList);
        checkScreenDataValid(cloneVideoWallData, sourceModel, consoleIgnoreList);

        ScenarioData[] sourceScenarios =
            sourceModel.getVpDataModel().getVideoWallScenarios(sourceIndex).clone();
        for (ScenarioData scenario : sourceScenarios) {
          VideoWallData scenarioVideoWallData = scenario.getVideoWallData();
          checkVideoDataValid(scenarioVideoWallData, extenderIgnoreList);
          checkScreenDataValid(scenarioVideoWallData, sourceModel, consoleIgnoreList);
        }

        for (int destIndex = 0; destIndex < VideoWallGroupData.GROUP_COUNT; destIndex++) {
          if (destinationModel
              .getVpDataModel()
              .getVideoWallGroupData()
              .getData(destIndex)
              .isDataValid()) {
            continue;
          } else {
            destinationModel
                .getVpDataModel()
                .getVideoWallGroupData()
                .setData(destIndex, cloneVideoWallData);

            int scenarioBegin =
                destIndex * (CaesarConstants.SCENARIO_SIZE / VideoWallGroupData.GROUP_COUNT);
            int scenarioEnd =
                scenarioBegin + CaesarConstants.SCENARIO_SIZE / VideoWallGroupData.GROUP_COUNT;
            int length = sourceScenarios.length;
            for (int m = scenarioBegin; m < scenarioEnd; m++) {
              if (m - scenarioBegin < length) {
                sourceScenarios[m - scenarioBegin].setIndex(m);
                destinationModel
                    .getVpDataModel()
                    .setScenarioDataByIndex(m, sourceScenarios[m - scenarioBegin]);
              } else {
                destinationModel.getVpDataModel().setScenarioDataByIndex(m, null);
              }
            }
            break;
          }
        }
      }
    } catch (CloneNotSupportedException ex) {
      logger.error("", ex);
    }
  }

  /** 检查VideoData是否还存在. */
  private void checkVideoDataValid(VideoWallData wallData, List<ExtenderData> ignoreList) {
    for (int j = 0; j < wallData.getVideoCnt(); j++) {
      VideoWallGroupData.CompleteVideoData videoData = wallData.getCompleteVideoData(j);
      if (videoData != null) {
        boolean anyMatch =
            ignoreList.stream().anyMatch(item -> item.getId() == videoData.getCpuId());
        if (anyMatch) {
          videoData.reset();
        }
      }
    }
  }

  /** 检查ScreenData是否还存在. */
  private void checkScreenDataValid(
      VideoWallData wallData, CaesarSwitchDataModel model, List<ConsoleData> ignoreList) {
    for (int j = 0; j < wallData.getRows() * wallData.getColumns(); j++) {
      VideoWallGroupData.CompleteScreenData screenData = wallData.getCompleteScreenData(j);
      if (screenData != null) {
        for (VpConsoleData vpConsoleData :
            model.getConfigDataManager().getActiveEndpointVpconsoles()) {
          if (vpConsoleData.getId() == screenData.getConId()) {
            VpConsoleData parent = vpConsoleData.getParent();
            ConsoleData[] inPortList = parent.getInPortList();
            boolean atLeastOne = false;
            for (ConsoleData consoleData : inPortList) {
              if (consoleData != null) {
                boolean anyMatch =
                    ignoreList.stream().anyMatch(item -> item.getId() == consoleData.getId());
                if (!anyMatch) {
                  atLeastOne = true;
                  break;
                }
              }
            }
            if (!atLeastOne) {
              screenData.resetId();
            }
            break;
          }
        }
      }
    }
  }

  private int getNewConsoleDataIndex(CaesarConfigDataModel model, ConsoleData oldConsoleData) {
    if (this.consoleIdMapper.containsKey(oldConsoleData.getId())) {
      int newId = this.consoleIdMapper.get(oldConsoleData.getId());
      ConsoleData newConsoleData = model.getConfigDataManager().getConsoleData4Id(newId);
      return newConsoleData.getOid();
    }
    return -1;
  }

  private int getNewCpuDataIndex(CaesarConfigDataModel model, CpuData oldCpuData) {
    if (this.cpuIdMapper.containsKey(oldCpuData.getId())) {
      int newId = this.cpuIdMapper.get(oldCpuData.getId());
      CpuData newCpuData = model.getConfigDataManager().getCpuData4Id(newId);
      return newCpuData.getOid();
    }
    return -1;
  }

  private int getNewUserDataIndex(UserData oldUserData) {
    return this.userIdMapper.getOrDefault(oldUserData.getId(), -1);
  }

  private void addIgnoreMessage(
      ConfigDataManager manager,
      boolean isConsole,
      int index,
      int key,
      int pos,
      CaesarConstants.FunctionKey.Cmd.Command command,
      String p1,
      String p2) {
    if (isConsole) {
      ConsoleData master = manager.getConsoleData(index - 1);
      logger.warn(
          MessageFormat.format(
              "Removing macro from RX Device {0}, "
                  + "due to missing device "
                  + "(Key=F{1}, Pos={2}, Command={3}, Param1={4}, Param2={5})",
              master.getName(), key + 1, pos + 1, command.getName(), p1, p2));
    } else {
      UserData master = manager.getUserData(index - 1);
      logger.warn(
          MessageFormat.format(
              "Removing macro from User {0}, "
                  + "due to missing device "
                  + "(Key=F{1}, Pos={2}, Command={3}, Param1={4}, Param2={5})",
              master.getName(), key + 1, pos + 1, command.getName(), p1, p2));
    }
  }

  /**
   * 判断是否忽略此console，如果所有的extender都被忽略了的话，就需要忽略了.
   *
   * @param consoleData consoleData
   * @param verbose verbose
   * @return 如果忽略，返回true
   */
  private boolean skipDevice(ConsoleData consoleData, boolean verbose) {
    boolean skipDevice = false;

    int extenderCount = 0;
    int skipCount = 0;
    for (int i = 0; i < CaesarConstants.Extender.CPUCON; i++) {
      ExtenderData extenderData = consoleData.getExtenderData(i);
      if (extenderData != null) {
        extenderCount++;
        if (this.extenderIgnoreList.contains(extenderData)) {
          skipCount++;
        }
      }
    }
    if (extenderCount > 0 && extenderCount == skipCount) {
      if (verbose) {
        logger.info(
            MessageFormat.format(
                "After removing already existing extenders, the empty device {0} will be removed",
                consoleData.getName()));
      }
      skipDevice = true;
    }
    return skipDevice;
  }

  /**
   * 判断是否忽略此cpu，如果所有的extender都被忽略了的话，就需要忽略了.
   *
   * @param cpuData cpudata
   * @param verbose verbose
   * @return 如果忽略，返回true
   */
  private boolean skipDevice(CpuData cpuData, boolean verbose) {
    boolean skipDevice = false;

    int extenderCount = 0;
    int skipCount = 0;
    for (int i = 0; i < CaesarConstants.Extender.CPUCON; i++) {
      ExtenderData extenderData = cpuData.getExtenderData(i);
      if (extenderData != null) {
        extenderCount++;
        if (this.extenderIgnoreList.contains(extenderData)) {
          skipCount++;
        }
      }
    }
    if (extenderCount > 0 && extenderCount == skipCount) {
      if (verbose) {
        logger.info(
            MessageFormat.format(
                "After removing already existing extenders, the empty device {0} will be removed",
                cpuData.getName()));
      }
      skipDevice = true;
    }
    return skipDevice;
  }
}
