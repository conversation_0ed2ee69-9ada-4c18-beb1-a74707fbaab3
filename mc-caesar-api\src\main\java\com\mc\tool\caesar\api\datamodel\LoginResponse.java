package com.mc.tool.caesar.api.datamodel;

/**
 * 登录响应数据模型.
 */
public class LoginResponse {
  /**
   * 登录状态枚举.
   */
  public enum LoginStatus {
    SUCCESS,
    FAILURE,
    INVALID_USERNAME,
    UNKNOWN_USERNAME_PASSWORD,
    NO_LOGIN_PERMISSION,
    NO_RESPONSE,
    VERSION_MISMATCH
  }

  public LoginResponse(LoginStatus status) {
    this.status = status;
    this.minVersion = null;
  }

  public LoginResponse(LoginStatus status, VersionDef minVersion) {
    this.status = status;
    this.minVersion = minVersion;
  }

  public LoginStatus getStatus() {
    return status;
  }

  public VersionDef getMinVersion() {
    return minVersion;
  }

  private final LoginStatus status;
  private final VersionDef minVersion;
}
