package com.mc.tool.caesar.api.communication;

import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.InterfaceAddress;
import java.net.SocketException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * io控制管理接口，可创建WLAN、串口、文件等读写对象.
 */
public class IoController {

  private static final Logger LOG = Logger.getLogger(IoController.class.getName());
  private static final SocketManager manager = new SocketManager();

  public static ReadWriteController createIp(String host, int port) {
    return new Socket(host, port);
  }

  public static BroadcastController createBroadcast(InterfaceAddress interfaceAddress, int port,
      InetAddress broadcast) {
    return new Broadcast(interfaceAddress, port, broadcast);
  }

  public static ReadController createFileInput(File inputFile) {
    return new FileInput(inputFile);
  }

  public static ReadController createFileInput(String inputFilePath) {
    return createFileInput(new File(inputFilePath));
  }

  public static WriteController createFileOutput(File outputFile) {
    return new FileOutput(outputFile);
  }

  public static WriteController createFileOutput(String outputFilePath) {
    return createFileOutput(new File(outputFilePath));
  }

  /**
   * .
   */
  public interface BroadcastController extends IoController.WriteController {

    void open() throws SocketException;

    void close();
  }

  /**
   * .
   */
  public interface OpenCloseController {

    void connect(long paramLong) throws DeviceConnectionException;

    void disconnect(long paramLong);

    void addConnectionListener(long paramLong,
        ConnectionListener paramConnectionListener);

    void removeConnectionListener(long paramLong,
        ConnectionListener paramConnectionListener);
  }

  /**
   * .
   */
  public interface ReadController extends IoController.OpenCloseController {

    int available() throws IOException;

    int read(byte[] paramArrayOfByte, int paramInt1, int paramInt2)
        throws IOException;

    int read(byte[] paramArrayOfByte) throws IOException;

    int read() throws IOException;
  }

  /**
   * .
   */
  public interface WriteController extends IoController.OpenCloseController {

    void write(byte[] paramArrayOfByte, int paramInt1, int paramInt2)
        throws IOException;

    void write(byte[] paramArrayOfByte) throws IOException;

    void write(int paramInt) throws IOException;
  }

  /**
   * .
   */
  public interface ReadWriteController
      extends IoController.ReadController, IoController.WriteController {

  }

  private abstract static class Impl implements IoController.ReadWriteController {

    private final Lock lock = new ReentrantLock(true);
    private OutputStream outputStream = null;
    private InputStream inputStream = null;
    private Closeable closeable = null;

    @Override
    public final void connect(long key) throws DeviceConnectionException {
      this.lock.lock();
      try {
        try {
          connectImpl(key);
        } catch (IOException ioe) {
          LOG.info("Fail to connect " + getConnectionInformation() + ":key(" + key + ")!");
          disconnect(key);
          throw new DeviceConnectionException("Error, connecting! " + getConnectionInformation(),
              ioe);
        }
      } finally {
        this.lock.unlock();
      }
    }

    protected abstract void connectImpl(long paramLong) throws IOException;

    protected abstract String getConnectionInformation();

    @Override
    public final void disconnect(long key) {
      this.lock.lock();

      try {
        IoController.LOG.info("close socket: " + getConnectionInformation() + ":key(" + key + ")"
            + " for disconnect.");
        disconnectImpl(key);
        if (null != this.inputStream) {
          try {
            this.inputStream.close();
          } catch (IOException ioe2) {
            IoController.LOG.log(Level.SEVERE, null, ioe2);
          } finally {
            this.inputStream = null;
          }
        }
        if (null != this.outputStream) {
          try {
            this.outputStream.close();
          } catch (IOException ioe2) {
            IoController.LOG.log(Level.SEVERE, null, ioe2);
          } finally {
            this.outputStream = null;
          }
        }
        if (null != this.closeable) {
          try {
            this.closeable.close();
          } catch (IOException ioe2) {
            IoController.LOG.log(Level.SEVERE, null, ioe2);
          } finally {
            this.closeable = null;
          }
        }
      } finally {
        this.lock.unlock();
      }
    }

    protected void disconnectImpl(long key) {

    }

    @Override
    public final int available() throws IOException {
      this.lock.lock();
      try {
        return getInputStream().available();
      } finally {
        this.lock.unlock();
      }
    }

    @Override
    public final int read(byte[] data, int offset, int length) throws IOException {
      this.lock.lock();
      try {
        return getInputStream().read(data, offset, length);
      } finally {
        this.lock.unlock();
      }
    }

    @Override
    public final int read(byte[] data) throws IOException {
      this.lock.lock();
      try {
        return getInputStream().read(data);
      } finally {
        this.lock.unlock();
      }
    }

    @Override
    public final int read() throws IOException {
      this.lock.lock();
      try {
        return getInputStream().read();
      } finally {
        this.lock.unlock();
      }
    }

    @Override
    public final void write(byte[] data, int offset, int length) throws IOException {
      this.lock.lock();
      try {
        getOutputStream().write(data, offset, length);
        getOutputStream().flush();
      } finally {
        this.lock.unlock();
      }
    }

    @Override
    public final void write(byte[] data) throws IOException {
      this.lock.lock();
      try {
        getOutputStream().write(data);
        getOutputStream().flush();
      } finally {
        this.lock.unlock();
      }
    }

    @Override
    public final void write(int data) throws IOException {
      this.lock.lock();
      try {
        getOutputStream().write(data);
        getOutputStream().flush();
      } finally {
        this.lock.unlock();
      }
    }

    protected final void setCloseable(Closeable closeable) {
      this.closeable = closeable;
    }

    protected final void setOutputStream(OutputStream outputStream) {
      this.outputStream = outputStream;
    }

    protected final void setInputStream(InputStream inputStream) {
      this.inputStream = inputStream;
    }

    private InputStream getInputStream() throws IOException {
      if (null == this.inputStream) {
        throw new IOException("Input stream is null!");
      }
      return this.inputStream;
    }

    private OutputStream getOutputStream() throws IOException {
      if (null == this.outputStream) {
        throw new IOException("Output stream is null!");
      }
      return this.outputStream;
    }
  }

  private static final class Broadcast implements IoController.BroadcastController {

    private final InterfaceAddress interfaceAddress;
    private DatagramSocket socket = null;
    private final int port;
    private final InetAddress broadcast;

    public Broadcast(InterfaceAddress interfaceAddress, int port, InetAddress broadcast) {
      this.interfaceAddress = interfaceAddress;
      this.port = port;
      this.broadcast = broadcast;
    }

    @Override
    public void open() throws SocketException {
      InetSocketAddress adr = new InetSocketAddress(this.interfaceAddress.getAddress(), 0);
      this.socket = new DatagramSocket(adr);
      this.socket.setBroadcast(true);
    }

    @Override
    public void write(byte[] data) throws IOException {
      DatagramPacket packet = new DatagramPacket(data, 0, data.length, this.broadcast, this.port);
      if (this.socket != null) {
        this.socket.send(packet);
      }

    }

    @Override
    public void write(byte[] data, int offset, int length) throws IOException {
      throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public void write(int data) throws IOException {
      throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public void close() {
      if (this.socket != null) {
        this.socket.close();
      }
    }

    @Override
    public void connect(long key) throws DeviceConnectionException {
      throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public void disconnect(long key) {
      throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public void addConnectionListener(long key, ConnectionListener listener) {
      throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public void removeConnectionListener(long key, ConnectionListener listener) {
      throw new UnsupportedOperationException("Not supported yet.");
    }
  }

  private static final class Socket extends IoController.Impl {

    private final String host;
    private final int port;
    private CloseableSocket sock;

    public Socket(String host, int port) {
      super();
      this.host = host;
      this.port = port;
    }

    @Override
    public void addConnectionListener(long key, ConnectionListener listener) {
      IoController.manager.addSocketistener(key, listener);
    }

    @Override
    public void removeConnectionListener(long key, ConnectionListener listener) {
      IoController.manager.removeSocketListener(key, listener);
    }

    @Override
    protected void connectImpl(long key) throws IOException {
      CloseableSocket oldSock = this.sock;
      this.sock = IoController.manager.getSocket(key, this.host, this.port);
      if (oldSock == null || !oldSock.equals(this.sock)) {
        this.sock.setSoTimeout(10000);
        setCloseable(this.sock);
        setOutputStream(this.sock.getOutputStream());
        setInputStream(this.sock.getInputStream());
      }
    }

    @Override
    protected void disconnectImpl(long key) {
      IoController.manager.remove(key);
      this.sock = null;
    }

    @Override
    protected String getConnectionInformation() {
      return this.host + ":" + this.port;
    }
  }

  private static final class FileInput extends IoController.Impl {

    private final File file;

    public FileInput(File file) {
      super();
      this.file = file;
    }

    @Override
    protected void connectImpl(long key) throws IOException {
      setInputStream(createInputStream());
    }

    private InputStream createInputStream() throws IOException {
      return new FileInputStream(this.file);
    }

    @Override
    protected String getConnectionInformation() {
      return String.format("Opening File(%s) for reading failed.",
          this.file.getAbsolutePath());
    }

    @Override
    public void addConnectionListener(long key, ConnectionListener listener) {
      throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public void removeConnectionListener(long key, ConnectionListener listener) {
      throw new UnsupportedOperationException("Not supported yet.");
    }
  }

  private static final class FileOutput extends IoController.Impl {

    private final File file;

    public FileOutput(File file) {
      super();
      this.file = file;
    }

    @Override
    protected void connectImpl(long key) throws IOException {
      setOutputStream(createOutputStream());
    }

    private OutputStream createOutputStream() throws IOException {
      return new FileOutputStream(this.file);
    }

    @Override
    protected String getConnectionInformation() {
      return String.format("Opening File(%s) for reading failed.",
          this.file.getAbsolutePath());
    }

    @Override
    public void addConnectionListener(long key, ConnectionListener listener) {
      throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public void removeConnectionListener(long key, ConnectionListener listener) {
      throw new UnsupportedOperationException("Not supported yet.");
    }
  }

  private static final class SocketManager {

    @SuppressWarnings("unused")
    private static final long INTERVAL = 30000L;
    private final Map<Long, CloseableSocket> sockets = new HashMap<>();
    private final Map<Long, Long> lastRequestMapper = new HashMap<>();
    private static Timer timer = new Timer("SocketManager", true);
    private final Lock lock = new ReentrantLock();
    private Map<Long, ConnectionListener> listenerMap = new HashMap<>();

    public SocketManager() {
      timer.schedule(new SocketDeallocator(), 30000L, 30000L);
    }

    public void addSocketistener(long key, ConnectionListener listener) {
      this.lock.lock();
      try {
        if (!this.listenerMap.containsKey(key)) {
          this.listenerMap.put(key, listener);
        }
      } finally {
        this.lock.unlock();
      }
    }

    public void removeSocketListener(long key, ConnectionListener listener) {
      this.lock.lock();
      try {
        this.listenerMap.remove(key);
      } finally {
        this.lock.unlock();
      }
    }

    private void fireSocketOpened(long key) {
      ConnectionListener listener = this.listenerMap.get(key);
      if (listener != null) {
        listener.connected();
      }
    }

    public CloseableSocket getSocket(long key, String host, int port) throws IOException {
      CloseableSocket socket = getSocket(key);
      if (socket == null) {
        socket = new CloseableSocket();
        socket.setTcpNoDelay(true);
        socket.connect(new InetSocketAddress(host, port), 10000);
        fireSocketOpened(key);
        addSocket(key, socket);
        IoController.LOG.info("create socket: " + key + "  host " + host);
      }

      return socket;
    }

    private CloseableSocket getSocket(long key) {
      this.lock.lock();
      try {
        Long keyObject = key;
        CloseableSocket result = this.sockets.get(keyObject);
        if (result != null) {
          this.lastRequestMapper.put(keyObject, System.currentTimeMillis());
        }
        return result;
      } finally {
        this.lock.unlock();
      }
    }

    private void addSocket(long key, CloseableSocket socket) {
      this.lock.lock();
      try {
        Long keyObject = key;
        sockets.put(keyObject, socket);
        this.lastRequestMapper.put(keyObject, System.currentTimeMillis());
      } finally {
        this.lock.unlock();
      }
    }

    public void remove(long key) {
      this.lock.lock();
      try {
        this.sockets.remove(key);
        this.lastRequestMapper.remove(key);
      } finally {
        this.lock.unlock();
      }
    }

    private final class SocketDeallocator extends TimerTask {

      private final List<Long> deleteableKeys = new ArrayList<>();

      private SocketDeallocator() {

      }

      @Override
      public void run() {
        IoController.SocketManager.this.lock.lock();
        try {
          for (Map.Entry<Long, Long> entry : IoController.SocketManager.this.lastRequestMapper
              .entrySet()) {
            if (entry.getValue() + 30000L < System.currentTimeMillis()) {
              CloseableSocket socket =
                  IoController.SocketManager.this.sockets.remove(entry.getKey());
              this.deleteableKeys.add(entry.getKey());
              try {
                socket.getInputStream().close();
                socket.close();
              } catch (IOException ex) {
                IoController.LOG.log(Level.WARNING, null, ex);
              }
              IoController.LOG.info("close socket: " + socket.getInetAddress().getHostAddress()
                  + ":" + socket.getPort() + " for deallocator.");
            }
          }
          for (Long key : this.deleteableKeys) {
            IoController.SocketManager.this.lastRequestMapper.remove(key);
          }
          this.deleteableKeys.clear();
        } finally {
          IoController.SocketManager.this.lock.unlock();
        }
      }
    }
  }
}

