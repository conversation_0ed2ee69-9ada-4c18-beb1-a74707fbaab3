package com.mc.tool.caesar.api;

import lombok.Getter;

/**
 * 外设参数枚举.
 */
@Getter
public enum CaesarExtArg {
  EXT_ARG_ANALOG_INPUT_ID(1),
  EXT_ARG_USB_ENABLE_ID(2),
  EXT_ARG_VIDEO_QP_ID(4),
  EXT_ARG_TOUCHING_SCREEN_ID(5),
  EXT_ARG_AUDIO_TRIGGER(7),
  EXT_ARG_EVENT_ENABLE(8),
  EXT_ARG_TRIGGER_HOLD_TIME(9),
  EXT_ARG_HIGH_COMPRESSION_RATIO(10),
  EXT_ARG_OSD_MENU(11),
  EXT_ARG_ICRON_ENABLE(12), // USB透传 1-开 0-关
  EXT_ARG_UART_BAUD_RATE(13), // 串口波特率 0-115200 1-56000 2-38400 3-19200 4-14400 5-9600 6-4800 7-2400
  EXT_ARG_DOUBLE_DP_ENABLE(14); // 双DP开关 1-双屏 0-单屏

  CaesarExtArg(int val) {
    this.value = val;
  }

  private final int value;

}
