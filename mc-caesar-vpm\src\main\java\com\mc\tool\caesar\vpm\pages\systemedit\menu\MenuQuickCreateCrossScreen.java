package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.CaesarOperationPageNew;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCrossScreenFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import java.util.Collections;
import java.util.Optional;
import java.util.stream.Collectors;
import javafx.scene.control.AlertEx;
import javafx.scene.control.ButtonType;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class MenuQuickCreateCrossScreen extends MenuQuickCreate {

  private final CaesarDeviceController deviceController;

  /** 快速创建跨屏组. */
  public MenuQuickCreateCrossScreen(
      SystemEditControllable controllable, CaesarDeviceController deviceController) {
    super(controllable);
    this.deviceController = deviceController;
    this.setText(Bundle.NbBundle.getMessage("menu.grouping.crossscreen"));
  }

  @Override
  protected void initTerminal() {
    nodes =
        controllable.getModel().getAllTerminals().stream()
            .filter(VisualEditTerminal::isRx)
            .filter(VisualEditTerminal::isOnline)
            .filter(item -> item.getParent() instanceof CaesarMatrix)
            .collect(Collectors.toList());
  }

  @Override
  protected void handleAction() {
    dialog
        .showAndWait()
        .ifPresent(
            nodeList -> {
              if (!nodeList.isEmpty()) {
                String groupName = nameField.getText();
                MultiScreenData screenData =
                    deviceController.getDataModel().getConfigDataManager().getFreeMultiScreenData();
                if (screenData == null) {
                  ViewUtility.showAlert(
                      getParentPopup().getOwnerWindow(),
                      CaesarI18nCommonResource.getString("cross_screen.fail_to_create"),
                      AlertEx.AlertExType.WARNING);
                  return;
                }
                Optional<MenuCrossScreen4Caesar.CrossScreenInfo> result =
                    MenuCrossScreen4Caesar.getCrossScreenInfoFromDialog(
                        getParentPopup().getOwnerWindow(), groupName, nodeList.size());
                if (result.isPresent()) {
                  CaesarCrossScreenFunc func =
                      controllable.addGroup(
                          result.get().getName(),
                          CaesarCrossScreenFunc.class,
                          nodeList.toArray(new VisualEditNode[0]));
                  if (func != null) {
                    func.setLayout(result.get().getRow(), result.get().getColumn());
                    deviceController.execute(
                        () -> {
                          screenData.setStatusActive(true);
                          screenData.setStatusNew(true);
                        });
                    func.setDeviceData(screenData);
                    // 监听名称修改
                    func.nameProperty()
                        .addListener(
                            (obs, oldVal, newVal) -> {
                              if (func.getDeviceData() == null
                                  || func.getDeviceData().isStatusNew()
                                  || !func.getDeviceData().isStatusActive()) {
                                return;
                              }
                              deviceController.execute(
                                  () -> {
                                    func.getDeviceData().setName(func.getName());
                                    try {
                                      deviceController
                                          .getDataModel()
                                          .sendMultiscreenData(
                                              Collections.singletonList(func.getDeviceData()));
                                    } catch (DeviceConnectionException | BusyException exception) {
                                      log.warn("Fail to send multiscreen data!", exception);
                                    }
                                  });
                            });

                    // 提示切换到配置页面
                    Optional<ButtonType> askResult =
                        ViewUtility.getConfirmResultFromDialog(
                            getParentPopup().getOwnerWindow(),
                            CaesarI18nCommonResource.getString("page.switch"),
                            CaesarI18nCommonResource.getString("page.switch_to_operation"));
                    if (askResult.isPresent() && askResult.get() == ButtonType.YES) {
                      ApplicationBase applicationBase =
                          InjectorProvider.getInjector().getInstance(ApplicationBase.class);
                      applicationBase
                          .getViewManager()
                          .switchToPage(
                              controllable.getEntity(), CaesarOperationPageNew.NAME, func);
                    }
                  }
                }
              }
            });
  }

  @Override
  protected void initBundle() {
    groupNameLabel.setText(Bundle.NbBundle.getMessage("menu.grouping.crossscreen.name"));
    dialog.setTitle(Bundle.NbBundle.getMessage("menu.grouping.crossscreen"));
  }

  @Override
  protected void setNameFieldText() {
    nameField.setText("Group");
  }
}
