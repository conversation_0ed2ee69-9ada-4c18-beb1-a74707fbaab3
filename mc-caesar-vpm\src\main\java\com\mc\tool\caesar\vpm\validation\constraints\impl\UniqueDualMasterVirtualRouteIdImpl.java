package com.mc.tool.caesar.vpm.validation.constraints.impl;

import com.mc.tool.caesar.api.datamodel.SystemConfigData;
import com.mc.tool.caesar.vpm.validation.constraints.UniqueDualMasterVirtualRouteId;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 验证路由ID不能与其他网络配置的路由ID相同的实现类.
 * 此验证器需要通过自定义的ConstraintValidatorFactory注入SystemConfigData依赖.
 */
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UniqueDualMasterVirtualRouteIdImpl
    implements ConstraintValidator<UniqueDualMasterVirtualRouteId, Integer> {

  /**
   * 系统配置数据.
   */
  private SystemConfigData systemConfigData;

  @Override
  public void initialize(UniqueDualMasterVirtualRouteId constraintAnnotation) {
    // 初始化方法，暂时不需要特殊处理
  }

  @Override
  public boolean isValid(Integer value, ConstraintValidatorContext context) {
    if (systemConfigData == null || value == null
        || systemConfigData.getNetworkDataPreset2() == null) {
      return true; // 无法获取systemConfigData，跳过验证
    }
    // 获取路由ID
    int routeId = systemConfigData.getNetworkDataPreset2().getNetworkBits();
    return !value.equals(routeId);
  }
}
