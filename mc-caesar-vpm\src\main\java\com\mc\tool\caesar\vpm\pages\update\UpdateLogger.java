package com.mc.tool.caesar.vpm.pages.update;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class UpdateLogger {
  private ObservableList<String> logs;

  /**
   * Contructor.
   *
   * @param logs 日志列表
   */
  public UpdateLogger(ObservableList<String> logs) {
    this.logs = logs;
  }

  public UpdateLogger() {}

  /**
   * 添加日志.
   *
   * @param log 日志文本
   */
  public void addLog(String log, Object... exceptions) {
    UpdateLogger.log.info(log, exceptions);
    final String newlog =
        "["
            + DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS").format(LocalDateTime.now())
            + "] "
            + log;
    Platform.runLater(() -> getLogs().add(newlog));
  }

  /** 清除日志. */
  public void clearLog() {
    Platform.runLater(() -> getLogs().clear());
  }

  /**
   * 获取日志内容.
   *
   * @return 日志内容
   */
  public ObservableList<String> getLogs() {
    if (logs == null) {
      logs = FXCollections.observableArrayList();
    }
    return logs;
  }
}
