package com.mc.tool.caesar.api.utils;

import com.mc.tool.caesar.api.interfaces.Threshold;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.TreeMap;

/**
 * .
 */
public final class PropertyObject implements Comparable<PropertyObject> {

  private final String property;
  private final int[] indizes;
  private Map<Integer, Object> valueMap = Collections.emptyMap();

  /**
   * PropertyObject.
   */
  public PropertyObject(String property, int[] indizes) {
    assert null != property : "propertyName is null";
    assert null != indizes : "indizes is null";
    for (int index = 0; index < indizes.length; index++) {
      assert indizes[index] >= 0 : "indizes[" + index + "] must be >= 0";
    }
    this.property = property;
    this.indizes = Arrays.copyOf(indizes, indizes.length);
  }

  public int[] getIndizes() {
    return Arrays.copyOf(this.indizes, this.indizes.length);
  }

  public String getProperty() {
    return this.property;
  }

  /**
   * overrideValue.
   */
  public boolean overrideValue(Threshold threshold, Object obj) {
    PropertyObject propertyObject = this;
    synchronized (propertyObject) {
      return this.addValue(threshold, obj, true);
    }
  }

  /**
   * addValue.
   */
  public boolean addValue(Threshold threshold, Object obj) {
    PropertyObject propertyObject = this;
    synchronized (propertyObject) {
      return this.addValueImpl(threshold.getThreshold(), obj, false);
    }
  }

  /**
   * addValue.
   */
  public boolean addValue(Threshold threshold, Object obj, boolean override) {
    PropertyObject propertyObject = this;
    synchronized (propertyObject) {
      return this.addValueImpl(threshold.getThreshold(), obj, override);
    }
  }

  /**
   * addValueImpl.
   */
  private boolean addValueImpl(int threshold, Object obj, boolean override) {
    synchronized (this) {
      boolean add = override || !this.valueMap.containsKey(threshold);
      if (add) {
        if (this.valueMap.isEmpty()) {
          this.valueMap = new HashMap<>();
        }
        this.valueMap.put(threshold, obj);
      }
      return add;
    }
  }

  /**
   * removeValues.
   */
  public Object removeValues(Threshold threshold) {
    synchronized (this) {
      if (this.valueMap.isEmpty()) {
        return Boolean.TRUE;
      }
      Object smallestValue = null;
      int smallestThreshold = Integer.MAX_VALUE;
      Iterator<Map.Entry<Integer, Object>> it = this.valueMap.entrySet().iterator();
      while (it.hasNext()) {
        Map.Entry<Integer, Object> entry = it.next();
        if (entry.getKey() >= threshold.getThreshold()) {
          if (entry.getKey().compareTo(smallestThreshold) < 0) {
            smallestValue = entry.getValue();
          }
          it.remove();
        }
      }
      if (this.valueMap.isEmpty()) {
        this.valueMap = Collections.emptyMap();
      }
      return smallestValue;
    }
  }

  /**
   * getValue.
   */
  public Object getValue(Threshold threshold) {
    synchronized (this) {
      int smallestThreshold = Integer.MAX_VALUE;
      for (Integer key : this.valueMap.keySet()) {
        if (key >= threshold.getThreshold()) {
          smallestThreshold = Math.min(smallestThreshold, key);
        }
      }
      return this.valueMap.get(smallestThreshold);
    }
  }

  /**
   * isChanged.
   */
  public boolean isChanged() {
    synchronized (this) {
      return !this.valueMap.isEmpty();
    }
  }

  /**
   * isChanged.
   */
  public boolean isChanged(Threshold threshold) {
    synchronized (this) {
      for (Integer key : this.valueMap.keySet()) {
        if (key >= threshold.getThreshold()) {
          return true;
        }
      }
      return false;
    }
  }

  /**
   * equals.
   */
  public boolean equals(Threshold threshold) {
    synchronized (this) {
      for (Integer key : this.valueMap.keySet()) {
        if (key == threshold.getThreshold()) {
          return true;
        }
      }
      return false;
    }
  }

  @Override
  public boolean equals(Object obj) {
    if (null == obj || !getClass().equals(obj.getClass())) {
      return false;
    }
    PropertyObject other = (PropertyObject) obj;

    return this.property.equals(other.property) && Arrays.equals(this.indizes, other.indizes);
  }

  /**
   * commit.
   */
  public void commit(Threshold threshold) {
    synchronized (this) {
      if (!isChanged(threshold)) {
        return;
      }
      // 把threshold最小的值放到小一级的threshold来存储
      Object smallestEntry = removeValues(threshold);
      if (threshold.getThreshold() > Threshold.ALL.getThreshold()) {
        addValueImpl(threshold.getThreshold() - 1, smallestEntry, false);
      }
    }
  }

  @Override
  public int hashCode() {
    int hash = 7;

    hash *= 29;
    hash += this.property.hashCode();
    hash *= 29;
    hash += Arrays.hashCode(this.indizes);

    return hash;
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();

    sb.append("PropertyObject[property=");
    sb.append(this.property);
    sb.append("; indizes=");
    sb.append(Arrays.toString(this.indizes));
    sb.append("; value=");
    sb.append(this.valueMap.entrySet());
    sb.append(']');

    return sb.toString();
  }

  @Override
  public int compareTo(PropertyObject po) {
    if (null == po) {
      return 1;
    }
    int cmpProperty = this.property.compareTo(po.property);
    if (0 != cmpProperty) {
      return cmpProperty;
    }
    int cmpIndexLength = po.indizes.length - this.indizes.length;
    if (0 != cmpIndexLength) {
      return cmpIndexLength;
    }
    for (int i = 0; i < this.indizes.length; i++) {
      int cmpIndex = po.indizes[i] - this.indizes[i];
      if (0 != cmpIndex) {
        return cmpIndex;
      }
    }
    return 0;
  }

  synchronized Map<Integer, Object> getValueMap() {
    return new TreeMap<>(this.valueMap);
  }
}

