ExtenderOsdUpdate.alert.warning.packageError.text=\u5347\u7EA7\u5305\u4E0D\u5339\u914D
ExtenderOsdUpdate.alert.warning.title=\u5347\u7EA7\u5305\u9519\u8BEF
ExtenderOsdUpdate.alert.warning.versionError.text=\u7248\u672C\u4FE1\u606F\u4E22\u5931
ExtenderOsdUpdate.cancel_btn=\u53D6\u6D88
ExtenderOsdUpdate.currentMd5Col.text=\u5F53\u524D\u7248\u672C
ExtenderOsdUpdate.deviceCol.text=\u8BBE\u5907
ExtenderOsdUpdate.hardwareVerCol.text=\u786C\u4EF6\u7248\u672C
ExtenderOsdUpdate.load_file=\u8F7D\u5165\u6587\u4EF6
ExtenderOsdUpdate.nameCol.text=\u5916\u8BBE
ExtenderOsdUpdate.offline=\u79BB\u7EBF\u8BBE\u5907
ExtenderOsdUpdate.pageTitle.text=\u5916\u8BBEOSD\u5347\u7EA7
ExtenderOsdUpdate.portCol.text=\u7AEF\u53E3
ExtenderOsdUpdate.progressCol.text=\u5347\u7EA7\u8FDB\u5EA6
ExtenderOsdUpdate.seleteAllButton.text=\u5168\u9009
ExtenderOsdUpdate.seleteAllButton.text.opposite=\u53D6\u6D88\u5168\u9009
ExtenderOsdUpdate.serialCol.text=\u5E8F\u5217\u53F7
ExtenderOsdUpdate.typeCol.text=\u7C7B\u578B
ExtenderOsdUpdate.updateDate.text=\u5347\u7EA7
ExtenderOsdUpdate.updateMd5Col.text=\u5347\u7EA7\u7248\u672C
ExtenderOsdUpdate.update_btn=\u5347\u7EA7
update_log=\u66F4\u65B0\u65E5\u5FD7
