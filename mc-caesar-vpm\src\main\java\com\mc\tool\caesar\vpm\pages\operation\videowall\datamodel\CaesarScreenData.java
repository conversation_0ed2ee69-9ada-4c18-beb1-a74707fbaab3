package com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.ScreenObject;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.util.Pair;
import lombok.Getter;

/**
 * .
 */
public class CaesarScreenData implements ScreenObject {

  @Expose @Getter private ObjectProperty<VpGroup> bindingVpGroup = new SimpleObjectProperty<>(null);

  @Expose @Getter private IntegerProperty bindingPortIndex = new SimpleIntegerProperty(-1);

  @Override
  public boolean isEmpty() {
    return getVpScreen() == null;
  }

  public void setBingdingScreen(Pair<VpGroup, Integer> screen) {
    bindingPortIndex.set(screen.getValue());
    bindingVpGroup.set(screen.getKey());
  }

  /**
   * 获取绑定的vpcon数据.
   *
   * @return vpcon数据.
   */
  public VpConsoleData getVpScreen() {
    Pair<VpGroup, Integer> pair = new Pair<>(bindingVpGroup.get(), bindingPortIndex.get());
    if (pair.getKey() != null
        && pair.getValue() != null
        && pair.getKey().getVpConsoleData() != null
        && pair.getValue() >= 0
        && pair.getValue() < pair.getKey().getVpConsoleData().getOutPortCount()) {
      return pair.getKey().getVpConsoleData().getOutPortList()[pair.getValue()];
    } else {
      return null;
    }
  }

  @Override
  public void copyTo(ScreenObject screen) {
    if (!(screen instanceof CaesarScreenData)) {
      return;
    }
    CaesarScreenData caesarScreenData = (CaesarScreenData) screen;
    caesarScreenData.bindingVpGroup.set(bindingVpGroup.get());
    caesarScreenData.bindingPortIndex.set(bindingPortIndex.get());
  }
}
