package com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard;

import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.utility.UndecoratedWizard;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.beans.value.ChangeListener;
import javafx.scene.control.Button;
import javafx.scene.control.TableCell;
import javafx.scene.paint.Color;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public class MatrixGridConfigUtility {

  public static final String KEY_GRID_NAME = "key_grid_name";
  public static final String KEY_HOST_CONFIG = "key_host_config";
  public static final String KEY_DELETE_CONFIG = "key_delete_config";

  static final Color VALID_COLOR = Color.web("#60be51");
  static final Color INVALID_COLOR = Color.web("#cb594b");

  static final Integer[] PORT_LIST = new Integer[] {18, 36, 96, 124, 288, 384, 816};

  enum ValidateStatus {
    UNCHECK,
    VALID,
    DUPLICATE,
    UNACCESSABLE,
    UNCOMPLETE,
    NAME_EMPTY,
    NAME_TOO_LONG,
    NAME_DULPLICATE,
    NAME_FORMAT_ERROR,
    PORT_ERROR,
    PORT_TOO_MUCH
  }

  enum GridStatus {
    NORMAL,
    NEW,
    DELETE
  }

  static Collection<MatrixData4HostConfig> getMatrixGridConfigFromWizard(UndecoratedWizard wizard) {
    Object setting = wizard.getSettings().get(MatrixGridConfigUtility.KEY_HOST_CONFIG);
    List<MatrixData4HostConfig> items = new ArrayList<>();
    if (setting instanceof List<?>) {
      List<?> list = (List<?>) setting;
      for (Object item : list) {
        if (item instanceof MatrixData4HostConfig) {
          MatrixData4HostConfig config = (MatrixData4HostConfig) item;
          items.add(config);
        }
      }
    }
    return items;
  }

  static Collection<MatrixData4HostConfig> getMatrixGridDeleteItemsFromWizard(
      UndecoratedWizard wizard) {
    Object setting = wizard.getSettings().get(MatrixGridConfigUtility.KEY_DELETE_CONFIG);
    List<MatrixData4HostConfig> items = new ArrayList<>();
    if (setting instanceof List<?>) {
      List<?> list = (List<?>) setting;
      for (Object item : list) {
        if (item instanceof MatrixData4HostConfig) {
          MatrixData4HostConfig config = (MatrixData4HostConfig) item;
          items.add(config);
        }
      }
    }
    return items;
  }

  static boolean isWizardHasGridName(UndecoratedWizard wizard) {
    return wizard.getSettings().get(MatrixGridConfigUtility.KEY_GRID_NAME) != null;
  }

  static String getMatrixGridNameFromWizard(UndecoratedWizard wizard) {
    Object setting = wizard.getSettings().get(MatrixGridConfigUtility.KEY_GRID_NAME);
    if (setting instanceof String) {
      return (String) setting;
    } else {
      return "";
    }
  }

  static class MatrixData4HostConfig {
    @Setter @Getter private boolean merge = false;
    public boolean local = false;
    public StringProperty ip = new SimpleStringProperty("");
    public StringProperty user = new SimpleStringProperty("");
    public StringProperty pwd = new SimpleStringProperty("");
    public IntegerProperty port = new SimpleIntegerProperty(PORT_LIST[0]);
    public StringProperty name = new SimpleStringProperty("");
    public BooleanProperty keepId = new SimpleBooleanProperty(false);
    public ObjectProperty<ValidateStatus> hostConfigValid =
        new SimpleObjectProperty<>(ValidateStatus.UNCHECK);
    public ObjectProperty<ValidateStatus> gridSystemValid =
        new SimpleObjectProperty<>(ValidateStatus.UNCHECK);
    public ObjectProperty<GridStatus> gridStatus = new SimpleObjectProperty<>(GridStatus.NORMAL);

    private ChangeListener<String> changeListener;

    public MatrixData4HostConfig() {
      changeListener =
          (obs, oldVal, newVal) -> {
            hostConfigValid.set(ValidateStatus.UNCHECK);
            gridSystemValid.set(ValidateStatus.UNCHECK);
          };

      ip.addListener(changeListener);
      user.addListener(changeListener);
      pwd.addListener(changeListener);
      name.addListener((obs, oldVal, newVal) -> gridSystemValid.set(ValidateStatus.UNCHECK));
      port.addListener(
          (obs, oldVal, newVal) -> {
            hostConfigValid.set(ValidateStatus.UNCHECK);
            gridSystemValid.set(ValidateStatus.UNCHECK);
          });
    }
  }

  static class CategoryTableCell extends TableCell<MatrixData4HostConfig, String> {

    @Override
    public void updateIndex(int index) {
      super.updateIndex(index);
      if (!isEmpty()) {
        setText(
            CaesarI18nCommonResource.getString("matrixgrid.wizard.hostconfig.matrix")
                + (index + 1));
      } else {
        setText(null);
      }
    }
  }

  static class ValidTableCell extends TableCell<MatrixData4HostConfig, ValidateStatus> {

    @Override
    protected void updateItem(ValidateStatus item, boolean empty) {
      super.updateItem(item, empty);
      if (!empty && item != null) {
        switch (item) {
          case UNCHECK:
            setText("");
            break;
          case VALID:
            setText(CaesarI18nCommonResource.getString("matrixgrid.wizard.hostconfig.valid"));
            setTextFill(MatrixGridConfigUtility.VALID_COLOR);
            break;
          case DUPLICATE:
            setText(CaesarI18nCommonResource.getString("matrixgrid.wizard.hostconfig.duplicate"));
            setTextFill(MatrixGridConfigUtility.INVALID_COLOR);
            break;
          case UNACCESSABLE:
            setText(
                CaesarI18nCommonResource.getString("matrixgrid.wizard.hostconfig.unaccessable"));
            setTextFill(MatrixGridConfigUtility.INVALID_COLOR);
            break;
          case UNCOMPLETE:
            setText(CaesarI18nCommonResource.getString("matrixgrid.wizard.hostconfig.uncomplete"));
            setTextFill(MatrixGridConfigUtility.INVALID_COLOR);
            break;
          case NAME_EMPTY:
            setText(CaesarI18nCommonResource.getString("matrixgrid.wizard.hostconfig.name_empty"));
            setTextFill(MatrixGridConfigUtility.INVALID_COLOR);
            break;
          case NAME_TOO_LONG:
            setText(
                CaesarI18nCommonResource.getString("matrixgrid.wizard.hostconfig.name_too_long"));
            setTextFill(MatrixGridConfigUtility.INVALID_COLOR);
            break;
          case NAME_DULPLICATE:
            setText(
                CaesarI18nCommonResource.getString("matrixgrid.wizard.hostconfig.name_dulplicate"));
            setTextFill(MatrixGridConfigUtility.INVALID_COLOR);
            break;
          case NAME_FORMAT_ERROR:
            setText(
                CaesarI18nCommonResource.getString(
                    "matrixgrid.wizard.hostconfig.name_format_error"));
            setTextFill(MatrixGridConfigUtility.INVALID_COLOR);
            break;
          case PORT_ERROR:
            setText(CaesarI18nCommonResource.getString("matrixgrid.wizard.hostconfig.port_error"));
            setTextFill(MatrixGridConfigUtility.INVALID_COLOR);
            break;
          case PORT_TOO_MUCH:
            setText(
                CaesarI18nCommonResource.getString("matrixgrid.wizard.hostconfig.port_too_much"));
            setTextFill(MatrixGridConfigUtility.INVALID_COLOR);
            break;
          default:
            break;
        }
      } else {
        setText(null);
      }
    }
  }

  @FunctionalInterface
  interface ValidateFunction {
    void call(ValidateTableCell cell, MatrixData4HostConfig config);
  }

  static class ValidateTableCell extends TableCell<MatrixData4HostConfig, String> {

    private final ValidateFunction validateFunc;
    private boolean ignoreLocal;

    public ValidateTableCell(ValidateFunction validateFunc) {
      this(validateFunc, false);
    }

    public ValidateTableCell(ValidateFunction validateFunc, boolean ignoreLocal) {
      this.validateFunc = validateFunc;
      this.ignoreLocal = ignoreLocal;
    }

    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);

      if (!empty
          && getTableRow() != null
          && getTableRow().getItem() instanceof MatrixData4HostConfig) {
        final MatrixData4HostConfig config = (MatrixData4HostConfig) getTableRow().getItem();
        Button button = new Button();
        button.setPrefSize(70, 25);
        button.setText(CaesarI18nCommonResource.getString("matrixgrid.wizard.hostconfig.validate"));
        button.getStyleClass().add("common-button");
        this.setGraphic(button);
        if (config.local && ignoreLocal) {
          button.setDisable(true);
        }
        button.setOnAction(
            event -> {
              if (validateFunc != null) {
                validateFunc.call(this, config);
              }
            });
      }
    }
  }
}
