package com.mc.tool.caesar.vpm.userright;

import com.mc.tool.caesar.vpm.devices.CaesarUserRightGetter;
import com.mc.tool.framework.systemedit.datamodel.VideoWallFunc;

/**
 * .
 */
public class CaesarUserRightDecoratorBase implements CaesarUserRightGetter {
  protected final CaesarUserRightGetter userRight;

  public CaesarUserRightDecoratorBase(CaesarUserRightGetter userRight) {
    this.userRight = userRight;
  }

  @Override
  public boolean isPageVisible(String pageName) {
    return userRight.isPageVisible(pageName);
  }

  @Override
  public boolean isSystemLogVisible() {
    return userRight.isSystemLogVisible();
  }

  @Override
  public boolean isActivateConfigVisible() {
    return userRight.isActivateConfigVisible();
  }

  @Override
  public boolean isOsdConfigVisible() {
    return userRight.isOsdConfigVisible();
  }

  @Override
  public boolean isCrossScreenCreateDeletable() {
    return userRight.isCrossScreenCreateDeletable();
  }

  @Override
  public boolean isCrossScreenConnectable() {
    return userRight.isCrossScreenConnectable();
  }

  @Override
  public boolean isMultiScreenCreateDeletable() {
    return userRight.isMultiScreenCreateDeletable();
  }

  @Override
  public boolean isMultiScreenConnectable() {
    return userRight.isMultiScreenConnectable();
  }

  @Override
  public boolean isTxConfigEditable() {
    return userRight.isTxConfigEditable();
  }

  @Override
  public boolean isRxConfigEditable() {
    return userRight.isRxConfigEditable();
  }

  @Override
  public boolean isUserEditable() {
    return userRight.isUserEditable();
  }

  @Override
  public boolean isUserRightEditable() {
    return userRight.isUserRightEditable();
  }

  @Override
  public boolean isRxRightEditable() {
    return userRight.isRxRightEditable();
  }

  @Override
  public boolean isUserGroupEditable() {
    return userRight.isUserGroupEditable();
  }

  @Override
  public boolean isUserGroupRightEditable() {
    return userRight.isUserGroupRightEditable();
  }

  @Override
  public boolean isRxHotKeyEditable() {
    return userRight.isRxHotKeyEditable();
  }

  @Override
  public boolean isRxMacroEditable() {
    return userRight.isRxMacroEditable();
  }

  @Override
  public boolean isUserMacroEditable() {
    return userRight.isUserMacroEditable();
  }

  @Override
  public boolean isVideoWallEditable(VideoWallFunc func) {
    return userRight.isVideoWallEditable(func);
  }

  @Override
  public boolean isTxGroupCreateDeletable() {
    return true;
  }

  @Override
  public boolean isSystemEditRenamable() {
    return userRight.isSystemEditRenamable();
  }

  @Override
  public boolean isSystemEditGroupCreateDeletable() {
    return userRight.isSystemEditGroupCreateDeletable();
  }

  @Override
  public boolean isVideoWallCreateDeletable() {
    return userRight.isVideoWallCreateDeletable();
  }

  @Override
  public boolean isSeatCreateDeletable() {
    return userRight.isSeatCreateDeletable();
  }

  @Override
  public boolean isDeviceTypeChangable() {
    return userRight.isDeviceTypeChangable();
  }

  @Override
  public boolean isDeviceItemCreateDeletable() {
    return userRight.isDeviceItemCreateDeletable();
  }

  @Override
  public boolean isDeviceConnectable() {
    return userRight.isDeviceConnectable();
  }

  @Override
  public boolean isDeviceItemMovable() {
    return userRight.isDeviceItemMovable();
  }

  @Override
  public boolean isSystemScenarioEditable() {
    return userRight.isSystemScenarioEditable();
  }

  @Override
  public boolean isVideoWallLayoutEditable(VideoWallFunc func) {
    return userRight.isVideoWallLayoutEditable(func);
  }

  @Override
  public boolean isVideoWallWindowCreateDeletable(VideoWallFunc func) {
    return userRight.isVideoWallWindowCreateDeletable(func);
  }

  @Override
  public boolean isVideoWallWindowMovablale(VideoWallFunc func) {
    return userRight.isVideoWallWindowMovablale(func);
  }

  @Override
  public boolean isVideoWallWindowResizable(VideoWallFunc func) {
    return userRight.isVideoWallWindowResizable(func);
  }

  @Override
  public boolean isVideoWallScenarioCreateDeletable(VideoWallFunc func) {
    return userRight.isVideoWallScenarioCreateDeletable(func);
  }

  @Override
  public boolean isVideoWallScenarioActivatable(VideoWallFunc func) {
    return userRight.isVideoWallScenarioActivatable(func);
  }

  @Override
  public boolean isVideoWallConnectable(VideoWallFunc func) {
    return userRight.isVideoWallConnectable(func);
  }

  @Override
  public boolean isSeatConnectable() {
    return userRight.isSeatConnectable();
  }

  @Override
  public boolean isSeatScenarioCreateDeletable() {
    return userRight.isSeatScenarioCreateDeletable();
  }

  @Override
  public boolean isSeatSceanrioActivatable() {
    return userRight.isSeatSceanrioActivatable();
  }
}
