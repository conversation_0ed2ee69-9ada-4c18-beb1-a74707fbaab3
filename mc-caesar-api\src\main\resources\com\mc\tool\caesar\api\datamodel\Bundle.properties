ConfigData.checkArrays=Invalid {0} array size, start reinit
ConfigData.warning.tooNew.message=<html>\r\n<b>Configuration file conflict!</b>\r\n<br><br>\r\n<table cellspacing='0' cellpadding='0'>\r\n    <tr>\r\n        <td width='200'>Current file version:</td>\r\n        <td><b>%s</b></td>\r\n    </tr>\r\n    <tr>\r\n        <td width='200'>Supported file version by the tool:</td>\r\n        <td><b>%s</b></td>\r\n    </tr>\r\n</table>\r\n<br>The configuration file is too new. You have to download a newer tool version:\r\n<br><br>\r\n<table cellspacing='0' cellpadding='0'>\r\n    <tr><td width='120'>File Version V03.03:</td><td>Tool version ******* or older</td></tr>\r\n    <tr><td width='120'>File Version V03.04:</td><td>Tool version 0.0.0.6 </td></tr>\r\n</table>
ConfigData.warning.tooNew.title=Configuration file conflict!
ConfigData.warning.unsupported.message=<html>\r\n<b>The firmware is not supported by the tool.</b>\r\n<br><br>\r\n<table cellspacing='0' cellpadding='0'>\r\n    <tr>\r\n        <td width='200'>Current firmware version:</td>\r\n        <td><b>&nbsp;&nbsp;&nbsp;%s</b></td>\r\n    </tr>\r\n    <tr>\r\n        <td width='240'>Supported firmware version by the tool:</td>\r\n        <td><b>&gt;&nbsp;%s</b></td>\r\n    </tr>\r\n</table>\r\n<br>Please use Tool version *******.
ConfigData.warning.unsupported.title=Unsupported firmware!
ConsoleData.CPU=CPU Connected
ConsoleData.CPU.Tooltip=CPU Connected
ConsoleData.ConsoleVirtual=CON Assigned
ConsoleData.ConsoleVirtual.Tooltip=CON Assigned
ConsoleData.ID=ID
ConsoleData.ID.Tooltip=ID
ConsoleData.Name=Name
ConsoleData.Name.Tooltip=Name
ConsoleData.RDCPU=Red. CPU Connected
ConsoleData.RDCPU.Tooltip=Redundant CPU Connected
ConsoleData.ScanTime=Scan Time [sec]
ConsoleData.ScanTime.Tooltip=Scan Time
ConsoleData.Status.AllowLogin=Allow User ACL
ConsoleData.Status.AllowLogin.Tooltip=Allow User ACL
ConsoleData.Status.AllowScan=Allow CPU Scan
ConsoleData.Status.AllowScan.Tooltip=Allow CPU Scan
ConsoleData.Status.ForceLogin=Force Login
ConsoleData.Status.ForceLogin.Tooltip=Force Login
ConsoleData.Status.ForceMacro=Show Macro List
ConsoleData.Status.ForceMacro.Tooltip=Show Macro List
ConsoleData.Status.ForceScan=Force CPU Scan
ConsoleData.Status.ForceScan.Tooltip=Force CPU Scan
ConsoleData.Status.LOSFrame=LOS Frame
ConsoleData.Status.LOSFrame.Tooltip=Loss Of Signal Frame
ConsoleData.Status.OsdDisabled=OSD Disabled
ConsoleData.Status.OsdDisabled.Tooltip=OSD Disabled
ConsoleData.Status.PortMode=Port Mode
ConsoleData.Status.PortMode.Tooltip=Operation mode that allows manual switching to ports 1-99 of each matrix.
ConsoleData.Status.Redundant=Redundancy Off
ConsoleData.Status.Redundant.Tooltip=Disable Redundancy for this CON Device
ConsoleData.Status.Virtual=Virtual Device
ConsoleData.Status.Virtual.Tooltip=Virtual Device
ControlGroupData.Arrangement=Arrangement
ControlGroupData.Arrangement.Tooltip=One row with four displays or two rows with two displays each
ControlGroupData.Control1=Control
ControlGroupData.Control1.Tooltip=Control
ControlGroupData.Control2=Control
ControlGroupData.Control2.Tooltip=Control
ControlGroupData.Control3=Control
ControlGroupData.Control3.Tooltip=Control
ControlGroupData.Control4=Control
ControlGroupData.Control4.Tooltip=Control
ControlGroupData.Enabled1=Enabled
ControlGroupData.Enabled1.Tooltip=Enabled
ControlGroupData.Enabled2=Enabled
ControlGroupData.Enabled2.Tooltip=Enabled
ControlGroupData.Enabled3=Enabled
ControlGroupData.Enabled3.Tooltip=Enabled
ControlGroupData.Enabled4=Enabled
ControlGroupData.Enabled4.Tooltip=Enabled
ControlGroupData.Frame1=Frame [sec]
ControlGroupData.Frame1.Tooltip=Frame
ControlGroupData.Frame2=Frame [sec]
ControlGroupData.Frame2.Tooltip=Frame
ControlGroupData.Frame3=Frame [sec]
ControlGroupData.Frame3.Tooltip=Frame
ControlGroupData.Frame4=Frame [sec]
ControlGroupData.Frame4.Tooltip=Frame
ControlGroupData.Manual=Manual
ControlGroupData.Manual.Tooltip=Reduce switching to manual switching with hotkeys.<br>Disable automatic switching with mouse for multihead CPUs
ControlGroupData.Name1=Name
ControlGroupData.Name1.Tooltip=Name
ControlGroupData.Name2=Name
ControlGroupData.Name2.Tooltip=Name
ControlGroupData.Name3=Name
ControlGroupData.Name3.Tooltip=Name
ControlGroupData.Name4=Name
ControlGroupData.Name4.Tooltip=Name
ControlGroupData.Owner1=Owner
ControlGroupData.Owner1.Tooltip=Owner
ControlGroupData.Owner2=Owner
ControlGroupData.Owner2.Tooltip=Owner
ControlGroupData.Owner3=Owner
ControlGroupData.Owner3.Tooltip=Owner
ControlGroupData.Owner4=Owner
ControlGroupData.Owner4.Tooltip=Owner
CpuData.Console=CON Connected
CpuData.Console.Tooltip=CON Connected
CpuData.ID=ID
CpuData.ID.Tooltip=ID
CpuData.Name=Name
CpuData.Name.Tooltip=Name
CpuData.RealCpu=CPU Assigned
CpuData.RealCpu.Tooltip=CPU Assigned
CpuData.Status.AllowPrivate=Allow Private
CpuData.Status.AllowPrivate.Tooltip=Allow Private
CpuData.Status.FixFrame=Fix Frame
CpuData.Status.FixFrame.Tooltip=Fix Frame
CpuData.Status.ForcePrivate=Force Private
CpuData.Status.ForcePrivate.Tooltip=Force Private
CpuData.Status.Virtual=Virtual Device
CpuData.Status.Virtual.Tooltip=Virtual Device
DisplayData.C_Speed=Double Click Time [ms]
DisplayData.C_Speed.Tooltip=Adjustment of the time slot for a double click
DisplayData.H_Speed=Horizontal Mouse Speed [1/x]
DisplayData.H_Speed.Tooltip=Adjustment of the horizontal mouse speed
DisplayData.Keyboard=Keyboard Layout
DisplayData.Keyboard.Tooltip=Set the OSD keyboard layout according to the used keyboard
DisplayData.V_Speed=Vertical Mouse Speed [1/x]
DisplayData.V_Speed.Tooltip=Adjustment of the vertical mouse speed
DisplayData.VideoMode=Video Mode
DisplayData.VideoMode.Tooltip=Video Mode
EdidData.Audio=Audio
EdidData.Audio.Tooltip=Audio
EdidData.EdidVersion=EDID Version
EdidData.EdidVersion.Tooltip=EDID Version
EdidData.HorizontalActivePixel=H. Active Pixels
EdidData.HorizontalActivePixel.Tooltip=Horizontal Active Pixels
EdidData.HorizontalFrequency=H. Frequency [kHz]
EdidData.HorizontalFrequency.Tooltip=H. Frequency
EdidData.ManufacturerID=Manufacturer Name
EdidData.ManufacturerID.Tooltip=Manufacturer Name
EdidData.ManufacturerProductCode=Product Code
EdidData.ManufacturerProductCode.Tooltip=Product Code
EdidData.ManufacturerWeek=Manufacturer Week
EdidData.ManufacturerWeek.Tooltip=Week of Manufacture
EdidData.ManufacturerYear=Manufacturer Year
EdidData.ManufacturerYear.Tooltip=Year of Manufacture
EdidData.PixelClock=Pixel Clock [Mhz]
EdidData.PixelClock.Tooltip=Pixel Clock
EdidData.SerialNumber=Serial Number
EdidData.SerialNumber.Tooltip=Serial Number
EdidData.State=EDID Checksum
EdidData.State.Invalid=Invalid
EdidData.State.Reset=Reset
EdidData.State.Tooltip=EDID Checksum
EdidData.State.Valid=Valid
EdidData.VerticalBlankingPixel=V. Active Lines
EdidData.VerticalBlankingPixel.Tooltip=Vertical Active Lines
EdidData.VerticalFrequency=V. Frequency [Hz]
EdidData.VerticalFrequency.Tooltip=V. Frequency
ExtenderData.Con=CON Assigned
ExtenderData.Con.Tooltip=CON Assigned
ExtenderData.Cpu=CPU Assigned
ExtenderData.Cpu.Tooltip=CPU Assigned
ExtenderData.CpuCon=CPU/CON Assigned
ExtenderData.CpuCon.Tooltip=CPU/CON Assigned
ExtenderData.ID=ID
ExtenderData.ID.Tooltip=ID
ExtenderData.Name=Name
ExtenderData.Name.Tooltip=Name
ExtenderData.Port=Port
ExtenderData.Port.Tooltip=Port
ExtenderData.RDPort=Redundant Port
ExtenderData.RDPort.Tooltip=Redundant Port
ExtenderData.Status.FixPort=Fixed
ExtenderData.Status.FixPort.Tooltip=Fixed
ExtenderData.Status.UsbDiskEnable=Usb Disk Enable
ExtenderData.Status.UsbDiskEnable.Tooltip=Usb Disk Enable
MatrixData.Active=Active
MatrixData.Device=Device
MatrixData.Ports=Ports
OsdData.Col=Horizontal Position [10 px]
OsdData.Col.Tooltip=Horizontal OSD position. Range: -128 ... +127
OsdData.OsdPosition=OSD Position Presets
OsdData.OsdPosition.Tooltip=OSD Position Presets
OsdData.Preview=Preview
OsdData.Preview.Tooltip=Preview
OsdData.Row=Vertical Position [10 px]
OsdData.Row.Tooltip=Vertical OSD position. Range: -128 ... +127
OsdData.Status.Active=Enable Connection Info
OsdData.Status.Active.Tooltip=Enable Connection Info to show the current connection status
OsdData.Status.Select=Enable CPU Selection
OsdData.Status.Select.Tooltip=<html>When executing the key sequence for opening the OSD,<br>a selection list for switching CPU Devices will<br>be displayed in the position of the extender OSD.
OsdData.Status.Update=Update Connection Info
OsdData.Status.Update.Tooltip=Connections will be updated during fade-in of Extender OSD
OsdData.Timeout=Display Time [sec]
OsdData.Timeout.Tooltip=Duration of OSD fade-in (0 = unlimited)
SystemConfigData.AccessData.ForceBits.ConAccess=Enable New CON
SystemConfigData.AccessData.ForceBits.ConAccess.Tooltip=Enable CPU access for new CON devices
SystemConfigData.AccessData.ForceBits.ConLock=Enable Console ACL
SystemConfigData.AccessData.ForceBits.ConLock.Tooltip=Enable CPU Access Control List for all consoles
SystemConfigData.AccessData.ForceBits.CpuDisconnct=Auto Disconnect
SystemConfigData.AccessData.ForceBits.CpuDisconnct.Tooltip=Disconnect console from current CPU upon opening the OSD
SystemConfigData.AccessData.ForceBits.Login=Force User Login
SystemConfigData.AccessData.ForceBits.Login.Tooltip=Require user login to enter OSD
SystemConfigData.AccessData.ForceBits.UserAccess=Enable New User
SystemConfigData.AccessData.ForceBits.UserAccess.Tooltip=Enable CPU access for new users
SystemConfigData.AccessData.ForceBits.UserConAnd=AND User/CON ACL
SystemConfigData.AccessData.ForceBits.UserConAnd.Tooltip=AND user and CON Access Control List (reduce access)
SystemConfigData.AccessData.ForceBits.UserConOr=OR User/CON ACL
SystemConfigData.AccessData.ForceBits.UserConOr.Tooltip=OR user and CON Access Control List (extend access)
SystemConfigData.AccessData.ForceBits.UserLock=Enable User ACL
SystemConfigData.AccessData.ForceBits.UserLock.Tooltip=Enable CPU Access Control List for all users
SystemConfigData.AccessData.TimeoutDisplay=OSD Timeout [sec]
SystemConfigData.AccessData.TimeoutDisplay.Tooltip=Specify inactivity time to quit OSD automatically (0 = deactivated)
SystemConfigData.AccessData.TimeoutLogout=Auto Logout [min]
SystemConfigData.AccessData.TimeoutLogout.Tooltip=Specify inactivity time for automatic user logout (0 = deactivated, -1 = unlimited)
SystemConfigData.AutoIdData.AutoId.RealConId=ID Real CON Device
SystemConfigData.AutoIdData.AutoId.RealConId.Tooltip=Start ID for automatic assignment of real CON devices
SystemConfigData.AutoIdData.AutoId.RealCpuId=ID Real CPU Device
SystemConfigData.AutoIdData.AutoId.RealCpuId.Tooltip=Start ID for automatic assignment of real CPU devices
SystemConfigData.AutoIdData.AutoId.VirtualConId=ID Virtual CON Device
SystemConfigData.AutoIdData.AutoId.VirtualConId.Tooltip=Start ID for automatic assignment of virtual CON devices
SystemConfigData.AutoIdData.AutoId.VirtualCpuId=ID Virtual CPU Device
SystemConfigData.AutoIdData.AutoId.VirtualCpuId.Tooltip=Start ID for automatic assignment of virtual CPU devices
SystemConfigData.AutoIdData.ForceBits.AutoConfig=Enable Auto Config
SystemConfigData.AutoIdData.ForceBits.AutoConfig.Tooltip=Assign new EXT unit to a new CPU or CON device
SystemConfigData.LdapData.Address=LDAP Server
SystemConfigData.LdapData.Address.Tooltip=LDAP Server
SystemConfigData.LdapData.BaseDN=Base DN
SystemConfigData.LdapData.BaseDN.Tooltip=Example: ou=user,dc=mydomain,dc=net
SystemConfigData.LdapData.Port=Port
SystemConfigData.LdapData.Port.Tooltip=Port
SystemConfigData.MatrixGridData.ForceBits.Grid=Matrix Grid Enabled
SystemConfigData.MatrixGridData.ForceBits.Grid.Tooltip=Enabling/Disabling the matrix grid requires a restart
SystemConfigData.NetworkData.Address=IP Address
SystemConfigData.NetworkData.Address.Tooltip=IP Address
SystemConfigData.NetworkData.Gateway=Gateway
SystemConfigData.NetworkData.Gateway.Tooltip=Gateway
SystemConfigData.NetworkData.MacAddress=MAC Address
SystemConfigData.NetworkData.MacAddress.Tooltip=Mac Address
SystemConfigData.NetworkData.Netmask=Subnet Mask
SystemConfigData.NetworkData.Netmask.Tooltip=Subnet Mask
SystemConfigData.NetworkData.NetworkBits.Api=API Service
SystemConfigData.NetworkData.NetworkBits.Api.Tooltip=Enable API service (Port:5555)
SystemConfigData.NetworkData.NetworkBits.Dhcp=DHCP
SystemConfigData.NetworkData.NetworkBits.Dhcp.Tooltip=Dynamic configuration of network parameters via DHCP server
SystemConfigData.NetworkData.NetworkBits.Ftp=FTP Server
SystemConfigData.NetworkData.NetworkBits.Ftp.Tooltip=Enable FTP server for configuration file transfers
SystemConfigData.NetworkData.NetworkBits.Ldap=LDAP
SystemConfigData.NetworkData.NetworkBits.Ldap.Tooltip=Enable LDAP
SystemConfigData.NetworkData.NetworkBits.Snmp=SNMP Agent
SystemConfigData.NetworkData.NetworkBits.Snmp.Tooltip=Enable SNMP Agent for GET requests and traps
SystemConfigData.NetworkData.NetworkBits.Sntp=SNTP
SystemConfigData.NetworkData.NetworkBits.Sntp.Tooltip=Enable network time server synchronisation
SystemConfigData.NetworkData.NetworkBits.Syslog=Syslog
SystemConfigData.NetworkData.NetworkBits.Syslog.Tooltip=Enable Syslog Messages for status reporting
SystemConfigData.NetworkData.SnmpAgentPort=Port
SystemConfigData.NetworkData.SnmpAgentPort.Tooltip=Port
SystemConfigData.SnmpData.Address=SNMP Server
SystemConfigData.SnmpData.Address.Tooltip=SNMP Server
SystemConfigData.SnmpData.Port=Port
SystemConfigData.SnmpData.Port.Tooltip=Port
SystemConfigData.SnmpData.SnmpBits.FanTray1=Fan Tray 1
SystemConfigData.SnmpData.SnmpBits.FanTray1.Tooltip=Notification about the status of fan tray 1
SystemConfigData.SnmpData.SnmpBits.FanTray2=Fan Tray 2
SystemConfigData.SnmpData.SnmpBits.FanTray2.Tooltip=Notification about the status of fan tray 2
SystemConfigData.SnmpData.SnmpBits.InsertBoard=Insert I/O Board
SystemConfigData.SnmpData.SnmpBits.InsertBoard.Tooltip=Notification about the insertion of a new I/O board into a slot
SystemConfigData.SnmpData.SnmpBits.InsertExtender=Insert Extender
SystemConfigData.SnmpData.SnmpBits.InsertExtender.Tooltip=Notification about a newly connected extender to the matrix
SystemConfigData.SnmpData.SnmpBits.InvalidBoard=Invalid I/O Board
SystemConfigData.SnmpData.SnmpBits.InvalidBoard.Tooltip=Notification about a non-properly working I/O board
SystemConfigData.SnmpData.SnmpBits.PowerSupply1=Power Supply 1
SystemConfigData.SnmpData.SnmpBits.PowerSupply1.Tooltip=Notification about the status of power supply unit 1
SystemConfigData.SnmpData.SnmpBits.PowerSupply2=Power Supply 2
SystemConfigData.SnmpData.SnmpBits.PowerSupply2.Tooltip=Notification about the status of power supply unit 2
SystemConfigData.SnmpData.SnmpBits.PowerSupply3=Power Supply 3
SystemConfigData.SnmpData.SnmpBits.PowerSupply3.Tooltip=Notification about the status of power supply unit 3
SystemConfigData.SnmpData.SnmpBits.RemoveBoard=Remove I/O Board
SystemConfigData.SnmpData.SnmpBits.RemoveBoard.Tooltip=Notification about the removal of an I/O board out of a slot
SystemConfigData.SnmpData.SnmpBits.RemoveExtener=Remove Extender
SystemConfigData.SnmpData.SnmpBits.RemoveExtener.Tooltip=Notification about a removed extender from the matrix
SystemConfigData.SnmpData.SnmpBits.Status=Status
SystemConfigData.SnmpData.SnmpBits.Status.Tooltip=Notification about the matrix status
SystemConfigData.SnmpData.SnmpBits.SwitchCommand=Switch Command
SystemConfigData.SnmpData.SnmpBits.SwitchCommand.Tooltip=Notification about a performed switching operation at the matrix
SystemConfigData.SnmpData.SnmpBits.Temperature=Temperature
SystemConfigData.SnmpData.SnmpBits.Temperature.Tooltip=Notification about the temperature within the matrix
SystemConfigData.SnmpData.SnmpBits.Trap=Enable Traps
SystemConfigData.SnmpData.SnmpBits.Trap.Tooltip=Enable Traps
SystemConfigData.SntpData.Address=SNTP Server
SystemConfigData.SntpData.Address.Tooltip=SNTP Server
SystemConfigData.SntpData.RealTimeClock=Date And Time
SystemConfigData.SntpData.RealTimeClock.Tooltip=Date and time of real time clock
SystemConfigData.SntpData.TimeZone=Time Zone
SystemConfigData.SntpData.TimeZone.Tooltip=Time Zone
SystemConfigData.SwitchData.ForceBits.AutoConnect=CPU Auto Connect
SystemConfigData.SwitchData.ForceBits.AutoConnect.Tooltip=Connect to next available CPU, requires keyboard or mouse
SystemConfigData.SwitchData.ForceBits.ConDisconnect=Force Disconnect
SystemConfigData.SwitchData.ForceBits.ConDisconnect.Tooltip=Enforce full KVM access to CPU, other consoles are disconnected
SystemConfigData.SwitchData.ForceBits.CpuConnect=Force Connect
SystemConfigData.SwitchData.ForceBits.CpuConnect.Tooltip=Enforce full KVM access to CPU, other consoles retain video
SystemConfigData.SwitchData.ForceBits.CpuWatch=Enable Video Sharing
SystemConfigData.SwitchData.ForceBits.CpuWatch.Tooltip=Allow shared video access to CPU
SystemConfigData.SwitchData.ForceBits.FKeySingle=Macro Single Step
SystemConfigData.SwitchData.ForceBits.FKeySingle.Tooltip=Execute macros in a single step mode
SystemConfigData.SwitchData.ForceBits.KeyboardConnect=Keyboard Mouse Connect
SystemConfigData.SwitchData.ForceBits.KeyboardConnect.Tooltip=Enable CPU control request by keyboard & mouse activity
SystemConfigData.SwitchData.ForceBits.MouseConnect=Mouse Connect
SystemConfigData.SwitchData.ForceBits.MouseConnect.Tooltip=Enable CPU control request by mouse activity
SystemConfigData.SwitchData.TimeoutDisconnect=CPU Timeout [min]
SystemConfigData.SwitchData.TimeoutDisconnect.Tooltip=Specify inactivity period at currently connected CPU after which CPU will be disconnected automatically (0 = deactivated)
SystemConfigData.SwitchData.TimeoutShare=Release Time [sec]
SystemConfigData.SwitchData.TimeoutShare.Tooltip=Specify inactivity time to accept CPU control request from another console
SystemConfigData.SyslogData.Address=Syslog Server
SystemConfigData.SyslogData.Address.Tooltip=Syslog Server
SystemConfigData.SyslogData.Facility=Facility
SystemConfigData.SyslogData.Facility.Tooltip=Syslog Facility
SystemConfigData.SyslogData.Port=Port
SystemConfigData.SyslogData.Port.Tooltip=Syslog Port
SystemConfigData.SyslogData.SysLevel=Log Level
SystemConfigData.SyslogData.SysLevel.Debug=Debug
SystemConfigData.SyslogData.SysLevel.Debug.Tooltip=Debug
SystemConfigData.SyslogData.SysLevel.Error=Error
SystemConfigData.SyslogData.SysLevel.Error.Tooltip=Error
SystemConfigData.SyslogData.SysLevel.Info=Info
SystemConfigData.SyslogData.SysLevel.Info.Tooltip=Info
SystemConfigData.SyslogData.SysLevel.Notice=Notice
SystemConfigData.SyslogData.SysLevel.Notice.Tooltip=Notice
SystemConfigData.SyslogData.SysLevel.Tooltip=Log Level
SystemConfigData.SyslogData.SysLevel.Warning=Warning
SystemConfigData.SyslogData.SysLevel.Warning.Tooltip=Warning
SystemConfigData.SyslogData.Syslog.Enabled=Enable Syslog
SystemConfigData.SyslogData.Syslog.Enabled.Tooltip=Enable Syslog Messages for status reporting
SystemConfigData.SystemData.Device=Device
SystemConfigData.SystemData.Device.Tooltip=Host name for network environment (recommended characters: a-z, A-Z, 0-9, -)
SystemConfigData.SystemData.ForceBits.AutoSave=Auto Save
SystemConfigData.SystemData.ForceBits.AutoSave.Tooltip=Save matrix status automatically
SystemConfigData.SystemData.ForceBits.ComEcho=Enable COM Echo
SystemConfigData.SystemData.ForceBits.ComEcho.Tooltip=Echo all switch commands via communication ports
SystemConfigData.SystemData.ForceBits.Default=Load Default
SystemConfigData.SystemData.ForceBits.Default.Tooltip=When performing a cold start or a restart of the matrix, the configuration stored in Default will be always activated
SystemConfigData.SystemData.ForceBits.EchoOnly=Echo Only
SystemConfigData.SystemData.ForceBits.EchoOnly.Tooltip=Synchronize matrix with echo only
SystemConfigData.SystemData.ForceBits.Invalid=Invalid I/O Boards
SystemConfigData.SystemData.ForceBits.Invalid.Tooltip=Shall/Must be OFF during operation, enable during matrix updates only
SystemConfigData.SystemData.ForceBits.LanEcho=Enable LAN Echo
SystemConfigData.SystemData.ForceBits.LanEcho.Tooltip=Echo all switch commands via LAN ports
SystemConfigData.SystemData.ForceBits.OldEcho=Enable Old Echo
SystemConfigData.SystemData.ForceBits.OldEcho.Tooltip=Echo internal switch commands with old format
SystemConfigData.SystemData.ForceBits.OnlineConfig=Online Configuration
SystemConfigData.SystemData.ForceBits.OnlineConfig.Tooltip=Enable online configuraton mode of the management tool
SystemConfigData.SystemData.ForceBits.Redundancy=Enable Redundancy
SystemConfigData.SystemData.ForceBits.Redundancy.Tooltip=Enable automatic switching for redundant extenders
SystemConfigData.SystemData.ForceBits.RemoveSlave=Remove I/O Boards
SystemConfigData.SystemData.ForceBits.RemoveSlave.Tooltip=Remove I/O Boards while missing the secondary controller board (576)
SystemConfigData.SystemData.ForceBits.Slave=Sub Matrix
SystemConfigData.SystemData.ForceBits.Slave.Tooltip=Allow hotkey control in cascaded environment
SystemConfigData.SystemData.ForceBits.Synchronize=Synchronize
SystemConfigData.SystemData.ForceBits.Synchronize.Tooltip=Synchronize matrix with master matrix
SystemConfigData.SystemData.ForceBits.UartEnable=Enable Uart
SystemConfigData.SystemData.ForceBits.UartEnable.Tooltip=Enable Uart
SystemConfigData.SystemData.Info=Info
SystemConfigData.SystemData.Info.Tooltip=Description of current matrix configuration
SystemConfigData.SystemData.MasterIP=Master IP Address
SystemConfigData.SystemData.MasterIP.Tooltip=Set the network address of the master matrix
SystemConfigData.SystemData.Name=Name
SystemConfigData.SystemData.Name.Tooltip=Name of current matrix configuration
SystemData.InternalLogLevel=Log Level
SystemData.InternalLogLevel.Debug=Debug
SystemData.InternalLogLevel.Debug.Tooltip=Debug
SystemData.InternalLogLevel.Error=Error
SystemData.InternalLogLevel.Error.Tooltip=Error
SystemData.InternalLogLevel.Info=Info
SystemData.InternalLogLevel.Info.Tooltip=Info
SystemData.InternalLogLevel.Notice=Notice
SystemData.InternalLogLevel.Notice.Tooltip=Notice
SystemData.InternalLogLevel.Tooltip=Log Level
SystemData.InternalLogLevel.Warning=Warning
SystemData.InternalLogLevel.Warning.Tooltip=Warning
SystemData.Multicast=Multicast
SystemData.Multicast.Tooltip=Grid Multicast or Broadcast (***************)
UserData.FullName=Full Name
UserData.FullName.Tooltip=Firstname Lastname
UserData.ID=ID
UserData.ID.Tooltip=ID
UserData.Name=Name
UserData.Name.Tooltip=Name
UserData.Password=Password
UserData.Password.Tooltip=Password
UserData.Rights.ADMIN=Administrator
UserData.Rights.ADMIN.Tooltip=Administrator
UserData.Rights.FTP=FTP
UserData.Rights.FTP.Tooltip=FTP
UserData.Rights.LDAP=LDAP User
UserData.Rights.LDAP.Tooltip=LDAP User
UserData.Rights.POWER=Power User
UserData.Rights.POWER.Tooltip=Power User
UserData.Rights.SUPER=Super User
UserData.Rights.SUPER.Tooltip=Super User
