package com.mc.tool.caesar.vpm.gui.pages.hotkey;

import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.caesar.vpm.pages.hotkeyconfiguration.CaesarHotkeyConfigurationPage;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.utility.InjectorProvider;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.stream.IntStream;
import javafx.scene.Node;
import javafx.scene.control.ListView;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableView;
import javafx.scene.input.KeyCode;
import org.junit.Assert;
import org.junit.Test;

/**
 * .
 */
public class MacroTest extends AppGuiTestBase {

  @Override
  public void beforeAll() {}

  @Test
  public void testRxMacro() {
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }
      clickOn("#" + CaesarHotkeyConfigurationPage.NAME);
      clickOn(GuiTestConstants.HOTKEY_MACRO_RX_TAB);
      createMacroImpl(0, 0, 0, 1, 1, 0);

      clickOn(GuiTestConstants.BUTTON_APPLY);
      // 检查显示
      TableCell cmdCell =
          (TableCell)
              lookupTableCell(
                  lookup(GuiTestConstants.HOTKEY_MACRO_TABLE),
                  0,
                  GuiTestConstants.HOTKEY_MACRO_TABLE_CMD_COLUMN);
      Assert.assertTrue(cmdCell.getText().contains(GuiTestConstants.HOTKEY_MARCO_CMD_FULL));
      TableCell param1Cell =
          (TableCell)
              lookupTableCell(
                  lookup(GuiTestConstants.HOTKEY_MACRO_TABLE),
                  0,
                  GuiTestConstants.HOTKEY_MACRO_TABLE_PARAM1_COLUMN);
      Assert.assertTrue(param1Cell.getText().contains(GuiTestConstants.CON_00));
      // 检查数据
      ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
      CaesarEntity entity = (CaesarEntity) app.getEntityMananger().getAllEntity().iterator().next();
      CaesarDeviceController controller = entity.getController();
      Assert.assertEquals(1, countMacro(controller));

      // 设置最后一项，检查列表滚动后的情况
      TableView macroTable = lookup(GuiTestConstants.HOTKEY_MACRO_TABLE).query();
      macroTable.scrollTo(15);
      try {
        Thread.sleep(100);
      } catch (InterruptedException e) {
        e.printStackTrace();
      }
      cmdCell =
          (TableCell)
              lookupTableCell(
                  lookup(GuiTestConstants.HOTKEY_MACRO_TABLE),
                  15,
                  GuiTestConstants.HOTKEY_MACRO_TABLE_CMD_COLUMN);
      selectMacroItemByOffset(1, cmdCell);
      clickOn(GuiTestConstants.BUTTON_APPLY);
      // 检查显示
      Assert.assertTrue(cmdCell.getText().contains(GuiTestConstants.HOTKEY_MARCO_CMD_FULL));
      param1Cell =
          (TableCell)
              lookupTableCell(
                  lookup(GuiTestConstants.HOTKEY_MACRO_TABLE),
                  15,
                  GuiTestConstants.HOTKEY_MACRO_TABLE_PARAM1_COLUMN);
      Assert.assertTrue(param1Cell.getText().contains(GuiTestConstants.HOTKEY_MACRO_PARAM_CURR_RX));
      Assert.assertEquals(2, countMacro(controller));
      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testCopyRxMacroToRx() {
    // 复制宏，检查宏个数
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }
      clickOn("#" + CaesarHotkeyConfigurationPage.NAME);
      clickOn(GuiTestConstants.HOTKEY_MACRO_RX_TAB);

      createMacroImpl(0, 0, 0, 1, 1, 0);

      clickOn(GuiTestConstants.BUTTON_APPLY);

      ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
      CaesarEntity entity = (CaesarEntity) app.getEntityMananger().getAllEntity().iterator().next();
      CaesarDeviceController controller = entity.getController();
      Assert.assertEquals(1, countMacro(controller));
      // 复制
      clickOn(GuiTestConstants.BUTTON_COPY_MACRO_TO_RX);
      targetWindow(GuiTestConstants.WINDOW_COPY_MACRO)
          .clickOn(GuiTestConstants.BUTTON_MOVE_TO_TARGET_ALL);
      targetWindow(GuiTestConstants.WINDOW_COPY_MACRO).clickOn(GuiTestConstants.BUTTON_CONFIRM);
      Assert.assertEquals(2, countMacro(controller));
      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testCopyRxMacroToRxWithouEnoughSpace() {
    // 复制宏，不够宏空间
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }
      clickOn("#" + CaesarHotkeyConfigurationPage.NAME);
      clickOn(GuiTestConstants.HOTKEY_MACRO_RX_TAB);

      for (int i = 0; i < 16; i++) {
        createMacroImpl(0, 0, i, 1, 1, 0);
      }
      copyMacroToKeyImpl(0, 0, IntStream.range(1, 16).toArray());

      clickOn(GuiTestConstants.BUTTON_APPLY);

      ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
      CaesarEntity entity = (CaesarEntity) app.getEntityMananger().getAllEntity().iterator().next();
      CaesarDeviceController controller = entity.getController();
      Assert.assertEquals(256, countMacro(controller));
      // 复制
      clickOn(GuiTestConstants.BUTTON_COPY_MACRO_TO_RX);
      targetWindow(GuiTestConstants.WINDOW_COPY_MACRO)
          .clickOn(GuiTestConstants.BUTTON_MOVE_TO_TARGET_ALL);
      targetWindow(GuiTestConstants.WINDOW_COPY_MACRO).clickOn(GuiTestConstants.BUTTON_CONFIRM);
      Thread.sleep(5000);
      Assert.assertEquals(8192, countMacro(controller));
      File tempFile = loadResourceStatus("com/mc/tool/caesar/vpm/36_hw1.2_full_rx.status");
      tempFile.delete();
    } catch (IOException | InterruptedException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  protected void copyMacroToKeyImpl(int sourceIndex, int sourceKeyIndex, int... targetKeyIndexes) {
    Node sourceCell =
        lookupFirstTableCell(lookup(GuiTestConstants.HOTKEY_MACRO_SOURCE_LIST), sourceIndex);
    clickOn(sourceCell);

    ListView keyList = lookup(GuiTestConstants.HOTKEY_MACRO_KEY_LIST).query();
    keyList.scrollTo(sourceKeyIndex);
    try {
      Thread.sleep(100);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }
    Node keyCell =
        lookupFirstListCell(lookup(GuiTestConstants.HOTKEY_MACRO_KEY_LIST), sourceKeyIndex);
    clickOn(keyCell);
    clickOn(GuiTestConstants.BUTTON_COPY_MACRO_LIST);
    for (int index : targetKeyIndexes) {
      keyList.scrollTo(index);
      try {
        Thread.sleep(100);
      } catch (InterruptedException e) {
        e.printStackTrace();
      }
      keyCell = lookupFirstListCell(lookup(GuiTestConstants.HOTKEY_MACRO_KEY_LIST), index);
      clickOn(keyCell);
      clickOn(GuiTestConstants.BUTTON_PASTE_MACRO_LIST);
    }
  }

  protected void createMacroImpl(
      int sourceIndex,
      int keyIndex,
      int macroIndex,
      int cmdOffset,
      int param1Offset,
      int param2Offset) {
    Node sourceCell =
        lookupFirstTableCell(lookup(GuiTestConstants.HOTKEY_MACRO_SOURCE_LIST), sourceIndex);
    clickOn(sourceCell);

    ListView keyList = lookup(GuiTestConstants.HOTKEY_MACRO_KEY_LIST).query();
    keyList.scrollTo(keyIndex);
    try {
      Thread.sleep(100);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }
    Node keyCell = lookupFirstListCell(lookup(GuiTestConstants.HOTKEY_MACRO_KEY_LIST), keyIndex);
    clickOn(keyCell);

    TableView macroTable = lookup(GuiTestConstants.HOTKEY_MACRO_TABLE).query();
    macroTable.scrollTo(macroIndex);
    try {
      Thread.sleep(100);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }
    Node cmdCell =
        lookupTableCell(
            lookup(GuiTestConstants.HOTKEY_MACRO_TABLE),
            macroIndex,
            GuiTestConstants.HOTKEY_MACRO_TABLE_CMD_COLUMN);
    selectMacroItemByOffset(cmdOffset, cmdCell);

    Node param1Cell =
        lookupTableCell(
            lookup(GuiTestConstants.HOTKEY_MACRO_TABLE),
            macroIndex,
            GuiTestConstants.HOTKEY_MACRO_TABLE_PARAM1_COLUMN);
    selectMacroItemByOffset(param1Offset, param1Cell);

    Node param2Cell =
        lookupTableCell(
            lookup(GuiTestConstants.HOTKEY_MACRO_TABLE),
            macroIndex,
            GuiTestConstants.HOTKEY_MACRO_TABLE_PARAM2_COLUMN);

    selectMacroItemByOffset(param2Offset, param2Cell);
  }

  private void selectMacroItemByOffset(int offset, Node cell) {
    if (offset > 0) {
      clickOn(cell);
      doubleClickOn(cell);
      type(Arrays.copyOf(new KeyCode[] {KeyCode.DOWN}, offset));
      type(KeyCode.ENTER);
    }
  }

  private int countMacro(CaesarDeviceController controller) {
    int result = 0;
    for (FunctionKeyData data : controller.getDataModel().getConfigData().getFunctionKeyDatas()) {
      if (data.isStatusActive()) {
        result += 1;
      }
    }
    return result;
  }
}
