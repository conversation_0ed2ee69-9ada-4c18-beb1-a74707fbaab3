package com.mc.tool.caesar.api.interfaces;

import java.lang.ref.WeakReference;

/**
 * .
 */
public class WeakChangeListener implements ChangedListener {

  private final WeakReference<ChangedListener> weakRef;

  public WeakChangeListener(ChangedListener listener) {
    weakRef = new WeakReference<>(listener);
  }

  @Override
  public void handleChanged(ChangedEvent paramChangedEvent) {
    ChangedListener listener = weakRef.get();
    if (listener != null) {
      listener.handleChanged(paramChangedEvent);
    }
  }
}
