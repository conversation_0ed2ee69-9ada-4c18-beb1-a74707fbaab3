package com.mc.tool.caesar.vpm.util.vp;

/**
 * .
 */
public class Vp6Constants {

  // 常量定义
  public static final int INVALID_ID = 0xFEFEFEFE;
  public static final int BGIMG_TX_ID = 0xEEEEEEEE;
  public static final int BGIMG_PORT = 8;
  public static final int WIDTH_2K = 1920;
  public static final int ALPHA_FACTOR = 255;

  public static final int IN_PORT_COUNT = 8;
  public static final int IN_PORT_4K_COUNT = IN_PORT_COUNT / 2;
  public static final int VIRTUAL_PORT = 8;
  public static final int OUT_PORT_COUNT = 8;
  public static final int PROCESS_UNIT_COUNT = 20;
  public static final int LAYER_PER_PORT = 6;
  public static final int LAYER_COUNT = OUT_PORT_COUNT * LAYER_PER_PORT;
  public static final int MAX_UNIQUE_LAYER_CNT = 8;
  public static final int MAX_HORZ_CUT_CNT = 8;
  public static final int INPUT_INIT_VALUE = 0;
  public static final int MAX_TOTAL_LAYER = 16;
}
