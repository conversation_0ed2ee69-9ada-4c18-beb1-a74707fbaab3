<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Tab?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TabPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<fx:root type="VBox" xmlns="http://javafx.com/javafx" xmlns:fx="http://javafx.com/fxml/1"
  stylesheets="@permissionconfiguration_view.css">
  <children>
    <TabPane fx:id="tabPane" prefHeight="700.0" prefWidth="1100.0" tabClosingPolicy="UNAVAILABLE"
      VBox.vgrow="ALWAYS">
      <tabs>
        <Tab text="%PermissionConfiguration.user.tab">
          <content>
            <VBox id="main-container">
              <children>
                <VBox fx:id="searchFieldBox" VBox.vgrow="ALWAYS">
                  <children>
                    <TableView fx:id="tableView" VBox.vgrow="ALWAYS">
                      <columns>
                        <TableColumn fx:id="indexCol" prefWidth="75.0" text="C1"/>
                        <TableColumn fx:id="idCol" prefWidth="150.0" text="C2"/>
                        <TableColumn fx:id="usernameCol" prefWidth="150.0" text="C3"/>
                        <TableColumn fx:id="permissionCol" prefWidth="200.0" text="C5"/>
                        <TableColumn fx:id="usergroupCol" prefWidth="150.0" text="C6"/>
                        <TableColumn fx:id="editCol" prefWidth="150.0" text="C7"/>
                      </columns>
                    </TableView>
                  </children>
                </VBox>
                <HBox id="tool-box" maxHeight="23.0" VBox.vgrow="ALWAYS">
                  <children>
                    <Button fx:id="newAction" mnemonicParsing="false" styleClass="common-button"
                      text="%PermissionConfiguration.user.new_user">
                      <HBox.margin>
                        <Insets/>
                      </HBox.margin>
                    </Button>
                    <Button fx:id="deleteAction" mnemonicParsing="false" styleClass="common-button"
                      text="%PermissionConfiguration.user.delect_user">
                      <HBox.margin>
                        <Insets/>
                      </HBox.margin>
                    </Button>
                  </children>
                </HBox>
              </children>
            </VBox>
          </content>
        </Tab>
      </tabs>
    </TabPane>
  </children>
</fx:root>
