package com.mc.tool.caesar.vpm.userright;

import com.mc.tool.caesar.vpm.devices.CaesarUserRightGetter;
import com.mc.tool.framework.systemedit.datamodel.VideoWallFunc;

/**
 * .
 */
public class CaesarAdminUserRight implements Caesar<PERSON>serRightGetter {

  @Override
  public boolean isSystemEditRenamable() {
    return true;
  }

  @Override
  public boolean isSystemEditGroupCreateDeletable() {
    return true;
  }

  @Override
  public boolean isVideoWallCreateDeletable() {
    return true;
  }

  @Override
  public boolean isSeatCreateDeletable() {
    return true;
  }

  @Override
  public boolean isDeviceTypeChangable() {
    return true;
  }

  @Override
  public boolean isDeviceItemCreateDeletable() {
    return true;
  }

  @Override
  public boolean isDeviceConnectable() {
    return true;
  }

  @Override
  public boolean isDeviceItemMovable() {
    return true;
  }

  @Override
  public boolean isSystemScenarioEditable() {
    return true;
  }

  @Override
  public boolean isVideoWallLayoutEditable(VideoWallFunc func) {
    return true;
  }

  @Override
  public boolean isVideoWallWindowCreateDeletable(VideoWallFunc func) {
    return true;
  }

  @Override
  public boolean isVideoWallWindowMovablale(VideoWallFunc func) {
    return true;
  }

  @Override
  public boolean isVideoWallWindowResizable(VideoWallFunc func) {
    return true;
  }

  @Override
  public boolean isVideoWallScenarioCreateDeletable(VideoWallFunc func) {
    return true;
  }

  @Override
  public boolean isVideoWallScenarioActivatable(VideoWallFunc func) {
    return true;
  }

  @Override
  public boolean isVideoWallConnectable(VideoWallFunc func) {
    return true;
  }

  @Override
  public boolean isSeatConnectable() {
    return true;
  }

  @Override
  public boolean isSeatScenarioCreateDeletable() {
    return true;
  }

  @Override
  public boolean isSeatSceanrioActivatable() {
    return true;
  }

  @Override
  public boolean isPageVisible(String pageName) {
    return true;
  }

  @Override
  public boolean isSystemLogVisible() {
    return true;
  }

  @Override
  public boolean isActivateConfigVisible() {
    return true;
  }

  @Override
  public boolean isCrossScreenCreateDeletable() {
    return true;
  }

  @Override
  public boolean isCrossScreenConnectable() {
    return true;
  }

  @Override
  public boolean isMultiScreenCreateDeletable() {
    return true;
  }

  @Override
  public boolean isMultiScreenConnectable() {
    return true;
  }

  @Override
  public boolean isTxConfigEditable() {
    return true;
  }

  @Override
  public boolean isRxConfigEditable() {
    return true;
  }

  @Override
  public boolean isUserEditable() {
    return true;
  }

  @Override
  public boolean isUserRightEditable() {
    return true;
  }

  @Override
  public boolean isRxRightEditable() {
    return true;
  }

  @Override
  public boolean isUserGroupEditable() {
    return true;
  }

  @Override
  public boolean isUserGroupRightEditable() {
    return true;
  }

  @Override
  public boolean isRxHotKeyEditable() {
    return true;
  }

  @Override
  public boolean isRxMacroEditable() {
    return true;
  }

  @Override
  public boolean isUserMacroEditable() {
    return true;
  }

  @Override
  public boolean isOsdConfigVisible() {
    return true;
  }

  @Override
  public boolean isVideoWallEditable(VideoWallFunc func) {
    return true;
  }

  @Override
  public boolean isTxGroupCreateDeletable() {
    return true;
  }
}
