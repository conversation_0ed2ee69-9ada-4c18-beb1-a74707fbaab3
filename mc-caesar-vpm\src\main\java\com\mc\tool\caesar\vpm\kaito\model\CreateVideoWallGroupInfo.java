package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 创建全光拼接屏组信息.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class CreateVideoWallGroupInfo {
  /**
   * 诺瓦主机IP.
   */
  private String ip;

  private String name;
  /**
   * 诺瓦主机端口，默认为8000.
   **/
  private Integer port;
  /**
   * 诺瓦接入方ID.
   **/
  @JsonProperty("pId")
  private String pid;
  /**
   * 诺瓦接入方密钥，默认为空，表示不加密.
   **/
  private String secreteKey;
  /**
   * 诺瓦主机类型，可选择E或者ALPHA，默认为空，表示为E.
   */
  private String type;
}
