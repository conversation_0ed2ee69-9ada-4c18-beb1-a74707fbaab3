<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.CheckBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.PasswordField?>
<?import javafx.scene.control.Spinner?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<?import org.controlsfx.control.ListSelectionViewEx?>
<?import javafx.scene.control.ComboBox?>
<VBox prefWidth="350.0" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1"
      stylesheets="@editUserDataDialog.css" id="main-container">
  <HBox alignment="CENTER" prefHeight="30.0" prefWidth="200.0">
    <Label text="ID"/>
    <Region HBox.hgrow="ALWAYS"/>
    <TextField fx:id="idField" disable="true" editable="false" styleClass="right-item"/>
  </HBox>
  <HBox alignment="CENTER" prefHeight="30.0" prefWidth="200.0">
    <Label text="%PermissionConfiguration.user.username"/>
    <Region HBox.hgrow="ALWAYS"/>
    <TextField fx:id="usernameField" styleClass="right-item"/>
  </HBox>
  <HBox alignment="CENTER" prefHeight="5.0">
    <Region HBox.hgrow="ALWAYS"/>
    <Label fx:id="msgLabel" textFill="RED"/>
  </HBox>
  <HBox alignment="CENTER" prefHeight="30.0">
    <Label text="%PermissionConfiguration.user.password"/>
    <Region HBox.hgrow="ALWAYS"/>
    <PasswordField fx:id="passwordField" styleClass="right-item"/>
  </HBox>
  <HBox alignment="CENTER" prefHeight="30.0">
    <Label text="%PermissionConfiguration.edit_user_dialog.confirm"/>
    <Region HBox.hgrow="ALWAYS"/>
    <PasswordField fx:id="rePasswordField" styleClass="right-item"/>
  </HBox>
  <HBox alignment="CENTER" prefHeight="30.0" prefWidth="200.0">
    <Label text="%PermissionConfiguration.edit_user_dialog.role"/>
    <Region HBox.hgrow="ALWAYS"/>
    <ComboBox fx:id="rightsComboBox" styleClass="right-item"/>
  </HBox>
  <HBox alignment="CENTER" prefHeight="30.0" prefWidth="200.0">
    <Label text="%PermissionConfiguration.user.usergroup"/>
    <Region HBox.hgrow="ALWAYS"/>
    <HBox styleClass="right-item, right-box" fx:id="container">

    </HBox>
  </HBox>
  <HBox alignment="CENTER" prefHeight="30.0" prefWidth="200.0">
    <Label text="%PermissionConfiguration.edit_user_dialog.mouseSpeed"/>
    <Region HBox.hgrow="ALWAYS"/>
    <Spinner styleClass="right-item" fx:id="mouseSpeedSpinner"/>
  </HBox>
  <HBox alignment="CENTER" prefHeight="30.0" prefWidth="200.0">
    <Label text="%PermissionConfiguration.edit_user_dialog.rememberLogin"/>
    <Region HBox.hgrow="ALWAYS"/>
    <CheckBox styleClass="right-item" fx:id="autoConnectCheckBox"/>
  </HBox>
  <HBox styleClass="selection-box" fx:id="selectionBox">
    <ListSelectionViewEx fx:id="videoWallRightsSelectionView"/>
  </HBox>
  <HBox alignment="CENTER" prefHeight="4.0">
    <Region HBox.hgrow="ALWAYS"/>
    <Label fx:id="warningMsg" textFill="RED"/>
  </HBox>
</VBox>
