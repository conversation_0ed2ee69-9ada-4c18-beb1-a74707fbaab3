package com.mc.tool.caesar.api.exception;

/**
 * .
 */
public class InvalidIpException extends Exception {

  /**
   * .
   */
  private static final long serialVersionUID = 1L;
  public static final String DESCRIPTION = "The IP is not valid!";

  public InvalidIpException() {
    super(InvalidIpException.DESCRIPTION);
  }

  public InvalidIpException(Throwable cause) {
    super(InvalidIpException.DESCRIPTION, cause);
  }
}

