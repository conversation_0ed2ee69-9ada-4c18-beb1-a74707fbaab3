package com.mc.tool.caesar.vpm.util;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.datamodel.ConfigData.IoMode;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.systemedit.controller.CaesarSystemEditController;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import javafx.application.Platform;
import javafx.scene.control.AlertEx.AlertExType;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class ReloadUtility {

  /**
   * 刷新数据.
   *
   * @param caesarSystemEditController systemedit
   * @param deviceController 设备控制器
   */
  public static void refreshData(
      CaesarSystemEditController caesarSystemEditController,
      CaesarDeviceController deviceController) {
    deviceController.beginUpdate();
    // 备份当前数据
    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    CfgWriter writer = new CfgWriter(baos);
    try {
      deviceController.getDataModel().getConfigData().writeData(writer, IoMode.Available);
    } catch (ConfigException | RuntimeException exception) {
      log.warn("Fail to backup config data!");
      baos.reset();
    }
    // 刷新数据
    try {
      deviceController.getDataModel().reloadAll();
      Platform.runLater(
          () -> {
            try {
              caesarSystemEditController.loadData(false);
              if (deviceController.getDataModel().isHwVersionIncompatible()) {
                PlatformUtility.runInFxThreadLater(() -> ViewUtility.showAlert(
                    null, CaesarI18nCommonResource.getString("check.matrix_version"),
                    AlertExType.ERROR));
              }
            } finally {
              deviceController.endUpdate();
            }
          });
    } catch (ConfigException | BusyException | RuntimeException ex) {
      log.warn("reload all failed!", ex);
      PlatformUtility.runInFxThreadLater(
          () -> {
            // 加载备份数据
            byte[] bytes = baos.toByteArray();
            if (bytes.length > 0) {
              try {
                deviceController
                    .getDataModel()
                    .getConfigData()
                    .readData(new CfgReader(new ByteArrayInputStream(bytes)), IoMode.Available);
              } catch (ConfigException | RuntimeException exception) {
                log.warn("Fail to load backed config data!");
              }
            }
            deviceController.endUpdate();
            ViewUtility.showAlert(
                null, CaesarI18nCommonResource.getString("check.try"), AlertExType.ERROR);
          });
    }
  }
}
