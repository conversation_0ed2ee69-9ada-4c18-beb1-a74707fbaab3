package com.mc.tool.caesar.vpm.view;

import com.mc.tool.framework.controller.FrameController;
import com.mc.tool.framework.interfaces.Page;
import java.util.ArrayList;
import java.util.List;
import javafx.scene.control.Tab;
import javafx.scene.layout.StackPane;

/**
 * .
 */
public class CaesarFrameController extends FrameController {

  @Override
  protected Tab createTabByPage(Page page) {
    // 由于页面的node比较多，如果使用parent的stylesheet的话，切换页面很慢的。
    // 所以是每个页面都引用common.css的样式，而不使用parent的样式。
    StackPane stackPane = new NoCssStackPane();
    stackPane.getChildren().add(page.getView());
    Tab tab = new Tab(page.getTitle(), stackPane);
    tab.getStyleClass().add(page.getStyleClass());
    tab.setId(page.getName());
    return tab;
  }

  static class NoCssStackPane extends StackPane {

    @Override
    public List<String> impl_getAllParentStylesheets() {
      return new ArrayList<>();
    }
  }
}
