package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.vpm.pages.systemedit.controller.CaesarVideoWallFuncManager;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarVideoWallFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.framework.operation.videowall.datamodel.LayoutData;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import java.util.ArrayList;

/**
 * .
 */
public class MenuQuickCreateVideoWall extends MenuQuickCreate {

  private final CaesarVideoWallFuncManager videoWallFuncManager;

  /** 快速创建视频墙. */
  public MenuQuickCreateVideoWall(
      SystemEditControllable controllable, CaesarVideoWallFuncManager videoWallFuncManager) {
    super(controllable);
    this.videoWallFuncManager = videoWallFuncManager;
    this.setText(Bundle.NbBundle.getMessage("menu.grouping.videowall"));
  }

  @Override
  protected void initTerminal() {
    nodes = new ArrayList<>();
    for (VisualEditNode root : controllable.getModel().getRoots()) {
      if (root instanceof CaesarMatrix) {
        CaesarMatrix matrix = (CaesarMatrix) root;
        for (VpGroup vpGroup : matrix.getAllVpGroup()) {
          if (!(vpGroup.getParent() instanceof CaesarVideoWallFunc)) {
            nodes.add(vpGroup);
          }
        }
      }
    }
  }

  @Override
  protected void handleAction() {
    dialog
        .showAndWait()
        .ifPresent(
            nodeList -> {
              if (!nodeList.isEmpty()) {
                String groupName = nameField.getText();
                CaesarVideoWallFunc func =
                    controllable.addGroup(
                        groupName,
                        CaesarVideoWallFunc.class,
                        nodeList.toArray(new VisualEditNode[0]));
                if (func != null && nodeList.size() > 0) {
                  int index = getEmptyIndex();
                  func.setVideoWallIndex(index);
                  LayoutData layoutData = func.getVideoWallObject().getLayoutData();
                  layoutData.getRowsProperty().set(nodeList.size());
                  layoutData.getColumnsProperty().set(8);
                  videoWallFuncManager.setVideoWallFunc(index, func);
                }
              }
            });
  }

  private int getEmptyIndex() {
    int index = -1;
    for (int i = 0; i < VideoWallGroupData.GROUP_COUNT; i++) {
      if (videoWallFuncManager.getVideoWallFunc(i) == null) {
        index = i;
        break;
      }
    }
    return index;
  }

  @Override
  protected void initBundle() {
    groupNameLabel.setText(Bundle.NbBundle.getMessage("menu.grouping.videowall.name"));
    dialog.setTitle(Bundle.NbBundle.getMessage("menu.grouping.videowall"));
  }

  @Override
  protected void setNameFieldText() {
    nameField.setText("VideoWall");
  }
}
