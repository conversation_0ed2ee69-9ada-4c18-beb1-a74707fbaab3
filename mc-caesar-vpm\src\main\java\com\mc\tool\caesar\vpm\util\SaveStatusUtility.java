package com.mc.tool.caesar.vpm.util;

import com.google.gson.Gson;
import com.mc.common.util.ZipUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarExtArg;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.ConfigData.IoMode;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.ExtenderStatusInfo;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.datamodel.ScenarioData;
import com.mc.tool.caesar.api.datamodel.VersionSet;
import com.mc.tool.caesar.api.datamodel.extargs.ExtArgBean;
import com.mc.tool.caesar.api.datamodel.extargs.ExtBooleanArg;
import com.mc.tool.caesar.api.datamodel.vp.VpConConfigData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData.VpResolution;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipOutputStream;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class SaveStatusUtility {
  public static final String STATUS_MODEL_NAME = "model.json";

  /**
   * 保存状态.
   *
   * @param file 目标文件.
   * @param controller controller
   * @return 如果保存成功，返回true.
   */
  public static boolean saveStatus(File file, CaesarDeviceController controller, String modelData) {
    FileOutputStream fos = null;
    ZipOutputStream zos = null;
    try {
      fos = new FileOutputStream(file);
      zos = new ZipOutputStream(new BufferedOutputStream(fos));
      String tempPath = System.getProperty("java.io.tmpdir");
      // 保存model
      File modelFile = new File(tempPath + STATUS_MODEL_NAME);
      if (saveModel(modelFile, modelData)) {
        ZipUtility.zipFile(zos, modelFile);
      }
      // 保存dtc
      File configFile = new File(tempPath + CaesarConstants.STATUS_CONFIG_NAME);
      if (saveConfig(configFile, controller.getDataModel())) {
        ZipUtility.zipFile(zos, configFile);
      }

      // 保存模块状态
      File moduleFile = new File(tempPath + CaesarConstants.STATUS_MODULE_NAME);
      if (saveModule(moduleFile, controller)) {
        ZipUtility.zipFile(zos, moduleFile);
      }
      // 保存端口状态
      File portFile = new File(tempPath + CaesarConstants.STATUS_PORT_NAME);
      if (savePort(portFile, controller)) {
        ZipUtility.zipFile(zos, portFile);
      }
      // 保存vp
      File vpFile = new File(tempPath + CaesarConstants.STATUS_VP_NAME);
      if (saveVp(vpFile, controller)) {
        ZipUtility.zipFile(zos, vpFile);
      }

      // 保存vp数据
      for (VpConsoleData vpConsoleData :
          controller.getDataModel().getConfigDataManager().getActiveVpconsolses()) {
        if (!vpConsoleData.isStatusOnline()) {
          continue;
        }
        try {
          VpConConfigData data = controller.getDataModel().getVpconConfigData(vpConsoleData);
          if (data == null) {
            log.warn("Fail to read vp config data for {}", vpConsoleData.getName());
            continue;
          }
          File vpDataBinFile = new File(tempPath + "vpconfig_" + vpConsoleData.getName() + ".bin");
          if (saveVpConfig2Bin(vpDataBinFile, data)) {
            ZipUtility.zipFile(zos, vpDataBinFile);
          }

          File vpDataTxtFile = new File(tempPath + "vpconfig_" + vpConsoleData.getName() + ".txt");
          if (saveVpConfig2Txt(vpDataTxtFile, data)) {
            ZipUtility.zipFile(zos, vpDataTxtFile);
          }
        } catch (BusyException | RuntimeException exception) {
          log.warn("Fail to read vp config data for {}", vpConsoleData.getName(), exception);
        }
      }

      // 保存vp预案
      File vpScenarioFile = new File(tempPath + CaesarConstants.STATUS_VP_SCENARIO_NAME);
      if (saveVpScenario(vpScenarioFile, controller)) {
        ZipUtility.zipFile(zos, vpScenarioFile);
      }
      // 保存vp分辨率
      File vpresFile = new File(tempPath + CaesarConstants.STATUS_VP_RES_NAME);
      if (saveVpconResolution(vpresFile, controller)) {
        ZipUtility.zipFile(zos, vpresFile);
      }
      // 保存额外配置
      File extraConfFile = new File(tempPath + CaesarConstants.STATUS_EXTRA_CONF_NAME);
      if (ConfigListUtility.saveExtraConfig(controller.getDataModel(), extraConfFile.getPath())) {
        ZipUtility.zipFile(zos, extraConfFile);
      }
      // 保存版本
      File versionFile = new File(tempPath + CaesarConstants.STATUS_VERSION_NAME);
      if (saveVersion(versionFile, controller)) {
        ZipUtility.zipFile(zos, versionFile);
      }
      // 保存序列号
      File serialFile = new File(tempPath + CaesarConstants.STATUS_SERIAL_NAME);
      if (saveSerial(serialFile, controller)) {
        ZipUtility.zipFile(zos, serialFile);
      }

      File extInfo2 = new File(tempPath, CaesarConstants.STATUS_EXTINFO2_NAME);
      if (saveExtInfo2(extInfo2, controller)) {
        ZipUtility.zipFile(zos, extInfo2);
      }
      // 保存EDID
      File edid = new File(tempPath, CaesarConstants.STATUS_EDID_NAME);
      if (saveEdid(edid, controller)) {
        ZipUtility.zipFile(zos, edid);
      }
      // 保存外设参数
      File extargs = new File(tempPath, CaesarConstants.STATUS_EXTARGS_NAME);
      if (saveExtArgs(extargs, controller)) {
        ZipUtility.zipFile(zos, extargs);
      }
      // 保存杂项信息
      File misc = new File(tempPath, CaesarConstants.STATUS_MISC_NAME);
      if (saveMisc(misc, controller)) {
        ZipUtility.zipFile(zos, misc);
      }
      // 保存备份数据

      // 保存当前的java环境
      File environmentFile = new File(tempPath + CaesarConstants.STATUS_ENVIRONMENT_NAME);
      if (saveEnvironment(environmentFile, controller)) {
        ZipUtility.zipFile(zos, environmentFile);
      }

      return true;
    } catch (FileNotFoundException exception) {
      log.warn("Fial to write {}!", file.getName(), exception);
      return false;
    } catch (RuntimeException exception) {
      log.warn("Fail to save status!", exception);
      return false;
    } finally {
      if (zos != null) {
        try {
          zos.close();
        } catch (IOException exception) {
          log.warn("Fail to close zip output stream!", exception);
        }
      }
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException exception) {
          log.warn("Fail to close file output stream!", exception);
        }
      }
    }
  }

  /**
   * 保存数据模型.
   *
   * @param file 目标文件.
   * @param model 模型.
   */
  public static boolean saveModel(File file, String model) {
    FileOutputStream fos = null;
    try {
      fos = new FileOutputStream(file);
      fos.write(model.getBytes(StandardCharsets.UTF_8));
      return true;
    } catch (IOException exception) {
      log.warn("Fail to save model!", exception);
      return false;
    } finally {
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException exception) {
          log.warn("Fail to close stream!", exception);
        }
      }
    }
  }

  /**
   * 保存配置.
   *
   * @param file 目标文件
   * @param dataModel data model
   */
  public static boolean saveConfig(File file, CaesarSwitchDataModel dataModel) {
    FileOutputStream fos = null;
    try {
      fos = new FileOutputStream(file);
      dataModel.writeData(fos, IoMode.Available);
      return true;
    } catch (FileNotFoundException exception) {
      log.warn("Fail to save config!", exception);
      return false;
    } catch (ConfigException exception) {
      log.warn("Fail to write config!", exception);
      return false;
    } finally {
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException exception) {
          log.warn("Fail to close stream!", exception);
        }
      }
    }
  }

  /**
   * 保存版本信息.
   *
   * @param file 目标文件
   * @param controller controller
   */
  public static boolean saveVersion(File file, CaesarDeviceController controller) {
    FileOutputStream fos = null;
    try {
      fos = new FileOutputStream(file);

      Map<Integer, VersionSet> versions = new HashMap<>();
      for (ExtenderData extenderData :
          controller.getDataModel().getConfigDataManager().getActiveExtenders()) {
        versions.put(extenderData.getId(), extenderData.getVersion());
      }

      String content = new Gson().toJson(versions);
      fos.write(content.getBytes(StandardCharsets.UTF_8));
      return true;
    } catch (IOException exception) {
      log.warn("Fail to save version!", exception);
      return false;
    } finally {
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException exception) {
          log.warn("Fail to close stream!", exception);
        }
      }
    }
  }

  /**
   * 保存视频墙配置.
   *
   * @param file 目标文件
   * @param controller controller
   */
  public static boolean saveVp(File file, CaesarDeviceController controller) {
    FileOutputStream fos = null;
    try {
      fos = new FileOutputStream(file);

      controller.getDataModel().getVpDataModel().getVideoWallGroupData().write(fos);
      return true;
    } catch (IOException exception) {
      log.warn("Fail to save vp!", exception);
      return false;
    } finally {
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException exception) {
          log.warn("Fail to close stream!", exception);
        }
      }
    }
  }

  /**
   * 保存vp配置数据到二进制文件.
   *
   * @param file 文件
   * @param data 数据
   * @return 保存成功与否
   */
  public static boolean saveVpConfig2Bin(File file, VpConConfigData data) {
    FileOutputStream fos = null;
    try {
      fos = new FileOutputStream(file);
      data.write(fos);
      return true;
    } catch (IOException exception) {
      log.warn("Fail to save vp config 2 bin!");
      return false;
    } finally {
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException exception) {
          log.warn("Fail to close stream!", exception);
        }
      }
    }
  }

  /**
   * 保存vp配置数据到文本文件.
   *
   * @param file 文件
   * @param data 数据
   * @return 保存成功与否
   */
  public static boolean saveVpConfig2Txt(File file, VpConConfigData data) {
    FileOutputStream fos = null;
    try {
      fos = new FileOutputStream(file);
      fos.write(data.toString().getBytes(StandardCharsets.UTF_8));
      return true;
    } catch (IOException exception) {
      log.warn("Fail to save vp config 2 txt!");
      return false;
    } finally {
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException exception) {
          log.warn("Fail to close stream!", exception);
        }
      }
    }
  }

  /**
   * 保存视频墙预案.
   *
   * @param file 目标文件
   * @param controller controller
   */
  public static boolean saveVpScenario(File file, CaesarDeviceController controller) {
    FileOutputStream fos = null;
    try {
      fos = new FileOutputStream(file);
      CfgWriter writer = new CfgWriter(fos);
      for (ScenarioData scenarioData :
          controller.getDataModel().getVpDataModel().getScenarioDatas()) {
        writer.write2ByteSmallEndian(scenarioData.getIndex());
        scenarioData.writeData(writer);
      }
      return true;
    } catch (IOException | ConfigException exception) {
      log.warn("Fail to save vp scenario!", exception);
      return false;
    } finally {
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException exception) {
          log.warn("Fail to close stream!", exception);
        }
      }
    }
  }

  /**
   * 保存序列号.
   *
   * @param file 目标文件
   * @param controller controller
   */
  public static boolean saveSerial(File file, CaesarDeviceController controller) {
    FileOutputStream fos = null;
    try {
      fos = new FileOutputStream(file);

      String content = new Gson().toJson(controller.getDataModel().getSerialCache());
      fos.write(content.getBytes(StandardCharsets.UTF_8));
      return true;
    } catch (IOException exception) {
      log.warn("Fail to save serial!", exception);
      return false;
    } finally {
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException exception) {
          log.warn("Fail to close stream!", exception);
        }
      }
    }
  }

  /**
   * 保存外设额外信息.
   *
   * @param file 目标文件
   * @param controller controller
   */
  public static boolean saveExtInfo2(File file, CaesarDeviceController controller) {
    FileOutputStream fos = null;
    try {
      fos = new FileOutputStream(file);

      Map<Integer, ExtenderStatusInfo> extInfos = new HashMap<>();
      for (ExtenderData extenderData :
          controller.getDataModel().getConfigDataManager().getActiveExtenders()) {
        extInfos.put(extenderData.getId(), extenderData.getExtenderStatusInfo());
      }

      String content = new Gson().toJson(extInfos);
      fos.write(content.getBytes(StandardCharsets.UTF_8));
      return true;
    } catch (IOException exception) {
      log.warn("Fail to save extinfo2!", exception);
      return false;
    } finally {
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException exception) {
          log.warn("Fail to close stream!", exception);
        }
      }
    }
  }

  /**
   * 保存edid.
   *
   * @param file 目标文件
   * @param controller controller
   */
  public static boolean saveEdid(File file, CaesarDeviceController controller) {
    FileOutputStream fos = null;
    try {
      fos = new FileOutputStream(file);

      Map<Integer, List<String>> edids = new HashMap<>();
      for (ExtenderData extenderData :
          controller.getDataModel().getConfigDataManager().getActiveExtenders()) {
        List<String> list = new ArrayList<>();
        for (int i = 0; i < extenderData.getExtenderStatusInfo().getInterfaceCount(); i++) {
          list.add(extenderData.getEdidProperty(i).get());
        }
        edids.put(extenderData.getId(), list);
      }

      String content = new Gson().toJson(edids);
      fos.write(content.getBytes(StandardCharsets.UTF_8));
      return true;
    } catch (IOException exception) {
      log.warn("Fail to save edid!", exception);
      return false;
    } finally {
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException exception) {
          log.warn("Fail to close stream!", exception);
        }
      }
    }
  }

  /**
   * 保存外设额外信息.
   *
   * @param file 目标文件
   * @param controller controller
   */
  public static boolean saveExtArgs(File file, CaesarDeviceController controller) {
    FileOutputStream fos = null;
    try {
      fos = new FileOutputStream(file);

      Map<Integer, ExtArgBean> extInfos = new HashMap<>();
      for (ExtenderData extenderData :
          controller.getDataModel().getConfigDataManager().getActiveExtenders()) {
        ExtArgBean bean = new ExtArgBean();
        if (extenderData.hasExtArg(CaesarExtArg.EXT_ARG_ANALOG_INPUT_ID)) {
          bean.audioInput = extenderData.getAnalogAudioInputProperty().get();
        }
        if (extenderData.hasExtArg(CaesarExtArg.EXT_ARG_USB_ENABLE_ID)) {
          bean.usbEnable = extenderData.getUsbDiskEnableProperty().get()
              ? ExtBooleanArg.TRUE : ExtBooleanArg.FALSE;
        }
        if (extenderData.hasExtArg(CaesarExtArg.EXT_ARG_VIDEO_QP_ID)) {
          bean.videoQp = extenderData.getVideoQpProperty().get();
        }
        if (extenderData.hasExtArg(CaesarExtArg.EXT_ARG_TOUCHING_SCREEN_ID)) {
          bean.touchingScreen = extenderData.getTouchEnableProperty().get()
              ? ExtBooleanArg.TRUE : ExtBooleanArg.FALSE;
        }
        if (extenderData.hasExtArg(CaesarExtArg.EXT_ARG_AUDIO_TRIGGER)) {
          bean.audioTrigger = extenderData.getAudioTriggerProperty().get();
        }
        if (extenderData.hasExtArg(CaesarExtArg.EXT_ARG_EVENT_ENABLE)) {
          bean.eventEnable = extenderData.getEventEnableProperty().get()
              ? ExtBooleanArg.TRUE : ExtBooleanArg.FALSE;
        }
        if (extenderData.hasExtArg(CaesarExtArg.EXT_ARG_TRIGGER_HOLD_TIME)) {
          bean.triggerHoldTime = extenderData.getTriggerHoldTimeProperty().get();
        }
        if (extenderData.hasExtArg(CaesarExtArg.EXT_ARG_HIGH_COMPRESSION_RATIO)) {
          bean.highCompressionRatio = extenderData.getHighCompressionRatioProperty().get()
              ? ExtBooleanArg.TRUE : ExtBooleanArg.FALSE;
        }
        if (extenderData.hasExtArg(CaesarExtArg.EXT_ARG_OSD_MENU)) {
          bean.osdMenu = extenderData.getOsdMenuProperty().get()
              ? ExtBooleanArg.TRUE : ExtBooleanArg.FALSE;
        }
        if (extenderData.hasExtArg(CaesarExtArg.EXT_ARG_ICRON_ENABLE)) {
          bean.icronEnable = extenderData.getIcronEnableProperty().get()
             ? ExtBooleanArg.TRUE : ExtBooleanArg.FALSE;
        }
        if (extenderData.hasExtArg(CaesarExtArg.EXT_ARG_UART_BAUD_RATE)) {
          bean.uartBaudRate = extenderData.getUartBaudRateProperty().get();
        }
        if (extenderData.hasExtArg(CaesarExtArg.EXT_ARG_DOUBLE_DP_ENABLE)) {
          bean.dpMode = extenderData.getDpModeProperty().get();
        }

        extInfos.put(extenderData.getId(), bean);
      }

      String content = new Gson().toJson(extInfos);
      fos.write(content.getBytes(StandardCharsets.UTF_8));
      return true;
    } catch (IOException exception) {
      log.warn("Fail to save analog audio input!", exception);
      return false;
    } finally {
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException exception) {
          log.warn("Fail to close stream!", exception);
        }
      }
    }
  }

  /**
   * 保存杂项数据.
   *
   * @param file file
   * @param controller controller
   * @return 如果保存成功，返回true
   */
  public static boolean saveMisc(File file, CaesarDeviceController controller) {
    Map<String, Object> miscMap = new HashMap<>();
    miscMap.put(
        CaesarConstants.STATUS_MISC_MASTER,
        controller.getDataModel().getConfigData().getGridIndex());
    miscMap.put(
        CaesarConstants.STATUS_DOUBLE_BACKUP,
        controller.getDataModel().getConfigData().getMatrixStatus().isDoubleBackup());

    FileOutputStream fos = null;
    try {
      fos = new FileOutputStream(file);
      String content = new Gson().toJson(miscMap);
      fos.write(content.getBytes(StandardCharsets.UTF_8));
      return true;
    } catch (IOException exception) {
      log.warn("Fail to save environment!", exception);
      return false;
    } finally {
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException exception) {
          log.warn("Fail to close stream!", exception);
        }
      }
    }
  }

  /**
   * 保存当前环境.
   *
   * @param file 目标文件
   * @param controller controller
   */
  public static boolean saveEnvironment(File file, CaesarDeviceController controller) {
    FileOutputStream fos = null;
    try {
      fos = new FileOutputStream(file);

      Map<String, String> environments = new HashMap<>();
      environments.put("tool.verion", System.getProperty("toolversion"));
      environments.put("java.class.path", System.getProperty("java.class.path"));
      environments.put("java.home", System.getProperty("java.home"));
      environments.put("java.vendor", System.getProperty("java.vendor"));
      environments.put("java.vendor.url", System.getProperty("java.vendor.url"));
      environments.put("java.version", System.getProperty("java.version"));
      environments.put("os.arch", System.getProperty("os.arch"));
      environments.put("os.name", System.getProperty("os.name"));
      environments.put("os.version", System.getProperty("os.version"));

      String content = new Gson().toJson(environments);
      fos.write(content.getBytes(StandardCharsets.UTF_8));
      return true;
    } catch (IOException exception) {
      log.warn("Fail to save environment!", exception);
      return false;
    } finally {
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException exception) {
          log.warn("Fail to close stream!", exception);
        }
      }
    }
  }

  public static boolean saveBackup(File fos, CaesarDeviceController controller) {
    return false;
  }

  /**
   * 保存端口信息.
   *
   * @param file 目标文件
   * @param controller controller
   */
  public static boolean savePort(File file, CaesarDeviceController controller) {
    FileOutputStream fos = null;
    try {
      fos = new FileOutputStream(file);

      CfgWriter writer = new CfgWriter(fos);
      for (PortData portData : controller.getDataModel().getConfigData().getPortDatas()) {
        portData.writeData(writer);
      }
      return true;
    } catch (IOException | ConfigException exception) {
      log.warn("Fail to save port!", exception);
      return false;
    } finally {
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException exception) {
          log.warn("Fail to close stream!", exception);
        }
      }
    }
  }

  /**
   * 保存模块信息.
   *
   * @param file 目标文件
   * @param controller controller
   */
  public static boolean saveModule(File file, CaesarDeviceController controller) {
    FileOutputStream fos = null;
    try {
      fos = new FileOutputStream(file);

      CfgWriter writer = new CfgWriter(fos);
      for (ModuleData moduleData :
          controller.getDataModel().getSwitchModuleData().getModuleDatas()) {
        moduleData.writeData(writer);
      }
      return true;
    } catch (IOException | ConfigException exception) {
      log.warn("Fail to save port!", exception);
      return false;
    } finally {
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException exception) {
          log.warn("Fail to close stream!", exception);
        }
      }
    }
  }

  /**
   * 保存vpcon分辨率.
   *
   * @param file 目标文件
   * @param controller controller
   */
  public static boolean saveVpconResolution(File file, CaesarDeviceController controller) {
    FileOutputStream fos = null;
    try {
      fos = new FileOutputStream(file);
      Map<Integer, Pair<Integer, Integer>[]> resolutions = new HashMap<>();
      for (VpConsoleData vpcon :
          controller.getDataModel().getConfigDataManager().getActiveVpconsolses()) {
        Pair<Integer, Integer>[] res = new Pair[vpcon.getOutPortCount()];
        for (int i = 0; i < vpcon.getOutPortCount(); i++) {
          VpResolution vpResolution = vpcon.getOutResolution(i);
          if (vpResolution == null) {
            res[i] = null;
          } else {
            res[i] = new Pair<>((int) vpResolution.getWidth(), (int) vpResolution.getHeight());
          }
        }
        resolutions.put(vpcon.getId(), res);
      }
      String content = new Gson().toJson(resolutions);
      fos.write(content.getBytes(StandardCharsets.UTF_8));
      return true;
    } catch (IOException exception) {
      log.warn("Fail to save port!", exception);
      return false;
    } finally {
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException exception) {
          log.warn("Fail to close stream!", exception);
        }
      }
    }
  }
}
