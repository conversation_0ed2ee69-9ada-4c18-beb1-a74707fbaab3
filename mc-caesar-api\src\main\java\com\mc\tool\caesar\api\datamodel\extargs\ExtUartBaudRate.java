package com.mc.tool.caesar.api.datamodel.extargs;

import lombok.Getter;

/**
 * 串口波特率.
 */
@Getter
public enum ExtUartBaudRate implements ExtArgObject {
  BR_115200(0, "115200"),
  BR_56000(1, "56000"),
  BR_38400(2, "38400"),
  BR_19200(3, "19200"),
  BR_14400(4, "14400"),
  BR_9600(5, "9600"),
  BR_4800(6, "4800"),
  BR_2400(7, "2400");
  private final int value;
  private final String name;

  ExtUartBaudRate(int value, String name) {
    this.value = value;
    this.name = name;
  }

  @Override
  public byte[] toBytes() {
    return new byte[] {(byte) getValue()};
  }

  @Override
  public String toString() {
    return name;
  }
}
