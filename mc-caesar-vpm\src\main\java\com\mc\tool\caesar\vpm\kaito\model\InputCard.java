package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import javax.validation.constraints.NotNull;
import lombok.Setter;

/**
 * 输入卡.
 */
@Setter
@JsonPropertyOrder({
    InputCard.JSON_PROPERTY_INPUT_ID,
    InputCard.JSON_PROPERTY_SLOT_ID,
    InputCard.JSON_PROPERTY_NAME,
    InputCard.JSON_PROPERTY_TYPE,
    InputCard.JSON_PROPERTY_VERSION,
    InputCard.JSON_PROPERTY_SN
})
public class InputCard {
  public static final String JSON_PROPERTY_INPUT_ID = "inputId";
  private Integer inputId;

  public static final String JSON_PROPERTY_SLOT_ID = "slotId";
  private Integer slotId;

  public static final String JSON_PROPERTY_NAME = "name";
  private String name;

  public static final String JSON_PROPERTY_TYPE = "type";
  private Integer type;

  public static final String JSON_PROPERTY_VERSION = "version";
  private String version;

  public static final String JSON_PROPERTY_SN = "sn";
  private String sn;


  public InputCard inputId(Integer inputId) {
    this.inputId = inputId;
    return this;
  }

  /**
   * 输入卡ID.
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_INPUT_ID)
  @JsonInclude()
  public Integer getInputId() {
    return inputId;
  }


  public InputCard slotId(Integer slotId) {
    this.slotId = slotId;
    return this;
  }

  /**
   * 输入卡槽位ID.
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_SLOT_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getSlotId() {
    return slotId;
  }


  public InputCard name(String name) {
    this.name = name;
    return this;
  }

  /**
   * 输入卡名称.
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public String getName() {
    return name;
  }


  public InputCard type(Integer type) {
    this.type = type;
    return this;
  }

  /**
   * 0表示普通输入卡，1表示子母卡.
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_TYPE)
  @JsonInclude()
  public Integer getType() {
    return type;
  }

  public InputCard version(String version) {
    this.version = version;
    return this;
  }

  /**
   * 版本.
   **/
  @JsonProperty(JSON_PROPERTY_VERSION)
  @JsonInclude()
  public String getVersion() {
    return version;
  }

  public InputCard sn(String sn) {
    this.sn = sn;
    return this;
  }

  /**
   * 序列号.
   **/
  @JsonProperty(JSON_PROPERTY_SN)
  @JsonInclude()
  public String getSn() {
    return sn;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InputCard inputCard = (InputCard) o;
    return Objects.equals(this.inputId, inputCard.inputId)
        && Objects.equals(this.slotId, inputCard.slotId)
        && Objects.equals(this.name, inputCard.name)
        && Objects.equals(this.type, inputCard.type)
        && Objects.equals(this.version, inputCard.version)
        && Objects.equals(this.sn, inputCard.sn);
  }

  @Override
  public int hashCode() {
    return Objects.hash(inputId, slotId, name, type, version, sn);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("InputCard {\n");
    sb.append("    inputId: ").append(toIndentedString(inputId)).append("\n");
    sb.append("    slotId: ").append(toIndentedString(slotId)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    version: ").append(toIndentedString(version)).append("\n");
    sb.append("    sn: ").append(toIndentedString(sn)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
