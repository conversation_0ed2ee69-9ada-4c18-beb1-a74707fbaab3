package com.mc.tool.caesar.vpm.pages.extenderupdate.offlinemanager;

import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.utility.UndecoratedAlert;
import com.mc.tool.framework.utility.UndecoratedDialog;
import java.beans.PropertyChangeListener;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import javafx.application.Platform;
import javafx.beans.property.ReadOnlyIntegerWrapper;
import javafx.beans.property.ReadOnlyStringWrapper;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.control.CheckBox;
import javafx.scene.control.TabPane;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.cell.CheckBoxTableCell;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.VBox;
import javafx.stage.Window;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class OffineManagerView extends VBox implements Initializable {
  private static final String EXTENDER_TAB_ID = "EXTENDER";
  private static final String CPU_TAB_ID = "CPU";
  private static final String CON_TAB_ID = "CON";
  @FXML
  private TabPane tabPane;
  @FXML
  private TableView<DeviceItem<ExtenderData>> extenderTableView;
  @FXML
  private TableView<DeviceItem<CpuData>> cpuTableView;
  @FXML
  private TableView<DeviceItem<ConsoleData>> conTableView;

  @FXML
  private TableColumn<DeviceItem<CpuData>, String> cpuExtenderTableColumn;
  @FXML
  private TableColumn<DeviceItem<ConsoleData>, String> conExtenderTableColumn;

  private WeakAdapter weakAdapter = new WeakAdapter();

  private CaesarDeviceController deviceController;
  private final String[] dataChange = {
      ExtenderData.PROPERTY_STATUS,
      CpuData.PROPERTY_STATUS,
      CpuData.PROPERTY_STATUS_DELETE,
      ConsoleData.PROPERTY_STATUS,
      ConsoleData.PROPERTY_STATUS_DELETE
  };

  private static ObservableList<DeviceItem<ExtenderData>> extenderList =
      FXCollections.observableArrayList();
  private static ObservableList<DeviceItem<CpuData>> cpuList = FXCollections.observableArrayList();
  private static ObservableList<DeviceItem<ConsoleData>> consoleList =
      FXCollections.observableArrayList();

  private String selectedTab;

  private OffineManagerView(CaesarDeviceController deviceController) {
    this.deviceController = deviceController;
    deviceController
        .getDataModel()
        .addPropertyChangeListener(
            dataChange,
            weakAdapter.wrap(
                (PropertyChangeListener)
                    evt ->
                        PlatformUtility.runInFxThread(
                            () -> {
                              updateExtenderList();
                              updateCpuList();
                              updateConsoleList();
                            })));
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    FXMLLoader loader = new FXMLLoader(classLoader.getResource(
        "com/mc/tool/caesar/vpm/pages/extenderupdate/offlinemanager/offlinemanager_view.fxml"));
    loader.setResources(
        ResourceBundle.getBundle("com.mc.tool.caesar.vpm.pages.extenderupdate.Bundle"));
    loader.setController(this);
    loader.setRoot(this);
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load offlinemanager_view.fxml", exception);
    }
  }

  public String getSelectedTab() {
    return selectedTab;
  }

  /**
   * 显示管理窗口.
   *
   * @param deviceController 设备管理器.
   */
  public static void showView(Window owner, CaesarDeviceController deviceController) {
    OffineManagerView view = new OffineManagerView(deviceController);
    UndecoratedDialog<ButtonType> dialog = new UndecoratedDialog<>();
    dialog.initOwner(owner);
    dialog.setTitle(CaesarI18nCommonResource.getString("offline_manager.title"));
    dialog.getDialogPane().setContent(view);
    dialog.getDialogPane().getButtonTypes().addAll(ButtonType.CLOSE, ButtonType.APPLY);
    Node node = dialog.getDialogPane().lookupButton(ButtonType.APPLY);
    if (node instanceof Button) {
      Button button = (Button) node;
      button.setText(CaesarI18nCommonResource.getString("offline_manager.delete_btn"));
      button.addEventFilter(
          MouseEvent.MOUSE_PRESSED,
          (event) -> {
            switch (view.getSelectedTab()) {
              case EXTENDER_TAB_ID:
                deleteExt(owner, deviceController);
                break;
              case CPU_TAB_ID:
                deleteCpu(owner, deviceController);
                break;
              case CON_TAB_ID:
                deleteCon(owner, deviceController);
                break;
              default:
                break;
            }
            event.consume();
          });
    }
    dialog.showAndWait();
  }

  private static void deleteExt(Window owner, CaesarDeviceController controller) {
    List<ExtenderData> extenderDataList = new ArrayList<>();
    List<ConsoleData> consolesChangeList = new ArrayList<>();
    List<CpuData> cpusChangeList = new ArrayList<>();
    List<PortData> portsChangeList = new ArrayList<>();
    FilteredList<DeviceItem<ExtenderData>> selectedExtList =
        extenderList.filtered(
            (data) -> data.getSelected().get() && !data.getData().isStatusOnline());
    if (selectedExtList.isEmpty()) {
      showEmptyAlert(owner);
      return;
    }
    for (DeviceItem<ExtenderData> deviceItem : selectedExtList) {
      extenderDataList.add(deviceItem.getData());
    }

    controller.execute(
        () -> {
          for (ExtenderData extenderData : extenderDataList) {
            if (extenderData.getConsoleData() != null
                && !consolesChangeList.contains(extenderData.getConsoleData())) {
              ConsoleData consoleData = extenderData.getConsoleData();
              for (int i = 0; i < CaesarConstants.Extender.CPUCON; i++) {
                if (extenderData.equals(consoleData.getExtenderData(i))) {
                  consoleData.setExtenderData(i, null);
                  consoleData.setPorts(consoleData.getPorts() - 1);
                  break;
                }
              }
              consolesChangeList.add(consoleData);
            } else if (extenderData.getCpuData() != null
                && !cpusChangeList.contains(extenderData.getCpuData())) {
              CpuData cpuData = extenderData.getCpuData();
              for (int i = 0; i < CaesarConstants.Extender.CPUCON; i++) {
                if (extenderData.equals(cpuData.getExtenderData(i))) {
                  cpuData.setExtenderData(i, null);
                  cpuData.setPorts(cpuData.getPorts() - 1);
                  break;
                }
              }
              cpusChangeList.add(cpuData);
            }
            PortData portData = extenderData.getPortData();
            if (portData != null) {
              portData.setExtenderData(null);
              portData.setType(0);
              portsChangeList.add(portData);
            }
            PortData rdPortData = extenderData.getRdPortData();
            if (rdPortData != null) {
              rdPortData.setExtenderData(null);
              rdPortData.setType(0);
              portsChangeList.add(rdPortData);
            }
            extenderData.delete(false);
          }
          try {
            if (!consolesChangeList.isEmpty()) {
              controller.getDataModel().sendConsoleData(consolesChangeList);
            }
            if (!cpusChangeList.isEmpty()) {
              controller.getDataModel().sendCpuData(cpusChangeList);
            }
            if (!extenderDataList.isEmpty()) {
              controller.getDataModel().sendExtenderData(extenderDataList);
            }
            if (!portsChangeList.isEmpty()) {
              controller.getDataModel().sendPortData(portsChangeList);
            }
          } catch (DeviceConnectionException | BusyException ex) {
            log.warn("Delete extenderdata failed!", ex);
          }
        });
  }

  private static void deleteCpu(Window owner, CaesarDeviceController controller) {
    List<CpuData> cpusChangeList = new ArrayList<>();
    List<ExtenderData> extenderChangeList = new ArrayList<>();
    List<ConsoleData> consolesChangeList = new ArrayList<>();
    List<UserData> usersChangeList = new ArrayList<>();
    FilteredList<DeviceItem<CpuData>> selectedCpuList =
        cpuList.filtered((data) -> data.getSelected().get());
    if (selectedCpuList.isEmpty()) {
      showEmptyAlert(owner);
      return;
    }
    List<CpuData> cpus = new ArrayList<>();
    for (DeviceItem<CpuData> deviceItem : selectedCpuList) {
      cpus.add(deviceItem.getData());
    }

    controller.execute(
        () -> {
          for (CpuData obj : cpus) {
            for (ConsoleData consoleData : obj.getConfigDataManager().getActiveConsoles()) {
              if (consoleData.isNoAccess(obj)) {
                consoleData.setNoAccess(obj, true);
              }
              if (consoleData.isVideoAccess(obj)) {
                consoleData.setVideoAccess(obj, false);
              }
              if (consoleData.isStatusPrivate()) {
                consoleData.setStatusPrivate(false);
              }
              for (int i = 0; i < CaesarConstants.Cpu.FAVORITE; i++) {
                if (obj.equals(consoleData.getFavoriteData(i))) {
                  consoleData.setFavoriteData(i, null);
                }
              }
              if (obj.equals(consoleData.getCpuData())) {
                consoleData.setCpuData(null);
                consoleData.setStatusVideoOnly(false);
              }
              consolesChangeList.add(consoleData);
            }
            for (ExtenderData extenderData : obj.getExtenderDatas()) {
              if (obj.equals(extenderData.getCpuData())) {
                extenderData.setCpuData(null);
              }
              extenderChangeList.add(extenderData);
            }
            if (obj.getRealCpuData() != null && !cpusChangeList.contains(obj.getRealCpuData())) {
              CpuData cpuData = obj.getRealCpuData();
              cpuData.setRealCpuData(null);
              cpusChangeList.add(cpuData);
            }
            for (UserData userData : obj.getConfigDataManager().getActiveUsers()) {
              if (userData.isNoAccess(obj)) {
                userData.setNoAccess(obj, true);
              }
              if (userData.isVideoAccess(obj)) {
                userData.setVideoAccess(obj, false);
              }
              for (int i = 0; i < CaesarConstants.Cpu.FAVORITE; i++) {
                if (obj.equals(userData.getFavoriteData(i))) {
                  userData.setFavoriteData(i, null);
                }
              }
              usersChangeList.add(userData);
            }
            obj.delete(false);
            if (!cpusChangeList.contains(obj)) {
              cpusChangeList.add(obj);
            }
          }
          try {
            if (!consolesChangeList.isEmpty()) {
              controller.getDataModel().sendConsoleData(consolesChangeList);
            }
            if (!cpusChangeList.isEmpty()) {
              controller.getDataModel().sendCpuData(cpusChangeList);
            }
            if (!extenderChangeList.isEmpty()) {
              controller.getDataModel().sendExtenderData(extenderChangeList);
            }
            if (!usersChangeList.isEmpty()) {
              controller.getDataModel().sendUserData(usersChangeList);
            }
          } catch (DeviceConnectionException | BusyException ex) {
            log.warn("Delete Cpu failed!", ex);
          }
        });
  }

  private static void deleteCon(Window owner, CaesarDeviceController controller) {
    List<CpuData> cpusChangeList = new ArrayList<>();
    List<ExtenderData> extenderChangeList = new ArrayList<>();
    List<ConsoleData> consolesChangeList = new ArrayList<>();
    List<FunctionKeyData> functionKeyChangeList = new ArrayList<>();
    FilteredList<DeviceItem<ConsoleData>> selectedConList =
        consoleList.filtered((data) -> data.getSelected().get());
    if (selectedConList.isEmpty()) {
      showEmptyAlert(owner);
      return;
    }
    List<ConsoleData> consoles = new ArrayList<>();
    for (DeviceItem<ConsoleData> deviceItem : selectedConList) {
      consoles.add(deviceItem.getData());
    }

    controller.execute(
        () -> {
          for (ConsoleData obj : consoles) {
            for (ExtenderData extenderData : obj.getExtenderDatas()) {
              if (obj.equals(extenderData.getConsoleData())) {
                extenderData.setConsoleData(null);
              }
              extenderChangeList.add(extenderData);
            }
            if (obj.getCpuData() != null && !cpusChangeList.contains(obj.getCpuData())) {
              CpuData cpuData = obj.getCpuData();
              if (cpuData != null && obj.equals(cpuData.getConsoleData())) {
                cpuData.setConsoleData(null);
                cpuData.setStatusPrivate(false);
                cpusChangeList.add(cpuData);
              }
            }
            if (obj.getRdCpuData() != null && !cpusChangeList.contains(obj.getRdCpuData())) {
              CpuData cpuData = obj.getRdCpuData();
              if (cpuData != null && obj.equals(cpuData.getConsoleData())) {
                cpuData.setConsoleData(null);
                cpuData.setStatusPrivate(false);
                cpusChangeList.add(cpuData);
              }
            }
            for (FunctionKeyData functionKeyData :
                controller.getDataModel().getConfigData().getFunctionKeyDatas()) {
              if (functionKeyData.getHostSubscript() == obj.getOid() + 1) {
                functionKeyData.reset();
                functionKeyChangeList.add(functionKeyData);
              }
            }
            obj.delete(false);
            if (!consolesChangeList.contains(obj)) {
              consolesChangeList.add(obj);
            }
          }
          try {
            if (!consolesChangeList.isEmpty()) {
              controller.getDataModel().sendConsoleData(consolesChangeList);
            }
            if (!consolesChangeList.isEmpty()) {
              controller.getDataModel().sendCpuData(cpusChangeList);
            }
            if (!extenderChangeList.isEmpty()) {
              controller.getDataModel().sendExtenderData(extenderChangeList);
            }
            if (!functionKeyChangeList.isEmpty()) {
              controller.getDataModel().sendFunctionKeyData(functionKeyChangeList);
            }
          } catch (DeviceConnectionException | BusyException ex) {
            log.warn("Delete Con failed!", ex);
          }
        });
  }

  private static void showEmptyAlert(Window owner) {
    Platform.runLater(
        () -> {
          UndecoratedAlert alert = new UndecoratedAlert(AlertExType.WARNING);
          alert.initOwner(owner);
          alert.setTitle(CaesarI18nCommonResource.getString("offline_manager.empty_alert.title"));
          alert.setHeaderText(null);
          alert.setContentText(
              CaesarI18nCommonResource.getString("offline_manager.empty_alert.text"));
          alert.showAndWait();
        });
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    selectedTab = tabPane.getSelectionModel().getSelectedItem().getId();
    tabPane
        .getSelectionModel()
        .selectedItemProperty()
        .addListener(
            weakAdapter.wrap(
                (obsevable, oldValue, newValue) -> {
                  if (newValue != null) {
                    selectedTab = newValue.getId();
                  }
                }));

    initSelectableTableView(extenderTableView, true);
    initSelectableTableView(cpuTableView, false);
    initSelectableTableView(conTableView, false);

    cpuExtenderTableColumn.setCellValueFactory(
        (feat) -> {
          ExtenderData data = feat.getValue().getData().getExtenderData(0);
          if (data == null) {
            return new ReadOnlyStringWrapper("");
          } else {
            return new ReadOnlyStringWrapper(data.getName());
          }
        });

    conExtenderTableColumn.setCellValueFactory(
        (feat) -> {
          ExtenderData data = feat.getValue().getData().getExtenderData(0);
          if (data == null) {
            return new ReadOnlyStringWrapper("");
          } else {
            return new ReadOnlyStringWrapper(data.getName());
          }
        });

    extenderTableView.setItems(extenderList);
    cpuTableView.setItems(cpuList);
    conTableView.setItems(consoleList);

    updateExtenderList();
    updateCpuList();
    updateConsoleList();
  }

  protected <T extends DataObject> void initSelectableTableView(
      TableView<DeviceItem<T>> tableView, Boolean isExt) {
    TableColumn<DeviceItem<T>, Boolean> selectedColumn = new TableColumn<>();
    CheckBox selectAllCheckBox = new CheckBox();
    selectAllCheckBox
        .selectedProperty()
        .addListener(
            weakAdapter.wrap(
                (observable, oldValue, newValue) -> {
                  if (newValue != null) {
                    for (DeviceItem<T> item : tableView.getItems()) {
                      item.getSelected().set(newValue);
                    }
                  }
                }));
    selectedColumn.setGraphic(selectAllCheckBox);

    TableColumn<DeviceItem<T>, String> nameColumn = new TableColumn<>();
    nameColumn.setCellValueFactory(
        (feat) -> new ReadOnlyStringWrapper(feat.getValue().getData().getName()));

    if (isExt) {
      nameColumn.setText(CaesarI18nCommonResource.getString("offline_manager.ext_name"));
      TableColumn<DeviceItem<T>, String> deviceColumn = new TableColumn<>();
      deviceColumn.setText(CaesarI18nCommonResource.getString("offline_manager.device"));
      deviceColumn.setCellValueFactory(
          (feat) -> {
            if (((ExtenderData) feat.getValue().getData()).getCpuData() != null) {
              return new ReadOnlyStringWrapper(
                  ((ExtenderData) feat.getValue().getData()).getCpuData().getName());
            } else if (((ExtenderData) feat.getValue().getData()).getConsoleData() != null) {
              return new ReadOnlyStringWrapper(
                  ((ExtenderData) feat.getValue().getData()).getConsoleData().getName());
            }
            return new ReadOnlyStringWrapper("");
          });
      tableView.getColumns().addAll(0, Arrays.asList(selectedColumn, nameColumn, deviceColumn));
    } else {
      nameColumn.setText(CaesarI18nCommonResource.getString("offline_manager.name"));
      TableColumn<DeviceItem<T>, Number> idColumn = new TableColumn<>();
      idColumn.setText("ID");
      idColumn.setCellValueFactory(
          (feat) -> new ReadOnlyIntegerWrapper(feat.getValue().getData().getId()));
      tableView.getColumns().addAll(0, Arrays.asList(selectedColumn, idColumn, nameColumn));
    }

    selectedColumn.setCellValueFactory((feat) -> feat.getValue().getSelected());
    selectedColumn.setCellFactory(CheckBoxTableCell.forTableColumn(selectedColumn));
    tableView.setEditable(true);
  }

  protected void updateExtenderList() {
    Map<ExtenderData, DeviceItem<ExtenderData>> existSet = new HashMap<>();

    for (DeviceItem<ExtenderData> item : extenderList) {
      existSet.put(item.getData(), item);
    }

    List<DeviceItem<ExtenderData>> newList = new ArrayList<>();
    for (ExtenderData extenderData :
        deviceController.getDataModel().getConfigDataManager().getActiveExtenders()) {
      if (extenderData.getPort() != 0
          && extenderData.getPortData().getExtenderData() == extenderData) {
        continue;
      }
      if (extenderData.getRdPort() != 0
          && extenderData.getRdPortData().getExtenderData() == extenderData) {
        continue;
      }
      if (extenderData.isStatusDelete()) {
        continue;
      }
      DeviceItem<ExtenderData> item = existSet.get(extenderData);
      if (item == null) {
        item = new DeviceItem<>();
        item.setData(extenderData);
      }
      newList.add(item);
    }
    extenderList.setAll(newList);
  }

  protected void updateCpuList() {
    Map<CpuData, DeviceItem<CpuData>> existSet = new HashMap<>();

    for (DeviceItem<CpuData> item : cpuList) {
      existSet.put(item.getData(), item);
    }

    List<DeviceItem<CpuData>> newList = new ArrayList<>();
    for (CpuData cpuData : deviceController.getDataModel().getConfigDataManager().getActiveCpus()) {
      ExtenderData extenderData = cpuData.getExtenderData(0);
      if (extenderData != null && extenderData.getCpuData() == cpuData
              && (extenderData.getPort() != 0 || extenderData.getRdPort() != 0)) {
        continue;
      }
      if (cpuData.isStatusDelete()) {
        continue;
      }
      DeviceItem<CpuData> item = existSet.get(cpuData);
      if (item == null) {
        item = new DeviceItem<>();
        item.setData(cpuData);
      }
      newList.add(item);
    }
    cpuList.setAll(newList);
  }

  protected void updateConsoleList() {
    Map<ConsoleData, DeviceItem<ConsoleData>> existSet = new HashMap<>();

    for (DeviceItem<ConsoleData> item : consoleList) {
      existSet.put(item.getData(), item);
    }

    List<DeviceItem<ConsoleData>> newList = new ArrayList<>();
    for (ConsoleData conData :
        deviceController.getDataModel().getConfigDataManager().getActiveConsoles()) {
      ExtenderData extenderData = conData.getExtenderData(0);
      if (extenderData != null && extenderData.getConsoleData() == conData
              && (extenderData.getPort() != 0 || extenderData.getRdPort() != 0)) {
        continue;
      }
      if (extenderData != null && extenderData.isStatusDelete()) {
        continue;
      }
      DeviceItem<ConsoleData> item = existSet.get(conData);
      if (item == null) {
        item = new DeviceItem<>();
        item.setData(conData);
      }
      newList.add(item);
    }
    consoleList.setAll(newList);
  }
}
