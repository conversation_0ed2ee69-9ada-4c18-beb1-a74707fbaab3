package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarConstants.System.Forcebits;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.api.utils.Utilities;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;

/**
 * SystemData.
 *
 * @brief 系统的字符串项配置.
 */
public class SystemData extends AbstractData {

  public static final String PROPERTY_BASE = "SystemConfigData.SystemData.";
  /**
   * System Data 下的 General窗口标题下的Auto Save.
   */
  public static final String PROPERTY_FORCE_BITS_AUTO_SAVE =
      "SystemConfigData.SystemData.ForceBits.AutoSave";
  /**
   * System Data 下的 General窗口标题下的Enable COM Echo.
   */
  public static final String PROPERTY_FORCE_BITS_COM_ECHO =
      "SystemConfigData.SystemData.ForceBits.ComEcho";
  /**
   * System Data 下的 General窗口标题下的 Enable LAN Echo.
   */
  public static final String PROPERTY_FORCE_BITS_LAN_ECHO =
      "SystemConfigData.SystemData.ForceBits.LanEcho";
  /**
   * System Data 下的 General窗口标题下的 Enable Redundancy.
   */
  public static final String PROPERTY_FORCE_BITS_REDUNDANCY =
      "SystemConfigData.SystemData.ForceBits.Redundancy";
  public static final String PROPERTY_FORCE_BITS_FORCE_PUSH_GET =
      "SystemConfigData.SystemData.ForceBits.Redundancy";
  /**
   * System Data 下的 General窗口标题下的Sub Matrix.
   */
  public static final String PROPERTY_FORCE_BITS_SLAVE =
      "SystemConfigData.SystemData.ForceBits.Slave";
  /**
   * System Data 下的 General窗口标题下的Echo Only.
   */
  public static final String PROPERTY_FORCE_BITS_ECHOONLY =
      "SystemConfigData.SystemData.ForceBits.EchoOnly";
  /**
   * System Data 下的 General窗口标题下的Synchronize.
   */
  public static final String PROPERTY_FORCE_BITS_SYNCHRONIZE =
      "SystemConfigData.SystemData.ForceBits.Synchronize";
  /**
   * System Data 下的 General窗口标题下的Invalid I/O Boards.
   */
  public static final String PROPERTY_FORCE_BITS_INVALID =
      "SystemConfigData.SystemData.ForceBits.Invalid";
  /**
   * System Data 下的 General窗口标题下的Enable Old Echo.
   */
  public static final String PROPERTY_FORCE_BITS_OLDECHO =
      "SystemConfigData.SystemData.ForceBits.OldEcho";

  public static final String PROPERTY_FORCE_UART_ENABLE =
      "SystemConfigData.SystemData.ForceBits.UartEnable";
  /**
   * System Data 下的 General窗口标题下的Load Default.
   */
  public static final String PROPERTY_FORCE_BITS_DEFAULT =
      "SystemConfigData.SystemData.ForceBits.Default";
  public static final String PROPERTY_FORCE_BITS_REMOVESLAVE =
      "SystemConfigData.SystemData.ForceBits.RemoveSlave";
  public static final String PROPERTY_FORCE_BITS_ONLINECONFIG =
      "SystemConfigData.SystemData.ForceBits.OnlineConfig";
  /**
   * System Data 下的 General窗口标题下的name.
   */
  public static final String FIELD_NAME = "Name";
  public static final String PROPERTY_NAME = "SystemConfigData.SystemData.Name";
  public static final String FIELD_INFO = "Info";
  public static final String PROPERTY_INFO = "SystemConfigData.SystemData.Info";
  public static final String FIELD_DEVICE = "Device";
  /**
   * System Data 下的 General窗口标题下的Device.
   */
  public static final String PROPERTY_DEVICE = "SystemConfigData.SystemData.Device";
  public static final String FIELD_MASTER_IP = "MasterIP";
  /**
   * System Data 下的 General窗口标题下的 Master IP Address.
   */
  public static final String PROPERTY_MASTER_IP = "SystemConfigData.SystemData.MasterIP";
  public static final String DEFAULT_DEVICENAME = "SWITCH_01";
  public static final String DEFAULT_CONFIGNAME = "Standard";
  public static final String DEFAULT_CONFIGINFO = "Factory settings";
  private final SystemConfigData systemConfigData;
  @Expose
  private StringProperty device = new SimpleStringProperty(); // host name for network environment
  @Expose
  private StringProperty name = new SimpleStringProperty(); // name of current matrix configuration
  @Expose
  private StringProperty info = new SimpleStringProperty();
  // decription of current matrix configuration

  @Expose
  private StringProperty masterIp = new SimpleStringProperty("s0.0.0.0".substring(1));
  // ip of master matrix

  /**
   * .
   */
  public SystemData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager,
      String fqn, SystemConfigData systemConfigData) {
    super(pcs, configDataManager, -1, fqn);
    this.systemConfigData = systemConfigData;

    initCommitRollback();
  }

  @Override
  public void initDefaults() {
    setDevice("SWITCH_01");
    setName("Standard");
    setInfo("Factory settings");
    setRedundancy(true);
  }

  public boolean isAutoSave() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.AUTOSAVE);
  }

  /**
   * .
   */
  public void setAutoSave(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.AUTOSAVE}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isComEcho() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.COMECHO);
  }

  /**
   * .
   */
  public void setComEcho(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.COMECHO}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isLanEcho() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.LANECHO);
  }

  /**
   * .
   */
  public void setLanEcho(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.LANECHO}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isForcePushGet() {
    return Utilities.areBitsSet(systemConfigData.getForceBits(), Forcebits.FORCEPUSHGET);
  }

  /**
   * .
   */
  public void setForcePushGet(boolean force) {
    systemConfigData
        .setForceBits(Utilities.setBits(systemConfigData.getForceBits(), force, new int[]{
            Forcebits.FORCEPUSHGET}));
  }

  public boolean isRedundancy() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.REDUNDANT);
  }

  /**
   * .
   */
  public void setRedundancy(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.REDUNDANT}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public String getInfo() {
    return this.info.get();
  }

  /**
   * .
   */
  public void setInfo(String info) {
    String oldValue = this.info.get();
    this.info.set(info);

    firePropertyChange(SystemData.PROPERTY_INFO, oldValue, info, new int[0]);
  }

  public StringProperty getInfoProperty() {
    return this.info;
  }

  public String getName() {
    return this.name.get();
  }

  /**
   * .
   */
  public void setName(String name) {
    String oldValue = this.name.get();
    this.name.set(name);

    firePropertyChange(SystemData.PROPERTY_NAME, oldValue, name, new int[0]);
  }

  public StringProperty getNameProperty() {
    return this.name;
  }

  public String getDevice() {
    return this.device.get();
  }

  /**
   * .
   */
  public void setDevice(String device) {
    String oldValue = this.device.get();
    this.device.set(device);
    firePropertyChange(SystemData.PROPERTY_DEVICE, oldValue, device, new int[0]);
  }

  public StringProperty getDeviceProperty() {
    return this.device;
  }

  public byte[] getMasterIp() {
    return IpUtil.getAddressByte(this.masterIp.get());
  }

  /**
   * .
   */
  public void setMasterIp(byte[] masterIp) {
    byte[] oldValue = IpUtil.getAddressByte(this.masterIp.get());
    this.masterIp.set(IpUtil.getAddressString(masterIp));

    firePropertyChange(SystemData.PROPERTY_MASTER_IP, oldValue, masterIp, new int[0]);
  }

  public StringProperty getMasterIpProperty() {
    return this.masterIp;
  }


  public boolean isSlave() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.SLAVE);
  }

  /**
   * .
   */
  public void setSlave(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.SLAVE}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isEchoOnly() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.ECHOONLY);
  }

  /**
   * .
   */
  public void setEchoOnly(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.ECHOONLY}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isSynchronize() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.SYNCHRONIZE);
  }

  /**
   * .
   */
  public void setSynchronize(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.SYNCHRONIZE}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isDefaultBackup() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.DEFAULT_BACKUP);
  }

  public void setDefaultBackup(boolean enable) {
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enable, new int[]{Forcebits.DEFAULT_BACKUP}));
  }

  public boolean isInvalidIoBoard() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.INVALID);
  }

  /**
   * .
   */
  public void setInvalidIoBoard(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.INVALID}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isOldEcho() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.OLDECHO);
  }

  /**
   * .
   */
  public void setOldEcho(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.OLDECHO}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isUartEnable() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.UART_ENABLE);
  }

  /**
   * .
   */
  public void setUartEnable(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.UART_ENABLE}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isDefault() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.DEFAULT);
  }

  /**
   * .
   */
  public void setDefault(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.DEFAULT}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isRemoveSlave() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.REMOVESLAVE);
  }

  /**
   * .
   */
  public void setRemoveSlave(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.REMOVESLAVE}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isOnlineConfig() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.ONLINECONFIG);
  }

  /**
   * .
   */
  public void setOnlineConfig(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.ONLINECONFIG}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (SystemData.PROPERTY_NAME.equals(propertyName)) {
      setName(String.class.cast(value));
    } else if (SystemData.PROPERTY_INFO.equals(propertyName)) {
      setInfo(String.class.cast(value));
    } else if (SystemData.PROPERTY_DEVICE.equals(propertyName)) {
      setDevice(String.class.cast(value));
    } else if (SystemData.PROPERTY_MASTER_IP.equals(propertyName)) {
      setMasterIp(byte[].class.cast(value));
    }
  }
}

