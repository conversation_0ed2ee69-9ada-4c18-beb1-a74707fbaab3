package com.mc.tool.caesar.api.datamodel.vp;

/**
 * .
 */
public class Vp6Builder implements VpBuilder {
  public static final String NAME_FORMAT_STRING = "VP6_%05d";

  @Override
  public VpConConfigData createConfigData() {
    return new Vp6ConfigData();
  }

  @Override
  public int getVpconId(int extId, int conId) {
    return extId & 0xfffffff8;
  }

  @Override
  public int getVpconInputIndex(int extId) {
    return extId & 0x07;
  }

  @Override
  public String formatName(int id) {
    return String.format(NAME_FORMAT_STRING, id);
  }
}
