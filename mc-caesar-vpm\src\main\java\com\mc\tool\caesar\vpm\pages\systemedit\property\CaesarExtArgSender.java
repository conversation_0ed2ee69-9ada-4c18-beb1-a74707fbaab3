package com.mc.tool.caesar.vpm.pages.systemedit.property;

import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.extargs.ExtArgObject;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarExtArgSender<T> implements CaesarPropertySender<T> {
  private final CaesarDeviceController controller;
  private final ExtenderData extenderData;
  private final String propertyName;
  private final int extArgId;

  /**
   * .
   *
   * @param controller controller
   * @param extenderData extender
   * @param extArgId 参数id
   * @param propertyName extender的属性名
   */
  public CaesarExtArgSender(
      CaesarDeviceController controller,
      ExtenderData extenderData,
      int extArgId,
      String propertyName) {
    this.controller = controller;
    this.extenderData = extenderData;
    this.propertyName = propertyName;
    this.extArgId = extArgId;
  }

  @Override
  public void sendValue(T value) {
    controller.execute(
        () -> {
          extenderData.setProperty(propertyName, value);
          try {
            if (value instanceof ExtArgObject) {
              controller.getDataModel().setExtArg(extenderData, extArgId, (ExtArgObject) value);
            } else if (value instanceof Boolean) {
              controller.getDataModel().setExtBooleanArg(extenderData, extArgId, (Boolean) value);
            } else if (value instanceof Number) {
              controller.getDataModel()
                  .setExtNumberArg(extenderData, extArgId, ((Number) value).intValue());
            } else {
              log.warn("ERROR type of value :" + value.getClass().getName());
            }
          } catch (ConfigException | BusyException | DeviceConnectionException exc) {
            log.warn("Fail to set {} for {}", propertyName, extenderData.getName(), exc);
          }
        });
  }
}
