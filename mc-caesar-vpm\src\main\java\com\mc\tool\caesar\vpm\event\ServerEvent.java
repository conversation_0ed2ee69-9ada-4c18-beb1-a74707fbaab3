package com.mc.tool.caesar.vpm.event;

import java.net.InetAddress;

/**
 * .
 */
public abstract class ServerEvent {
  private InetAddress inetAddress;
  private byte[] rawBytes;
  private int rawLength = -1;

  /** . */
  public ServerEvent(byte[] message, int length, InetAddress inetAddress, String charset) {
    initialize(message, length, inetAddress, charset);

    parse();
  }

  protected void initialize(byte[] message, int length, InetAddress inetAddress, String charset) {
    this.rawBytes = message;
    this.rawLength = length;
    this.inetAddress = inetAddress;
  }

  public InetAddress getInetAddress() {
    return this.inetAddress;
  }

  public byte[] getRawBytes() {
    return this.rawBytes.clone();
  }

  public int getRawLength() {
    return this.rawLength;
  }

  protected abstract void parse();
}
