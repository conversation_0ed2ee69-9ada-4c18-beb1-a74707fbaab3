/*
 * Javolution - Java(TM) Solution for Real-Time and Embedded Systems Copyright (C) 2012 - Javolution
 * (http://javolution.org/) All rights reserved.
 *
 * Permission to use, copy, modify, and distribute this software is freely granted, provided that
 * this notice is preserved.
 */

package com.mc.tool.caesar.api.io;

/**
 * <p>
 * Equivalent to <code>C/C++ union</code>; this class works in the same way as {@link Struct}
 * (sub-class) except that all members are mapped to the same location in memory.
 * </p>
 * <p>
 * Here is an example of C union: {@code union Number { int   asInt; float asFloat; char
 * asString[12]; };}
 * </p>
 * <p>
 * And its Java equivalent: {@code public class Number extends Union { Signed32   asInt    = new
 * Signed32(); Float32    asFloat  = new Float32(); Utf8String asString = new Utf8String(12); }}
 * </p>
 * <p>
 * As for any {@link Struct}, fields are directly accessible: {@code Number num = new Number();
 * num.asInt.set(23); num.asString.set("23"); // Null terminated (C compatible) float f =
 * num.asFloat.get();}
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">Jean-<PERSON>utelle</a>
 * @version 1.0, October 4, 2004
 */
public abstract class Union extends Struct {

  /**
   * Default constructor.
   */
  public Union() {

  }

  /**
   * Returns <code>true</code>.
   *
   * @return <code>true</code>
   */
  @Override
  public final boolean isUnion() {
    return true;
  }
}
