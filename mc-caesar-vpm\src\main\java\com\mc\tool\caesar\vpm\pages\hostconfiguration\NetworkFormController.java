package com.mc.tool.caesar.vpm.pages.hostconfiguration;

import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.MatrixStatus;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.vpm.devices.CaesarDemoDeviceController;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.framework.utility.UndecoratedAlert;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.event.ActionEvent;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.control.AlertEx;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Label;
import javafx.scene.layout.HBox;

/** . */
public class NetworkFormController implements Initializable {

  @FXML private Label backupLabel;
  @FXML private Label ipAddress2Label;
  @FXML private Label netMask2Label;
  @FXML private Label gateway2Label;
  @FXML private HBox ipAddress2Box;
  @FXML private HBox netMask2Box;
  @FXML private HBox gateway2Box;
  @FXML private Button rebootBtn;
  @FXML private Label rebootLabel;

  @FXML private Label dualMasterVirtualIpLabel;
  @FXML private Label dualMasterVirtualRouteIdLabel;
  @FXML private HBox virtualIpBox;
  @FXML private HBox routeIdBox;

  private final CaesarDeviceController deviceController;
  private final WeakAdapter weakAdapter = new WeakAdapter();

  public NetworkFormController(CaesarDeviceController deviceController) {
    this.deviceController = deviceController;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    boolean visible = deviceController instanceof CaesarDemoDeviceController
        || deviceController.getDataModel().getConfigData().getMatrixStatus()
        .isDoubleBackup();
    setVisible(backupLabel, visible);
    setVisible(ipAddress2Label, visible);
    setVisible(netMask2Label, visible);
    setVisible(gateway2Label, visible);
    setVisible(ipAddress2Box, visible);
    setVisible(netMask2Box, visible);
    setVisible(gateway2Box, visible);
    setVisible(rebootBtn, visible);
    setVisible(rebootLabel, visible);

    if (visible) {
      MatrixStatus status = deviceController.getDataModel().getConfigData().getMatrixStatus();
      rebootBtn.setDisable(status.isDoubleBackup() && !status.isActive());
      rebootBtn.setOnAction(
          weakAdapter.wrap(
              (EventHandler<ActionEvent>)
                  event -> {
                    UndecoratedAlert alert = new UndecoratedAlert(AlertEx.AlertExType.WARNING);
                    alert.initOwner(rebootBtn.getScene().getWindow());
                    alert.setHeaderText(null);
                    alert.getButtonTypes().add(ButtonType.CANCEL);
                    alert.setTitle(Bundle.NbBundle.getMessage("network.reboot.alert.title"));
                    alert.setContentText(
                        Bundle.NbBundle.getMessage("network.reboot.alert.content"));
                    alert.showAndWait().ifPresent((type) -> {
                      if (type.equals(ButtonType.OK)) {
                        deviceController.execute(deviceController::dualMatrixReboot);
                      }
                    });
                  }));
    }
    boolean visible2 = false;
    ModuleData module = deviceController.getDataModel().getSwitchModuleData().getModuleData(0);
    if (module != null) {
      CaesarConstants.Module.Type type = CaesarConstants.Module.Type.valueOf(module.getType());
      if (type == null || CaesarConstants.Module.Type.PLUGIN_384.equals(type)
          || CaesarConstants.Module.Type.PLUGIN_816.equals(type)
          || CaesarConstants.Module.Type.PLUGIN_144.equals(type)) {
        visible2 = true;
      }
    }
    setVisible(dualMasterVirtualIpLabel, visible2);
    setVisible(dualMasterVirtualRouteIdLabel, visible2);
    setVisible(virtualIpBox, visible2);
    setVisible(routeIdBox, visible2);
  }

  private void setVisible(Node node, boolean visible) {
    node.managedProperty().set(visible);
    node.visibleProperty().set(visible);
  }
}
