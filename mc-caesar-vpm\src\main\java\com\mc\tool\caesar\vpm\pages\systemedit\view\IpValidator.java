package com.mc.tool.caesar.vpm.pages.systemedit.view;

import com.mc.common.util.CommonResource;
import javafx.scene.control.Control;
import org.controlsfx.validation.ValidationResult;
import org.controlsfx.validation.Validator;

class IpValidator implements Validator<String> {
  private static final String IP_REGX =
      "((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})" + "(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}";

  @Override
  public ValidationResult apply(Control target, String value) {
    return ValidationResult.fromErrorIf(
        target,
        CommonResource.getString("ip_input_dialog.ip_format_error"),
        !value.matches(IP_REGX));
  }
}
