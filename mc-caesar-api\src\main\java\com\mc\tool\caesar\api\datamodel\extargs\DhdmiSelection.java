package com.mc.tool.caesar.api.datamodel.extargs;

/**
 * .
 */
public enum DhdmiSelection implements ExtArgObject {
  HDMI1(0), HDMI2(1), AUTO(2);

  private int value;

  DhdmiSelection(int value) {
    this.value = value;
  }

  public int getValue() {
    return value;
  }

  @Override
  public byte[] toBytes() {
    return new byte[]{(byte) getValue()};
  }


  /**
   * fromValue.
   */
  public static DhdmiSelection fromValue(int value) {
    for (DhdmiSelection dhdmiSelection : DhdmiSelection.values()) {
      if (dhdmiSelection.getValue() == value) {
        return dhdmiSelection;
      }
    }
    return null;
  }
}
