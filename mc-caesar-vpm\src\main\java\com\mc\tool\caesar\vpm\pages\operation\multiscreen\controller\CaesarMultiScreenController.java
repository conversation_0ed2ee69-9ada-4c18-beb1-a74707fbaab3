package com.mc.tool.caesar.vpm.pages.operation.multiscreen.controller;

import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.MultiviewData;
import com.mc.tool.caesar.api.datamodel.MultiviewLayoutType;
import com.mc.tool.caesar.api.datamodel.MultiviewSource;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.utils.CpuDataStateWrapper;
import com.mc.tool.caesar.api.utils.CpuDataStateWrapper.Access;
import com.mc.tool.caesar.api.utils.DisconnectCpuDataStateWrapper;
import com.mc.tool.caesar.api.utils.ExtendedSwitchUtility;
import com.mc.tool.caesar.api.utils.SwitchType;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.multiscreen.datamodel.CaesarMultiScreenData;
import com.mc.tool.caesar.vpm.pages.operation.multiscreen.view.CaesarMultiScreenItem;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMultiScreenFunc;
import com.mc.tool.caesar.vpm.util.TerminalUtility;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.operation.crossscreen.controller.MultiScreenController;
import com.mc.tool.framework.systemedit.datamodel.VisualEditConnection;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.TypeWrapper;
import java.beans.PropertyChangeListener;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;
import javafx.beans.property.ObjectProperty;
import javafx.event.WeakEventHandler;
import javafx.scene.Node;
import javafx.scene.canvas.Canvas;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.MenuItem;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.AnchorPane;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

/**
 * .
 */
@Slf4j
public class CaesarMultiScreenController
    extends MultiScreenController<
        CaesarMultiScreenData,
        CaesarMultiScreenFunc,
        CaesarMultiScreenControllable,
        CaesarMultiScreenItem>
    implements CaesarMultiScreenControllable {

  @Getter
  private CaesarDeviceController deviceController;
  protected WeakAdapter weakAdapter = new WeakAdapter();
  private VisualEditModel model;

  protected final String[] updateData = {
      ConsoleData.PROPERTY_MULTIVIEW_INDEX, MultiviewData.PROPERTY_LAYOUT_TYPE, MultiviewSource.PROPERTY_US_CONNECTED_CPU_INDEX
  };

  @Override
  public void init(VisualEditModel model, CaesarMultiScreenFunc currentFunction) {
    super.init(model, currentFunction);
    this.model = model;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    super.initialize(location, resources);
    screenList.addEventFilter(MouseEvent.MOUSE_PRESSED, event -> {
      if (event.getButton() == MouseButton.SECONDARY && event.getClickCount() == 1
          && event.getPickResult().getIntersectedNode() instanceof Canvas
          && event.getPickResult().getIntersectedNode().getParent() instanceof AnchorPane
          && event.getPickResult().getIntersectedNode().getParent()
          .getParent() instanceof CaesarMultiScreenItem) {
        CaesarMultiScreenItem multiScreenItem =
            (CaesarMultiScreenItem) event.getPickResult().getIntersectedNode().getParent()
                .getParent();
        if (multiScreenItem.getTarget().get() instanceof CaesarConTerminal) {
          ConsoleData conData =
              ((CaesarConTerminal) multiScreenItem.getTarget().get()).getConsoleData();
          if (conData.isMultiview()) {
            ContextMenu contextMenu = new ContextMenu();
            for (MultiviewLayoutType type : MultiviewLayoutType.values()) {
              MenuItem menuItem = getMenuItem(type, conData);
              contextMenu.getItems().add(menuItem);
            }
            contextMenu.show(multiScreenItem.getScene().getWindow(), event.getScreenX(),
                event.getScreenY());
          }
        }
        event.consume();
      }
    });
  }

  @NotNull
  private MenuItem getMenuItem(MultiviewLayoutType type, ConsoleData conData) {
    MenuItem menuItem = new MenuItem(type.getName());
    menuItem.setOnAction(
        new WeakEventHandler<>(actionEvent -> deviceController.execute(() -> {
          try {
            deviceController.getDataModel().setMultiviewLayout(conData.getId(), type);
            MultiviewData multiviewData = conData.getMultiviewData();
            Optional.ofNullable(multiviewData).ifPresent(data -> data.setLayoutType(type));
          } catch (DeviceConnectionException | BusyException exception) {
            log.error("Fail to set multiviewData LayoutType!", exception);
          }
        })));
    return menuItem;
  }

  @Override
  protected CaesarMultiScreenItem createScreenItem(
      int row, int column, ObjectProperty<VisualEditTerminal> target) {
    return new CaesarMultiScreenItem(row, column, target, model, func, this);
  }

  @Override
  protected CaesarMultiScreenData createFuncData() {
    return new CaesarMultiScreenData();
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    super.setDeviceController(deviceController);
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
      updateMultiviewMode();
      //注意并发问题
      this.deviceController.getDataModel().addPropertyChangeListener(updateData,
          weakAdapter.wrap((PropertyChangeListener) evt -> updateMultiviewMode()));
    } else {
      log.error("Error device controller type!", new Exception());
    }
  }

  /**
   * 更新Multiview模式.
   */
  public void updateMultiviewMode() {
    for (Node child : screenList.getChildren()) {
      if (child instanceof CaesarMultiScreenItem) {
        ((CaesarMultiScreenItem) child).updateMultiviewMode();
      }
    }
  }

  @Override
  public void connectScreen(VisualEditTerminal tx, VisualEditTerminal rx, String mode) {
    connectScreen(Collections.singletonList(new VisualEditConnection(tx, null, rx, null, mode)));
  }

  @Override
  public void connectScreen(Collection<VisualEditConnection> connections) {
    List<ConsoleData> rxs = new ArrayList<>();
    List<CpuData> txs = new ArrayList<>();
    List<String> modes = new ArrayList<>();
    // 获取数据
    for (VisualEditConnection conn : connections) {
      VisualEditTerminal rx = conn.getRxTerminal();
      VisualEditTerminal tx = conn.getTxTerminal();

      if (tx instanceof CaesarCpuTerminal && rx instanceof CaesarConTerminal) {
        CaesarCpuTerminal cpu = (CaesarCpuTerminal) tx;
        CaesarConTerminal con = (CaesarConTerminal) rx;
        CpuData cpuData = cpu.getCpuData();
        ConsoleData consoleData = con.getConsoleData();
        if (cpuData == null) {
          log.warn("Cpu data is null!");
          continue;
        }
        if (consoleData == null) {
          log.warn("Console data is null!");
          continue;
        }
        rxs.add(consoleData);
        txs.add(cpuData);
        modes.add(conn.getMode());
      } else if (tx == null && rx instanceof CaesarConTerminal) {
        CaesarConTerminal con = (CaesarConTerminal) rx;
        ConsoleData consoleData = con.getConsoleData();
        rxs.add(consoleData);
        txs.add(null);
        modes.add(conn.getMode());
      } else {
        log.warn("Error terminal child!");
      }
    }
    // 发送连接

    deviceController.execute(
        () -> {
          int size = txs.size();
          for (int i = 0; i < size; i++) {
            ConsoleData consoleData = rxs.get(i);
            CpuData cpuData = txs.get(i);
            String mode = modes.get(i);
            if (cpuData == null) {
              ExtendedSwitchUtility.asyncDisconnectWithoutReload(
                  deviceController.getDataModel(),
                  consoleData,
                  new DisconnectCpuDataStateWrapper());
            } else {
              SwitchType switchType = SwitchType.valueOf(mode);
              switch (switchType) {
                case FULL:
                case PRIVATE:
                case FULL_SHARED:
                  ExtendedSwitchUtility.asyncFullAccessWithoutReload(
                      deviceController.getDataModel(),
                      consoleData,
                      new CpuDataStateWrapper(cpuData, Access.FULL),
                      true);
                  break;
                case VIDEO:
                case SHARED:
                  ExtendedSwitchUtility.asyncVideoAccessWidthoutReload(
                      deviceController.getDataModel(),
                      consoleData,
                      new CpuDataStateWrapper(cpuData, CpuDataStateWrapper.Access.FULL));
                  break;
                default:
                  break;
              }
            }
          }
        });
  }

  /**
   * 获取Multiview通道列表.
   */
  @Override
  public Collection<TypeWrapper> getMultiviewChannel(VisualEditTerminal rx) {
    return TerminalUtility.getMultiviewChannel(rx);
  }

  @Override
  public void connectMultiview(VisualEditTerminal tx, VisualEditTerminal rx, int mode,
                               int channel) {
    if (tx instanceof CaesarCpuTerminal && rx instanceof CaesarConTerminal) {
      CaesarCpuTerminal cpu = (CaesarCpuTerminal) tx;
      CaesarConTerminal con = (CaesarConTerminal) rx;
      CpuData cpuData = cpu.getCpuData();
      ConsoleData consoleData = con.getConsoleData();
      if (cpuData == null) {
        log.warn("Cpu data is null!");
        return;
      }
      if (consoleData == null) {
        log.warn("Console data is null!");
        return;
      }
      deviceController.execute(() -> {
        try {
          deviceController.getDataModel().switchMultiviewTx(cpuData, consoleData, channel, mode);
          //切换成功后,更新数据
          deviceController.getDataModel().reloadMultiviewData();
        } catch (ConfigException | BusyException | DeviceConnectionException ex) {
          log.error("switchMultiviewTx failed", ex);
        }
      });
    } else if (tx == null && rx instanceof CaesarConTerminal) {
      CaesarConTerminal con = (CaesarConTerminal) rx;
      ConsoleData consoleData = con.getConsoleData();
      //断开连接
      deviceController.execute(() -> {
        try {
          deviceController.getDataModel().switchMultiviewTx(null, consoleData, channel, mode);
          //切换成功后,更新数据
          deviceController.getDataModel().reloadMultiviewData();
        } catch (ConfigException | BusyException | DeviceConnectionException ex) {
          log.error("switchMultiviewTx failed", ex);
        }
      });
    } else {
      log.warn("Error terminal child!");
    }
  }
}
