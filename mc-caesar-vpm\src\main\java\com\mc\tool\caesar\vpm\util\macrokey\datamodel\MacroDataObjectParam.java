package com.mc.tool.caesar.vpm.util.macrokey.datamodel;

import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.api.interfaces.Oidable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import lombok.Data;

/**
 * .
 */
@Data
public class MacroDataObjectParam implements MacroParam {
  private DataObject dataObject;
  private List<MacroParam> children = new ArrayList<>();

  public MacroDataObjectParam(DataObject dataObject) {
    this.dataObject = dataObject;
  }

  @Override
  public String getFullName() {
    if (dataObject == null) {
      return "";
    } else if (dataObject instanceof ConstantParam) {
      return dataObject.getName();
    } else {
      return String.format("%05d   %s", dataObject.getId(), dataObject.getName());
    }
  }

  @Override
  public Collection<MacroParam> getChildren() {
    return children;
  }

  @Override
  public void addChild(MacroParam param) {
    children.add(param);
  }

  @Override
  public void removeChild(MacroParam param) {
    children.remove(param);
  }

  @Override
  public boolean hasOid() {
    return dataObject instanceof Oidable;
  }

  @Override
  public int getOid() {
    if (dataObject instanceof Oidable) {
      return ((Oidable) dataObject).getOid();
    } else {
      return -1;
    }
  }
}
