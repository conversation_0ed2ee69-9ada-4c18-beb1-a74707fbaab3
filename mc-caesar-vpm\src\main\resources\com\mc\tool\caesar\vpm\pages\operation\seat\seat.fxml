<?xml version="1.0" encoding="UTF-8"?>

<?import com.mc.common.control.gridview.GridViewEx?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<fx:root type="javafx.scene.layout.VBox" xmlns="http://javafx.com/javafx/8"
  xmlns:fx="http://javafx.com/fxml/1" stylesheets="@seat.css">
  <Region prefHeight="40" minHeight="40"/>
  <VBox VBox.vgrow="ALWAYS" id="screen-list-container">
    <GridViewEx id="screen-list" fx:id="screenList" VBox.vgrow="ALWAYS"/>
  </VBox>
  <Region prefHeight="40" minHeight="40"/>
</fx:root>