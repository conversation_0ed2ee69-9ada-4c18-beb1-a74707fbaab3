package com.mc.tool.caesar.vpm.gui.pages.systemedit;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.framework.utility.dialogues.FileDialogue;
import com.mc.tool.framework.utility.dialogues.FileDialogueFactory;
import com.mc.tool.framework.view.FrameView;
import java.io.File;
import java.io.IOException;
import javafx.stage.Stage;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * .
 */
@RunWith(MockitoJUnitRunner.class)
public class ExportGraphTest extends AppGuiTestBase {

  @Mock private FileDialogueFactory fileDialogueFactory;

  @Mock private FileDialogue fileDialogue;

  @Override
  public void beforeAll() {}

  @Override
  public void start(Stage stage) throws Exception {
    FrameView.setModule(
        binder -> binder.bind(FileDialogueFactory.class).toInstance(fileDialogueFactory));
    when(fileDialogueFactory.createFileDialogue()).thenReturn(fileDialogue);
    super.start(stage);
  }

  @Test
  public void testLargeExport() {
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      clickOn(GuiTestConstants.MENU_FILE);
      clickOn(GuiTestConstants.MENU_EXPORT);

      File imageFile = File.createTempFile("test", ".png");
      when(fileDialogue.showSaveDialog(any())).thenReturn(imageFile);
      clickOn(GuiTestConstants.MENU_EXPORT_GRAPH);
      Assert.assertTrue(imageFile.length() > 1024); // 至少1k
      imageFile.delete();
      File tempFile = loadResourceStatus("com/mc/tool/caesar/vpm/816_hw1.2_full.status");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }
}
