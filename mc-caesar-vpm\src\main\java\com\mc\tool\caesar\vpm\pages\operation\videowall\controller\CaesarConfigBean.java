package com.mc.tool.caesar.vpm.pages.operation.videowall.controller;

import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoWallData;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.LongProperty;
import javafx.beans.property.ObjectProperty;
import javafx.scene.paint.Color;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * .
 */
public class CaesarConfigBean {
  private final IntegerProperty rows;
  private final IntegerProperty columns;
  private final IntegerProperty width;
  private final IntegerProperty height;

  private final IntegerProperty osdLeft;
  private final IntegerProperty osdTop;
  private final IntegerProperty osdWidth;
  private final IntegerProperty osdHeight;
  private final DoubleProperty osdAlpha;
  private final ObjectProperty<Color> osdColor;
  private final ObjectProperty<Color> bgColor;

  private BooleanProperty enable;
  private IntegerProperty horzSync;
  private IntegerProperty horzBackPorch;
  private IntegerProperty horzFrontPorch;
  private BooleanProperty horzPolarity;
  private IntegerProperty vertSync;
  private IntegerProperty vertBackPorch;
  private IntegerProperty vertFrontPorch;
  private BooleanProperty vertPolarity;
  private LongProperty clock;

  /**
   * Constructor.
   *
   * @param videoWallData video wall data.
   */
  public CaesarConfigBean(CaesarVideoWallData videoWallData) {
    rows = videoWallData.getLayoutData().getRowsProperty();
    columns = videoWallData.getLayoutData().getColumnsProperty();
    width = videoWallData.getLayoutData().getResWidth();
    height = videoWallData.getLayoutData().getResHeight();

    osdLeft = videoWallData.getOsdData().osdLeft;
    osdTop = videoWallData.getOsdData().osdTop;
    osdWidth = videoWallData.getOsdData().osdWidth;
    osdHeight = videoWallData.getOsdData().osdHeight;
    osdAlpha = videoWallData.getOsdData().osdAlpha;
    osdColor = videoWallData.getOsdData().osdColor;
    bgColor = videoWallData.getOsdData().bgColor;

    enable = videoWallData.getOutputData().enable;
    horzSync = videoWallData.getOutputData().horzSync;
    horzBackPorch = videoWallData.getOutputData().horzBackPorch;
    horzFrontPorch = videoWallData.getOutputData().horzFrontPorch;
    horzPolarity = videoWallData.getOutputData().horzPolarity;
    vertSync = videoWallData.getOutputData().vertSync;
    vertBackPorch = videoWallData.getOutputData().vertBackPorch;
    vertFrontPorch = videoWallData.getOutputData().vertFrontPorch;
    vertPolarity = videoWallData.getOutputData().vertPolarity;
    clock = videoWallData.getOutputData().clock;
  }

  @Min(1)
  @Max(10)
  public int getRows() {
    return rows.get();
  }

  @Min(1)
  @Max(50)
  public int getColumns() {
    return columns.get();
  }

  @Min(1)
  @Max(5000)
  public int getWidth() {
    return width.get();
  }

  @Min(1)
  @Max(3000)
  public int getHeight() {
    return height.get();
  }

  public int getOsdLeft() {
    return osdLeft.get();
  }

  public int getOsdTop() {
    return osdTop.get();
  }

  public int getOsdWidth() {
    return osdWidth.get();
  }

  public int getOsdHeight() {
    return osdHeight.get();
  }

  public double getOsdAlpha() {
    return osdAlpha.get();
  }

  public Color getOsdColor() {
    return osdColor.get();
  }

  public Color getBgColor() {
    return bgColor.get();
  }

  public boolean isEnable() {
    return enable.get();
  }

  public int getHorzSync() {
    return horzSync.get();
  }

  public int getHorzBackPorch() {
    return horzBackPorch.get();
  }

  public int getHorzFrontPorch() {
    return horzFrontPorch.get();
  }

  public boolean isHorzPolarity() {
    return horzPolarity.get();
  }

  public int getVertSync() {
    return vertSync.get();
  }

  public int getVertBackPorch() {
    return vertBackPorch.get();
  }

  public int getVertFrontPorch() {
    return vertFrontPorch.get();
  }

  public boolean isVertPolarity() {
    return vertPolarity.get();
  }

  public long getClock() {
    return clock.get();
  }
}
