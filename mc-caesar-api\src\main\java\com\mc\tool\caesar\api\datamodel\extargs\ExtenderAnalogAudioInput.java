package com.mc.tool.caesar.api.datamodel.extargs;

/**
 * .
 */
public enum ExtenderAnalogAudioInput implements ExtArgObject {
  LINE_IN(0), MIC_IN(1);

  private final int value;

  ExtenderAnalogAudioInput(int value) {
    this.value = value;
  }

  public int getValue() {
    return value;
  }

  @Override
  public byte[] toBytes() {
    return new byte[]{(byte) getValue()};
  }

  @Override
  public String toString() {
    String result = super.toString();
    result = result.replace("_", " ");
    return result;
  }
}
