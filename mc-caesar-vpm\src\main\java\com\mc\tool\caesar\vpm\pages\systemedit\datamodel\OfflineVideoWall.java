package com.mc.tool.caesar.vpm.pages.systemedit.datamodel;

import javafx.beans.property.BooleanProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;

/**
 * OfflineVideoWall.
 */
public class OfflineVideoWall {
  private final BooleanProperty selected = new SimpleBooleanProperty(false);
  private final IntegerProperty index = new SimpleIntegerProperty();
  private final StringProperty name = new SimpleStringProperty();

  public boolean isSelected() {
    return selected.get();
  }

  public BooleanProperty selectedProperty() {
    return selected;
  }

  public void setSelected(boolean selected) {
    this.selected.set(selected);
  }

  public String getName() {
    return name.get();
  }

  public StringProperty nameProperty() {
    return name;
  }

  public void setName(String name) {
    this.name.set(name);
  }

  public int getIndex() {
    return index.get();
  }

  public IntegerProperty indexProperty() {
    return index;
  }

  public void setIndex(int index) {
    this.index.set(index);
  }
}
