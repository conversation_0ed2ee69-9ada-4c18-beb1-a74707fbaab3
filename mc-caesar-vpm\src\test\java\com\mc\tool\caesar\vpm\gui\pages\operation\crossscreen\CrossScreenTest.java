package com.mc.tool.caesar.vpm.gui.pages.operation.crossscreen;

import com.mc.common.util.RunUtil;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.caesar.vpm.pages.operation.CaesarOperationPageNew;
import com.mc.tool.caesar.vpm.pages.systemedit.CaesarSystemEditPage;
import com.mc.tool.caesar.vpm.pages.systemedit.CaesarSystemEditPageView;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarTerminalBase;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbTxTerminal;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.operation.view.VideoSourceTree;
import com.mc.tool.framework.systemedit.datamodel.VisualEditConnection;
import com.mc.tool.framework.utility.InjectorProvider;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import javafx.scene.Node;
import javafx.scene.control.Button;
import javafx.scene.control.Labeled;
import javafx.scene.control.TabPane;
import javafx.scene.layout.GridPane;
import org.junit.Assert;
import org.junit.Test;

/**
 * .
 */
public class CrossScreenTest extends AppGuiTestBase {

  @Override
  public void beforeAll() {}

  @Test
  public void testCreate() {
    try {
      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }

      ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
      CaesarEntity entity = (CaesarEntity) app.getEntityMananger().getAllEntity().iterator().next();

      CaesarSystemEditPageView view =
          (CaesarSystemEditPageView) entity.getPageByName(CaesarSystemEditPage.NAME).getView();

      RunUtil.waitForUiDone(
          () -> {
            selectTerminal(
                view.getControllable().getGraph().getSelectionModel(),
                CaesarConTerminal.class,
                GuiTestConstants.CON_00,
                true);
            selectTerminal(
                view.getControllable().getGraph().getSelectionModel(),
                CaesarConTerminal.class,
                GuiTestConstants.CON_01,
                true);
          });
      rightClickOn(findTerminalNode(CaesarConTerminal.class, GuiTestConstants.CON_01));

      clickOn(GuiTestConstants.MENU_CREATE_CROSS_SCREEN);

      Button confirmButton =
          targetWindow(GuiTestConstants.DIALOG_SET_NAME)
              .lookup(GuiTestConstants.BUTTON_CONFIRM)
              .query();
      Assert.assertTrue(confirmButton.isDisabled());

      targetWindow(GuiTestConstants.DIALOG_SET_NAME)
          .clickOn(GuiTestConstants.CROSS_SCREEN_NAME_INPUT);
      targetWindow(GuiTestConstants.DIALOG_SET_NAME).write("test");
      targetWindow(GuiTestConstants.DIALOG_SET_NAME)
          .clickOn(GuiTestConstants.CROSS_SCREEN_ROW_INPUT);
      targetWindow(GuiTestConstants.DIALOG_SET_NAME).write("1");
      targetWindow(GuiTestConstants.DIALOG_SET_NAME)
          .clickOn(GuiTestConstants.CROSS_SCREEN_COLUMN_INPUT);
      targetWindow(GuiTestConstants.DIALOG_SET_NAME).write("1");
      // 范围太小，应该为disable
      Assert.assertTrue(confirmButton.isDisabled());
      // 1 * 2
      targetWindow(GuiTestConstants.DIALOG_SET_NAME)
          .clickOn(GuiTestConstants.CROSS_SCREEN_ROW_INPUT);
      targetWindow(GuiTestConstants.DIALOG_SET_NAME).eraseText(5);
      targetWindow(GuiTestConstants.DIALOG_SET_NAME).write("2");
      Assert.assertFalse(confirmButton.isDisabled());
      // 确定
      targetWindow(GuiTestConstants.DIALOG_SET_NAME).clickOn(GuiTestConstants.BUTTON_CONFIRM);
      targetWindow(GuiTestConstants.DIALOG_SWITCH).clickOn(GuiTestConstants.BUTTON_YES);
      // 检查是否有切换页面
      TabPane pane = lookup(GuiTestConstants.TAB_PANE_PAGES).query();
      Assert.assertEquals(
          CaesarOperationPageNew.NAME, pane.getSelectionModel().getSelectedItem().getId());
      // 检查内容
      Thread.sleep(1000);
      GridPane screenList = lookup(GuiTestConstants.CROSS_SCREEN_SCREEN_LIST).query();
      Assert.assertEquals(2 + 1, screenList.getChildren().size()); // + 1是因为有grid line

      Node con0 = screenList.getChildren().get(1);
      Labeled label0 = (Labeled) con0.lookup(GuiTestConstants.CROSS_SCREEN_SCREEN_TEXT);
      Assert.assertEquals(GuiTestConstants.CON_00, label0.getText());

      Node con1 = screenList.getChildren().get(2);
      Labeled label1 = (Labeled) con1.lookup(GuiTestConstants.CROSS_SCREEN_SCREEN_TEXT);
      Assert.assertEquals(GuiTestConstants.CON_01, label1.getText());

      // 连接
      Node cpu00Node = findTerminalNode(CaesarCpuTerminal.class, GuiTestConstants.CPU_00);
      CaesarCpuTerminal tx =
          (CaesarCpuTerminal) ((CellSkin) cpu00Node.getUserData()).getCell().getBindedObject();
      Node con00Node = findTerminalNode(CaesarConTerminal.class, GuiTestConstants.CON_00);
      CaesarConTerminal rx =
          (CaesarConTerminal) ((CellSkin) con00Node.getUserData()).getCell().getBindedObject();

      VisualEditConnection conn =
          new VisualEditConnection(
              tx,
              ConnectorIdentifier.getIdentifier(CaesarTerminalBase.CAESAR_NORMAL_PORT),
              rx,
              ConnectorIdentifier.getIdentifier(CaesarTerminalBase.CAESAR_NORMAL_PORT),
              "FULL");

      RunUtil.waitForUiDone(
          () -> {
            entity.getVisualEditModel().addConnection(conn);
          });

      Assert.assertEquals(
          GuiTestConstants.CON_00 + "[" + GuiTestConstants.CPU_00 + "]", label0.getText());
    } catch (IOException | InterruptedException e) {
      e.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testUsbSource() {
    // USB TX不应该出现在信号源列表
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }
      // 创建USB
      createUsbImpl(2, "33", 1);
      // 创建跨屏
      createCrossScreenImpl(
          Arrays.asList(GuiTestConstants.CON_00, GuiTestConstants.CON_01), 2, 2, "test", true);

      Node tree = lookup(GuiTestConstants.VIDEO_SOURCE_TREE).query();
      if (tree instanceof VideoSourceTree) {
        Assert.assertFalse(
            traverseTree(
                ((VideoSourceTree) tree).getRoot(), (obj) -> obj instanceof CaesarUsbTxTerminal));
      } else {
        Assert.fail();
      }

      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }
}
