package com.mc.tool.caesar.api.datamodel;

import com.mc.tool.caesar.api.datamodel.vp.LayoutInfoGetter;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import lombok.Data;
import org.junit.Assert;
import org.junit.Test;

/**
 * .
 */
public class ScenarioDataTest {

  @Data
  class LayoutForTest implements LayoutInfoGetter {
    int horzCount;
    int vertCount;
    int resolutionHeight;
    int resolutionWidth;
    Collection<Integer> resolutionWidths = new ArrayList<>();
    Collection<Integer> resolutionHeights = new ArrayList<>();
    boolean multipleResolutionEnable;
    Collection<Integer> horzMargins = new ArrayList<>();
    Collection<Integer> vertMargins = new ArrayList<>();
    int leftCompensation;
    int rightCompensation;
    int topCompensation;
    int bottomCompensation;
    int compensationScaleThreshold;
  }

  @Test
  public void testDumpAndLoadFullScenarioSimple() throws ConfigException, IOException {
    ScenarioData scenarioData = new ScenarioData();
    scenarioData.setIndex(0);
    scenarioData.setConnections(new HashMap<>());
    scenarioData.setConfigs(new HashMap<>());
    // 模拟布局数据
    LayoutForTest layout = new LayoutForTest();
    layout.horzCount = VideoWallGroupData.VideoWallData.MAX_WIDTH_COUNT;
    layout.vertCount = VideoWallGroupData.VideoWallData.MAX_CON_COUNT / layout.horzCount;
    layout.resolutionWidth = 1920;
    layout.resolutionHeight = 1080;
    layout.multipleResolutionEnable = true;
    for (int i = 0; i < layout.horzCount; i++) {
      layout.resolutionWidths.add(1920 + i);
    }
    for (int i = 0; i < layout.vertCount; i++) {
      layout.resolutionHeights.add(1080 + i);
    }
    for (int i = 0; i < layout.horzCount - 1; i++) {
      layout.horzMargins.add(i);
    }
    for (int i = 0; i < layout.vertCount - 1; i++) {
      layout.vertMargins.add(layout.vertCount - i);
    }
    VideoWallGroupData.VideoWallData videoWallData = new VideoWallGroupData.VideoWallData();
    videoWallData.setLayout(layout);
    // 模拟屏幕数据
    for (int i = 0; i < videoWallData.getColumns() * videoWallData.getRows(); i++) {
      videoWallData.getCompleteScreenData(i).setOid((short) i);
    }
    // 模拟窗口数据
    for (int i = 0; i < VideoWallGroupData.VideoWallData.MAX_VIDEO_COUNT; i++) {
      videoWallData.getCompleteVideoData(i).setLeft(i + 1920);
      videoWallData.getCompleteVideoData(i).setTop(i + 1080);
    }
    videoWallData.setVideoCnt(VideoWallGroupData.VideoWallData.MAX_VIDEO_COUNT);
    scenarioData.setVideoWallData(videoWallData);

    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    scenarioData.writeData(new CfgWriter(baos));

    ScenarioData newScenario = new ScenarioData();
    newScenario.readData(new CfgReader(new ByteArrayInputStream(baos.toByteArray())), null);

    ByteArrayOutputStream oldVideoWallStream = new ByteArrayOutputStream();
    ByteArrayOutputStream newVideoWallStream = new ByteArrayOutputStream();
    scenarioData.getVideoWallData().write(oldVideoWallStream);
    newScenario.getVideoWallData().write(newVideoWallStream);
    Assert.assertArrayEquals(oldVideoWallStream.toByteArray(), newVideoWallStream.toByteArray());
  }
}
