package com.mc.tool.caesar.vpm.pages.permissionconfiguration;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.AccessControlObject;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.utils.AdvancedBitSet;
import com.mc.tool.caesar.vpm.pages.hotkeyconfiguration.CopyDialog;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.cpuright.CpuRightBaseView;
import com.mc.tool.caesar.vpm.util.cpuright.TempAccessControlObject;
import com.mc.tool.framework.interfaces.DeviceControllable;
import java.beans.PropertyChangeListener;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.ResourceBundle;
import javafx.beans.property.SimpleObjectProperty;
import javafx.fxml.FXML;
import javafx.scene.control.Button;
import javafx.scene.control.CheckBox;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.control.TableColumn;
import javafx.scene.layout.HBox;
import javafx.stage.Modality;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class UserCpuRightView extends CpuRightBaseView<UserData> {
  @FXML protected HBox btnBox;
  protected Button copyBtn;

  private final String[] changeStatus = {UserData.PROPERTY_STATUS};

  /** Constructor. */
  public UserCpuRightView() {
    ResourceBundle bundle = ResourceBundle.getBundle("com.mc.tool.caesar.vpm.i18n.common");
    sourceListTitle.set(bundle.getString("cpu_right.user_list"));
    accessBlockTitle.set(bundle.getString("cpu_right.user_cpu_right_management"));

    selectedItem.addListener(
        weakAdapter.wrap(
            (observable, oldValue, newValue) -> {
              if (newValue == null) {
                copyBtn.setDisable(true);
              } else if (newValue instanceof TempAccessControlObject) {
                copyBtn.setDisable(false);
              }
            }));
  }

  private void onCopy() {
    List<UserData> userList =
        new ArrayList<>(deviceController.getDataModel().getConfigDataManager().getActiveUsers());
    userList.remove(getTableSelectionModel().getSelectedItem());
    CopyDialog<UserData> copyDialog = new CopyDialog<>();
    copyDialog.setTitle(
        CaesarI18nCommonResource.getString("copy_cpu_right_dialog.user_cpu_right.title"));
    ((Label) copyDialog.getCopySelectionView().getSourceHeader())
        .setText(
            CaesarI18nCommonResource.getString(
                "copy_cpu_right_dialog.user_cpu_right.source_header.title"));
    ((Label) copyDialog.getCopySelectionView().getTargetHeader())
        .setText(
            CaesarI18nCommonResource.getString(
                "copy_cpu_right_dialog.user_cpu_right.target_header.title"));
    copyDialog.getCopySelectionView().setCellFactory(col -> new SelectionViewCell());
    copyDialog.getCopySelectionView().getSourceItems().setAll(userList);
    if (sourceList != null && sourceList.getScene() != null) {
      copyDialog.initOwner(sourceList.getScene().getWindow());
    }
    copyDialog.initModality(Modality.APPLICATION_MODAL);
    copyDialog.showAndWait();
    Collection<UserData> result = copyDialog.getResult();
    if (result != null) {
      AdvancedBitSet videoAccess =
          new AdvancedBitSet(getTableSelectionModel().getSelectedItem().getVideoAccessBitSet());
      AdvancedBitSet noAccess =
          new AdvancedBitSet(getTableSelectionModel().getSelectedItem().getNoAccessBitSet());
      AdvancedBitSet usbNoAccess =
          new AdvancedBitSet(getTableSelectionModel().getSelectedItem().getUsbNoAccessBitSet());

      updatingProperty.set(true);
      deviceController
          .submitAsync(
              () -> {
                List<UserData> sendData = new ArrayList<>();
                for (UserData userData : result) {
                  userData.setVideoAccessBits(videoAccess.getBits());
                  userData.setNoAccessBits(noAccess.getBits());
                  userData.setUsbNoAccessBits(usbNoAccess.getBits());
                  sendData.add(userData);
                }
                try {
                  deviceController.getDataModel().sendUserData(sendData);
                } catch (DeviceConnectionException | BusyException exception) {
                  log.warn("Fail to send user data!", exception);
                }
              })
          .whenComplete(
              (item, throwable) -> {
                if (throwable != null) {
                  log.warn("Fail to send user data!", throwable);
                }
                PlatformUtility.runInFxThread(() -> updatingProperty.set(false));
              });
    }
  }

  private static class SelectionViewCell extends ListCell<UserData> {

    @Override
    protected void updateItem(UserData item, boolean empty) {
      super.updateItem(item, empty);
      if (empty || item == null) {
        setText("");
      } else {
        setText(item.getName());
      }
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    super.initialize(location, resources);
    copyBtn = new Button(
        CaesarI18nCommonResource.getString("copy_cpu_right_dialog.user_cpu_right.title"));
    copyBtn.setDisable(true);
    copyBtn.getStyleClass().add("common-button");
    copyBtn.setOnAction(event -> onCopy());
    btnBox.getChildren().add(0, copyBtn);
    sourceIdColumn.setCellValueFactory(feat -> feat.getValue().getIdProperty());
    sourceNameColumn.setCellValueFactory(feat -> feat.getValue().getNameProperty());
    //usb2.0透传
    TableColumn<CpuData, CheckBox> fullUsbAccessColumn = new TableColumn<>();
    fullUsbAccessColumn.setText("USB2.0透传");
    fullUsbAccessColumn.setCellValueFactory(param -> {
      CpuData cpuData = param.getValue();
      CheckBox checkBox = new CheckBox("启用");
      if (selectedItem.get() != null) {
        checkBox.setSelected(!selectedItem.get().isUsbNoAccess(cpuData));
      } else {
        checkBox.setSelected(false);
      }
      checkBox.selectedProperty().addListener((observable, oldValue, newValue) -> {
        if (selectedItem.get() != null) {
          selectedItem.get().setUsbNoAccess(cpuData, !newValue);
        }
      });
      checkBox.setDisable(!this.deviceController.isLoginUserAdmin());
      return new SimpleObjectProperty<>(checkBox);
    });
    fullTable.getColumns().add(fullUsbAccessColumn);
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    super.setDeviceController(deviceController);

    if (this.deviceController != null) {
      sourceRawList.setAll(
          this.deviceController.getDataModel().getConfigDataManager().getActiveUsers());

      CaesarSwitchDataModel model = this.deviceController.getDataModel();
      model.addPropertyChangeListener(
          changeStatus,
          weakAdapter.wrap(
              (PropertyChangeListener)
                  evt ->
                      PlatformUtility.runInFxThread(
                          () ->
                              sourceRawList.setAll(
                                  model.getConfigDataManager().getActiveUsers()))));
    }
  }

  @Override
  protected void onSendNewAccess(AccessControlObject item) {
    if (item instanceof UserData) {
      UserData data = (UserData) item;
      try {
        deviceController.getDataModel().sendUserData(Collections.singletonList(data));
      } catch (DeviceConnectionException | BusyException exception) {
        log.warn("Fail to send user data!", exception);
      }
    }
  }

  @Override
  public void updateEditableStatus() {
    if (deviceController == null) {
      return;
    }
    editableProperty.set(deviceController.getCaesarUserRight().isUserRightEditable());
  }
}
