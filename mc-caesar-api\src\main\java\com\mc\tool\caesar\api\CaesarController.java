package com.mc.tool.caesar.api;

import com.mc.common.util.RunUtil;
import com.mc.tool.caesar.api.CommunicationBuilder.Add;
import com.mc.tool.caesar.api.communication.ConnectionListener;
import com.mc.tool.caesar.api.communication.IoController;
import com.mc.tool.caesar.api.datamodel.BranchData;
import com.mc.tool.caesar.api.datamodel.ConfigData;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.ExtenderInfo;
import com.mc.tool.caesar.api.datamodel.ExtenderNetworkInfo;
import com.mc.tool.caesar.api.datamodel.ExtenderStatusInfo;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.api.datamodel.LoginResponse;
import com.mc.tool.caesar.api.datamodel.MatrixData;
import com.mc.tool.caesar.api.datamodel.MatrixStatus;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.datamodel.MultiviewData;
import com.mc.tool.caesar.api.datamodel.MultiviewLayoutType;
import com.mc.tool.caesar.api.datamodel.MultiviewOutputMode;
import com.mc.tool.caesar.api.datamodel.OpticalModuleInfo;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.datamodel.ResolutionData;
import com.mc.tool.caesar.api.datamodel.ScenarioDescribeData;
import com.mc.tool.caesar.api.datamodel.SourceCropData;
import com.mc.tool.caesar.api.datamodel.SystemTimeData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.datamodel.UserGroupData;
import com.mc.tool.caesar.api.datamodel.VersionDef;
import com.mc.tool.caesar.api.datamodel.VersionSet;
import com.mc.tool.caesar.api.datamodel.vp.Vp6OutputData;
import com.mc.tool.caesar.api.datamodel.vp.VpConConfigData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData.VpResolution;
import com.mc.tool.caesar.api.datamodel.vp.VpType;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.exception.UnexpectedResponseException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.interfaces.CommitRollback;
import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.api.interfaces.Oidable;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.CfgError;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CollectionUtil;
import com.mc.tool.caesar.api.utils.Utilities;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.swing.SwingUtilities;

/**
 * 所有的设备控制的命令的实现.
 */
public class CaesarController extends BasicTeraController {

  private static final Logger LOG = Logger.getLogger(CaesarController.class.getName());
  private static final Threshold CONTROLLER_THRESHOLD = Threshold.CONTROLLER;
  private final DateTimeFormatter df =
      DateTimeFormatter.ofPattern(CaesarConstants.DATE_TIME_FORMAT_yyyy_MM_dd_HH_mm_ss);
  protected final ConfigDataManager configDataManager;
  protected final CaesarSwitchDataModel switchDataModel;
  protected long identifier;

  /**
   * .
   */
  public CaesarController(CaesarSwitchDataModel switchDataModel) {
    if (switchDataModel == null) {
      this.switchDataModel = null;
      this.configDataManager = null;
    } else {
      this.configDataManager = switchDataModel.getConfigDataManager();
      this.switchDataModel = switchDataModel;
    }
  }

  @Override
  public void setIdentifier(long identifier) {
    if (switchDataModel != null) {
      throw new UnsupportedOperationException("not supported");
    } else {
      this.identifier = identifier;
    }
  }

  @Override
  protected long getIdentifier() {
    if (switchDataModel != null) {
      return this.switchDataModel.getIdentifier();
    } else {
      return identifier;
    }
  }

  public void addConnectionListener(ConnectionListener listener) throws DeviceConnectionException {
    getIoController().addConnectionListener(getIdentifier(), listener);
  }

  public void removeConnectionListener(ConnectionListener listener)
      throws DeviceConnectionException {
    getIoController().removeConnectionListener(getIdentifier(), listener);
  }

  /**
   * .
   */
  public void setSave() throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setSave");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.SYSTEM_SET_SYSCMD) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add4Byte(CaesarControllerConstants.BIN_CMD_SAVE)
            .add4Byte(0).add4Byte(0);
      }
    }.work();
  }

  /**
   * .
   */
  public void setShutdown(final int value)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setShutdown");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.SYSTEM_SET_SYSCMD) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add4Byte(CaesarControllerConstants.BIN_CMD_SHUTDOWN)
            .add4Byte(255).add4Byte(value);
      }
    }.work();
  }

  /**
   * .
   */
  public void setCpuVirtualOut(int level1, int level2)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setCpuVirtualOut");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.SYSTEM_SET_SYSCMD) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add4Byte(CaesarControllerConstants.BIN_CMD_CPU_VIRTUAL_OUT)
            .add4Byte(level1).add4Byte(level2);
      }
    }.work();
  }

  /**
   * .
   */
  public void setCpuVirtualIn(int level1, int level2)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setCpuVirtualIn");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.SYSTEM_SET_SYSCMD) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add4Byte(CaesarControllerConstants.BIN_CMD_CPU_VIRTUAL_IN)
            .add4Byte(level1).add4Byte(level2);
      }
    }.work();
  }

  /**
   * .
   */
  public void setFactoryReset(final int slot)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setFactoryReset");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.SYSTEM_SET_SYSCMD) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add4Byte(CaesarControllerConstants.BIN_CMD_RESET)
            .add4Byte(slot).add4Byte(0);
      }
    }.work();
  }

  /**
   * .
   */
  public void setRestart(final int module)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setRestart");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.SYSTEM_SET_SYSCMD) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add4Byte(CaesarControllerConstants.BIN_CMD_RESTART)
            .add4Byte(module).add4Byte(0);
      }
    }.work();
  }

  /**
   * .
   */
  public void setServiceMode(final int id, final boolean serviceMode)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setServiceMode");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.SYSTEM_SET_SYSCMD) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add4Byte(CaesarControllerConstants.BIN_CMD_SERVICE)
            .add4Byte(id).add4Byte(serviceMode ? 1 : 0);
      }
    }.work();
  }

  /**
   * .
   */
  public void setServiceMode(final int moduleId, final int portId, boolean serviceMode)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setServiceMode");
    if (serviceMode) {
      new Worker<Void>(this, CaesarControllerConstants.SystemRequest.SYSTEM_SET_SYSCMD) {
        @Override
        protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
          return builder
              .add4Byte(CaesarControllerConstants.BIN_CMD_SERVICE_MODE_ON)
              .add4Byte(moduleId).add4Byte(portId);
        }
      }.work();
    } else {
      new Worker<Void>(this, CaesarControllerConstants.SystemRequest.SYSTEM_SET_SYSCMD) {
        @Override
        protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
          return builder
              .add4Byte(CaesarControllerConstants.BIN_CMD_SERVICE_MODE_OFF)
              .add4Byte(moduleId).add4Byte(portId);
        }
      }.work();
    }
  }

  /**
   * .
   */
  public void setUpdateMode(final int id, final int type)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setUpdateMode");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.SYSTEM_SET_SYSCMD) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add4Byte(CaesarControllerConstants.BIN_CMD_UPDATE)
            .add4Byte(id).add4Byte(type);
      }
    }.work();
  }

  /**
   * 获取osd的MD5信息.
   *
   * @param level1 io序号
   * @param level2 端口序号
   * @return md5 获取到的MD5，如果数据解析失败，返回空字符串
   * @throws DeviceConnectionException 设备连接异常
   * @throws ConfigException           配置异常
   * @throws BusyException             设备忙
   */
  public String getOsdMd5(final byte level1, final byte level2)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getOsdMd5");
    return new Worker<String>(this, CaesarControllerConstants.NioPicRequest.GET_OSD_MD5) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        return paramAdd.add1Byte(level1).add1Byte(level2).add1Byte(0);
      }

      @Override
      protected String getResponse(CfgReader cfgReader) throws ConfigException {
        cfgReader.readByteValue();
        cfgReader.readByteValue();
        cfgReader.readByteValue();
        return new String(cfgReader.readByteArray(), StandardCharsets.UTF_8);
      }
    }.work();
  }

  /**
   * .
   */
  public void setEdidData(final byte level1, final byte level2, final byte type, byte[] edid)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setEdidData");
    new Worker<Void>(this, CaesarControllerConstants.NioPicRequest.SET_EDID_DATA) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add1Byte(level1).add1Byte(level2).add1Byte(type).add(edid);
      }
    }.work();
  }

  /**
   * .
   */
  public byte[] getEdidData(final byte level1, final byte level2, final byte type)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getEdidData");
    return new Worker<byte[]>(this, CaesarControllerConstants.NioPicRequest.GET_EDID_DATA) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add1Byte(level1).add1Byte(level2).add1Byte(type);
      }

      @Override
      protected byte[] getResponse(CfgReader cfgReader) throws ConfigException {
        cfgReader.readByteValue();
        cfgReader.readByteValue();
        cfgReader.readByteValue();
        return cfgReader.readByteArray();
      }
    }.work();
  }

  /**
   * .
   */
  public ExtenderStatusInfo getExtInfo(final byte level1, final byte level2)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getExtInfo");
    return new Worker<ExtenderStatusInfo>(this,
        CaesarControllerConstants.NioPicRequest.GET_EXTINFO) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add1Byte(level1).add1Byte(level2).add1Byte(1);
      }

      @Override
      protected ExtenderStatusInfo getResponse(CfgReader cfgReader) throws ConfigException {
        cfgReader.readByteValue();
        cfgReader.readByteValue();
        cfgReader.readByteValue();
        ExtenderStatusInfo info = new ExtenderStatusInfo();
        info.readData(cfgReader);
        return info;
      }
    }.work();
  }

  /**
   * 获取外设参数.
   *
   * @param level1 slot
   * @param level2 port
   * @param ids    参数id集合
   * @return id与参数值的map
   * @throws DeviceConnectionException 设备连接异常
   * @throws BusyException             设备忙
   * @throws ConfigException           数据有误
   */
  public Map<Integer, byte[]> getExtArgs(final byte level1, final byte level2,
                                         Collection<Integer> ids)
      throws DeviceConnectionException, BusyException, ConfigException {
    if (ids == null || ids.isEmpty()) {
      return new HashMap<>();
    }
    return new Worker<Map<Integer, byte[]>>(this, NioPicRequest.GET_EXT_ARGS) {

      @Override
      protected Add buildRequest(Add builder) {
        builder.add1Byte(level1).add1Byte(level2).add1Byte(0)
            .add1Byte(ids.size());
        for (Integer id : ids) {
          builder.add1Byte(id);
        }
        return builder;
      }

      @Override
      protected Map<Integer, byte[]> getResponse(CfgReader cfgReader) throws ConfigException {
        cfgReader.readByteValue();
        cfgReader.readByteValue();
        cfgReader.readByteValue();
        Map<Integer, byte[]> result = new HashMap<>();
        int count = cfgReader.readByteValue();
        for (int i = 0; i < count; i++) {
          int id = cfgReader.readByteValue();
          int len = cfgReader.readByteValue();
          byte[] value = cfgReader.readByteArray(len);
          result.put(id, value);
        }
        return result;
      }
    }.work();
  }

  /**
   * 设置外设参数.
   *
   * @param level1 slot
   * @param level2 port
   * @param args   参数id与参数值的map
   * @throws DeviceConnectionException 设备连接失败
   * @throws BusyException             设备忙
   * @throws ConfigException           配置数据错误
   */
  public void setExtArgs(final byte level1, final byte level2, Map<Integer, byte[]> args)
      throws DeviceConnectionException, BusyException, ConfigException {
    if (args == null || args.size() == 0) {
      return;
    }
    new Worker<Void>(this, NioPicRequest.SET_EXT_ARGS) {

      @Override
      protected Add buildRequest(Add builder) {
        builder.add1Byte(level1).add1Byte(level2).add1Byte(0).add1Byte(args.size());
        for (Map.Entry<Integer, byte[]> entry : args.entrySet()) {
          builder.add1Byte(entry.getKey());
          builder.add1Byte(entry.getValue().length);
          builder.add(entry.getValue());
        }
        return builder;
      }
    }.work();
  }

  /**
   * .
   */
  public LoginResponse login(final byte[] username, final byte[] password, VersionDef vpmVersion)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "login");
    return new Worker<LoginResponse>(this,
        CaesarControllerConstants.NioPicRequest.UI_USER_LOGIN_RESQUEST) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        final int passwordCount = 20;
        byte[] userNameBuffer;
        if (username.length == CaesarConstants.NAME_BYTE_LEN) {
          userNameBuffer = username;
        } else {
          userNameBuffer = new byte[CaesarConstants.NAME_BYTE_LEN];
          System.arraycopy(username, 0, userNameBuffer, 0,
              Math.min(CaesarConstants.NAME_BYTE_LEN, username.length));
        }
        byte[] passwordBuffer;
        if (password.length == passwordCount) {
          passwordBuffer = password;
        } else {
          passwordBuffer = new byte[passwordCount];
          System.arraycopy(password, 0, passwordBuffer, 0,
              Math.min(passwordCount, password.length));
        }
        return builder.add(userNameBuffer).add(passwordBuffer).add2Byte(vpmVersion.getMasterVersion())
            .add2Byte(vpmVersion.getSubVersion()).add1Byte(vpmVersion.getYear()).add1Byte(vpmVersion.getMonth())
            .add1Byte(vpmVersion.getDay());
      }

      @Override
      protected LoginResponse getResponse(CfgReader cfgReader) throws ConfigException {
        int rsp1 = cfgReader.readByteValue();
        int rsp2 = cfgReader.readByteValue();
        LoginResponse rsp = new LoginResponse(LoginResponse.LoginStatus.NO_RESPONSE);

        if (rsp1 == 0) {
          rsp = new LoginResponse(LoginResponse.LoginStatus.SUCCESS);
        } else if (rsp1 == 1 && rsp2 == 0) {
          rsp = new LoginResponse(LoginResponse.LoginStatus.INVALID_USERNAME);
        } else if (rsp1 == 1 && rsp2 == 1) {
          rsp = new LoginResponse(LoginResponse.LoginStatus.UNKNOWN_USERNAME_PASSWORD);
        } else if (rsp1 == 1 && rsp2 == 2) {
          rsp = new LoginResponse(LoginResponse.LoginStatus.NO_LOGIN_PERMISSION);
        } else if (rsp1 == 1 && rsp2 == 4) {
          VersionDef version = new VersionDef();
          version.readData(cfgReader);
          rsp = new LoginResponse(
              LoginResponse.LoginStatus.VERSION_MISMATCH, version);
        }
        if (cfgReader.available() > 0) {
          cfgReader.readByteArray(8184);
        }
        return rsp;
      }
    }.work();
  }

  /**
   * .
   */
  public byte[] getSerial(final byte level1, final byte level2)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getSerial");
    return new Worker<byte[]>(this, CaesarControllerConstants.NioPicRequest.GET_SERIAL) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add1Byte(level1).add1Byte(level2)
            .add1Byte(1);
      }

      @Override
      protected byte[] getResponse(CfgReader cfgReader) throws ConfigException {
        byte rspLevel1 = (byte) cfgReader.readByteValue();
        byte rspLevel2 = (byte) cfgReader.readByteValue();
        byte rspLevel3 = (byte) cfgReader.readByteValue();
        byte[] serial = cfgReader.readByteArray();
        if (cfgReader.available() > 0) {
          cfgReader.readByteArray(8184);
        }
        if (rspLevel1 == level1 && rspLevel2 == level2 && rspLevel3 == 1) {
          return serial;
        }
        CaesarController.LOG.log(Level.SEVERE,
            "Wrong Serial Level (expected: {0}_{1}_{2}, response: {3}_{4}_{5})",
            new Object[] {level1, level2, 1, rspLevel1, rspLevel2, rspLevel3});
        return new byte[0];
      }
    }.work();
  }

  /**
   * .
   */
  public void setVpconConfigData(final byte level1, final byte level2, VpConConfigData data)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setVpconConfigData " + level1 + "_" + level2);
    new Worker<Void>(this, CaesarControllerConstants.NioPicRequest.SET_VPCON_DATA) {

      @Override
      protected Add buildRequest(Add builder) throws IOException {
        builder.setChecksumEnabled(true);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        data.write(baos);
        return builder.add1Byte(level1).add1Byte(level2)
            .add1Byte(VPCON_CONFIG_LEVEL_3).add(baos.toByteArray());
      }
    }.work();
  }

  /**
   * 设置vpcon的输出数据.
   *
   * @param level1 slot号
   * @param level2 端口号
   * @param data   输出数据
   * @throws DeviceConnectionException 设备连接出错
   * @throws ConfigException           配置出错
   * @throws BusyException             设备正忙
   */
  public void setVpconOutputData(final byte level1, final byte level2, Vp6OutputData data)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setVpconOutputData " + level1 + "_" + level2);
    new Worker<Void>(this, CaesarControllerConstants.NioPicRequest.SET_VPCON_OUTPUT) {

      @Override
      protected Add buildRequest(Add builder) throws IOException {
        builder.setChecksumEnabled(true);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        data.write(baos);
        return builder.add1Byte(level1).add1Byte(level2).add1Byte(0)
            .add(baos.toByteArray());
      }
    }.work();
  }

  /**
   * 从vp6读取配置数据.
   *
   * @param level1 slot
   * @param level2 端口
   * @param type   类型
   * @return 配置数据
   */
  public VpConConfigData getVpconConfigDataReal(final byte level1, final byte level2, VpType type)
      throws IOException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getVpconConfigDataReal " + level1 + "_" + level2);
    VpConConfigData configData = type.createConfigData();
    int totalsize = configData.size();
    int readedSize = 0;
    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    if (type == VpType.VP6) {
      while (readedSize < totalsize) {
        int size = Math.min(totalsize - readedSize, 512);
        byte[] data = getExtVpconConfigData(level1, level2, readedSize, size);
        if (data.length != size) {
          throw new ConfigException(-1,
              String.format(
                  "Unexpected size in get vpconconfigdata real. Offset: %s, Expected: %d. Actual: %d",
                  readedSize, size, data.length));
        }
        baos.write(data);
        readedSize += size;
      }
    } else if (type == VpType.VP7) {
      byte[] data = getExtVpconConfigData(level1, level2, 0, totalsize);
      if (data.length != totalsize) {
        throw new ConfigException(-1,
            String.format(
                "Unexpected size in get vpconconfigdata real. Offset: %s, Expected: %d. Actual: %d",
                0, totalsize, data.length));
      }
      baos.write(data);
    } else {
      throw new ConfigException(-1, "Unsupported vp type: " + type);
    }
    configData.read(new ByteArrayInputStream(baos.toByteArray()));
    return configData;
  }

  /**
   * 获取VPCON配置数据.
   *
   * @param level1 vpcon第一个端口的所在的slot
   * @param level2 vpcon第一个端口的所在的port在slot中的序号
   * @return 配置数据
   */
  public byte[] getExtVpconConfigData(final byte level1, final byte level2, int offset,
                                      int len)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.INFO,
        "getExtVpconConfigData " + level1 + "_" + level2 + " offset:" + offset + " len:" + len);
    return new Worker<byte[]>(this,
        NioPicRequest.GET_EXT_VPCON_DATA) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add1Byte(level1).add1Byte(level2)
            .add1Byte(VPCON_CONFIG_LEVEL_3).add4Byte(offset).add4Byte(len);
      }

      @Override
      protected byte[] getResponse(CfgReader cfgReader) throws ConfigException {
        cfgReader.readByteValue(); // level1
        cfgReader.readByteValue(); // level2
        cfgReader.readByteValue(); // level3
        int offset = cfgReader.readInteger();   // offset
        int len = cfgReader.readInteger();   // size
        LOG.log(Level.INFO,
            String.format("get ext vpcon configdata result offset:%d, len: %d", offset, len));
        return cfgReader.readByteArray();
      }
    }.work();
  }

  /**
   * 获取vpcon的自定义输出数据.
   *
   * @param level1 slot号.
   * @param level2 端口号.
   * @return 如果获取成功，返回输出数据，否则返回null.
   * @throws DeviceConnectionException 设备连接出错
   * @throws ConfigException           配置出错
   * @throws BusyException             设备正忙
   */
  public Vp6OutputData getVpconOutputData(final byte level1, final byte level2)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getVpconOutputData " + level1 + "_" + level2);
    return new Worker<Vp6OutputData>(this,
        CaesarControllerConstants.NioPicRequest.GET_VPCON_OUTPUT) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add1Byte(level1).add1Byte(level2).add1Byte(0);
      }

      @Override
      protected Vp6OutputData getResponse(CfgReader cfgReader) throws ConfigException {
        //level1
        cfgReader.readByteValue();
        //level2
        cfgReader.readByteValue();
        //level3
        cfgReader.readByteValue();
        Vp6OutputData outputData = new Vp6OutputData();
        byte[] bytes = cfgReader.readByteArray(outputData.size());
        ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
        try {
          outputData.read(bais);
        } catch (IOException exception) {
          CaesarController.LOG.log(Level.SEVERE, "read data error", exception);
          return null;
        }

        if (cfgReader.available() > 0) {
          cfgReader.readByteArray(8184);
        }
        return outputData;
      }
    }.work();
  }

  /**
   * 获取VPCON的分辨率.
   */
  public VpResolution[] getVpResolution(final byte level1, final byte level2)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getVpconConfigData " + level1 + "_" + level2);
    return new Worker<VpResolution[]>(this,
        CaesarControllerConstants.NioPicRequest.GET_VPCON_RESOLUTION) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add1Byte(level1).add1Byte(level2)
            .add1Byte(VPCON_RESOLUTION_LEVEL_3);
      }

      @Override
      protected VpResolution[] getResponse(CfgReader cfgReader) throws ConfigException {
        byte rspLevel1 = (byte) cfgReader.readByteValue();
        byte rspLevel2 = (byte) cfgReader.readByteValue();
        byte rspLevel3 = (byte) cfgReader.readByteValue();
        int count = cfgReader.readByteValue();
        VpResolution[] resolution = new VpResolution[count];

        for (int i = 0; i < count; i++) {
          resolution[i] = new VpResolution();
          byte[] bytes = cfgReader.readByteArray(resolution[0].size());
          ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
          try {
            resolution[i].read(bais);
          } catch (IOException ex) {
            CaesarController.LOG.log(Level.SEVERE, "read data error", ex);
            return new VpResolution[0];
          }
        }

        if (cfgReader.available() > 0) {
          cfgReader.readByteArray(8184);
        }
        if (rspLevel1 == level1 && rspLevel2 == level2 && rspLevel3 == VPCON_RESOLUTION_LEVEL_3) {
          return resolution;
        }
        CaesarController.LOG
            .log(Level.SEVERE, "Wrong Version Level (expected: {0}_{1}_{2}, response: {3}_{4}_{5})",
                new Object[] {level1, level2, VPCON_RESOLUTION_LEVEL_3, rspLevel1, rspLevel2,
                    rspLevel3});
        return new VpResolution[0];
      }
    }.work();
  }

  /**
   * .
   */
  public VersionSet getVersion(final byte level1, final byte level2)
      throws DeviceConnectionException, ConfigException, BusyException {
    final byte level3 = CaesarControllerConstants.VERSION_INFO_LEVEL_3;
    LOG.log(Level.FINEST, "getVersion " + level1 + "_" + level2 + "_" + level3);
    return new Worker<VersionSet>(this, CaesarControllerConstants.NioPicRequest.GET_VERSION) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add1Byte(level1).add1Byte(level2).add1Byte(level3);
      }

      @Override
      protected VersionSet getResponse(CfgReader cfgReader) throws ConfigException {
        byte rspLevel1 = (byte) cfgReader.readByteValue();
        byte rspLevel2 = (byte) cfgReader.readByteValue();
        byte rspLevel3 = (byte) cfgReader.readByteValue();

        VersionSet versionSet = new VersionSet();
        versionSet.readData(cfgReader);
        if (cfgReader.available() > 0) {
          cfgReader.readByteArray(8184);
        }
        if (rspLevel1 == level1 && rspLevel2 == level2 && rspLevel3 == level3) {
          return versionSet;
        }
        CaesarController.LOG.log(Level.SEVERE,
            "Wrong Version Level (expected: {0}_{1}_{2}, response: {3}_{4}_{5})",
            new Object[] {level1, level2, level3, rspLevel1, rspLevel2, rspLevel3});
        return null;
      }
    }.work();
  }

  /**
   * 获取版本.
   */
  public void getVersion() throws DeviceConnectionException, BusyException, ConfigException {
    new Worker<Void>(this, SystemRequest.GET_CFGDATA) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        builder.add4Byte(0).add4Byte(4);
        return builder;
      }

      @Override
      protected Void getResponse(CfgReader cfgReader) throws ConfigException {
        cfgReader.readInteger();
        cfgReader.readInteger();
        int version = cfgReader.readInteger();
        RunUtil.waitForUiDoneWithException(
            () -> switchDataModel.getConfigData().getConfigMetaData().setVersion(version));
        return null;
      }
    }.work();
  }

  /**
   * 获取外设的分辨率.
   *
   * @param level1 slot号
   * @param level2 端口号
   * @return 分辨率，如果获取不到返回空
   * @throws DeviceConnectionException 设备连接不了
   * @throws ConfigException           配置数据有误
   * @throws BusyException             设备正忙
   */
  public ResolutionData getExtResolution(final byte level1, final byte level2, int index)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getVersion " + level1 + "_" + level2);
    return new Worker<ResolutionData>(this,
        CaesarControllerConstants.NioPicRequest.GET_EXT_RESOLUTION) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        return paramAdd.add1Byte(level1).add1Byte(level2).add1Byte(index);
      }

      @Override
      protected ResolutionData getResponse(CfgReader cfgReader)
          throws IOException, ConfigException {
        cfgReader.readByteValue();
        cfgReader.readByteValue();
        cfgReader.readByteValue();
        ResolutionData resolutionData = new ResolutionData();
        resolutionData
            .read(new ByteArrayInputStream(cfgReader.readByteArray(resolutionData.size())));
        while (cfgReader.available() > 0) {
          cfgReader.readByteArray();
        }
        return resolutionData;
      }
    }.work();
  }

  @Override
  public void setSystemFileOpen(int fileType)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setSystemFileOpen");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.SYSTEM_SETFILE_START) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        return paramAdd.add1Byte(fileType);
      }

    }.work();
  }

  @Override
  public void setSystemFileClose(int fileType)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setSystemFileClose");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.SYSTEM_SETFILE_END) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        return paramAdd.add1Byte(fileType);
      }

    }.work();
  }

  @Override
  public byte[] readSystemFile(int fileType, int len)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "readSystemFile");
    return new Worker<byte[]>(this, CaesarControllerConstants.SystemRequest.SYSTEM_SETFILE_READ) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        return paramAdd.add1Byte(fileType).add4Byte(len);
      }

      @Override
      protected byte[] getResponse(CfgReader cfgReader) throws ConfigException {
        // file type
        cfgReader.readByteValue();
        int rspLen = cfgReader.readInteger();
        if (rspLen == 0) {
          return new byte[0];
        } else {
          return cfgReader.readByteArray(rspLen);
        }
      }

    }.work();
  }

  @Override
  public void writeSystemFile(int fileType, byte[] data)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "writeSystemFile");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.SYSTEM_SETFILE_WRITE) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        return paramAdd.add1Byte(fileType).add4Byte(data.length).add(data);
      }

    }.work();
  }

  /**
   * .
   */
  public void setUpdateOpen(final byte level1, final byte level2, final byte level3,
                            final byte... bytes) throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setUpdateOpen");
    new Worker<Void>(this, CaesarControllerConstants.NioPicRequest.SET_UPDATEOPEN) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add1Byte(level1).add1Byte(level2).add1Byte(level3).add(bytes);
      }
    }.work();
  }

  /**
   * 获取外设网络信息.
   */
  public ExtenderNetworkInfo getExtNetworkInfo(final byte level1, final byte level2)
      throws ConfigException, BusyException, DeviceConnectionException {
    LOG.log(Level.FINEST, "getExtNetworkInfo " + level1 + "_" + level2);
    return new Worker<ExtenderNetworkInfo>(this,
        CaesarControllerConstants.NioPicRequest.PHY_GET_EXT_NETWORK_INFO) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add1Byte(level1).add1Byte(level2)
            .add1Byte(VPCON_CONFIG_LEVEL_3);
      }

      @Override
      protected ExtenderNetworkInfo getResponse(CfgReader cfgReader) throws ConfigException {
        cfgReader.readByteValue(); // level1
        cfgReader.readByteValue(); // level2
        cfgReader.readByteValue(); // level3
        ExtenderNetworkInfo info = new ExtenderNetworkInfo();
        info.setAddress(cfgReader.readByteArray(4));
        info.setNetmask(cfgReader.readByteArray(4));
        info.setGateway(cfgReader.readByteArray(4));
        byte[] mac = cfgReader.readByteArray(6);
        info.setMacAddress(
            String.format("%02x:%02x:%02x:%02x:%02x:%02x", mac[0] & 0xff, mac[1] & 0xff,
                mac[2] & 0xff, mac[3] & 0xff, mac[4] & 0xff, mac[5] & 0xff));
        return info;
      }
    }.work();
  }

  /**
   * 设置外设网络信息.
   */
  public void setExtNetworkInfo(final byte level1, final byte level2,
                               final ExtenderNetworkInfo info)
      throws ConfigException, BusyException, DeviceConnectionException {
    LOG.log(Level.FINEST, "setExtNetworkInfo " + level1 + "_" + level2);
    new Worker<Void>(this, CaesarControllerConstants.NioPicRequest.PHY_SET_EXT_NETWORK_INFO) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add1Byte(level1).add1Byte(level2)
            .add1Byte(0).add(info.getAddress())
            .add(info.getNetmask()).add(info.getGateway());
      }
    }.work();
  }

  /**
   * .
   */
  public void setUpdateWrite(final byte level1, final byte level2, final byte level3,
                             final int offset, final byte[] data, final boolean checksumEnabled)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setUpdateWrite");
    if (data.length > 8176) {
      throw new IllegalArgumentException("Too much data! Does not fit into Buffer!");
    }
    new Worker<Void>(this, CaesarControllerConstants.NioPicRequest.SET_UPDATEWRITE) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        builder.setChecksumEnabled(checksumEnabled);
        return builder.add1Byte(level1).add1Byte(level2).add1Byte(level3).add4Byte(offset)
            .add4Byte(data.length).add(data);
      }
    }.work();
  }

  /**
   * .
   */
  public void setUpdateClose(final byte level1, final byte level2, final byte level3)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setUpdateClose");
    new Worker<Void>(this, CaesarControllerConstants.NioPicRequest.SET_UPDATECLOSE) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add1Byte(level1).add1Byte(level2).add1Byte(level3);
      }
    }.work();
  }

  /**
   * 写VPCON的SDCARD数据.
   *
   * @param level1          slot
   * @param level2          port
   * @param offset          地址偏移
   * @param data            写的数据
   * @param checksumEnabled 是否使用checksum
   */
  public void setVpSdCard(final byte level1, final byte level2, int offset, byte[] data,
                          boolean checksumEnabled) throws DeviceConnectionException, BusyException, ConfigException {
    LOG.log(Level.FINEST, "setVpSdCard");
    if (data.length > 8176) {
      throw new IllegalArgumentException("Too much data! Does not fit into Buffer!");
    }
    new Worker<Void>(this, NioPicRequest.SET_VP_SDCARD) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        builder.setChecksumEnabled(checksumEnabled);
        return builder.add1Byte(level1).add1Byte(level2).add1Byte(0).add4Byte(offset)
            .add4Byte(data.length).add(data);
      }
    }.work();
  }

  /**
   * 重置vpcon的sdcard.
   *
   * @param level1 slot
   * @param level2 port
   */
  public void resetVpSdCard(final byte level1, final byte level2)
      throws DeviceConnectionException, BusyException, ConfigException {
    LOG.log(Level.FINEST, "resetVpSdCard");
    new Worker<Void>(this, CaesarControllerConstants.NioPicRequest.SET_VP_SDCARD) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        builder.setChecksumEnabled(false);
        return builder.add1Byte(level1).add1Byte(level2).add1Byte(0xff).add4Byte(0).add4Byte(0);
      }
    }.work();
  }

  /**
   * 外设并行升级.
   *
   * @param bytes 升级的bitmap，byte的个数必须是2的倍数，要升级的外设在对应位置1，bitmap[extender.oid] = 1
   */
  public void setExtenderParallelUpdateOpen(final byte... bytes)
      throws DeviceConnectionException, ConfigException, BusyException {
    setUpdateOpen((byte) 0xff, (byte) 0xff, (byte) 0xff, bytes);
  }

  /**
   * 多升级包外设并行升级.
   *
   * @param bytes 升级的bitmap，byte的个数必须是2的倍数，要升级的外设在对应位置1，bitmap[extender.oid] = 1
   */
  public void setExtenderMultiPacketParallelUpdateOpen(final byte... bytes)
      throws DeviceConnectionException, ConfigException, BusyException {
    setUpdateOpen((byte) 0xff, (byte) 0xff, (byte) 2, bytes);
  }

  public void setExtenderParallelUpdateWrite(final int offset, final byte[] data,
                                             final boolean checksumEnabled)
      throws DeviceConnectionException, ConfigException, BusyException {
    setUpdateWrite((byte) 0xff, (byte) 0xff, (byte) 0xff, offset, data, checksumEnabled);
  }

  public void setExtenderParallelUpdateClose(boolean successful)
      throws DeviceConnectionException, ConfigException, BusyException {
    setUpdateClose((byte) 0xff, (byte) 0xff, (byte) (successful ? 0 : 1));
  }

  public void setExtenderMultiPacketParallelUpdateClose()
      throws DeviceConnectionException, ConfigException, BusyException {
    setUpdateClose((byte) 0xff, (byte) 0xff, (byte) 2);
  }

  public void setVpParallelUploadOpen(final byte... bytes)
      throws DeviceConnectionException, ConfigException, BusyException {
    setUpdateOpen((byte) 0xff, (byte) 0xff, (byte) 0, bytes);
  }

  public void setVpParallelUploadWrite(final int offset, final byte[] data,
                                       final boolean checksumEnabled)
      throws DeviceConnectionException, ConfigException, BusyException {
    setUpdateWrite((byte) 0xff, (byte) 0xff, (byte) 0, offset, data, checksumEnabled);
  }

  public void setVpParallelUploadClose(boolean successful)
      throws DeviceConnectionException, ConfigException, BusyException {
    setUpdateClose((byte) 0xff, (byte) 0xff, (byte) (successful ? 0 : 1));
  }

  /**
   * IO并行升级.
   *
   * @param bytes 升级的bitmap，byte的个数必须是2的倍数，要升级的IO在对应位置1，bitmap[module.oid] = 1
   */
  public void setIoParallelUpdateOpen(final byte... bytes)
      throws DeviceConnectionException, ConfigException, BusyException {
    setUpdateOpen((byte) 0xff, (byte) 0, (byte) 0, bytes);
  }

  public void setIoParallelUpdateWrite(final int offset, final byte[] data,
                                       final boolean checksumEnabled)
      throws DeviceConnectionException, ConfigException, BusyException {
    setUpdateWrite((byte) 0xff, (byte) 0, (byte) 0, offset, data, checksumEnabled);
  }

  public void setIoParallelUpdateClose(boolean successful)
      throws DeviceConnectionException, ConfigException, BusyException {
    setUpdateClose((byte) 0xff, (byte) 0, (byte) (successful ? 0 : 1));
  }

  /**
   * .
   */
  public void setReset(final byte level1, final byte level2, final byte level3)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setReset");
    new Worker<Void>(this, CaesarControllerConstants.NioPicRequest.SET_RESET) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add1Byte(level1).add1Byte(level2).add1Byte(level3);
      }
    }.work();
  }

  /**
   * .
   */
  public int[] getExtId(final byte level1, final byte level2, final byte level3)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getId");
    return new Worker<int[]>(this, CaesarControllerConstants.NioPicRequest.GET_EXTID) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add1Byte(level1).add1Byte(level2).add1Byte(level3);
      }

      @Override
      protected int[] getResponse(CfgReader cfgReader) throws ConfigException {
        byte rspLevel1 = cfgReader.read4ByteValue();
        byte rspLevel2 = cfgReader.read4ByteValue();
        byte rspLevel3 = cfgReader.read4ByteValue();
        int rspId1 = cfgReader.readInteger();
        int rspId2 = cfgReader.readInteger();
        int status = cfgReader.readInteger();
        if (rspLevel1 == level1 && rspLevel2 == level2 && rspLevel3 == level3) {
          return new int[] {rspId1, rspId2, status};
        }
        return new int[0];
      }
    }.work();
  }

  /**
   * .
   */
  public String getExtName(final byte level1, final byte level2)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getName");

    return new Worker<String>(this, NioPicRequest.GET_EXTNAME) {
      @Override
      protected Add buildRequest(Add builder) {
        return builder.add1Byte(level1).add1Byte(level2).add1Byte(GET_EXTNAME_LEVEL_3);
      }

      @Override
      protected String getResponse(CfgReader cfgReader) throws ConfigException {
        byte rspLevel1 = (byte) cfgReader.readByteValue();
        byte rspLevel2 = (byte) cfgReader.readByteValue();
        byte rspLevel3 = (byte) cfgReader.readByteValue();
        String rspName = cfgReader.readString(cfgReader.size() - 3, 0);
        if (rspLevel1 == level1 && rspLevel2 == level2 && rspLevel3 == GET_EXTNAME_LEVEL_3) {
          return rspName;
        }
        return null;
      }
    }.work();
  }

  /**
   * .
   */
  public String readMacAddress() throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "readMacAddress");
    return new Worker<String>(this, NioPicRequest.GET_MATRIX_MAC) {
      @Override
      protected Add buildRequest(Add builder) {
        return builder.add(new byte[] {0, 0, 0});
      }

      @Override
      protected String getResponse(CfgReader cfgReader) throws ConfigException {
        cfgReader.readByteValue();
        cfgReader.readByteValue();
        cfgReader.readByteValue();
        byte[] mac = cfgReader.readByteArray(6);
        return String.format("%02x:%02x:%02x:%02x:%02x:%02x", mac[0] & 0xff, mac[1] & 0xff,
            mac[2] & 0xff, mac[3] & 0xff, mac[4] & 0xff, mac[5] & 0xff);
      }
    }.work();
  }

  /**
   * 断开所有连接.
   */
  public void setAllOff() throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setAllOff");
    new Worker<Void>(this, CaesarControllerConstants.SwitchRequest.SET_ALLOFF) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder;
      }
    }.work();
  }

  /**
   * 获取与consoleData有视频连接的cpu.
   */
  public CpuData getCpuConnectToCon(ConsoleData consoleData)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getCpuConnectToCon");
    if (consoleData == null) {
      return null;
    }

    return new Worker<CpuData>(this,
        CaesarControllerConstants.SwitchRequest.GET_CPU_CONNECT_TO_CON) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        return paramAdd.add2Byte(consoleData.getId());
      }

      @Override
      protected CpuData getResponse(CfgReader cfgReader) throws ConfigException {
        int conId = cfgReader.read2ByteValue();
        int cpuId = cfgReader.read2ByteValue();
        if (conId != consoleData.getId()) {
          LOG.log(Level.WARNING, "The con id is not same as input!");
        }
        return CaesarController.this.configDataManager.getCpuData4Id(cpuId);
      }
    }.work();
  }

  /**
   * 设置视频连接.
   */
  public void setCpuConnectToCon(ConsoleData consoleData, CpuData cpuData)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setCpuConnectToCon");
    if (consoleData == null || cpuData == null) {
      return;
    }
    new Worker<Void>(this, CaesarControllerConstants.SwitchRequest.SET_CPU_CONNECT_TO_CON) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        paramAdd.add2Byte(consoleData.getId());
        paramAdd.add2Byte(cpuData.getId());
        return paramAdd;
      }

    }.work();
  }

  /**
   * 获取目标CON组的视频连接关系.
   */
  public Map<ConsoleData, CpuData> getCpusConnectToCons(Collection<ConsoleData> cons)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getCpusConnectToCons");
    return new Worker<Map<ConsoleData, CpuData>>(this,
        CaesarControllerConstants.SwitchRequest.GET_CPUS_CONNECT_TO_CONS) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        cons.removeAll(Collections.singleton(null));
        paramAdd.add2Byte(cons.size());
        for (ConsoleData consoleData : cons) {
          paramAdd.add2Byte(consoleData.getId());
        }
        return paramAdd;
      }

      @Override
      protected Map<ConsoleData, CpuData> getResponse(CfgReader cfgReader) throws ConfigException {
        Map<ConsoleData, CpuData> result = new HashMap<>();
        int count = cfgReader.read2ByteValue();
        for (int i = 0; i < count; i++) {
          int conId = cfgReader.read2ByteValue();
          int cpuId = cfgReader.read2ByteValue();

          ConsoleData consoleData =
              CaesarController.this.configDataManager.getConsoleData4Id(conId);
          CpuData cpuData = CaesarController.this.configDataManager.getCpuData4Id(cpuId);
          result.put(consoleData, cpuData);
        }
        return result;
      }
    }.work();
  }


  /**
   * 建立video access 连接.
   *
   * @param map console与cpu的连接关系的map，都不能为空
   */
  public void setCpusConnectToCons(final Map<ConsoleData, CpuData> map)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setConBlock");
    new Worker<Void>(this, CaesarControllerConstants.SwitchRequest.SET_CPUS_CONNECT_TO_CONS) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        CommunicationBuilder.Add bldr = builder.add2Byte(map.size());
        for (Map.Entry<ConsoleData, CpuData> entry : map.entrySet()) {
          bldr = bldr.add2Byte(entry.getKey().getId());
          bldr = bldr.add2Byte(entry.getValue().getId());
        }
        return bldr;
      }
    }.work();
  }

  /**
   * 获取consoleData全连接的cpu.
   */
  public CpuData getCpuConnectToConBidirectional(ConsoleData consoleData)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getCpuConnectToConBidirectional");
    if (consoleData == null) {
      return null;
    }

    return new Worker<CpuData>(this,
        CaesarControllerConstants.SwitchRequest.GET_CPU_CONNECT_TO_CON_BIDIRECTIONAL) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        return paramAdd.add2Byte(consoleData.getId());
      }

      @Override
      protected CpuData getResponse(CfgReader cfgReader) throws ConfigException {
        int conId = cfgReader.read2ByteValue();
        int cpuId = cfgReader.read2ByteValue();
        if (conId != consoleData.getId()) {
          LOG.log(Level.WARNING, "The con id is not same as input!");
        }
        return CaesarController.this.configDataManager.getCpuData4Id(cpuId);
      }
    }.work();
  }

  /**
   * 获取目标CON组的全连接关系.
   */
  public Map<ConsoleData, CpuData> getCpusConnectToConsBidirectional(Collection<ConsoleData> cons)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getCpusConnectToCons");
    return new Worker<Map<ConsoleData, CpuData>>(this,
        CaesarControllerConstants.SwitchRequest.GET_CPUS_CONNECT_TO_CONS_BIDIRECTIONAL) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        cons.removeAll(Collections.singleton(null));
        paramAdd.add2Byte(cons.size());
        for (ConsoleData consoleData : cons) {
          paramAdd.add2Byte(consoleData.getId());
        }
        return paramAdd;
      }

      @Override
      protected Map<ConsoleData, CpuData> getResponse(CfgReader cfgReader) throws ConfigException {
        Map<ConsoleData, CpuData> result = new HashMap<>();
        int count = cfgReader.read2ByteValue();
        for (int i = 0; i < count; i++) {
          int conId = cfgReader.read2ByteValue();
          int cpuId = cfgReader.read2ByteValue();

          ConsoleData consoleData =
              CaesarController.this.configDataManager.getConsoleData4Id(conId);
          CpuData cpuData = CaesarController.this.configDataManager.getCpuData4Id(cpuId);
          result.put(consoleData, cpuData);
        }
        return result;
      }
    }.work();
  }

  /**
   * .
   */
  public void setCpuConsoleConnecton(final CpuData cpuData, final ConsoleData consoleData)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setCpuConsole");
    new Worker<Void>(this, CaesarControllerConstants.SwitchRequest.SET_CPUCON_CONNECTION) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add2Byte(cpuData == null ? 0 : cpuData.getId())
            .add2Byte(consoleData.getId());
      }
    }.work();
  }

  /**
   * .
   */
  public void setCpuConsoleConnectionBlock(final Map<ConsoleData, CpuData> map)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setCpuConsoleBlock");
    new Worker<Void>(this, CaesarControllerConstants.SwitchRequest.SET_CPUCON_CONNECTION_BLOCK) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        CommunicationBuilder.Add bldr = builder.add2Byte(map.size());
        for (Map.Entry<ConsoleData, CpuData> entry : map.entrySet()) {
          bldr = bldr.add2Byte(entry.getValue() != null ? entry.getValue().getId() : 0);
          bldr = bldr.add2Byte(entry.getKey().getId());
        }
        return bldr;
      }
    }.work();
  }

  /**
   * 设置多个视频连接.
   *
   * @param map 连接内容.
   */
  public void setCpuConsoleConnectionBlockByVideoMode(final Map<ConsoleData, CpuData> map)
      throws DeviceConnectionException, BusyException, ConfigException {
    LOG.log(Level.FINEST, "setCpuConsoleConnectionBlockByVideoMode");
    List<ConsoleData> keys = new ArrayList<>(map.keySet());
    int writedSize = 0;
    int maxWriteSize = 169;
    while (writedSize < keys.size()) {
      List<ConsoleData> subList = keys
          .subList(writedSize, Math.min(writedSize + maxWriteSize, keys.size()));
      writedSize += subList.size();
      new Worker<Void>(this, SwitchRequest.SET_CPUCON_CONNECTION_BLOCK_BYMODE) {
        @Override
        protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
          CommunicationBuilder.Add bldr = builder.add2Byte(subList.size());
          for (ConsoleData consoleData : subList) {
            CpuData cpuData = map.get(consoleData);
            bldr = bldr
                .add2Byte(cpuData == null ? 0 : cpuData.getId());
            bldr = bldr.add2Byte(consoleData.getId());
            bldr.add2Byte(1); // 视频连接
          }
          return bldr;
        }
      }.work();
    }
  }

  /**
   * .
   */
  public void getCpuConsoleMatrix()
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getCpuConsoleMatrix");
    new Worker<Void>(this, CaesarControllerConstants.SwitchRequest.GET_CPUCON_MATRIX) {
      private Collection<CpuData> cpuDatas = Collections.emptyList();
      private Collection<ConsoleData> consoleDatas = Collections.emptyList();

      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder;
      }

      @Override
      protected Void getResponse(CfgReader cfgReader) throws ConfigException {
        int size = cfgReader.size();
        int rspCountCpu = cfgReader.read2ByteValue();
        int rspCountConsole = cfgReader.read2ByteValue();
        Collection<CpuData> cpus = new ArrayList<>();
        Collection<ConsoleData> consoles = new ArrayList<>();

        boolean newFormat = size - 4 == (rspCountCpu + rspCountConsole) * 4;
        for (int idxCpu = 0; idxCpu < rspCountCpu; idxCpu++) {
          CpuData cpuData;
          if (newFormat) {
            cpuData =
                CaesarController.this.configDataManager.getCpuData4Id(cfgReader.read2ByteValue());
          } else {
            cpuData = CaesarController.this.configDataManager.getCpuData(idxCpu);
          }
          ConsoleData consoleData =
              CaesarController.this.configDataManager.getConsoleData4Id(cfgReader.read2ByteValue());
          if (cpuData != null) {
            cpuData.setConsoleData(
                (consoleData != null) && (consoleData.isStatusActive()) ? consoleData : null);
            cpus.add(cpuData);
          }
        }
        for (int idxConsole = 0; idxConsole < rspCountConsole; idxConsole++) {
          ConsoleData consoleData;
          if (newFormat) {
            consoleData = CaesarController.this.configDataManager
                .getConsoleData4Id(cfgReader.read2ByteValue());
          } else {
            consoleData = CaesarController.this.configDataManager.getConsoleData(idxConsole);
          }
          CpuData cpuData =
              CaesarController.this.configDataManager.getCpuData4Id(cfgReader.read2ByteValue());
          if (consoleData != null) {
            consoleData
                .setCpuData((cpuData != null) && (cpuData.isStatusActive()) ? cpuData : null);
            consoles.add(consoleData);
          }
        }
        this.cpuDatas = cpus;
        this.consoleDatas = consoles;

        return null;
      }

      @Override
      protected void workDone() {
        for (CpuData cpuData : this.cpuDatas) {
          cpuData.commitProperty(CaesarController.CONTROLLER_THRESHOLD, CpuData.PROPERTY_CONSOLE);
        }
        for (ConsoleData consoleData : this.consoleDatas) {
          consoleData
              .commitProperty(CaesarController.CONTROLLER_THRESHOLD, ConsoleData.PROPERTY_CPU);
        }

        List<CpuData> disconnectedCpus = new ArrayList<>(configDataManager.getActiveCpus());
        disconnectedCpus.removeAll(cpuDatas);
        for (CpuData data : disconnectedCpus) {
          data.setConsoleData(null);
        }

        List<ConsoleData> disconnetedConsoles =
            new ArrayList<>(configDataManager.getActiveConsoles());
        disconnetedConsoles.removeAll(consoleDatas);
        for (ConsoleData data : disconnetedConsoles) {
          data.setCpuData(null);
        }
      }
    }.work();
  }

  /**
   * 设置多组cpu与console的连接关系.
   */
  public void setCpuConsoleMatrix()
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setCpuConsoleMatrix");
    new Worker<Void>(this, CaesarControllerConstants.SwitchRequest.SET_CPUCON_MATRIX) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        CommunicationBuilder.Add bldr = builder;

        bldr = bldr.add2Byte(CaesarController.this.configDataManager.getActiveCpus().size());
        bldr = bldr.add2Byte(CaesarController.this.configDataManager.getActiveConsoles().size());
        for (CpuData cpuData : configDataManager.getActiveCpus()) {
          bldr = bldr.add2Byte(cpuData.getId());
          ConsoleData consoleData = cpuData.getConsoleData();
          bldr = bldr.add2Byte(consoleData != null ? consoleData.getId() : 0);
        }
        for (ConsoleData consoleData : configDataManager.getActiveConsoles()) {
          bldr = bldr.add2Byte(consoleData.getId());
          CpuData cpuData = consoleData.getCpuData();
          bldr = bldr.add2Byte(cpuData != null ? cpuData.getId() : 0);
        }
        return bldr;
      }

      @Override
      protected void workDone() {
        for (int i = 0; i < CaesarController.this.configDataManager.getConfigMetaData()
            .getCpuCount(); i++) {
          CaesarController.this.configDataManager.getCpuData(i)
              .commitProperty(CpuData.PROPERTY_CONSOLE);
        }
        for (int i = 0; i < CaesarController.this.configDataManager.getConfigMetaData()
            .getConsoleCount(); i++) {
          CaesarController.this.configDataManager.getConsoleData(i)
              .commitProperty(ConsoleData.PROPERTY_CPU);
        }
      }
    }.work();
  }

  /**
   * .
   */
  public void setCpuConsoleConnectionByMode(final CpuData cpuData, final ConsoleData consoleData,
                                            final int mode) throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setCpuConsole");
    new Worker<Void>(this, CaesarControllerConstants.SwitchRequest.SET_CPUCON_CONNECTION_BYMODE) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add2Byte(cpuData == null ? 0 : cpuData.getId()).add2Byte(consoleData.getId())
            .add2Byte(mode);
      }
    }.work();
  }

  /**
   * 获取当前设备的矩阵数据.
   *
   * @param index 当前设备的矩阵索引
   */
  public void getGridInfo(int index)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getGridInfo");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.GET_MATRIX_GRIDINFO) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add2Byte(index).add2Byte(1);
      }

      @Override
      protected Void getResponse(CfgReader cfgReader) throws ConfigException {
        cfgReader.read2ByteValue();
        cfgReader.read2ByteValue();
        CaesarController.this.switchDataModel.getConfigData().getGridData().readData(cfgReader);
        return null;
      }
    }.work();
  }

  /**
   * 获取矩阵数据.
   */
  public void getGridInfoByIndex(int index)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getGridInfo");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.GET_MATRIX_GRIDINFO) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add2Byte(index).add2Byte(1);
      }

      @Override
      protected Void getResponse(CfgReader cfgReader) throws ConfigException {
        cfgReader.read2ByteValue();
        cfgReader.read2ByteValue();
        CaesarController.this.switchDataModel.getConfigData().getMatrixData(index)
            .readData(cfgReader);
        return null;
      }
    }.work();
  }


  /**
   * 获取配置结构信息.
   */
  public void getMegaData(Version version)
      throws DeviceConnectionException, BusyException, ConfigException {
    new Worker<Void>(this, SystemRequest.GET_CFGDATA) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        builder.add4Byte(0).add4Byte(version.getMetaDataSize());
        return builder;
      }

      @Override
      protected Void getResponse(CfgReader cfgReader) throws ConfigException {
        cfgReader.readInteger();
        cfgReader.readInteger();
        RunUtil.waitForUiDoneWithException(
            () -> CaesarController.this.switchDataModel.getConfigData().getConfigMetaData()
                .readData(cfgReader));
        cfgReader.readByteArray();
        return null;
      }
    }.work();
  }

  /**
   * .
   */
  public void getConfigData() throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getConfigData");
    final ByteArrayOutputStream baos = new ByteArrayOutputStream();
    ensureConnection(this.switchDataModel.getIdentifier());

    while (baos.size() < this.switchDataModel.getConfigMetaData().getConfigDataSize()) {
      new Worker<Void>(this, SystemRequest.GET_CFGDATA) {
        private final int count = Math.min(1445,
            CaesarController.this.switchDataModel.getConfigMetaData().getConfigDataSize()
                - baos.size());

        @Override
        protected Add buildRequest(Add builder) {
          return builder.add4Byte(baos.size()).add4Byte(this.count);
        }

        @Override
        protected Void getResponse(CfgReader cfgReader) throws IOException, ConfigException {
          int rspOffset = cfgReader.readInteger();
          int rspCount = cfgReader.readInteger();
          byte[] rspData = cfgReader.readByteArray();
          if (baos.size() != rspOffset) {
            throw new IllegalArgumentException("Offset does not match!");
          }
          if (rspCount != this.count) {
            throw new IllegalArgumentException("Count does not match");
          }
          baos.write(rspData);
          return null;
        }
      }.work();
    }
    this.switchDataModel.readData(new ByteArrayInputStream(baos.toByteArray()),
        ConfigData.IoMode.All);
  }

  /**
   * .
   */
  public void getSystemData() throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getSystemData");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.GET_SYSDATA) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder;
      }

      @Override
      protected Void getResponse(CfgReader cfgReader) throws ConfigException {
        RunUtil.waitForUiDoneWithException(
            () -> CaesarController.this.switchDataModel.getConfigData().getSystemConfigData()
                .readData(cfgReader));
        return null;
      }
    }.work();
  }

  /**
   * .
   */
  public void setSystemData() throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setSystemData");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.SET_SYSDATA) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder)
          throws ConfigException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream(660);

        CaesarController.this.switchDataModel.getConfigData().getSystemConfigData()
            .writeData(new CfgWriter(baos));

        return builder.add(baos.toByteArray());
      }
    }.work();
  }

  /**
   * .
   */
  public void getPortData(Iterable<PortData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getPortData");
    getCommunicatableByConfigData(CaesarControllerConstants.SystemRequest.GET_CFGDATA,
        requestedDatas, CollectionUtil.getList(this.switchDataModel.getConfigData().getPortDatas()),
        switchDataModel.getConfigMetaData().getUtilVersion().getClassOffset(PortData.class),
        switchDataModel.getConfigMetaData().getPortSize(),
        "PortData");
  }

  /**
   * .
   */
  public void setPortData(Iterable<PortData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setPortData");
    setCommunicatable(CaesarControllerConstants.SystemRequest.SET_PORTDATA, requestedDatas,
        switchDataModel.getConfigMetaData().getPortSize(), "PortData");
  }

  /**
   * .
   */
  public void getCpuData(Iterable<CpuData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getCpuData");
    getCommunicatableByConfigData(CaesarControllerConstants.SystemRequest.GET_CFGDATA,
        requestedDatas, CollectionUtil.getList(this.switchDataModel.getConfigData().getCpuDatas()),
        switchDataModel.getConfigMetaData().getUtilVersion().getClassOffset(CpuData.class),
        switchDataModel.getConfigMetaData().getCpuSize(), "CpuData");
  }

  /**
   * .
   */
  public void setCpuData(Iterable<CpuData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setCpuData");
    setCommunicatable(CaesarControllerConstants.SystemRequest.SET_CPUDATA, requestedDatas,
        switchDataModel.getConfigMetaData().getCpuSize(), "CpuData");
  }

  /**
   * .
   */
  public void getConsoleData(Iterable<ConsoleData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getConsoleData");
    getCommunicatableByConfigData(CaesarControllerConstants.SystemRequest.GET_CFGDATA,
        requestedDatas,
        CollectionUtil.getList(this.switchDataModel.getConfigData().getConsoleDatas()),
        switchDataModel.getConfigMetaData().getUtilVersion().getClassOffset(ConsoleData.class),
        switchDataModel.getConfigMetaData().getConsoleSize(),
        "ConsoleData");
  }

  /**
   * .
   */
  public void setConsoleData(Iterable<ConsoleData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setConsoleData");
    setCommunicatable(CaesarControllerConstants.SystemRequest.SET_CONDATA, requestedDatas,
        switchDataModel.getConfigMetaData().getConsoleSize(), "ConsoleData");
  }

  /**
   * .
   */
  public void getExtenderData(Iterable<ExtenderData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getExtenderData");
    getCommunicatableByConfigData(CaesarControllerConstants.SystemRequest.GET_CFGDATA,
        requestedDatas,
        CollectionUtil.getList(this.switchDataModel.getConfigData().getExtenderDatas()),
        switchDataModel.getConfigMetaData().getUtilVersion().getClassOffset(ExtenderData.class),
        switchDataModel.getConfigMetaData().getExtenderSize(),
        "ExtenderData");
  }

  /**
   * .
   */
  public void setExtenderData(Iterable<ExtenderData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setExtenderData");
    setCommunicatable(CaesarControllerConstants.SystemRequest.SET_EXTDATA, requestedDatas,
        switchDataModel.getConfigMetaData().getExtenderSize(), "ExtenderData");
  }

  /**
   * .
   */
  public void getUserData(Iterable<UserData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getUserData");
    getCommunicatableByConfigData(CaesarControllerConstants.SystemRequest.GET_CFGDATA,
        requestedDatas, CollectionUtil.getList(this.switchDataModel.getConfigData().getUserDatas()),
        switchDataModel.getConfigMetaData().getUtilVersion().getClassOffset(UserData.class),
        switchDataModel.getConfigMetaData().getUserSize(),
        "UserData");
  }

  /**
   * .
   */
  public void setUserData(Iterable<UserData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setUserData");
    setCommunicatable(CaesarControllerConstants.SystemRequest.SET_USERDATA, requestedDatas,
        switchDataModel.getConfigMetaData().getUserSize(), "UserData");
  }

  /**
   * .
   */
  public void getUserGroupData(Iterable<UserGroupData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getUserGroupData");
    getCommunicatableByConfigData(CaesarControllerConstants.SystemRequest.GET_CFGDATA,
        requestedDatas,
        CollectionUtil.getList(this.switchDataModel.getConfigData().getUserGroupDatas()),
        switchDataModel.getConfigMetaData().getUtilVersion().getClassOffset(UserGroupData.class),
        switchDataModel.getConfigMetaData().getUserGroupSize(),
        "UserGroupData");
  }

  /**
   * .
   */
  public void setUserGroupData(Iterable<UserGroupData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setUserGroupData");
    setCommunicatable(CaesarControllerConstants.SystemRequest.SET_USERGROUPDATA, requestedDatas,
        switchDataModel.getConfigMetaData().getUserGroupSize(), "UserGroupData");
  }

  /**
   * .
   */
  public void getMultiScreenData(Iterable<MultiScreenData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getMultiScreenData");
    getCommunicatableByConfigData(CaesarControllerConstants.SystemRequest.GET_CFGDATA,
        requestedDatas,
        CollectionUtil.getList(this.switchDataModel.getConfigData().getMultiScreenDatas()),
        switchDataModel.getConfigMetaData().getUtilVersion().getClassOffset(MultiScreenData.class),
        switchDataModel.getConfigMetaData().getMultiScreenSize(), "MultiScreenData");
  }

  /**
   * .
   */
  public void setMultiScreenData(Iterable<MultiScreenData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setMultiScreenData");
    setCommunicatable(CaesarControllerConstants.SystemRequest.SET_MULTISCREENDATA,
        requestedDatas, switchDataModel.getConfigMetaData().getMultiScreenSize(),
        "MultiScreenData");
  }

  /**
   * .
   */
  public void getModuleData(Iterable<ModuleData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getModuleData");
    getCommunicatable(CaesarControllerConstants.SystemRequest.GET_MODLIST, requestedDatas,
        CollectionUtil.getList(this.switchDataModel.getSwitchModuleData().getModuleDatas()),
        switchDataModel.getConfigMetaData().getUtilVersion().getClassSize(ModuleData.class),
        "ModuleData");
  }

  /**
   * .
   */
  public void getModuleData(Iterable<ModuleData> requestedDatas,
                            Iterable<ModuleData> allCommunicatables)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getModuleData");
    getCommunicatable(CaesarControllerConstants.SystemRequest.GET_MODLIST, requestedDatas,
        CollectionUtil.getList(allCommunicatables),
        switchDataModel.getConfigMetaData().getUtilVersion().getClassSize(ModuleData.class),
        "ModuleData");
  }

  /**
   * .
   */
  public void getTxRxGroupData(Iterable<TxRxGroupData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getTxRxGroupData");
    getCommunicatableByConfigData(SystemRequest.GET_CFGDATA, requestedDatas,
        CollectionUtil.getList(this.switchDataModel.getConfigData().getTxRxGroupDatas()),
        switchDataModel.getConfigMetaData().getUtilVersion().getClassOffset(TxRxGroupData.class),
        switchDataModel.getConfigMetaData().getTxRxGroupSize(), "TxRxGroupData");
  }

  /**
   * .
   */
  public void setBranchData(Iterable<BranchData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setBranchData");
    setCommunicatable(SystemRequest.SET_BRANCH_DATA, requestedDatas,
        switchDataModel.getConfigMetaData().getBranchDataSize(), "BranchData");
  }

  /**
   * .
   */
  public void getBranchData(Iterable<BranchData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getBranchData");
    getCommunicatable(SystemRequest.GET_BRANCH_DATA, requestedDatas,
        CollectionUtil.getList(this.switchDataModel.getConfigData().getBranchDatas()),
        switchDataModel.getConfigMetaData().getBranchDataSize(), "BranchData");
  }

  /**
   * .
   */
  public void setMultiviewData(Iterable<MultiviewData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setMultiviewData");
    setCommunicatable(SystemRequest.SET_MULTIVIEW_DATA, requestedDatas,
        switchDataModel.getConfigMetaData().getMultiviewDataSize(), "MultiviewData");
  }

  /**
   * .
   */
  public void getMultiviewData(Iterable<MultiviewData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getMultiviewData");
    getCommunicatable(SystemRequest.GET_MULTIVIEW_DATA, requestedDatas,
        CollectionUtil.getList(this.switchDataModel.getConfigData().getMultiviewDatas()),
        switchDataModel.getConfigMetaData().getMultiviewDataSize(), "MultiviewData");
  }

  /**
   * 设置多画面布局.
   */
  public void setMultiviewLayout(int id, MultiviewLayoutType type)
      throws ConfigException, BusyException, DeviceConnectionException {
    LOG.log(Level.FINEST, "setMultiviewLayout");
    new Worker<Void>(this, NioPicRequest.SET_MULTIVIEW_LAYOUT) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        return paramAdd.add2Byte(id).add2Byte(type.getIndex());
      }
    }.work();
  }

  /**
   * 设置多画面输出模式.
   */
  public void setMultiviewOutputMode(int id, MultiviewOutputMode mode)
      throws ConfigException, BusyException, DeviceConnectionException {
    LOG.log(Level.FINEST, "setMultiviewOutputMode");
    new Worker<Void>(this, NioPicRequest.SET_MULTIVIEW_OUTPUT_MODE) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        return paramAdd.add2Byte(id).add1Byte(mode.getIndex());
      }
    }.work();
  }
  
  /**
   * 设置视频墙重新同步视频.
   *
   * @param id 视频墙ID
   */
  public void setVideoWallResyncVideo(int id) throws ConfigException, BusyException, DeviceConnectionException {
    LOG.log(Level.FINEST, "setVideoWallResyncVideo");
    new Worker<Void>(this, NioPicRequest.SET_VIDEOWALL_RESYNC_VIDEO) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        return paramAdd.add2Byte(id);
      }
    }.work();
  }

  /**
   * 设置视频墙冻结视频.
   *
   * @param id     视频墙ID
   * @param freeze true表示冻结视频，false表示取消冻结
   * @param timeMs 自动解冻冻结时间，单位为毫秒
   */
  public void setVideoWallFreezeVideo(int id, boolean freeze, int timeMs)
      throws ConfigException, BusyException, DeviceConnectionException {
    LOG.log(Level.FINEST, "setVideoWallFreezeVideo");
    new Worker<Void>(this, NioPicRequest.SET_VIDEOWALL_FREEZE_VIDEO) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        return paramAdd.add2Byte(id).add1Byte(freeze ? 1 : 0).add2Byte(timeMs);
      }
    }.work();
  }

  /**
   * .
   */
  public void setSourceCropData(Iterable<SourceCropData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setSourceCropData");
    setCommunicatable(SystemRequest.SET_SOURCE_CROP_DATA, requestedDatas,
        switchDataModel.getConfigMetaData().getSourceCropDataSize(), "SourceCropData");
  }

  /**
   * .
   */
  public void getSourceCropData(Iterable<SourceCropData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getSourceCropData");
    getCommunicatable(SystemRequest.GET_SOURCE_CROP_DATA, requestedDatas,
        CollectionUtil.getList(this.switchDataModel.getConfigData().getSourceCropDatas()),
        switchDataModel.getConfigMetaData().getSourceCropDataSize(), "SourceCropData");
  }

  /**
   * .
   */
  public void setTxRxGroupData(Iterable<TxRxGroupData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setTxRxGroupData");
    setCommunicatable(SystemRequest.SET_TXRXGROUP, requestedDatas,
        switchDataModel.getConfigMetaData().getTxRxGroupSize(), "TxRxGroupData");
  }

  /**
   * .
   */
  public SystemTimeData getTime() throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getTime");
    return new Worker<SystemTimeData>(this, CaesarControllerConstants.SystemRequest.GET_TIME) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder;
      }

      @Override
      protected SystemTimeData getResponse(CfgReader cfgReader) throws ConfigException {
        int rspSeconds = 0xFF & cfgReader.readByteValue();
        int rspMinutes = 0xFF & cfgReader.readByteValue();
        int rspHours = 0xFF & cfgReader.readByteValue();
        // day of weak
        cfgReader.readByteValue();
        int rspDay = 0xFF & cfgReader.readByteValue();
        int rspMonth = 0xFF & cfgReader.readByteValue();
        int rspYear = 0xFF & cfgReader.readByteValue();

        String sdateTime = String
            .format("20%02X-%02X-%02X %02X:%02X:%02X", rspYear, rspMonth, rspDay, rspHours,
                rspMinutes, rspSeconds);
        int status = cfgReader.readByteValue();
        LocalDateTime time = null;
        try {
          time = LocalDateTime.parse(sdateTime, df);
        } catch (IllegalArgumentException ex) {
          CaesarController.LOG.log(Level.SEVERE, null, ex);
        }
        return new SystemTimeData(time, status);
      }
    }.work();
  }

  /**
   * .
   */
  public void startReceiveSyslog(final byte level1, final byte level2)
      throws DeviceConnectionException, ConfigException, BusyException {
    final byte level3 = CaesarControllerConstants.VERSION_INFO_LEVEL_3;
    LOG.log(Level.FINEST, "startReceiveSyslog " + level1 + "_" + level2 + "_" + level3);
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.GET_SYSLOG_START) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add1Byte(level1).add1Byte(level2).add1Byte(level3);
      }
    }.work();
  }

  /**
   * .
   */
  public void endReceiveSyslog() throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "startReceiveSyslog");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.GET_SYSLOG_END) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder;
      }
    }.work();
  }

  /**
   * .
   */
  public void setTime() throws DeviceConnectionException, ConfigException, BusyException {
    setTime(LocalDateTime.now());
  }

  /**
   * .
   */
  public void setTime(final LocalDateTime date)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setTime");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.SET_TIME) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        int dayOfWeek =
            CaesarConstants.JavaToTeraDayOfWeek.valueOf(date.getDayOfWeek().getValue()).getTeraId();

        return builder.add1Byte(Integer.parseInt("" + date.getSecond(), 16))
            .add1Byte(Integer.parseInt("" + date.getMinute(), 16))
            .add1Byte(Integer.parseInt("" + date.getHour(), 16))
            .add1Byte(Integer.parseInt("" + dayOfWeek, 16))
            .add1Byte(Integer.parseInt("" + date.getDayOfMonth(), 16))
            .add1Byte(Integer.parseInt("" + date.getMonthValue(), 16))
            .add1Byte(Integer.parseInt("" + date.getYear() % 100, 16));
      }
    }.work();
  }

  /**
   * .
   */
  public void activateConfig(final int configNr)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "activateConfig");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.SYSTEM_SET_SYSCMD) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder.add4Byte(CaesarControllerConstants.BIN_CMD_ACTIVE).add4Byte(configNr)
            .add4Byte(0);
      }
    }.work();
  }

  /**
   * .
   */
  public void setMatData(Iterable<MatrixData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setMatData");
    setCommunicatable(CaesarControllerConstants.SystemRequest.SET_MATDATA, requestedDatas,
        switchDataModel.getConfigMetaData().getMatrixSize(),
        "MatrixData");
  }

  /**
   * .
   */
  public int getConfigVersion() throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getConfigVersion");
    ensureConnection(this.switchDataModel.getIdentifier());

    return new Worker<Integer>(this, SystemRequest.GET_CPUAPP_VERSION) {

      @Override
      protected Integer getResponse(CfgReader cfgReader) throws ConfigException {
        VersionDef version = new VersionDef();
        version.readData(cfgReader);
        return (version.getMasterVersion() << 16) + version.getSubVersion();
      }

      @Override
      protected Add buildRequest(Add paramAdd) {
        return paramAdd;
      }
    }.work();
  }

  /**
   * .
   */
  public void getFunctionKeyData(Iterable<FunctionKeyData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getFunctionKeyData");
    getCommunicatableByConfigData(CaesarControllerConstants.SystemRequest.GET_CFGDATA,
        requestedDatas,
        CollectionUtil.getList(this.switchDataModel.getConfigData().getFunctionKeyDatas()),
        switchDataModel.getConfigMetaData().getUtilVersion().getClassOffset(FunctionKeyData.class),
        switchDataModel.getConfigMetaData().getFunctionKeySize(),
        "FunctionKeyData");
  }

  /**
   * .
   */
  public void setFunctionKeyData(Iterable<FunctionKeyData> requestedDatas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setFunctionKeyData");
    setCommunicatable(CaesarControllerConstants.SystemRequest.SET_FKEYDATA, requestedDatas,
        switchDataModel.getConfigMetaData().getFunctionKeySize(),
        "FunctionKeyData");
  }

  /**
   * .
   */
  public void getLicense() throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getLicense");
    final ByteArrayOutputStream baos = new ByteArrayOutputStream();
    ensureConnection(this.switchDataModel.getIdentifier());

    new Worker<Void>(this, SystemRequest.GET_LICENCE) {
      @Override
      protected Add buildRequest(Add builder) {
        return builder;
      }

      @Override
      protected Void getResponse(CfgReader cfgReader) throws IOException {
        baos.write(cfgReader.readByteArray());
        return null;
      }
    }.work();
    this.switchDataModel.getLicenseData()
        .read(new CfgReader(new ByteArrayInputStream(baos.toByteArray()), false));
  }

  /**
   * .
   */
  public Integer setLicenseKey(final String key)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setLicenseKey");
    return new Worker<Integer>(this, SystemRequest.SET_LICENCEKEY) {
      @Override
      protected Add buildRequest(Add builder) {
        return builder.add(key);
      }

      @Override
      protected Integer getResponse(CfgReader cfgReader) throws ConfigException {
        return cfgReader.readInteger();
      }
    }.work();
  }

  /**
   * 发送VP备份数据.
   *
   * @param offset 地址偏移, offset+data.size要小于256k
   * @param data   要发送的数据，如果数据量太大，需要切片
   */
  public void setVpBackup(int offset, byte[] data)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setVpBackup");
    final int bufferSize = 128;
    int length = data.length;
    while (length > bufferSize) {
      byte[] temp = Arrays.copyOfRange(data, 0, bufferSize);
      setVpBackup(offset, temp);
      length -= bufferSize;
      offset += bufferSize;
      data = Arrays.copyOfRange(data, bufferSize, data.length);
    }
    if (length > 0) {
      final int finalOffset = offset;
      final byte[] finalData = data;
      new Worker<Void>(this, CaesarControllerConstants.SystemRequest.SET_VPBACKUP_DATA) {
        @Override
        protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
          builder.add4Byte(finalOffset);
          builder.add2Byte(finalData.length);
          builder.add(finalData);
          return builder;
        }
      }.work();
    }
  }

  /**
   * 发送带入的外设信息.
   */
  public void sendExtImport(ExtenderInfo infos)
      throws ConfigException, BusyException, DeviceConnectionException {
    LOG.log(Level.FINEST, "sendExtImport");
    new Worker<Void>(this, SystemRequest.SET_EXT_IMPORT) {
      @Override
      protected Add buildRequest(Add builder) {
        byte[] name = infos.getName().getBytes(StandardCharsets.UTF_8);
        byte[] nameBuffer = new byte[CaesarConstants.NAME_BYTE_LEN];
        System.arraycopy(name, 0, nameBuffer, 0,
            Math.min(CaesarConstants.NAME_BYTE_LEN, name.length));
        return builder.add4Byte(infos.getExtraid()).add2Byte(infos.getId()).add(nameBuffer);
      }
    }.work();
  }

  /**
   * 设置USB透传设备绑定关系.
   */
  public void setUsbBinding(boolean isBind, PortData portData, ExtenderData extData,
                            DataObject data) throws ConfigException, BusyException, DeviceConnectionException {
    LOG.log(Level.FINEST, "sendExtImport");
    new Worker<Void>(this, SystemRequest.SET_USB_DEVICE_BINDING) {
      @Override
      protected Add buildRequest(Add builder) throws ConfigException {
        if (!(data instanceof ConsoleData) && !(data instanceof CpuData)) {
          throw new IllegalArgumentException("The fourth parameter is not CpuData or ConsoleData");
        }
        int type;
        int dataOid;
        ByteArrayOutputStream dataBaos = new ByteArrayOutputStream();
        if (data instanceof ConsoleData) {
          type = isBind ? CaesarConstants.USB_BIND_RX : CaesarConstants.USB_UNBIND_RX;
          dataOid = ((ConsoleData) data).getOid();
          ((ConsoleData) data).writeData(new CfgWriter(dataBaos));
        } else {
          type = isBind ? CaesarConstants.USB_BIND_TX : CaesarConstants.USB_UNBIND_TX;
          dataOid = ((CpuData) data).getOid();
          ((CpuData) data).writeData(new CfgWriter(dataBaos));
        }
        ByteArrayOutputStream portBaos = new ByteArrayOutputStream();
        portData.writeData(new CfgWriter(portBaos));
        ByteArrayOutputStream extBaos = new ByteArrayOutputStream();
        extData.writeData(new CfgWriter(extBaos));
        return builder.add1Byte(type).add2Byte(portData.getOid()).add(portBaos.toByteArray())
            .add2Byte(extData.getOid()).add(extBaos.toByteArray()).add2Byte(dataOid)
            .add(dataBaos.toByteArray());
      }
    }.work();
  }

  /**
   * 获取VP备份数据.
   *
   * @param offset 偏移地址 offset+length要小于256k
   * @param length 读取的长度
   */
  public byte[] getVpBackup(int offset, int length)
      throws ConfigException, BusyException, IOException {
    LOG.log(Level.FINEST, "getVpBackup");
    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    final int bufferSize = 128;
    while (length > 128) {
      baos.write(getVpBackup(offset, bufferSize));
      offset += bufferSize;
      length -= bufferSize;
    }

    if (length > 0) {
      final int finalOffset = offset;
      final int finalLength = length;
      byte[] result =
          new Worker<byte[]>(this, CaesarControllerConstants.SystemRequest.GET_VPBACKUP_DATA) {
            @Override
            protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
              builder.add4Byte(finalOffset);
              builder.add2Byte(finalLength);
              return builder;
            }

            @Override
            protected byte[] getResponse(CfgReader cfgReader) throws ConfigException {
              // offset
              cfgReader.readInteger();
              // length
              cfgReader.read2ByteValue();
              return cfgReader.readByteArray();
            }
          }.work();

      baos.write(result);
    }

    return baos.toByteArray();
  }

  /**
   * .
   */
  public void setScenarioData(int index, byte[] datas)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "setScenarioData");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.SET_SCENARIO_DATA) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        paramAdd.add2Byte(index);
        paramAdd.add(datas);
        return paramAdd;
      }

    }.work();
  }

  /**
   * .
   */
  public byte[] getScenarioData(int index, int contentMask)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getScenarioData");
    return new Worker<byte[]>(this, CaesarControllerConstants.SystemRequest.GET_SCENARIO_DATA) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        paramAdd.add2Byte(index);
        paramAdd.add2Byte(contentMask);
        return paramAdd;
      }

      @Override
      protected byte[] getResponse(CfgReader cfgReader) throws ConfigException {
        int readedIndex = cfgReader.read2ByteValue();
        if (readedIndex != index) {
          throw new IllegalArgumentException("Index does not match!");
        }
        return cfgReader.readByteArray();
      }
    }.work();
  }

  /**
   * .
   */
  public void deleteScenarioData(int index)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "deleteScenarioData");

    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.DELETE_SCENARIO_DATA) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        paramAdd.add2Byte(index);
        return paramAdd;
      }

    }.work();
  }

  /**
   * .
   */
  public Collection<ScenarioDescribeData> getScenarioList()
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "ScenarioDescribeData");
    return new Worker<List<ScenarioDescribeData>>(this,
        CaesarControllerConstants.SystemRequest.GET_SCENARIO_LIST) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        return paramAdd;
      }

      @Override
      protected List<ScenarioDescribeData> getResponse(CfgReader cfgReader)
          throws IOException, ConfigException {
        List<ScenarioDescribeData> list = new ArrayList<>();
        int count = cfgReader.read2ByteValue();
        for (int i = 0; i < count; i++) {
          ScenarioDescribeData data = new ScenarioDescribeData();
          byte[] bytes = cfgReader.readByteArray(data.size());
          if (bytes.length != data.size()) {
            throw new IllegalArgumentException("Data's length does not match");
          }
          data.read(new ByteArrayInputStream(bytes));
          list.add(data);
        }
        return list;
      }
    }.work();
  }

  /**
   * .
   */
  public void startScenario(int index, int offset)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "startScenario");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.START_SCENARIO) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        paramAdd.add2Byte(index);
        paramAdd.add4Byte(offset);
        return paramAdd;
      }
    }.work();
  }

  /**
   * 开始更新视频墙.
   */
  public void beginUpdateVideoWall()
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "beginUpdateVideoWall");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.BEGIN_UPDATE_VW) {
      @Override
      protected Add buildRequest(Add paramAdd) {
        return paramAdd;
      }
    }.work();
  }

  /**
   * 结束更新视频墙.
   */
  public void endUpdateVideoWall()
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "beginUpdateVideoWall");
    new Worker<Void>(this, CaesarControllerConstants.SystemRequest.END_UPDATE_VW) {
      @Override
      protected Add buildRequest(Add paramAdd) {
        return paramAdd;
      }
    }.work();
  }

  /**
   * 电源pdu控制指令.
   */
  public void switchPowerControl(CpuData cpuData, int mode)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "switchPowerControl");
    new Worker<Void>(this, SwitchRequest.SWITCH_POWERPDU_CONTROL) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        paramAdd.add2Byte(cpuData.getId());
        paramAdd.add1Byte(mode);
        paramAdd.add1Byte(cpuData.getPduPort());
        for (byte num : cpuData.getPduIp()) {
          paramAdd.add1Byte(num);
        }
        return paramAdd;
      }
    }.work();
  }

  /**
   * 切换多画面的连接.
   *
   * @param cpuData  TX
   * @param conData  RX
   * @param channel  通道索引
   * @param mode     连接模式
   * @param hdmiPort HDMI选择
   */
  public void switchMultiviewTx(CpuData cpuData, ConsoleData conData, int channel, int mode,
                                int hdmiPort)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "switchMultiviewTx");
    new Worker<Void>(this, SwitchRequest.SWITCH_MULTIVIEW_TX) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        paramAdd.add2Byte(mode);
        paramAdd.add2Byte(conData.getId());
        paramAdd.add1Byte(channel);
        if (cpuData != null) {
          paramAdd.add2Byte(cpuData.getId());
        } else {
          paramAdd.add2Byte(0);
        }
        paramAdd.add1Byte(hdmiPort);
        return paramAdd;
      }
    }.work();
  }

  /**
   * 切换多画面的所有视频连接，TX排列必须是4K60信号在前.
   */
  public void switchMultiviewAllVideo(ConsoleData conData, List<SwitchMultiviewAllVideoChannel> channels)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "switchMultiviewAllVideo");
    new Worker<Void>(this, SwitchRequest.SWITCH_MULTIVIEW_ALL_VIDEO) {

      @Override
      protected Add buildRequest(Add paramAdd) {
        paramAdd.add2Byte(conData.getId());
        for (SwitchMultiviewAllVideoChannel channel : channels) {
          paramAdd.add2Byte(channel.getCpuData() == null ? 0 : channel.getCpuData().getId());
          paramAdd.add1Byte(channel.getSourceIndex());
        }
        // 补齐8个通道
        if (channels.size() < 8) {
          for (int i = channels.size(); i < 8; i++) {
            paramAdd.add2Byte(0);
            paramAdd.add1Byte(0);
          }
        }
        return paramAdd;
      }
    }.work();
  }

  private void convertToBitMap(Map<Integer, Boolean> bitmap, byte[] bytes, int maxCount) {
    int index = 0;
    for (byte b : bytes) {
      for (int i = 0; i < 8; i++) {
        bitmap.put(index, Utilities.areBitsSet(b, true, 1 << i));
        index++;
        if (index >= maxCount) {
          return;
        }
      }
    }
  }

  /**
   * 获取激活的Tx.
   */
  public Map<Integer, Boolean> getActiveTx()
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getActiveTx");
    return new Worker<Map<Integer, Boolean>>(this, SystemRequest.GET_ACTIVE_TX) {
      @Override
      protected Add buildRequest(Add builder) {
        return builder;
      }

      @Override
      protected Map<Integer, Boolean> getResponse(CfgReader cfgReader) throws ConfigException {
        byte[] bytes = cfgReader.readByteArray(CaesarConstants.ACTIVE_TX_BUF_SIZE);
        cfgReader.readByteValue();
        Map<Integer, Boolean> activeMap =
            new HashMap<>(CaesarConstants.ACTIVE_TX_BUF_SIZE * CaesarConstants.BIT_SIZE);
        convertToBitMap(activeMap, bytes, switchDataModel.getConfigMetaData().getCpuCount());
        return activeMap;
      }
    }.work();
  }

  /**
   * 获取激活的Rx.
   */
  public Map<Integer, Boolean> getActiveRx()
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getActiveRx");
    return new Worker<Map<Integer, Boolean>>(this, SystemRequest.GET_ACTIVE_RX) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder;
      }

      @Override
      protected Map<Integer, Boolean> getResponse(CfgReader cfgReader) throws ConfigException {
        byte[] bytes = cfgReader.readByteArray(CaesarConstants.ACTIVE_RX_BUF_SIZE);
        cfgReader.readByteValue();
        Map<Integer, Boolean> activeMap =
            new HashMap<>(CaesarConstants.ACTIVE_RX_BUF_SIZE * CaesarConstants.BIT_SIZE);
        convertToBitMap(activeMap, bytes, switchDataModel.getConfigMetaData().getConsoleCount());
        return activeMap;
      }
    }.work();
  }

  /**
   * 获取激活的外设.
   */
  public Map<Integer, Boolean> getActiveExt()
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getActiveExt");
    return new Worker<Map<Integer, Boolean>>(this, SystemRequest.GET_ACTIVE_EXT) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder;
      }

      @Override
      protected Map<Integer, Boolean> getResponse(CfgReader cfgReader) throws ConfigException {
        byte[] bytes = cfgReader.readByteArray(CaesarConstants.ACTIVE_EXT_BUF_SIZE);
        cfgReader.readByteValue();
        Map<Integer, Boolean> activeMap =
            new HashMap<>(CaesarConstants.ACTIVE_EXT_BUF_SIZE * CaesarConstants.BIT_SIZE);
        convertToBitMap(activeMap, bytes, switchDataModel.getConfigMetaData().getExtenderCount());
        return activeMap;
      }
    }.work();
  }

  /**
   * 获取激活的用户.
   */
  public Map<Integer, Boolean> getActiveUser()
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getActiveUser");
    return new Worker<Map<Integer, Boolean>>(this, SystemRequest.GET_ACTIVE_USER) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder;
      }

      @Override
      protected Map<Integer, Boolean> getResponse(CfgReader cfgReader) throws ConfigException {
        byte[] bytes = cfgReader.readByteArray(CaesarConstants.ACTIVE_USER_BUF_SIZE);
        cfgReader.readByteValue();
        Map<Integer, Boolean> activeMap =
            new HashMap<>(CaesarConstants.ACTIVE_USER_BUF_SIZE * CaesarConstants.BIT_SIZE);
        convertToBitMap(activeMap, bytes, switchDataModel.getConfigMetaData().getUserCount());
        return activeMap;
      }
    }.work();
  }

  /**
   * 获取激活的用户组.
   */
  public Map<Integer, Boolean> getActiveUserGroup()
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getActiveUserGroup");
    return new Worker<Map<Integer, Boolean>>(this, SystemRequest.GET_ACTIVE_USER_GROUP) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder;
      }

      @Override
      protected Map<Integer, Boolean> getResponse(CfgReader cfgReader) throws ConfigException {
        byte[] bytes = cfgReader.readByteArray(CaesarConstants.ACTIVE_USER_GROUP_BUF_SIZE);
        cfgReader.readByteValue();
        Map<Integer, Boolean> activeMap =
            new HashMap<>(CaesarConstants.ACTIVE_USER_GROUP_BUF_SIZE * CaesarConstants.BIT_SIZE);
        convertToBitMap(activeMap, bytes, switchDataModel.getConfigMetaData().getUserGroupCount());
        return activeMap;
      }
    }.work();
  }

  /**
   * 获取激活的Multiscreen.
   */
  public Map<Integer, Boolean> getActiveMultiscreen()
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getActiveMultiscreen");
    return new Worker<Map<Integer, Boolean>>(this, SystemRequest.GET_ACTIVE_MULTISCREEN) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder;
      }

      @Override
      protected Map<Integer, Boolean> getResponse(CfgReader cfgReader) {
        byte[] bytes = cfgReader.readByteArray(CaesarConstants.ACTIVE_MULTISCREEN_SIZE);
        Map<Integer, Boolean> activeMap =
            new HashMap<>(CaesarConstants.ACTIVE_MULTISCREEN_SIZE * CaesarConstants.BIT_SIZE);
        convertToBitMap(activeMap, bytes,
            switchDataModel.getConfigMetaData().getMultiScreenCount());
        return activeMap;
      }
    }.work();
  }

  /**
   * 获取激活的MACRO.
   */
  public Map<Integer, Boolean> getActiveMacro()
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getActiveMacro");
    return new Worker<Map<Integer, Boolean>>(this, SystemRequest.GET_ACTIVE_MACRO) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder;
      }

      @Override
      protected Map<Integer, Boolean> getResponse(CfgReader cfgReader) throws ConfigException {
        byte[] bytes = cfgReader.readByteArray(CaesarConstants.ACTIVE_MACRO_BUF_SIZE);
        cfgReader.readByteValue();
        Map<Integer, Boolean> activeMap =
            new HashMap<>(CaesarConstants.ACTIVE_MACRO_BUF_SIZE * CaesarConstants.BIT_SIZE);
        convertToBitMap(activeMap, bytes,
            switchDataModel.getConfigMetaData().getFunctionKeyCount());
        return activeMap;
      }
    }.work();
  }

  /**
   * 获取激活的级联配置.
   */
  public Map<Integer, Boolean> getActiveGrid()
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getActiveGrid");
    return new Worker<Map<Integer, Boolean>>(this, SystemRequest.GET_ACTIVE_GRID) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder;
      }

      @Override
      protected Map<Integer, Boolean> getResponse(CfgReader cfgReader) throws ConfigException {
        byte[] bytes = cfgReader.readByteArray(CaesarConstants.ACTIVE_GRID_BUF_SIZE);
        cfgReader.readByteValue();
        Map<Integer, Boolean> activeMap =
            new HashMap<>(CaesarConstants.ACTIVE_GRID_BUF_SIZE * CaesarConstants.BIT_SIZE);
        convertToBitMap(activeMap, bytes, switchDataModel.getConfigMetaData().getMatrixCount());
        return activeMap;
      }
    }.work();
  }

  /**
   * 获取激活的TxRx分组.
   */
  public Map<Integer, Boolean> getActiveTxRxGroup()
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getActiveTxRxGroup");
    return new Worker<Map<Integer, Boolean>>(this, SystemRequest.GET_ACTIVE_TX_RX_GROUP) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder;
      }

      @Override
      protected Map<Integer, Boolean> getResponse(CfgReader cfgReader) {
        byte[] bytes = cfgReader.readByteArray(CaesarConstants.ACTIVE_TX_RX_GROUP_BUF_SIZE);
        Map<Integer, Boolean> activeMap =
            new HashMap<>(CaesarConstants.ACTIVE_TX_RX_GROUP_BUF_SIZE * CaesarConstants.BIT_SIZE);
        convertToBitMap(activeMap, bytes, switchDataModel.getConfigMetaData().getTxRxGroupCount());
        return activeMap;
      }
    }.work();
  }

  /**
   * 获取双主控状态.
   */
  public MatrixStatus getMatrixActiveState()
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "getMatrixActiveState");
    return new Worker<MatrixStatus>(this, BroadcastRequest.MATRIX_DOUBLEBACKUP_STATUS) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder;
      }

      @Override
      protected MatrixStatus getResponse(CfgReader cfgReader) throws ConfigException {
        int matrixStatus = cfgReader.readInteger();
        return MatrixStatus.builder().active((matrixStatus & 0x01) == 1)
            .backup((matrixStatus >> 1 & 0x01) == 1).doubleBackup((matrixStatus >> 2 & 0x01) == 1)
            .build();
      }
    }.work();
  }

  /**
   * 双主控重启.
   */
  public void dualMatrixReboot()
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "dualMatrixReboot");
    new Worker<Void>(this, SystemRequest.MATRIX_OPPOSITE_REBOOT) {
      @Override
      protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
        return builder;
      }
    }.work();
  }

  /**
   * 获取光模信息.
   */
  public List<OpticalModuleInfo> getOpticalModulesInfo()
      throws ConfigException, BusyException, DeviceConnectionException {
    LOG.log(Level.FINEST, "getOpticalModulesInfo");
    int requestStep = 5000;
    final int[] requestOffset = {0};
    final boolean[] isEnd = {false};
    final ByteArrayOutputStream baos = new ByteArrayOutputStream();
    while (!isEnd[0]) {
      new Worker<Void>(this, SystemRequest.GET_SFP_INFO) {

        @Override
        protected Add buildRequest(Add paramAdd) {
          isEnd[0] = requestOffset[0] % requestStep != 0;
          return paramAdd.add4Byte(requestOffset[0]).add4Byte(requestStep);
        }

        @Override
        protected Void getResponse(CfgReader cfgReader) throws ConfigException, IOException {
          int offset = cfgReader.readInteger();
          int len = cfgReader.readInteger();
          requestOffset[0] = offset + len;
          baos.write(cfgReader.readByteArray());
          return null;
        }
      }.work();
    }
    List<OpticalModuleInfo> list = new ArrayList<>();
    int baosSize = baos.size();
    int mod = baosSize % OpticalModuleInfo.SIZE;
    int size = baosSize / OpticalModuleInfo.SIZE;
    if (mod != 0) {
      throw new IllegalArgumentException("The data is not a multiple of " + OpticalModuleInfo.SIZE);
    }
    CfgReader reader = new CfgReader(new ByteArrayInputStream(baos.toByteArray()));
    for (int i = 0; i < size; i++) {
      OpticalModuleInfo data = new OpticalModuleInfo();
      data.readData(reader);
      list.add(data);
    }
    return list;
  }

  /**
   * 检查用户名是否符合规范.
   */
  public boolean checkUsernameValidity(String userName)
      throws DeviceConnectionException, ConfigException, BusyException {
    LOG.log(Level.FINEST, "checkUsernameValidity");
    return Boolean.TRUE.equals(
        new Worker<Boolean>(this, SystemRequest.CHECK_USERNAME_VALIDITY) {
          @Override
          protected Add buildRequest(Add builder) {
            builder.add(userName);
            return builder;
          }

          @Override
          protected Boolean getResponse(CfgReader cfgReader) throws ConfigException {
            return cfgReader.readByteValue() == 1;
          }
        }.work());
  }

  private static <T extends Oidable> Collection<List<T>> getSequentialObjects(
      Iterable<T> iterable) {
    List<T> objectList = CollectionUtil.getListWithoutNull(CollectionUtil.getSet(iterable));
    if (objectList.isEmpty()) {
      return Collections.emptyList();
    }
    Collection<List<T>> sequentialObjects = new ArrayList<>();

    objectList.sort(Oidable.COMPARATOR_OID);

    List<T> partialList = null;
    for (T object : objectList) {
      if (null == partialList) {
        partialList = new ArrayList<>();
      } else if (object.getOid()
          - partialList.get(partialList.size() - 1).getOid() > 1) {
        sequentialObjects.add(partialList);
        partialList = new ArrayList<>();
      }
      partialList.add(object);
    }
    if (!partialList.isEmpty()) {
      sequentialObjects.add(partialList);
    }
    return sequentialObjects;
  }

  private abstract static class Worker<T> {

    private final CaesarController tc;
    private final CaesarControllerConstants.Request request;

    public Worker(CaesarController tc, CaesarControllerConstants.Request request) {
      this.tc = tc;
      this.request = request;
    }

    public final T work() throws DeviceConnectionException, ConfigException, BusyException {
      Lock lock = this.tc.getLock();

      if (SwingUtilities.isEventDispatchThread()) {
        new Exception().printStackTrace();
      }
      this.tc.ensureConnection(this.tc.getIdentifier());

      lock.lock();
      try {
        IoController.ReadWriteController ioController = this.tc.getIoController();
        CommunicationBuilder.Tra transmitter =
            buildRequest(CommunicationBuilder.newRequestBuilder().setRequest(this.request));
        CommunicationBuilder.Rec receiver = transmitter.transmit(ioController);
        byte[] response;
        if (this.request.isReponse() == CaesarControllerConstants.Request.Response.DATA) {
          response = receiver.getResponse(ioController);
          CfgReader cfgReader = new CfgReader(new ByteArrayInputStream(response));
          boolean done = false;
          try {
            T rsp = getResponse(cfgReader);
            done = true;
            workDone();
            T tt = rsp;
            if (cfgReader.available() > 0) {
              throw new DeviceConnectionException(
                  "There are " + cfgReader.available() + " bytes left to be read!");
            }
            return tt;
          } finally {
            if (done && cfgReader.available() > 0) {
              throw new DeviceConnectionException(
                  "There are " + cfgReader.available() + " bytes left to be read!");
            }
          }
        }
        if (this.request.isReponse() == CaesarControllerConstants.Request.Response.ACK) {
          receiver.checkAcknowledged(ioController);
          workDone();
          return null;
        }
        return null;
      } catch (UnexpectedResponseException ure) {
        if (ure.getUnexpectedByte() == CaesarControllerConstants.BIN_BSY) {
          throw new BusyException();
        }
        if (ure.getUnexpectedByte() == CaesarControllerConstants.BIN_NAK) {
          throw new ConfigException(CfgError.NOT_FOUND, "NAK Received!");
        }
        try {
          while (this.tc.getIoController().available() > 0) {
            this.tc.getIoController().read();
          }
        } catch (IOException ex) {
          LOG.log(Level.WARNING, "Fail to read!", ex);
        }
        throw new DeviceConnectionException("Error! Command failed! " + this.request.name(), ure);
      } catch (IOException ioe) {
        this.tc.disconnect(this.tc.getIdentifier());
        throw new DeviceConnectionException("Error! Command failed! " + this.request.name(), ioe);
      } finally {
        lock.unlock();
      }
    }

    protected abstract CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add paramAdd)
        throws IOException, ConfigException;

    protected T getResponse(CfgReader cfgReader) throws IOException, ConfigException {
      throw new UnsupportedOperationException(
          "Request Object says it expects a data response. So implement it dummy!");
    }

    protected void workDone() {

    }
  }

  private <T extends CaesarCommunicatable & Oidable & CommitRollback> void getCommunicatable(
      CaesarControllerConstants.SystemRequest systemRequest, Iterable<T> requestedComunicatables,
      Collection<T> allCommunicatables, final int objectSize, String objectName)
      throws DeviceConnectionException, ConfigException, BusyException {
    List<T> communicatables = CollectionUtil.getListWithoutNull(requestedComunicatables);

    communicatables.sort(Oidable.COMPARATOR_OID);

    final Map<Integer, T> objectMap = Utilities.getOidMap(allCommunicatables);
    if (!objectMap.values().containsAll(communicatables)) {
      throw new ConfigException(CfgError.CONNECTION_ERROR,
          "Not all requested " + objectName + " can be retrieved");
    }
    ensureConnection(this.switchDataModel.getIdentifier());
    for (List<T> partialList : getSequentialObjects(communicatables)) {
      final List<T> list = new ArrayList<>(partialList);
      if (!list.isEmpty()) {
        list.sort(Oidable.COMPARATOR_OID);
        while (!list.isEmpty()) {
          new Worker<Void>(this, systemRequest) {
            private Collection<T> objects = Collections.emptyList();
            private final int offset = list.get(0).getOid();
            private final int count = Math.min(list.size(), 8183 / objectSize);

            @Override
            protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
              return builder.add2Byte(this.offset)
                  .add2Byte(this.count);
            }

            @Override
            protected Void getResponse(CfgReader cfgReader) throws ConfigException {
              int rspOffset = cfgReader.read2ByteValue();
              int rspCount = cfgReader.read2ByteValue();
              Collection<T> tcs = new ArrayList<>(rspCount);
              if (rspOffset != this.offset) {
                throw new IllegalArgumentException("Offset does not match!");
              }
              if (rspCount != this.count) {
                throw new IllegalArgumentException("Count does not match!");
              }
              for (int i = 0; i < this.count; i++) {
                T teraCommunicatable = objectMap.get(this.offset + i);
                teraCommunicatable.readData(cfgReader);
                list.remove(teraCommunicatable);
                tcs.add(teraCommunicatable);
              }
              this.objects = tcs;
              return null;
            }

            @Override
            protected void workDone() {
              for (T t : this.objects) {
                t.commit(CaesarController.CONTROLLER_THRESHOLD);
              }
            }
          }.work();
        }
      }
    }
  }

  private <T extends CaesarCommunicatable & Oidable & CommitRollback> void getCommunicatableByConfigData(
      CaesarControllerConstants.SystemRequest systemRequest, Iterable<T> requestedComunicatables,
      Collection<T> allCommunicatables, final int mainOffset, final int objectSize,
      String objectName) throws DeviceConnectionException, ConfigException, BusyException {
    if (objectSize <= 0) {
      throw new ConfigException(CfgError.UNKNOWN_ERROR,
          objectName + "'s object size is " + objectSize + "!");
    }
    List<T> communicatables = CollectionUtil.getListWithoutNull(requestedComunicatables);

    communicatables.sort(Oidable.COMPARATOR_OID);

    final Map<Integer, T> objectMap = Utilities.getOidMap(allCommunicatables);
    if (!objectMap.values().containsAll(communicatables)) {
      throw new ConfigException(CfgError.CONNECTION_ERROR,
          "Not all requested " + objectName + " can be retrieved");
    }
    ensureConnection(this.switchDataModel.getIdentifier());
    for (List<T> partialList : getSequentialObjects(communicatables)) {
      final List<T> list = new ArrayList<>(partialList);
      if (!list.isEmpty()) {
        list.sort(Oidable.COMPARATOR_OID);
        while (!list.isEmpty()) {
          new Worker<Void>(this, systemRequest) {
            private Collection<T> objects = Collections.emptyList();
            private final int offset = list.get(0).getOid();
            private final int count = Math.min(list.size(), 8183 / objectSize);
            private final int offsetAddr = offset * objectSize + mainOffset;
            private final int dataCount = count * objectSize;

            @Override
            protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder) {
              return builder.add4Byte(this.offsetAddr)
                  .add4Byte(this.dataCount);
            }

            @Override
            protected Void getResponse(CfgReader cfgReader) throws ConfigException {
              int rspOffset = cfgReader.readInteger();
              int rspCount = cfgReader.readInteger();

              if (rspOffset != this.offsetAddr) {
                throw new IllegalArgumentException("Offset does not match!");
              }
              if (rspCount != this.dataCount) {
                throw new IllegalArgumentException("Count does not match!");
              }
              Collection<T> tcs = new ArrayList<>(count);
              for (int i = 0; i < count; i++) {
                T teraCommunicatable = objectMap.get(this.offset + i);
                teraCommunicatable.readData(cfgReader);
                list.remove(teraCommunicatable);
                tcs.add(teraCommunicatable);
              }
              objects = tcs;

              return null;
            }

            @Override
            protected void workDone() {
              for (T t : this.objects) {
                t.commit(CaesarController.CONTROLLER_THRESHOLD);
              }
            }
          }.work();
        }
      }
    }
  }

  private <T extends CaesarCommunicatable & Oidable & CommitRollback> void setCommunicatable(
      CaesarControllerConstants.SystemRequest systemRequest, Iterable<T> requestedComunicatables,
      final int objectSize, String objectName)
      throws DeviceConnectionException, ConfigException, BusyException {
    List<T> communicatables = CollectionUtil.getListWithoutNull(requestedComunicatables);

    communicatables.sort(Oidable.COMPARATOR_OID);
    LOG.log(Level.FINE, "Set communiatable : " + objectName);
    ensureConnection(this.switchDataModel.getIdentifier());
    for (List<T> partialList : getSequentialObjects(communicatables)) {
      final List<T> list = new ArrayList<>(partialList);
      if (!list.isEmpty()) {

        list.sort(Oidable.COMPARATOR_OID);
        while (!list.isEmpty()) {
          new Worker<Void>(this, systemRequest) {
            private Collection<T> objects = Collections.emptyList();

            @Override
            protected CommunicationBuilder.Add buildRequest(CommunicationBuilder.Add builder)
                throws ConfigException {
              int offset = list.get(0).getOid();
              ByteArrayOutputStream baos = new ByteArrayOutputStream();
              Collection<T> tcs = new ArrayList<>(list.size());
              Iterator<T> iterator = list.iterator();

              int count = 0;
              while (iterator.hasNext()
                  && baos.size() < CaesarControllerConstants.BUFFER_SIZE - objectSize - 9) {
                T teraCommunicatable = iterator.next();

                teraCommunicatable.writeData(new CfgWriter(baos));
                tcs.add(teraCommunicatable);
                iterator.remove();
                count++;
              }
              this.objects = tcs;

              return builder.add2Byte(offset).add2Byte(count).add(baos.toByteArray());
            }

            @Override
            protected void workDone() {
              for (T t : this.objects) {
                t.commit(CaesarController.CONTROLLER_THRESHOLD);
              }
            }
          }.work();
        }
      }
    }
  }
}

