<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.mc.tool.caesar</groupId>
    <artifactId>mc-caesar-build</artifactId>
    <version>${revision}</version>
  </parent>
  <artifactId>mc-caesar-api</artifactId>

  <name>mc-caesar-api</name>
  <url>https://maven.apache.org</url>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.jxr.version>3.1.1</maven.jxr.version>
    <maven.checkstyle.version>3.1.2</maven.checkstyle.version>
    <maven.pmd.version>3.9.0</maven.pmd.version>
    <maven.findbugs.version>3.0.5</maven.findbugs.version>
    <lombok.version>1.18.26</lombok.version>
    <slf4j.version>2.0.7</slf4j.version>
    <maven.compiler.version>3.9.0</maven.compiler.version>
    <gson.version>2.10.1</gson.version>
    <commons-net.version>3.9.0</commons-net.version>
    <easyexcel.version>3.2.1</easyexcel.version>
  </properties>
  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>${maven.jxr.version}</version>
      </plugin>
    </plugins>
  </reporting>
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven.compiler.version}</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <encoding>utf-8</encoding>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>2.5</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>${maven.checkstyle.version}</version>
        <dependencies>
          <dependency>
            <groupId>com.puppycrawl.tools</groupId>
            <artifactId>checkstyle</artifactId>
            <version>9.0</version>
          </dependency>
        </dependencies>
        <configuration>
          <configLocation>../google_checks9.0.1.xml</configLocation>
          <encoding>UTF-8</encoding>
          <failsOnError>true</failsOnError>
          <failOnViolation>true</failOnViolation>
          <violationSeverity>info</violationSeverity>
        </configuration>
        <executions>
          <execution>
            <id>checkstyle</id>
            <phase>validate</phase>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <failOnViolation>true</failOnViolation>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>${maven.pmd.version}</version>
        <configuration>
          <analysisCache>true</analysisCache>
          <linkXRef>true</linkXRef>
        </configuration>
        <executions>
          <execution>
            <id>pmd</id>
            <phase>compile</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>${maven.findbugs.version}</version>
        <configuration>
          <effort>Max</effort>
          <threshold>Low</threshold>
          <xmlOutput>true</xmlOutput>
        </configuration>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <dependencies>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>annotations</artifactId>
      <version>3.0.1u2</version>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>${gson.version}</version>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok.version}</version>
    </dependency>
    <dependency>
      <groupId>commons-net</groupId>
      <artifactId>commons-net</artifactId>
      <version>${commons-net.version}</version>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>32.1.1-jre</version>
    </dependency>
    <dependency>
      <groupId>com.mc</groupId>
      <artifactId>mc-common</artifactId>
      <version>${mc.common.version}</version>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <version>${easyexcel.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.apache.commons</groupId>
          <artifactId>commons-compress</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId><!-- Note: For easyexcel, Replace new compress version -->
      <artifactId>commons-compress</artifactId>
      <version>1.26.2</version>
    </dependency>
  </dependencies>
</project>
