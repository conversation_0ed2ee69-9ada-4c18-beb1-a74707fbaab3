<?xml version="1.0" encoding="UTF-8"?>

<?import com.mc.tool.caesar.vpm.pages.operation.view.CaesarOperationFuncPane?>
<?import com.mc.tool.framework.operation.view.VideoSourceTree?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<fx:root id="root" prefHeight="720" prefWidth="1280"
  stylesheets="@multiscreen_func_panel.css"
  type="com.mc.tool.caesar.vpm.pages.operation.view.CaesarOperationFuncPane"
  xmlns:fx="http://javafx.com/fxml/1">

  <toolBoxContent>
    <FXCollections fx:factory="observableArrayList">
      <HBox HBox.Hgrow="ALWAYS" alignment="CENTER" styleClass="color-box">
        <Region id="fullRegion" fx:id="fullRegion" maxHeight="13.0" maxWidth="13.0"
          prefHeight="13.0" prefWidth="13.0"/>
        <Label text="%multiscreen.full_conn"/>
        <Region prefWidth="43"/>
        <Region id="videoRegion" fx:id="videoRegion" maxHeight="13.0" maxWidth="13.0"
          prefHeight="13.0" prefWidth="13.0"/>
        <Label text="%multiscreen.video_conn"/>
      </HBox>
      <Button id="menu-btn" fx:id="saveScenarioBtn" onAction="#onSaveScenario"
        styleClass="image-button" text="%toolbar.save_scenario"/>
      <Button id="menu-btn" onAction="#onSaveAsScenario"
        styleClass="image-button" text="%toolbar.save_as_scenario"/>
      <Button id="switch-btn" onAction="#onSwitch" styleClass="image-button"
        text="%toolbar.meeting_room"/>
    </FXCollections>
  </toolBoxContent>

  <sourceListContent>
    <FXCollections fx:factory="observableArrayList">
      <HBox alignment="CENTER_LEFT"
        prefHeight="23.0" styleClass="titleBox">
        <Label text="%cross_screen.source_list_title" styleClass="titleLabel"/>
      </HBox>
      <VideoSourceTree fx:id="sourceTree" VBox.vgrow="ALWAYS"/>
      <VBox fx:id="videoPreviewContainer" minHeight="139"/>
      <Region minHeight="1"/>
    </FXCollections>
  </sourceListContent>

  <propertyContent>
    <FXCollections fx:factory="observableArrayList">
      <Region minHeight="1" styleClass="seperator"/>
      <HBox alignment="CENTER_LEFT" prefHeight="23.0" styleClass="right-title">
        <Label text="%cross_screen.setting"/>
      </HBox>
      <Region minHeight="1" styleClass="seperator"/>
      <HBox styleClass="property-box">
        <Label text="%cross_screen.name"/>
        <TextField fx:id="multiScreenName"/>
      </HBox>
      <Region minHeight="1" styleClass="seperator"/>
      <VBox styleClass="property-box">
        <Label text="%multiscreen.layout"/>
        <HBox styleClass="property-box">
          <Label text="%multiscreen.row" minWidth="12"/>
          <TextField fx:id="multiscreenRow"/>
        </HBox>
        <HBox styleClass="property-box">
          <Label text="%multiscreen.column" minWidth="12"/>
          <TextField fx:id="multiscreenColumn"/>
        </HBox>
      </VBox>
      <Region minHeight="1" styleClass="seperator"/>
      <HBox styleClass="property-box">
      </HBox>
      <Region minHeight="1" styleClass="seperator"/>
      <VBox styleClass="property-box">
        <Label text="%multiscreen.rx_list"/>
        <TableView fx:id="rxList">
          <columns>
            <TableColumn text="%cross_screen.rx_list.index" fx:id="rxListIndexColumn"
              sortable="false"/>
            <TableColumn text="%cross_screen.rx_list.name" fx:id="rxListNameColumn"
              sortable="false"/>
          </columns>
          <columnResizePolicy>
            <TableView fx:constant="CONSTRAINED_RESIZE_POLICY"/>
          </columnResizePolicy>
        </TableView>
      </VBox>
      <Region VBox.Vgrow="ALWAYS"/>

    </FXCollections>
  </propertyContent>
</fx:root>
