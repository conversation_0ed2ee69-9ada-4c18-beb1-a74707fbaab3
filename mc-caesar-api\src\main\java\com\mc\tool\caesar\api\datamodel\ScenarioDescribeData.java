package com.mc.tool.caesar.api.datamodel;

import com.mc.tool.caesar.api.io.NormalStruct;

/**
 * 预案描述数据.
 *
 * <AUTHOR>
 */
public class ScenarioDescribeData extends NormalStruct {

  private Unsigned16 index = new Unsigned16();
  private Utf8String name = new Utf8String(32);
  private Unsigned16 horizCount = new Unsigned16();
  private Unsigned16 vertCount = new Unsigned16();

  public int getIndex() {
    return index.get();
  }

  public void setIndex(int index) {
    this.index.set((short) index);
  }

  public String getName() {
    return name.get();
  }

  public void setName(String name) {
    this.name.set(name);
  }

  public int getHorizCount() {
    return horizCount.get();
  }

  public void setHorizCount(int horizCount) {
    this.horizCount.set(horizCount);
  }

  public int getVertCount() {
    return vertCount.get();
  }

  public void setVertCount(int vertCount) {
    this.vertCount.set(vertCount);
  }

  @Override
  public boolean isPacked() {
    return true;
  }
}
