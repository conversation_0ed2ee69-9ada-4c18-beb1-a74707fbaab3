package com.mc.tool.caesar.vpm.pages.hotkeyconfiguration;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants.FunctionKey.Cmd.Command;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.macrokey.view.MacroKeyView;
import com.mc.tool.framework.interfaces.DeviceControllable;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javafx.application.Platform;

/**
 * .
 */
public class UserMacroKeyView extends MacroKeyView<UserData> {

  /** constructor. */
  public UserMacroKeyView() {
    copyToRxBtn.setText(CaesarI18nCommonResource.getString("macro_key.copy_to_other_user"));
    sourceHeader.set(CaesarI18nCommonResource.getString("copy_macro_dialog.user_source_header"));
    targetHeader.set(CaesarI18nCommonResource.getString("copy_macro_dialog.user_target_header"));
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    super.setDeviceController(deviceController);
    if (this.deviceController != null) {
      sourceRawList.setAll(
          this.deviceController.getDataModel().getConfigDataManager().getActiveUsers());
      updateDeviceDataList();

      this.deviceController
          .getDataModel()
          .addPropertyChangeListener(
              new String[] {
                FunctionKeyData.PROPERTY_HOST_SUBSCRIPT,
                FunctionKeyData.PROPERTY_HOTKEY,
                FunctionKeyData.PROPERTY_KEYLINE,
                FunctionKeyData.PROPERTY_MACROCMD,
                FunctionKeyData.PROPERTY_MACROTYPE,
                FunctionKeyData.PROPERTY_STATUS,
                FunctionKeyData.PROPERTY_MACRO_INTPARAM,
                FunctionKeyData.PROPERTY_MACRO_STRPARAM,
                UserData.PROPERTY_STATUS,
                FunctionKeyData.PROPERTY_MACRO_NAME
              },
              weakAdapter.wrap(
                  (PropertyChangeListener)
                      evt -> PlatformUtility.runInFxThread(this::updateDeviceDataList)));

      this.deviceController
          .getDataModel()
          .addPropertyChangeListener(
              UserData.PROPERTY_STATUS,
              weakAdapter.wrap(
                  (PropertyChangeListener)
                      evt ->
                          Platform.runLater(
                              () ->
                                  sourceRawList.setAll(
                                      this.deviceController.getDataModel()
                                          .getConfigDataManager().getActiveUsers()))));
    }
  }

  @Override
  protected Collection<FunctionKeyData> getFunctionKeyDatasForSource(UserData userData) {
    List<FunctionKeyData> newList = new ArrayList<>();
    for (FunctionKeyData functionKeyData :
        deviceController.getDataModel().getConfigData().getFunctionKeyDatas()) {
      try {
        Command.valueOf(functionKeyData.getMacroCmd());
      } catch (Exception exception) {
        continue;
      }

      if (functionKeyData.isConsole()) {
        continue;
      }

      if (functionKeyData.getHostSubscript() == userData.getOid() + 1) {
        newList.add(functionKeyData);
      }
    }
    return newList;
  }

  @Override
  protected String getSourceListTitle() {
    return CaesarI18nCommonResource.getString("macro_key.user_source_list_title");
  }

  @Override
  protected String getAccessBlockListTitle() {
    return CaesarI18nCommonResource.getString("macro_key.user_access_block_title");
  }
}
