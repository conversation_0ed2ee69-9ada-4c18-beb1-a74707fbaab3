package com.mc.tool.caesar.api.datamodel;

import static org.junit.Assert.assertEquals;

import com.mc.tool.caesar.api.datamodel.VideoWallGroupData.VideoWallData;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import org.junit.Assert;
import org.junit.Test;

/**
 * .
 */
public class VideoWallGroupDataTest {

  @Test
  public void testVideoWallData() {
    VideoWallGroupData videoWallGroupData = new VideoWallGroupData();
    VideoWallData data = videoWallGroupData.getData(1);
    data.setRows(2);
    data.setColumns(3);
    data.setVideoCnt(4);
    int[] ranges = data.getValidDataRange();

    assertEquals(4, ranges.length);
    assertEquals(220, ranges[0]);
    assertEquals(96, ranges[1]);
    assertEquals(3420, ranges[2]);
    assertEquals(192, ranges[3]);

    ranges = data.getBaseDataRange();
    assertEquals(2, ranges.length);
    assertEquals(0, ranges[0]);
    assertEquals(220, ranges[1]);
  }


}
