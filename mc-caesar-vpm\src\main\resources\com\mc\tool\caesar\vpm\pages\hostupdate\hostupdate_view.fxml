<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ListView?>
<?import javafx.scene.control.TreeTableColumn?>
<?import javafx.scene.control.TreeTableView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>
<fx:root stylesheets="@hostupdate_view.css" type="javafx.scene.layout.VBox"
  xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
  <HBox minHeight="40" prefHeight="40">
  </HBox>
  <Region minHeight="1" styleClass="seperator"/>
  <VBox id="main-container" VBox.Vgrow="ALWAYS">
    <TreeTableView fx:id="tableView" VBox.Vgrow="ALWAYS">
      <columns>
        <TreeTableColumn fx:id="nameCol" prefWidth="150" text="%HostUpdate.nameCol.text"/>
        <TreeTableColumn fx:id="typeCol" prefWidth="120" text="%HostUpdate.typeCol.text"/>
        <TreeTableColumn fx:id="portsCol" prefWidth="71.0" text="%HostUpdate.portCol.text"/>
        <TreeTableColumn fx:id="serialCol" prefWidth="147.0" text="%HostUpdate.serialCol.text"/>
        <TreeTableColumn fx:id="hardwareVerCol" prefWidth="50.0"
                         text="%HostUpdate.hardwareVerCol.text"/>
        <TreeTableColumn fx:id="currentVersionCol" prefWidth="97.0"
          text="%HostUpdate.currentVersionCol.text"/>
        <TreeTableColumn fx:id="currentDateCol" prefWidth="120"
          text="%HostUpdate.currentDateCol.text"/>
        <TreeTableColumn fx:id="updateVersionCol" prefWidth="120"
          text="%HostUpdate.updateVersionCol.text"/>
        <TreeTableColumn fx:id="updateDateCol" prefWidth="120"
          text="%HostUpdate.updateDateCol.text"/>
        <TreeTableColumn fx:id="updateCol" prefWidth="120.0" text="%HostUpdate.updateDate.text"/>
        <TreeTableColumn fx:id="progressCol" prefWidth="120.0" text="%HostUpdate.progressCol.text"/>
      </columns>
      <columnResizePolicy>
        <TreeTableView fx:constant="CONSTRAINED_RESIZE_POLICY"/>
      </columnResizePolicy>
      <VBox.margin>
        <Insets/>
      </VBox.margin>
    </TreeTableView>
    <HBox id="tool-box">
      <Button fx:id="loadButton" styleClass="common-button" text="%load_file"/>
      <Label fx:id="updateFilePathLabel" prefWidth="400">
        <font>
          <Font size="17.0"/>
        </font>
      </Label>
      <Region HBox.hgrow="ALWAYS"/>
      <Button fx:id="selectAllButton" styleClass="common-button"
        text="%HostUpdate.seleteAllButton.text">
      </Button>
      <Button fx:id="updateButton" styleClass="common-button" text="%update_btn">
      </Button>
      <Button fx:id="cancelButton" styleClass="common-button" text="%cancel_btn">
      </Button>
    </HBox>

    <HBox id="update-log-title-box">
      <Label id="update-log-title" text="%update_log"/>
    </HBox>

    <ListView fx:id="loglist" prefHeight="200">
    </ListView>

  </VBox>
</fx:root>
