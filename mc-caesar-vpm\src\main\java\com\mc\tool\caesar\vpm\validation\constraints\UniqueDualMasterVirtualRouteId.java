package com.mc.tool.caesar.vpm.validation.constraints;

import com.mc.tool.caesar.vpm.validation.constraints.impl.UniqueDualMasterVirtualRouteIdImpl;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import javax.validation.Constraint;
import javax.validation.Payload;

/**
 * 验证虚拟路由ID不能与其他网络配置的路由ID相同.
 */
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE,
    ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {UniqueDualMasterVirtualRouteIdImpl.class})
public @interface UniqueDualMasterVirtualRouteId {

  /**
   * 获取校验错误的文本.
   *
   * @return 校验文本
   */
  String message() default "{com.mc.tool.caesar.vpm.validation.constraints.unique_route_id.message}";

  /**
   * Groups.
   *
   * @return groups
   */
  Class<?>[] groups() default {};

  /**
   * Payload.
   *
   * @return payloads
   */
  Class<? extends Payload>[] payload() default {};
}
