package com.mc.tool.caesar.vpm.gui.pages.operation.videowall;

import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.caesar.vpm.pages.operation.CaesarOperationPageNew;
import org.junit.Test;
import org.testfx.assertions.api.Assertions;

/**
 * .
 */
public class PublishModeTest extends AppGuiTestBase {

  @Override
  public void beforeAll() {
    login();
  }

  @Test
  public void testSwitchPublishMode() {
    // 切换到系统操作页面
    clickOn("#" + CaesarOperationPageNew.NAME);
    // 选择自动发布
    clickOn(GuiTestConstants.PUBLISH_MODE);
    clickOn(GuiTestConstants.PUBLISH_MODE_AUTO);
    Assertions.assertThat(this.lookup("#publish-btn").queryButton()).isInvisible();
    // 选择手动发布
    clickOn(GuiTestConstants.PUBLISH_MODE);
    clickOn(GuiTestConstants.PUBLISH_MODE_MANUAL);
    Assertions.assertThat(this.lookup("#publish-btn").queryButton()).isVisible();
  }
}
