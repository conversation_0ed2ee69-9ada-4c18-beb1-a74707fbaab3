package com.mc.tool.caesar.vpm.util.vp;

/**
 * .
 */
public class VpVideoPortInfo {
  public int txId;
  public int txIndex;
  public int left;
  public int top;
  public int width;
  public int height;
  public int portIndex;

  @Override
  public int hashCode() {
    return txId ^ txIndex ^ left ^ top ^ width ^ height ^ portIndex;
  }

  @Override
  public boolean equals(Object obj) {
    if (obj instanceof VpVideoPortInfo) {
      VpVideoPortInfo input = (VpVideoPortInfo) obj;
      return input.txId == txId
          && input.txIndex == txIndex
          && input.left == left
          && input.top == top
          && input.width == width
          && input.height == height
          && input.portIndex == portIndex;
    } else {
      return false;
    }
  }
}
