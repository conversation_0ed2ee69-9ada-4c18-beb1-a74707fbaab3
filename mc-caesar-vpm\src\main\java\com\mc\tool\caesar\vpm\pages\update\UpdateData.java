package com.mc.tool.caesar.vpm.pages.update;

import com.mc.tool.caesar.api.datamodel.VersionDef;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.scene.control.CheckBox;
import javafx.scene.control.ProgressBar;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public class UpdateData {

  @Getter @Setter private int id;

  private SimpleStringProperty deviceName = new SimpleStringProperty();
  private SimpleStringProperty name = new SimpleStringProperty();
  private SimpleStringProperty ports = new SimpleStringProperty();
  private SimpleStringProperty type = new SimpleStringProperty();
  private SimpleStringProperty serial = new SimpleStringProperty();
  private SimpleStringProperty hardwareVersionProperty = new SimpleStringProperty();
  private SimpleStringProperty currentDate = new SimpleStringProperty();
  private SimpleStringProperty updateDate = new SimpleStringProperty();
  private SimpleBooleanProperty isDisabled = new SimpleBooleanProperty(true);
  private SimpleBooleanProperty isSelected = new SimpleBooleanProperty(false);
  private SimpleStringProperty currentVersionProperty = new SimpleStringProperty();
  private SimpleStringProperty updateVersionProperty = new SimpleStringProperty();

  private int level1;
  private byte level2;
  private String address;

  private String parentname;

  CheckBox checkBox = new CheckBox("update");
  ProgressBar progressBar = new ProgressBar(0);

  @Getter VersionDef currentVersion;
  @Getter VersionDef updateVersion;
  @Getter VersionDef hardwareVersionDef = null;

  public ProgressBar getProgressBar() {
    return this.progressBar;
  }

  /** . */
  public UpdateData(int level1, byte level2, String address) {
    this.level1 = level1;
    this.level2 = level2;
    this.address = address;
    this.checkBox.setVisible(false);
  }

  public StringProperty getCurrentVersionProperty() {
    return this.currentVersionProperty;
  }

  protected void updateCheckBoxVisibility() {
    checkBox.setVisible(getType() != null && getCurrentVersion() != null);
  }

  /**
   * 设置当前版本.
   *
   * @param currentVersion 当前版本.
   */
  public void setCurrentVersion(VersionDef currentVersion) {
    this.currentVersion = currentVersion;
    if (currentVersion != null) {
      this.currentVersionProperty.set(currentVersion.toString());
    } else {
      this.currentVersionProperty.set("");
    }
    updateCheckBoxVisibility();
  }

  public StringProperty getUpdateVersionProperty() {
    return this.updateVersionProperty;
  }

  /** . */
  public void setUpdateVersion(VersionDef updateVersion) {
    this.updateVersion = updateVersion;
    if (updateVersion != null) {
      this.updateVersionProperty.set(updateVersion.toString());
    } else {
      this.updateVersionProperty.set("");
    }
  }

  public String getAddress() {
    return this.address;
  }

  public void setAddress(String address) {
    this.address = address;
  }

  public int getLevel1() {
    return this.level1;
  }

  public void setLevel1(int level1) {
    this.level1 = level1;
  }

  public byte getLevel2() {
    return this.level2;
  }

  public void setLevel2(byte level2) {
    this.level2 = level2;
  }

  public CheckBox getCheckBox() {
    return this.checkBox;
  }

  public String getName() {
    return name.get();
  }

  public void setName(String name) {
    this.name.set(name);
  }

  public StringProperty getNameProperty() {
    return this.name;
  }

  public String getSerial() {
    return serial.get();
  }

  /**
   * 设置序列号.
   *
   * @param serial 序列号.
   */
  public void setSerial(String serial) {
    this.serial.set(serial);
  }

  public StringProperty getSerialProperty() {
    return this.serial;
  }

  public String getHardwareVersion() {
    return hardwareVersionProperty.get();
  }

  /**
   * 设置硬件版本.
   *
   * @param version 硬件版本
   */
  public void setHardwareVersion(VersionDef version) {
    hardwareVersionDef = version;
    if (isHardwareVersionValid()) {
      hardwareVersionProperty.set(
          String.format("%d.%d", version.getMasterVersion(), version.getSubVersion()));
    } else {
      hardwareVersionProperty.set("0.0");
    }
  }

  public StringProperty getHardwareVersionProperty() {
    return hardwareVersionProperty;
  }

  /**
   * 硬件版本是否有效.
   *
   * @return 如果有效返回true.
   */
  public boolean isHardwareVersionValid() {
    // 旧版本的外设是没有硬件版本的，所以要加以区分
    if (hardwareVersionDef == null) {
      return false;
    }
    if (hardwareVersionDef.getMasterVersion() < 1 || hardwareVersionDef.getMasterVersion() > 20) {
      return false;
    }
    if (hardwareVersionDef.getSubVersion() < 0 || hardwareVersionDef.getSubVersion() > 9) {
      return false;
    }
    return true;
  }

  /**
   * 硬件版本是否匹配.
   *
   * @param version 版本
   */
  public boolean isHardwareVersionMatch(VersionDef version) {
    // 如果当前硬件版本是无效的，那么任何版本都匹配
    if (!isHardwareVersionValid()) {
      return true;
    }
    //
    return hardwareVersionDef.getMasterVersion() == version.getMasterVersion()
        && hardwareVersionDef.getSubVersion() == version.getSubVersion();
  }

  public String getType() {
    return type.get();
  }

  public void setType(String type) {
    this.type.set(type);
    updateCheckBoxVisibility();
  }

  public StringProperty getTypeProperty() {
    return this.type;
  }

  public String getDeviceName() {
    return deviceName.get();
  }

  public void setDeviceName(String device) {
    this.deviceName.set(device);
  }

  public void setDeviceNameProperty(StringProperty deviceProperty) {
    this.deviceName.bind(deviceProperty);
  }

  public StringProperty getDeviceNameProperty() {
    return this.deviceName;
  }

  public String getPorts() {
    return ports.get();
  }

  public void setPorts(String ports) {
    this.ports.set(ports);
  }

  public StringProperty getPortsProperty() {
    return this.ports;
  }

  public Boolean getDisabled() {
    return isDisabled.get();
  }

  public void setDisabled(Boolean enabled) {
    this.isDisabled.set(enabled);
  }

  public String getCurrentDate() {
    return currentDate.get();
  }

  public void setCurrentDate(String date) {
    this.currentDate.set(date);
  }

  public StringProperty getCurrentDateProperty() {
    return this.currentDate;
  }

  public String getUpdateDate() {
    return updateDate.get();
  }

  public void setUpdateDate(String date) {
    this.updateDate.set(date);
  }

  public StringProperty getUpdateDateProperty() {
    return this.updateDate;
  }

  public SimpleBooleanProperty getDisabledProperty() {
    return this.isDisabled;
  }

  public Boolean getSelected() {
    return isSelected.get();
  }

  public void setSelected(Boolean selected) {
    this.isSelected.set(selected);
  }

  public BooleanProperty getSelectedProperty() {
    return this.isSelected;
  }

  public void setParentName(String name) {
    this.parentname = name;
  }

  public String getParentName() {
    return this.parentname;
  }
}
