package com.mc.tool.caesar.vpm.pages.operation;

import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.caesar.vpm.pages.operation.view.CaesarOperationPageViewNew;
import com.mc.tool.framework.interfaces.Page;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.scene.layout.Pane;

/**
 * .
 */
public class CaesarOperationPageNew implements Page {
  public static final String NAME = "operation";
  private final CaesarOperationPageViewNew view;
  private final BooleanProperty visibility = new SimpleBooleanProperty(true);

  public CaesarOperationPageNew(CaesarEntity entity) {
    view = new CaesarOperationPageViewNew(entity.getVisualEditModel());
    view.getControllable().setDeviceController(entity.getController());
  }

  @Override
  public String getTitle() {
    return Bundle.NbBundle.getMessage("OperationPage.title");
  }

  @Override
  public String getName() {
    return NAME;
  }

  @Override
  public Pane getView() {
    return view;
  }

  @Override
  public String getStyleClass() {
    return "caesar-operation-page";
  }

  @Override
  public BooleanProperty getVisibleProperty() {
    return visibility;
  }

  @Override
  public void showObject(Object object) {
    if (object instanceof VisualEditFunc) {
      view.getControllable().switchToFunction(((VisualEditFunc) object).getGuid());
    }
  }

  @Override
  public void close() {
    view.getControllable().close();
  }

  @Override
  public void refreshOnShow() {
    view.getControllable().refreshOnShow();
  }
}
