package com.mc.tool.caesar.vpm.pages.extenderosdupdate;

import com.mc.tool.framework.utility.InjectorProvider;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.fxml.FXMLLoader;
import javafx.scene.layout.VBox;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class ExtenderOsdUpdatePageView extends VBox {

  @Getter private ExtenderOsdUpdatePageController controller;

  /** . */
  public ExtenderOsdUpdatePageView() {
    try {
      controller =
          InjectorProvider.getInjector().getInstance(ExtenderOsdUpdatePageController.class);
      URL location =
          getClass()
              .getResource(
                  "/com/mc/tool/caesar/vpm/pages/"
                      + "extenderosdupdate/extenderosdupdate_view.fxml");
      FXMLLoader loader = new FXMLLoader(location);
      loader.setController(controller);
      loader.setResources(
          ResourceBundle.getBundle("com.mc.tool.caesar.vpm.pages.extenderosdupdate.Bundle"));
      loader.setRoot(this);
      loader.load();
    } catch (IOException exc) {
      log.warn("Can not load extenderupdate_view.fxml", exc);
    }
  }
}
