package com.mc.tool.caesar.vpm.util.macrokey.datamodel;

import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.CaesarConstants.FunctionKey.Cmd.Command;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ChangeListener;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.util.Pair;

/**
 * .
 */
public class MacroKeyGroup {
  private MacroKeyItem[][] macroKeys;
  private final ObservableList<FunctionKeyData> deviceData;
  private CaesarSwitchDataModel dataModel = null;

  private BooleanProperty changeProperty = new SimpleBooleanProperty(false);

  private ObservableList<Fkey> nonEmptyList = FXCollections.observableArrayList();

  private ObservableList<Fkey> readonlyNonEmptyList =
      FXCollections.unmodifiableObservableList(nonEmptyList);

  private boolean reloading = false;

  private WeakAdapter weakAdapter = new WeakAdapter();

  private int fkeySize;
  private int macroSize;

  /**
   * Constructor.
   *
   * @param deviceData device data
   */
  public MacroKeyGroup(ObservableList<FunctionKeyData> deviceData) {
    this.deviceData = deviceData;
    this.deviceData.addListener(
        weakAdapter.wrap((ListChangeListener<FunctionKeyData>) change -> reload()));
  }

  /** 初始化. */
  public void init(CaesarDeviceController deviceController) {
    this.dataModel = deviceController.getDataModel();
    fkeySize = deviceController.getDataModel().getConfigMetaData().getUtilVersion().getFkeySize();
    macroSize = deviceController.getDataModel().getConfigMetaData().getUtilVersion().getMacroSize();
    macroKeys = new MacroKeyItem[fkeySize][];
    initArrayImpl(macroKeys);
    initListener();
  }

  /** 删除所有的宏信息. */
  public void delete() {
    for (int i = 0; i < fkeySize; i++) {
      for (int j = 0; j < macroSize; j++) {
        macroKeys[i][j].delete();
      }
    }
  }

  protected void initArrayImpl(MacroKeyItem[][] values) {
    for (int i = 0; i < fkeySize; i++) {
      values[i] = new MacroKeyItem[macroSize];
      for (int j = 0; j < macroSize; j++) {
        values[i][j] = new MacroKeyItem();
      }
    }
  }

  protected void copyArray(MacroKeyItem[][] from, MacroKeyItem[][] to, boolean copyId) {
    for (int i = 0; i < fkeySize; i++) {
      for (int j = 0; j < macroSize; j++) {
        MacroKeyItem toItem = to[i][j];
        MacroKeyItem fromItem = from[i][j];
        if (copyId) {
          toItem.setFunctionKeyId(fromItem.getFunctionKeyId());
        }
        toItem.getCmd().set(fromItem.getCmd().get());
        toItem.getParam1().set(fromItem.getParam1().get());
        toItem.getParam2().set(fromItem.getParam2().get());
      }
    }
  }

  public void copyTo(MacroKeyGroup inGroup) {
    copyArray(macroKeys, inGroup.macroKeys, false);
  }

  protected void initListener() {
    ChangeListener<Command> cmdChangeListener = (obs, oldVal, newVal) -> checkChange();
    ChangeListener<MacroParam> paramChangeListener = (obs, oldVal, newVal) -> checkChange();
    for (int i = 0; i < fkeySize; i++) {
      for (int j = 0; j < macroSize; j++) {
        macroKeys[i][j].getCmd().addListener(weakAdapter.wrap(cmdChangeListener));
        macroKeys[i][j].getParam1().addListener(weakAdapter.wrap(paramChangeListener));
        macroKeys[i][j].getParam2().addListener(weakAdapter.wrap(paramChangeListener));
      }
    }
  }

  /**
   * 获取跟设备数据不一样的项.
   *
   * @return 不一样的项的集合, map的key为宏按键与宏位置的pair
   */
  public Map<Pair<Integer, Integer>, MacroKeyItem> getChangedItems(
      List<Integer> fkeyNameChangedList) {
    MacroKeyItem[][] temp = new MacroKeyItem[fkeySize][];
    initArrayImpl(temp);
    reloadImpl(temp);

    MacroKeyItem[][] copy = new MacroKeyItem[fkeySize][];
    initArrayImpl(copy);
    copyArray(macroKeys, copy, true);
    // 找出不同的项来配置
    Map<Pair<Integer, Integer>, MacroKeyItem> changedItems = new HashMap<>();
    Set<FunctionKeyData> freeFunctionKeyDatas = new HashSet<>();
    for (int i = 0; i < fkeySize; i++) {
      for (int j = 0; j < macroSize; j++) {
        // fkey名字有修改时，宏要有功能的情况下才算宏有修改
        if (fkeyNameChangedList.contains(i) && !copy[i][j].getCmd().get().equals(Command.NoFunction)
            || !temp[i][j].isEqual(copy[i][j])) {
          MacroKeyItem item = copy[i][j];
          FunctionKeyData functionKeyData;
          if (item.getFunctionKeyId() < 0) {
            functionKeyData = dataModel.getConfigDataManager().getFreeFunctionKeyData(freeFunctionKeyDatas);
            if (functionKeyData == null) {
              break;
            }
            freeFunctionKeyDatas.add(functionKeyData);
            item.setFunctionKeyId(functionKeyData.getOid());
          }
          changedItems.put(new Pair<>(i, j), item);
        }
      }
    }
    return changedItems;
  }

  protected void checkChange() {
    if (reloading) {
      return;
    }
    MacroKeyItem[][] temp = new MacroKeyItem[fkeySize][];
    initArrayImpl(temp);
    reloadImpl(temp);
    changeProperty.set(!Arrays.deepEquals(temp, macroKeys));
    updateNonEmptyList();
  }

  protected void reloadImpl(MacroKeyItem[][] values) {
    reloading = true;
    try {
      for (int i = 0; i < fkeySize; i++) {
        for (int j = 0; j < macroSize; j++) {
          values[i][j].getCmd().set(Command.NoFunction);
          values[i][j].getParam1().set(null);
          values[i][j].getParam2().set(null);
          values[i][j].setFunctionKeyId(-1);
        }
      }
      if (dataModel.getConfigDataManager() == null) {
        return;
      }
      for (FunctionKeyData data : deviceData) {
        if (data.getHotkey() < 0 || data.getHotkey() >= fkeySize) {
          continue;
        }
        if (data.getKeyline() < 0 || data.getKeyline() >= macroSize) {
          continue;
        }
        MacroKeyItem item = values[data.getHotkey()][data.getKeyline()];
        item.setFunctionKeyId(data.getOid());
        item.getCmd().set(Command.valueOf(data.getMacroCmd()));

        for (MacroParam param :
            getAllEndPointParams(Param1List.getList(null, item, dataModel.getConfigDataManager(), dataModel.getVpDataModel()))) {
          if (param == null) {
            continue;
          }
          if (item.getCmd().get() == Command.PushVideoWallScenarioWindow) {
            if (param instanceof MacroScenarioWindowParam
                && ((MacroScenarioWindowParam) param).getIndex() == data.getIntParam(2) - 1
                && ((MacroScenarioWindowParam) param)
                    .getWindowName()
                    .equals(data.getStringParam(0))) {
              item.getParam1().set(param);
              break;
            }
          } else if (item.getCmd().get() == Command.ActiveVideoWallScenario) {
            if (param instanceof MacroScenarioDataParam
                && ((MacroScenarioDataParam) param).getIndex() == data.getIntParam(2) - 1) {
              item.getParam1().set(param);
              break;
            }
          } else if (item.getCmd().get() == Command.SWITCH_MULTIVIEW_LAYOUT
              || item.getCmd().get() == Command.SWITCH_MULTIVIEW_SIGNAL) {
            if (param instanceof MacroMultiviewParam
                && ((MacroMultiviewParam) param).getIndex() == data.getIntParam(2) - 1) {
              item.getParam1().set(param);
              break;
            }
          } else if (param.getOid() == data.getIntParam(0) - 1) {
            item.getParam1().set(param);
            break;
          }
        }

        for (MacroParam param :
            getAllEndPointParams(
                Param2List.getList(new SimpleObjectProperty<>(), item, dataModel))) {
          if (param == null || !param.hasOid()) {
            continue;
          }
          if (param instanceof MacroDualHdmiParam) {
            MacroDualHdmiParam dualHdmiParam = (MacroDualHdmiParam) param;
            // 最高位: 0：HDMI1（默认），1：HDMI2
            if (dualHdmiParam.getOid() == ((data.getIntParam(1) - 1) & 0x7FFF)
                && dualHdmiParam.getIndex() == ((data.getIntParam(1) - 1) >> 15) + 1) {
              item.getParam2().set(param);
              break;
            }
          } else if (param instanceof MacroDataObjectParam
              && param.getOid() == data.getIntParam(1) - 1) {
            item.getParam2().set(param);
            break;
          }
        }
      }
    } finally {
      reloading = false;
    }
  }

  /** 重新加载. */
  public void reload() {
    reloadImpl(macroKeys);
    changeProperty.set(false);
    updateNonEmptyList();
  }

  /**
   * 获取key对应的宏列表.
   *
   * @param key key
   * @return 宏列表
   */
  public MacroKeyItem[] getMacroKeys(Fkey key) {
    if (key == null) {
      return new MacroKeyItem[0];
    } else {
      return macroKeys[key.ordinal()].clone();
    }
  }

  public boolean isChange() {
    return changeProperty.get();
  }

  public BooleanProperty changeProperty() {
    return changeProperty;
  }

  protected void updateNonEmptyList() {
    List<Fkey> newList = new ArrayList<>();
    for (int i = 0; i < fkeySize; i++) {
      for (int j = 0; j < macroSize; j++) {
        if (macroKeys[i][j].getCmd().get() != null
            && macroKeys[i][j].getCmd().get() != Command.NoFunction) {
          newList.add(Fkey.values()[i]);
          break;
        }
      }
    }
    nonEmptyList.setAll(newList);
  }

  /**
   * 获取有宏的按键的列表.
   *
   * @return 按键列表.
   */
  public ObservableList<Fkey> getNonEmptyList() {
    return readonlyNonEmptyList;
  }

  /**
   * 获取所有没有children的param.
   *
   * @return 没有children的param集合
   */
  public static Collection<MacroParam> getAllEndPointParams(Collection<MacroParam> params) {
    List<MacroParam> newParams = new ArrayList<>();
    for (MacroParam param : params) {
      if (param.getChildren().isEmpty()) {
        newParams.add(param);
      } else {
        newParams.addAll(getAllEndPointParams(param.getChildren()));
      }
    }
    return newParams;
  }
}
