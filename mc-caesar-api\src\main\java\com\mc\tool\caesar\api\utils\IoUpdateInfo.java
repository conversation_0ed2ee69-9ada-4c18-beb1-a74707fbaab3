package com.mc.tool.caesar.api.utils;

import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public class IoUpdateInfo {

  @Getter
  private CaesarSwitchDataModel model;
  private byte[] updateParams = null;

  @Getter
  @Setter
  private boolean updateTrunk;

  @Getter
  @Setter
  private int ioStartAddress;
  @Getter
  @Setter
  private int trunkStartAddress;
  @Getter
  @Setter
  private String updateIoFileName;
  @Getter
  @Setter
  private String updateTrunkFileName;

  /**
   * IoUpdateInfo.
   */
  public IoUpdateInfo(CaesarSwitchDataModel model, byte[] updateParams) {
    this.model = model;
    if (updateParams != null) {
      this.updateParams = updateParams.clone();
    }
  }

  /**
   * .
   */
  public byte[] getUpdateParams() {
    if (this.updateParams == null) {
      return new byte[0];
    } else {
      return this.updateParams.clone();
    }
  }

  /**
   * .
   */
  public void setUpdateParams(byte[] updateparams) {
    if (updateparams != null) {
      this.updateParams = updateparams.clone();
    } else {
      this.updateParams = null;
    }
  }
}
