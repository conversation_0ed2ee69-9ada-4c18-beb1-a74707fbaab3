config_decoder_group.btn=GroupConfig
edit_decoder_group_dialog.name_label=DecoderGroupName: 
edit_decoder_group_dialog.croppable_check=WhetherCardCanBeCut
assign_permissions_dialog.croppable_check=CutOrNot
assign_permissions_dialog.yes=YES
assign_permissions_dialog.no=NO
edit_decoder_group_dialog.select_rx=PleaseSelectRX
assign_permissions_selection_view.source_header=TX without assigned permissions
assign_permissions_selection_view.target_header=TX can be displayed
new_decode_card_dialog.title=Add Decoder Card
new_decode_card_dialog.header_text=Select decoding card binding relationship
new_decode_card_dialog.delete=Delete
new_decode_card_dialog.add_input_card=Add inputCard
new_decode_card_dialog.delete_input_card=Delete inputCard
new_decode_card_dialog.input_card=InputCard
create_decoder_group.btn=CreateDecoderGroup
decoder_group=DecoderGroup
host_label=CaesarKaito
delete_decoder_group.btn=DeleteGroup
splicing_screen_delete_dialog.header=Please select splicing screen
splicing_screen_delete_dialog.title=DeleteSplicingScreen
edit_decoder_group_dialog.title=CreateDecoderGroup
edit_decoder_group_dialog.type_requirement=The RX resolution type within the group must be consistent
assign_permissions_dialog.decoder_group_configuration=DecoderRroupConfiguration
assign_permissions_dialog.name_label=Name:
splicing_screen_input_dialog.header=Please enter all-optical splicing screen info
splicing_screen_input_dialog.ip_address=Ip address
splicing_screen_input_dialog.ip_format_error=Ip format is error
splicing_screen_input_dialog.name=Name
name_label=Name
host_type_label=HostType
kaito_input_list=KaitoInputCardList
index_column_text=Index
caesar_card_name_column_text=CaesarCardName
caesar_card_id_column_text=caesarCardId
caesar_card_port_column_text=CaesarCardPort
nowa_card_sn_column_text=NowaCardSn
nowa_card_id_column_text=NowaCardId
nowa_card_port_column_text=NowaCardPort
splicing_screen_input_dialog.pid=Nova id
splicing_screen_input_dialog.port=Port
splicing_screen_input_dialog.port_format_error=Port's format is error. Please input an integer at the range of 1~65536.
splicing_screen_input_dialog.secret_key=Nova secretkKey
splicing_screen_input_dialog.title=Create All-Optical Splice Screen
splicing_screen_input_dialog.type=Nova type
splicingscreen_page.title=SplicingScreen
toolbar.delete_splicing_screen=DeleteSplicingScreen
toolbar.new_decoder=addDecodeCard
toolbar.new_splicing_screen=CreateSplicingScreen
rx_list.label=RxList
rx_list.index=Index
rx_list.name=RxName
rx_list.port=RxPort
rx_list.input_id=InputId
rx_list.slot_id=SlotId