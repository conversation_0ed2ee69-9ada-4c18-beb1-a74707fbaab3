package com.mc.tool.caesar.api.datamodel;

import com.mc.tool.caesar.api.Bundle;
import com.mc.tool.caesar.api.interfaces.Nameable;
import lombok.Getter;

/**
 * 多画面输出模式.
 */
@Getter
public enum MultiviewOutputMode implements Nameable {
  FOUR_INTERFACE(0, Bundle.multiviewOutputMode(4)), // 4接口模式
  TWO_INTERFACE(1, Bundle.multiviewOutputMode(2)), // 2接口模式
  ONE_INTERFACE(2, Bundle.multiviewOutputMode(1)); // 1接口模式

  private final int index;
  private final String name;

  MultiviewOutputMode(int index, String name) {
    this.index = index;
    this.name = name;
  }

  /**
   * 根据索引获取枚举.
   */
  public static MultiviewOutputMode getByIndex(int index) {
    for (MultiviewOutputMode type : MultiviewOutputMode.values()) {
      if (type.index == index) {
        return type;
      }
    }
    return null;
  }
}
