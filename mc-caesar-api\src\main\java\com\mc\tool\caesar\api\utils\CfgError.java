package com.mc.tool.caesar.api.utils;

import com.mc.tool.caesar.api.exception.ConfigException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * .
 */
public final class CfgError {

  private static final Logger LOG = Logger.getLogger(CfgError.class.getName());
  public static final int IO = 1;
  public static final int EOF = -1;
  public static final int ENUM = -2;
  public static final int NOT_FOUND = -3;
  public static final int NO_CONNECTION = -4;
  public static final int NO_OPEN = -5;
  public static final int NO_SAVE = -6;
  public static final int CONNECTION_ERROR = -7;
  public static final int DATE_ERROR = -8;
  public static final int TIMEOUT = -9;
  public static final int INTERRUPT = -10;
  public static final int CONNECT_CLOSE_ERROR = -11;
  public static final int USER_ERROR = -12;
  public static final int RESTART_ERROR = -13;
  public static final int VERSION = -14;
  public static final int NEWER_VERSION = -15;
  public static final int NOT_SUPPORTED = -16;
  public static final int BUSY = -17;
  public static final int OPEN_CLOSE_ERROR = -101;
  public static final int UNKNOWN_ERROR = -666;

  public static void showError(String fileName, ConfigException ce) {
    LOG.log(Level.SEVERE, ce.getMessage(), ce);
  }
}

