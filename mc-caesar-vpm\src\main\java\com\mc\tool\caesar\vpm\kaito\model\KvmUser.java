package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import lombok.Setter;

/**
 * KVM系统的用户信息.
 */
@Setter
@JsonPropertyOrder({
    KvmUser.JSON_PROPERTY_ID,
    KvmUser.JSON_PROPERTY_NAME,
    KvmUser.JSON_PROPERTY_PERMISSION
})
public class KvmUser {
  public static final String JSON_PROPERTY_ID = "id";
  private Integer id;

  public static final String JSON_PROPERTY_NAME = "name";
  private String name;

  public static final String JSON_PROPERTY_PERMISSION = "permission";
  private Integer permission;

  /**
   * id.
   */
  public KvmUser id(Integer id) {
    this.id = id;
    return this;
  }

  /**
   * 用户ID.
   *
   * @return id
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude()
  public Integer getId() {
    return id;
  }

  /**
   * name.
   */
  public KvmUser name(String name) {
    this.name = name;
    return this;
  }

  /**
   * 用户名.
   *
   * @return name
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_NAME)
  @JsonInclude()
  public String getName() {
    return name;
  }

  /**
   * permission.
   */
  public KvmUser permission(Integer permission) {
    this.permission = permission;
    return this;
  }

  /**
   * 权限.
   *
   * @return permission
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_PERMISSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getPermission() {
    return permission;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    KvmUser kvmUser = (KvmUser) o;
    return Objects.equals(this.id, kvmUser.id)
        && Objects.equals(this.name, kvmUser.name)
        && Objects.equals(this.permission, kvmUser.permission);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, name, permission);
  }


  @Override
  public String toString() {
    return "KvmUser {\n"
        + "    id: " + toIndentedString(id) + "\n"
        + "    name: " + toIndentedString(name) + "\n"
        + "    permission: " + toIndentedString(permission) + "\n"
        + "}";
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
