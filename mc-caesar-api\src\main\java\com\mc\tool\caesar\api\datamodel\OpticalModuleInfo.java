package com.mc.tool.caesar.api.datamodel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import lombok.Data;

/**
 * .
 */
@Data
@HeadFontStyle(fontHeightInPoints = 12)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class OpticalModuleInfo implements CaesarCommunicatable {

  public static final int SIZE = 118;

  @ExcelProperty(value = {"主机", "端口"}, index = 0)
  private int port;
  @ExcelProperty(value = {"主机", "供应商"}, index = 1)
  private String supplier;
  @ExcelProperty(value = {"主机", "部件号"}, index = 2)
  private String pn;
  @ExcelProperty(value = {"主机", "温度(℃)"}, index = 3)
  @NumberFormat("#.#")
  private double temperature;
  @ExcelProperty(value = {"主机", "发送功率(uW)"}, index = 4)
  @NumberFormat("#.##")
  private double sendingPower1;
  @ExcelProperty(value = {"主机", "发送功率(dbm)"}, index = 5)
  @NumberFormat("#.##")
  private double sendingPower2;
  @ExcelProperty(value = {"主机", "接收功率(uW)"}, index = 6)
  @NumberFormat("#.##")
  private double receivedPower1;
  @ExcelProperty(value = {"主机", "接收功率(dbm)"}, index = 7)
  @NumberFormat("#.##")
  private double receivedPower2;

  @ExcelProperty(value = {"外设", "外设ID"}, index = 8)
  private int extId;
  @ExcelProperty(value = {"外设", "链路状态"}, index = 9)
  private String link1State = "链路1";
  @ExcelProperty(value = {"外设", "供应商"}, index = 10)
  private String link1Supplier;
  @ExcelProperty(value = {"外设", "部件号"}, index = 11)
  private String link1pn;
  @ExcelProperty(value = {"外设", "温度(℃)"}, index = 12)
  @NumberFormat("#.#")
  private double link1Temperature;
  @ExcelProperty(value = {"外设", "发送功率(uW)"}, index = 13)
  @NumberFormat("#.##")
  private double link1SendingPower1;
  @ExcelProperty(value = {"外设", "发送功率(dbm)"}, index = 14)
  @NumberFormat("#.##")
  private double link1SendingPower2;
  @ExcelProperty(value = {"外设", "接收功率(uW)"}, index = 15)
  @NumberFormat("#.##")
  private double link1ReceivedPower1;
  @ExcelProperty(value = {"外设", "接收功率(dbm)"}, index = 16)
  @NumberFormat("#.##")
  private double link1ReceivedPower2;

  @ExcelProperty(value = {"外设", "链路状态"}, index = 17)
  private String link2State = "链路2";
  @ExcelProperty(value = {"外设", "供应商"}, index = 18)
  private String link2Supplier;
  @ExcelProperty(value = {"外设", "部件号"}, index = 19)
  private String link2pn;
  @ExcelProperty(value = {"外设", "温度(℃)"}, index = 20)
  @NumberFormat("#.#")
  private double link2Temperature;
  @ExcelProperty(value = {"外设", "发送功率(uW)"}, index = 21)
  @NumberFormat("#.##")
  private double link2SendingPower1;
  @ExcelProperty(value = {"外设", "发送功率(dbm)"}, index = 22)
  @NumberFormat("#.##")
  private double link2SendingPower2;
  @ExcelProperty(value = {"外设", "接收功率(uW)"}, index = 23)
  @NumberFormat("#.##")
  private double link2ReceivedPower1;
  @ExcelProperty(value = {"外设", "接收功率(dbm)"}, index = 24)
  @NumberFormat("#.##")
  private double link2ReceivedPower2;

  /**
   * P(uW) = 1000uW ⋅ 10^(P(dBm)/ 10) https://www.rapidtables.com/convert/power/dBm_to_mW.html.
   * P(dBm) = 10 ⋅ log10( P(uW) / 1000uW) https://www.rapidtables.com/convert/power/mW_to_dBm.html.
   */
  private double convertUwToDbm(double uw) {
    if (uw == 0) {
      return 0;
    }
    return Math.log10(uw / 1000) * 10;
  }

  public double getSendingPower2() {
    sendingPower2 = convertUwToDbm(sendingPower1);
    return sendingPower2;
  }

  public double getReceivedPower2() {
    receivedPower2 = convertUwToDbm(receivedPower1);
    return receivedPower2;
  }

  public double getLink1SendingPower2() {
    link1SendingPower2 = convertUwToDbm(link1SendingPower1);
    return link1SendingPower2;
  }

  public double getLink1ReceivedPower2() {
    link1ReceivedPower2 = convertUwToDbm(link1ReceivedPower1);
    return link1ReceivedPower2;
  }

  public double getLink2SendingPower2() {
    link2SendingPower2 = convertUwToDbm(link2SendingPower1);
    return link2SendingPower2;
  }

  public double getLink2ReceivedPower2() {
    link2ReceivedPower2 = convertUwToDbm(link2ReceivedPower1);
    return link2ReceivedPower2;
  }

  @Override
  public void readData(CfgReader cfgReader) throws ConfigException {
    setPort(cfgReader.read2ByteValue());
    setSupplier(cfgReader.readString(12));
    setPn(cfgReader.readString(12));
    setTemperature(cfgReader.read2ByteValue() / 256.);
    setSendingPower1(cfgReader.read2ByteValue() / 10.);
    setReceivedPower1(cfgReader.read2ByteValue() / 10.);

    setExtId(cfgReader.read2ByteValue());
    setLink1Supplier(cfgReader.readString(12));
    setLink1pn(cfgReader.readString(12));
    setLink1Temperature(cfgReader.read2ByteValue() / 256.);
    setLink1SendingPower1(cfgReader.read2ByteValue() / 10.);
    setLink1ReceivedPower1(cfgReader.read2ByteValue() / 10.);

    setLink2Supplier(cfgReader.readString(12));
    setLink2pn(cfgReader.readString(12));
    setLink2Temperature(cfgReader.read2ByteValue() / 256.);
    setLink2SendingPower1(cfgReader.read2ByteValue() / 10.);
    setLink2ReceivedPower1(cfgReader.read2ByteValue() / 10.);
  }

  @Override
  public void writeData(CfgWriter cfgWriter) throws ConfigException {

  }
}
