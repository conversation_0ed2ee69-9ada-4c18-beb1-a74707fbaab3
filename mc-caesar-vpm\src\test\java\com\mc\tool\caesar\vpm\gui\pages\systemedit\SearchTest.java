package com.mc.tool.caesar.vpm.gui.pages.systemedit;

import com.mc.common.control.TextWrapper;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import javafx.scene.Node;
import javafx.scene.input.KeyCode;
import org.junit.Assert;
import org.junit.Test;

/**
 * .
 */
public class SearchTest extends AppGuiTestBase {

  @Override
  public void beforeAll() {
    login();
  }

  @Test
  public void testSearchTx() {
    clickOn("#search-btn");
    clickOn("#searchText");
    eraseText(20);
    write("T1_1-1001");
    press(KeyCode.ENTER);
    press(KeyCode.ENTER);

    Node node = lookupTerminal("T1_1-1001");
    Assert.assertNotNull(node);
    clickOn(node);
  }

  protected Node lookupTerminal(String text) {
    Node findedNode =
        lookup((node) -> node instanceof TextWrapper && ((TextWrapper) node).getText().equals(text))
            .lookup("#name-text")
            .query();
    return findedNode;
  }
}
