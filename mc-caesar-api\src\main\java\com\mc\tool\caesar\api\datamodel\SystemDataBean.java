package com.mc.tool.caesar.api.datamodel;

import javafx.beans.property.StringProperty;

/**
 * .
 */
public class SystemDataBean {

  private StringProperty device;
  private StringProperty name;
  private StringProperty info;
  private StringProperty masterIp;

  /**
   * Constructor.
   */
  public SystemDataBean(SystemData data) {
    device = data.getDeviceProperty();
    name = data.getNameProperty();
    info = data.getInfoProperty();
    masterIp = data.getMasterIpProperty();
  }

  public String getDevice() {
    return device.get();
  }

  public String getName() {
    return name.get();
  }

  public String getInfo() {
    return info.get();
  }

  public String getMasterIp() {
    return masterIp.get();
  }

}
