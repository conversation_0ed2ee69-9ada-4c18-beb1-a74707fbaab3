package com.mc.tool.caesar.vpm.pages.systemedit.controller;

import java.util.ResourceBundle;

class Bundle {
  static class NbBundle {
    protected static final String BASE_NAME = "com.mc.tool.caesar.vpm.pages.systemedit.Bundle";

    public static String getMessage(String id) {
      return ResourceBundle.getBundle(BASE_NAME).getString(id);
    }

    public static ResourceBundle getResourceBundle() {
      return ResourceBundle.getBundle(BASE_NAME);
    }
  }
}
