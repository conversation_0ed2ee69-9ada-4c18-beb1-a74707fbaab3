package com.mc.tool.caesar.vpm.pages.systemedit.property;

import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import javax.xml.bind.DatatypeConverter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarEdidSender implements CaesarPropertySender<String> {

  private final CaesarDeviceController controller;
  private final ExtenderData extenderData;
  private final String propertyName;
  private final int index;

  /** Construct. */
  public CaesarEdidSender(
      CaesarDeviceController controller,
      ExtenderData extenderData,
      String propertyName,
      int index) {
    this.controller = controller;
    this.extenderData = extenderData;
    this.propertyName = propertyName;
    this.index = index;
  }

  @Override
  public void sendValue(String value) {
    controller.execute(
        () -> {
          extenderData.setProperty(propertyName, value);
          try {
            controller
                .getDataModel()
                .setEdid(extenderData, index, DatatypeConverter.parseHexBinary(value));
          } catch (BusyException | ConfigException exception) {
            log.error("Fail to send value!", exception);
          }
        });
  }
}
