package com.mc.tool.caesar.vpm.util.vp;

import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.vp.Vp6ConfigData;
import com.mc.tool.caesar.api.datamodel.vp.Vp6OutputData;
import com.mc.tool.caesar.api.datamodel.vp.Vp7ConfigData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;

/**
 * .
 */


// 定义VpMatrix接口
public interface VpMatrix {

  // 获取传输设备的当前分辨率
  VpResolution getTxCurrentResolution(int txid, int sourceIndex);

  // 获取传输设备的最终分辨率
  VpResolution getTxFinalResolution(int txid, int sourceIndex);

  // 获取TX配置的裁剪数据
  VpTxCropData getTxConfigCropData(int txid, int sourceIndex, int crop);

  // 获取TX网格裁剪数据
  VpTxCropData getTxGridCropData(int txid, int gridIndex);

  // 判断接收设备是否在线
  boolean isRxOnline(int rxid);

  // 判断外部设备是否在线
  boolean isExtOnline(int extid);

  // 根据外部设备ID获取传输设备ID
  int getTxIdByExtId(int extid);

  // 根据外部设备ID获取接收设备ID
  int getRxIdByExtId(int extid);

  // 判断传输设备是否有双输入
  boolean hasDualInput(int txid);

  // 获取Vp7设备的类型
  Vp7InfType getVp7InfType(int vp7id);

  // 发送Vp6配置数据
  void sendVp6ConfigData(VpConsoleData vpConsoleData, Vp6ConfigData vp6ConfigData, Vp6OutputData outputData);

  // 连接视频
  void connectVideo(int rxid, int txid, int sourceIndex);

  ExtenderData findExt(int extid);

  VpConsoleData findVpCon(int vpConId);

  // 连接多视角视频
  void connectMultiviewVideo(int rxid, int txCnt, int[] txes);

  // 发送Vp7配置数据
  void sendVp7ConfigData(int vp7id, Vp7ConfigData data);
}

