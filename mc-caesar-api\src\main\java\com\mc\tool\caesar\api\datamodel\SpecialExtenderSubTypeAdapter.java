
package com.mc.tool.caesar.api.datamodel;

import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;
import com.mc.tool.caesar.api.CaesarConstants;
import java.io.IOException;


/**
 *  兼容旧版specialExtSubType枚举.
 */
public class SpecialExtenderSubTypeAdapter extends TypeAdapter<Integer> {

  @Override
  public void write(JsonWriter out, Integer value) throws IOException {
    out.value(value);
  }

  @Override
  public Integer read(JsonReader in) throws IOException {
    if (in.peek() == JsonToken.STRING) {
      String stringValue = in.nextString();
      try {
        return CaesarConstants.Extender.SpecialExtenderSubType.valueOf(stringValue).getValue();
      } catch (IllegalArgumentException e) {
        return 0;
      }
    } else if (in.peek() == JsonToken.NUMBER) {
      return in.nextInt();
    } else {
      in.skipValue();
      return 0;
    }
  }
}
