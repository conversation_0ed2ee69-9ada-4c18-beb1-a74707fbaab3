package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.graph.interfaces.CellSkin;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupDataType;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.systemedit.controller.CaesarSystemEditController;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.CpuGroup;
import com.mc.tool.caesar.vpm.util.CaesarNamePredicate;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.menu.MenuGroup;
import com.mc.tool.framework.systemedit.menu.predicate.MenuPredicateBinding;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javafx.beans.binding.BooleanBinding;
import javafx.scene.control.MenuItem;
import lombok.extern.slf4j.Slf4j;

/**
 * 创建监控解码分组菜单.
 */
@Slf4j
public class MenuMonitorDecoder4Caesar extends MenuItem {

  private final SystemEditControllable controllable;
  private TxRxGroupData unActiveTxRxGroup;

  /**
   * MenuMonitorDecoder4Caesar.
   */
  public MenuMonitorDecoder4Caesar(SystemEditControllable controllable) {
    this.controllable = controllable;
    this.setText(Bundle.NbBundle.getMessage("menu.group.monitor.decoder"));
    this.setOnAction(event -> onAction());
    this.disableProperty().bind(getMenuDisableBinding(controllable));
  }

  /**
   * 获取菜单是否应该禁掉的binding.
   *
   * @param controllable controllable
   * @return binding
   */
  public BooleanBinding getMenuDisableBinding(SystemEditControllable controllable) {
    MenuPredicateBinding binding = MenuGroup.getMenuDisableBinding(controllable);
    binding.addSingleSelectionPredicate(node -> !(node instanceof CaesarCpuTerminal));
    binding.addSingleSelectionPredicate(
        node -> node instanceof CaesarCpuTerminal && !(node.getParent() instanceof CaesarMatrix));
    if (controllable instanceof CaesarSystemEditController) {
      CaesarSwitchDataModel dataModel =
          ((CaesarSystemEditController) controllable).getDeviceController().getDataModel();
      boolean hasTxRxGroupData = dataModel.getConfigMetaData().getUtilVersion().hasTxRxGroupData();
      binding.addSingleSelectionPredicate(
          node -> node instanceof CaesarCpuTerminal && !hasTxRxGroupData);
      if (hasTxRxGroupData) {
        unActiveTxRxGroup = dataModel.getConfigDataManager().getUnActiveTxRxGroup();
        binding.addSingleSelectionPredicate(
            node -> node instanceof CaesarCpuTerminal && unActiveTxRxGroup == null);
        binding.addSingleSelectionPredicate(node -> node instanceof CaesarCpuTerminal
            && ((CaesarCpuTerminal) node).getCpuData().getScreenOffsetConfig().getActiveOffset() != 0);
      }
    }
    return binding;
  }

  private void onAction() {
    Collection<CellSkin> skins = controllable.getGraph().getSelectionModel().getSelectedCellSkin();
    List<VisualEditNode> nodeList = new ArrayList<>();
    for (CellSkin skin : skins) {
      if (skin.getCell().getBindedObject() instanceof VisualEditNode) {
        nodeList.add((VisualEditNode) skin.getCell().getBindedObject());
      } else {
        log.warn(
            "Cell's bindedobject is not VisualEditNode but {}", skin.getCell().getBindedObject());
      }
    }
    if (controllable instanceof CaesarSystemEditController
        && nodeList.stream().allMatch(item -> item instanceof CaesarCpuTerminal)) {
      CaesarDeviceController deviceController =
          ((CaesarSystemEditController) controllable).getDeviceController();
      List<String> existGroupNames =
          deviceController.getDataModel().getConfigDataManager().getActiveTxRxGroups().stream()
              .map(TxRxGroupData::getName).collect(Collectors.toList());
      Predicate<String> predicate = new CaesarNamePredicate(existGroupNames);
      String groupName = "DecoderGroup";
      ApplicationBase applicationBase =
          InjectorProvider.getInjector().getInstance(ApplicationBase.class);
      Optional<String> result =
          ViewUtility.getNameFromDialog(
              applicationBase.getMainWindow(), groupName, null, predicate);
      if (result.isPresent()) {
        groupName = result.get();
      } else {
        return;
      }
      CpuGroup cpuGroup = controllable.addGroup(
          groupName, CpuGroup.class, nodeList.toArray(new VisualEditNode[0]));
      cpuGroup.setTxRxGroupData(unActiveTxRxGroup);
      String finalGroupName = groupName;
      deviceController.execute(() -> {
        for (VisualEditNode node : nodeList) {
          if (node instanceof CaesarCpuTerminal) {
            CaesarCpuTerminal cpuTerminal = (CaesarCpuTerminal) node;
            cpuTerminal.getCpuData().setTxGroupIndex(unActiveTxRxGroup.getOid() + 1);
            cpuTerminal.getCpuData().getScreenOffsetConfig().setActiveMonitorDecoder(1);
          }
        }
        unActiveTxRxGroup.setName(finalGroupName);
        unActiveTxRxGroup.setType(TxRxGroupDataType.TX_MONITOR_DECODER_GROUP);
        ((CaesarSystemEditController) controllable).getDeviceController()
            .addTxRxGroupData(unActiveTxRxGroup);
      });
    }
  }
}
