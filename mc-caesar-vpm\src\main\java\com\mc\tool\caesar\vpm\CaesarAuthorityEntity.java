package com.mc.tool.caesar.vpm;

import com.google.gson.GsonBuilder;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.permissionconfiguration.CaesarPermissionConfigurationPage;
import com.mc.tool.caesar.vpm.util.CaesarModelJsonBuilder;
import com.mc.tool.framework.AbstractVisualEditEntity;
import com.mc.tool.framework.OemInfo;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.Page;
import com.mc.tool.framework.utility.EntityUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.TypeWrapper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.Node;
import javafx.scene.layout.Background;
import lombok.Getter;

/**
 * CaesarAuthorityEntity.
 */
public class CaesarAuthorityEntity extends AbstractVisualEditEntity {

  @Getter
  private final CaesarDeviceController controller;
  private final List<Page> pages = new ArrayList<>();

  /**
   * Constructor.
   */
  public CaesarAuthorityEntity(CaesarDeviceController controller) {
    this.controller = controller;
    controller.init();
    deserializeAndInit();
    pages.add(new CaesarPermissionConfigurationPage(this));
  }

  @Override
  protected GsonBuilder createModelSerializationBuilder() {
    return CaesarModelJsonBuilder.getBuilder();
  }

  @Override
  public String getName() {
    OemInfo info = InjectorProvider.getInjector().getInstance(ApplicationBase.class).getOemInfo();
    String product = "Caesar";
    if (info.getProductName() != null) {
      product = info.getProductName();
    }

    return String.format("%s(%s)", product, controller.getDefaultIp());
  }

  @Override
  public String getType() {
    return EntityUtility.DEVICE_TYPE + "caesar";
  }

  @Override
  public Collection<Page> getPages() {
    return pages;
  }

  @Override
  public boolean isImportable() {
    return false;
  }

  @Override
  public Collection<TypeWrapper> getExportableTypes() {
    return Collections.emptyList();
  }

  @Override
  public boolean exports(String type, String path) {
    return false;
  }

  @Override
  public boolean imports(String path) {
    return false;
  }

  @Override
  public ObservableList<Node> getLeftStatus() {
    return FXCollections.emptyObservableList();
  }

  @Override
  public ObservableList<Node> getRightStatus() {
    return FXCollections.emptyObservableList();
  }

  @Override
  public ObjectProperty<Background> getActiveBgProperty() {
    return new SimpleObjectProperty<>(null);
  }

  @Override
  public ObjectProperty<Background> getBackupBgProperty() {
    return new SimpleObjectProperty<>(null);
  }
}
