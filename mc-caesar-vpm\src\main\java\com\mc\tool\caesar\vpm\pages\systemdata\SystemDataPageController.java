package com.mc.tool.caesar.vpm.pages.systemdata;

import com.dooapp.fxform.FXForm;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.datamodel.SystemDataBean;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.systemdata.Bundle.NbBundle;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.ViewControllable;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.event.ActionEvent;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.ScrollPane;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class SystemDataPageController implements Initializable, ViewControllable {
  private CaesarDeviceController deviceController = null;

  @Setter private FXForm<SystemDataBean> fxform;

  @FXML private Button applyButton;
  @FXML private Button cancelButton;
  @FXML private ScrollPane formPane;

  private WeakAdapter weakAdapter = new WeakAdapter();

  /** . */
  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
    } else {
      log.warn("Error device controller type : {} !", deviceController.getClass().getName());
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {

    formPane.setContent(fxform);

    applyButton.setText(NbBundle.getMessage(SystemDataPageController.class, "ApplyButton.text"));
    cancelButton.setText(NbBundle.getMessage(SystemDataPageController.class, "CancelButton.text"));

    cancelButton.setOnAction(
        weakAdapter.wrap(
            new EventHandler<ActionEvent>() {

              @Override
              public void handle(ActionEvent event) {
                fxform.reload();
              }
            }));

    applyButton.setOnAction(
        weakAdapter.wrap(
            (EventHandler<ActionEvent>)
                event -> {
                  fxform.commit();
                  deviceController.execute(
                      () -> {
                        try {
                          deviceController.getDataModel().sendSystemData();
                        } catch (DeviceConnectionException | BusyException ex) {
                          log.warn("False to send system data!", ex);
                        }
                      });
                }));
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceController;
  }
}
