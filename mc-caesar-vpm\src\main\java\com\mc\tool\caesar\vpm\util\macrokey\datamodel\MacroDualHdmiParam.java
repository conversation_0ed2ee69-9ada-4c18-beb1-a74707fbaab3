package com.mc.tool.caesar.vpm.util.macrokey.datamodel;

import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.api.interfaces.Oidable;
import java.util.Collection;
import java.util.Collections;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * .
 */
@Data
@AllArgsConstructor
public class MacroDualHdmiParam implements MacroParam {
  private DataObject dataObject;
  private int index;

  @Override
  public String getFullName() {
    return String.format("[%d] %05d   %s", index, dataObject.getId(), dataObject.getName());
  }

  @Override
  public String getSimpleName() {
    return Integer.toString(index);
  }

  @Override
  public Collection<MacroParam> getChildren() {
    return Collections.emptyList();
  }

  @Override
  public void addChild(MacroParam param) {}

  @Override
  public void removeChild(MacroParam param) {}

  @Override
  public boolean hasOid() {
    return dataObject instanceof Oidable;
  }

  @Override
  public int getOid() {
    if (dataObject instanceof Oidable) {
      return ((Oidable) dataObject).getOid();
    } else {
      return -1;
    }
  }
}
