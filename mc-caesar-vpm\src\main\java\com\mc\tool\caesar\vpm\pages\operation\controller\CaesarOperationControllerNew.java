package com.mc.tool.caesar.vpm.pages.operation.controller;

import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.crossscreen.view.CaesarCrossScreenFuncPane;
import com.mc.tool.caesar.vpm.pages.operation.irregularcrossscreen.view.CaesarIrregularCrossScreenFuncPane;
import com.mc.tool.caesar.vpm.pages.operation.multiscreen.view.CaesarMultiScreenFuncPane;
import com.mc.tool.caesar.vpm.pages.operation.videowall.view.CaesarVideoWallFuncPane;
import com.mc.tool.caesar.vpm.pages.operation.view.CaesarOperationFuncPane;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCrossScreenFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarIrregularCrossScreenFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMultiScreenFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarVideoWallFunc;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.operation.controller.OperationPageControllable;
import com.mc.tool.framework.operation.office.OfficeWindow;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.ResourceBundle;
import javafx.application.Platform;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ChangeListener;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Bounds;
import javafx.scene.control.ComboBox;
import javafx.scene.control.ListCell;
import javafx.scene.image.ImageView;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarOperationControllerNew implements Initializable, OperationPageControllable {
  @FXML private HBox mainContainer;

  @FXML private ComboBox<VisualEditFunc> functionCombobox;

  private VisualEditModel systemEditModel = null;

  private ObjectProperty<VisualEditFunc> currentFunc = new SimpleObjectProperty<>();

  private Map<VisualEditFunc, CaesarOperationFuncPane> funcPanes = new HashMap<>();

  private CaesarOperationFuncPane defaultPane;

  private CaesarDeviceController deviceController = null;

  private ObjectProperty<VisualEditTerminal> previewTerminal = new SimpleObjectProperty<>();

  private ImageView previewImageView = new ImageView();

  private ChangeListener<VisualEditTerminal> previewChangeListener;

  private WeakAdapter weakAdapter = new WeakAdapter();

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
      for (CaesarOperationFuncPane pane : funcPanes.values()) {
        if (pane != null) {
          pane.getViewControllable().setDeviceController(deviceController);
        }
      }
    }
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceController;
  }

  @Override
  public void initModel(VisualEditModel model) {
    systemEditModel = model;
    defaultPane = new CaesarOperationFuncPane(systemEditModel);
  }

  @Override
  public void switchToFunction(String funcGuid) {
    for (VisualEditFunc func : systemEditModel.getAllOnlineOperatableFuncs()) {
      if (func.getGuid().equals(funcGuid)) {
        setCurrentFunc(func);
        OfficeWindow.hideWindow();
        return;
      }
    }
    log.warn("Can not find function {}!", funcGuid);
  }

  @Override
  public boolean isVideoPreviewable() {
    return false;
  }

  @Override
  public ObjectProperty<VisualEditTerminal> previewVideoProperty() {
    return null;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    functionCombobox.setItems(systemEditModel.getAllOnlineOperatableFuncs());
    functionCombobox.setCellFactory((view) -> createCell());
    functionCombobox.setButtonCell(createCell());

    HBox.setHgrow(defaultPane, Priority.ALWAYS);
    mainContainer.getChildren().add(defaultPane);

    previewChangeListener = (obs, oldVal, newVal) -> previewTerminal.set(newVal);

    currentFunc.bind(functionCombobox.getSelectionModel().selectedItemProperty());
    currentFunc.addListener(
        weakAdapter.wrap(
            (observable, oldVal, newVal) -> {
              mainContainer.getChildren().clear();
              if (newVal != null) {
                HBox.setHgrow(getOperationView(newVal), Priority.ALWAYS);
                mainContainer.getChildren().add(getOperationView(newVal));
              } else {
                mainContainer.getChildren().add(defaultPane);
              }

              // preview
              if (oldVal != null) {
                CaesarOperationFuncPane pane = getOperationView(oldVal);
                if (pane != null && pane.getSourceSeletionModel() != null) {
                  pane.getSourceSeletionModel()
                      .selectedItemProperty()
                      .removeListener(previewChangeListener);
                  pane.getPreviewContainer().getChildren().remove(previewImageView);
                }
              }
              if (newVal != null) {
                CaesarOperationFuncPane pane = getOperationView(newVal);
                if (pane != null && pane.getSourceSeletionModel() != null) {
                  pane.getSourceSeletionModel()
                      .selectedItemProperty()
                      .addListener(previewChangeListener);
                  pane.getPreviewContainer().getChildren().add(previewImageView);
                  VBox.setVgrow(previewImageView, Priority.ALWAYS);
                  pane.getPreviewContainer()
                      .setStyle("-fx-border-width:1 0 0 0; -fx-border-color:#cccccc");

                  previewImageView
                      .fitWidthProperty()
                      .bind(pane.getPreviewContainer().widthProperty());
                  previewImageView
                      .fitHeightProperty()
                      .bind(pane.getPreviewContainer().heightProperty());
                  previewTerminal.set(pane.getSourceSeletionModel().getSelectedItem());
                }
              }
            }));
    // 默认显示第一个
    Platform.runLater(this::selectOne);
    systemEditModel
        .getAllOnlineOperatableFuncs()
        .addListener(weakAdapter.wrap((ListChangeListener<VisualEditFunc>) change -> selectOne()));
  }

  protected void selectOne() {
    if (currentFunc.get() == null) {
      ObservableList<VisualEditFunc> funcs = systemEditModel.getAllOnlineOperatableFuncs();
      if (!funcs.isEmpty()) {
        setCurrentFunc(funcs.get(0));
      }
    }
  }

  /**
   * 设置当前的func.
   *
   * @param func 功能组.
   */
  public void setCurrentFunc(VisualEditFunc func) {
    if (functionCombobox.getItems().contains(func)) {
      functionCombobox.getSelectionModel().select(func);
    }
  }

  protected CaesarOperationFuncPane getOperationView(VisualEditFunc func) {
    if (funcPanes.get(func) != null) {
      return funcPanes.get(func);
    } else {
      if (func instanceof CaesarVideoWallFunc) {
        CaesarOperationFuncPane pane =
            new CaesarVideoWallFuncPane(
                (CaesarVideoWallFunc) func, systemEditModel, deviceController);
        pane.setOnSwitch(this::onSwitch);
        funcPanes.put(func, pane);
        return pane;
      } else if (func instanceof CaesarCrossScreenFunc) {
        CaesarOperationFuncPane pane =
            new CaesarCrossScreenFuncPane(
                (CaesarCrossScreenFunc) func, systemEditModel, deviceController);
        pane.setOnSwitch(this::onSwitch);
        funcPanes.put(func, pane);
        return pane;
      } else if (func instanceof CaesarMultiScreenFunc) {
        CaesarOperationFuncPane pane =
            new CaesarMultiScreenFuncPane(
                (CaesarMultiScreenFunc) func, systemEditModel, deviceController);
        pane.setOnSwitch(this::onSwitch);
        funcPanes.put(func, pane);
        return pane;
      } else if (func instanceof CaesarIrregularCrossScreenFunc) {
        CaesarOperationFuncPane pane =
            new CaesarIrregularCrossScreenFuncPane(
                (CaesarIrregularCrossScreenFunc) func, systemEditModel, deviceController);
        pane.setOnSwitch(this::onSwitch);
        funcPanes.put(func, pane);
        return pane;
      }
      return null;
    }
  }

  protected void onSwitch() {
    CaesarOperationFuncPane pane = getOperationView(currentFunc.get());
    if (pane == null) {
      return;
    }
    Bounds bounds = pane.getFunctionViewContainer().getBoundsInLocal();
    bounds = pane.getFunctionViewContainer().localToScreen(bounds);
    OfficeWindow.showWindow(
        this,
        systemEditModel,
        pane.getFunctionViewContainer().getScene().getWindow(),
        bounds.getMinX(),
        bounds.getMinY(),
        bounds.getWidth(),
        bounds.getHeight());
  }

  private ListCell<VisualEditFunc> createCell() {
    return new ComboListCell();
  }

  @Override
  public void close() {
    for (CaesarOperationFuncPane pane : funcPanes.values()) {
      pane.close();
    }
  }

  @Override
  public void refreshOnShow() {
    for (CaesarOperationFuncPane pane : funcPanes.values()) {
      pane.refreshOnShow();
    }
  }

  static class ComboListCell extends ListCell<VisualEditFunc> {
    @Override
    protected void updateItem(VisualEditFunc item, boolean empty) {
      super.updateItem(item, empty);

      if (empty || item == null) {
        textProperty().unbind();
        setText("");
      } else {
        textProperty().bind(item.nameProperty());
      }
    }
  }
}
