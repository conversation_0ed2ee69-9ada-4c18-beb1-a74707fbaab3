package com.mc.tool.caesar.vpm.pages.operation.videowall.view;

import com.google.gson.Gson;
import com.mc.tool.caesar.api.datamodel.SourceCropData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.videowall.controller.Bundle;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.DraggedVideoSourceInfo;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminalCropWrapper;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminalWrapper;
import com.mc.tool.framework.systemedit.datamodel.Resolution;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.UndecoratedDialog;
import java.util.Arrays;
import javafx.event.ActionEvent;
import javafx.event.EventHandler;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.control.AlertEx;
import javafx.scene.control.ButtonType;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Label;
import javafx.scene.control.MenuItem;
import javafx.scene.control.TreeCell;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.input.ClipboardContent;
import javafx.scene.input.Dragboard;
import javafx.scene.input.MouseEvent;
import javafx.scene.input.TransferMode;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.scene.paint.Color;
import javafx.scene.text.TextAlignment;
import javafx.stage.Modality;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarVideoWallVideoSourceTreeCell extends TreeCell<VisualEditNode> {

  @Data
  static class CropData {
    String name = "Crop";
    int left = 0;
    int top = 0;
    int right = 0;
    int bottom = 0;
  }

  static class MenuTx extends MenuItem {
    MenuTx(EventHandler<ActionEvent> handler) {
      setText(Bundle.NbBundle.getMessage("signalCut.add"));
      setOnAction(handler);
    }
  }

  static class MenuEdit extends MenuItem {
    MenuEdit(EventHandler<ActionEvent> handler) {
      setText(Bundle.NbBundle.getMessage("signalCut.edit"));
      setOnAction(handler);
    }
  }

  static class MenuDelete extends MenuItem {
    MenuDelete(EventHandler<ActionEvent> handler) {
      setText(Bundle.NbBundle.getMessage("signalCut.delete"));
      setOnAction(handler);
    }
  }

  private HBox box;
  private HBox innerBox;
  private Label nameLabel;
  private Label indexLabel;

  private CaesarDeviceController deviceController;

  /**
   * .
   */
  public CaesarVideoWallVideoSourceTreeCell(CaesarDeviceController deviceController) {
    this.deviceController = deviceController;
    box = new HBox();
    box.setAlignment(Pos.CENTER_LEFT);
    nameLabel = new Label();
    indexLabel = new Label();
    innerBox = new HBox();
    innerBox.setAlignment(Pos.CENTER);
    innerBox.getChildren().add(indexLabel);
    Region region = new Region();
    HBox.setHgrow(region, Priority.ALWAYS);
    box.getChildren().addAll(nameLabel, innerBox);
    box.setSpacing(10);
    indexLabel.setAlignment(Pos.CENTER);
    indexLabel.setTextAlignment(TextAlignment.CENTER);
    indexLabel.setTextFill(Color.web("#333333"));
    indexLabel.setPadding(new Insets(0));
    innerBox.setStyle("-fx-background-color: #cccccc; -fx-background-radius: 15; "
        + "-fx-pref-height: 15; -fx-pref-width: 15; " + "-fx-min-height: 15; -fx-min-width: 15; "
        + "-fx-max-height: 15; -fx-max-width: 15");
    innerBox.managedProperty().bind(innerBox.visibleProperty());
  }

  @Override
  protected void updateItem(VisualEditNode item, boolean empty) {
    super.updateItem(item, empty);
    setContextMenu(null);
    if (!empty) {
      graphicProperty().unbind();
      boolean supportCropTx = deviceController != null
          && deviceController.getDataModel().getConfigMetaData().getUtilVersion().supportCropTx();
      if (item instanceof VisualEditTerminal && !(item instanceof CaesarCpuTerminalCropWrapper)) {
        VisualEditTerminal terminal = (VisualEditTerminal) item;
        nameLabel.textProperty().bind(item.nameProperty());
        nameLabel.graphicProperty().bind(new CaesarVideoWallTargetDeviceLogoBinding(terminal));
        if (item instanceof CaesarCpuTerminalWrapper) {
          // 双HDMI TX
          CaesarCpuTerminalWrapper wrapper = (CaesarCpuTerminalWrapper) terminal;
          indexLabel.setText((wrapper.getIndex() + 1) + "");
          innerBox.setVisible(wrapper.canSeperate());
          if (supportCropTx) {
            // 添加右键菜单
            ContextMenu menu = new ContextMenu();
            menu.getItems().add(new MenuTx(this::onAddSignalCutAction));
            setContextMenu(menu);
          }
        } else {
          innerBox.setVisible(false);
        }
        setGraphic(box);
        textProperty().unbind();
        setText(null);
        this.setStyle("-fx-font-weight: normal");
      } else if (item instanceof CaesarCpuTerminalCropWrapper && supportCropTx) {
        // TX裁剪
        CaesarCpuTerminalCropWrapper wrapper = (CaesarCpuTerminalCropWrapper) item;
        int sourceCropIndex = wrapper.getSourceCropIndex();
        SourceCropData cropData = deviceController.getDataModel().getConfigData().getSourceCropData(sourceCropIndex);
        if (cropData != null) {
          nameLabel.textProperty().unbind();
          nameLabel.graphicProperty().bind(new CaesarVideoWallTargetDeviceLogoBinding(wrapper));
          nameLabel.setText(String.format("%s(%d,%d,%d,%d)", cropData.getName(), cropData.getLeft(), cropData.getTop(),
              cropData.getRight(), cropData.getBottom()));
          innerBox.setVisible(false);
          setGraphic(box);
          textProperty().unbind();
          setText(null);
          this.setStyle("-fx-font-weight: normal");
          // 添加右键菜单
          ContextMenu menu = new ContextMenu();
          menu.getItems().add(new MenuEdit(this::onEditSignalCutAction));
          menu.getItems().add(new MenuDelete(this::onDeleteSignalCutAction));
          setContextMenu(menu);
        } else {
          textProperty().set("mismatch at " + sourceCropIndex);
          setGraphic(null);
          this.setStyle("-fx-font-weight: bold;");
        }
      } else {
        textProperty().bind(item.nameProperty());
        setGraphic(null);
        this.setStyle("-fx-font-weight: bold;");
      }
      setOnDragDetected(this::onDragDetected);
    } else {
      graphicProperty().unbind();
      setGraphic(null);
      textProperty().unbind();
      setText(null);
    }
  }

  protected void onDragDetected(MouseEvent event) {
    if (getItem() != null && getItem() instanceof VisualEditTerminal) {
      ClipboardContent content = new ClipboardContent();
      content.putString(getItem().getGuid());
      DraggedVideoSourceInfo info = new DraggedVideoSourceInfo();
      if (getItem() instanceof CaesarCpuTerminalWrapper) {
        CaesarCpuTerminalWrapper wrapper = (CaesarCpuTerminalWrapper) getItem();
        info.setSourceIndex(wrapper.getIndex());
      }
      if (getItem() instanceof CaesarCpuTerminalCropWrapper) {
        CaesarCpuTerminalCropWrapper wrapper = (CaesarCpuTerminalCropWrapper) getItem();
        info.setCropIndex(wrapper.getSourceCropIndex() + 1);
      }
      content.putUrl(new Gson().toJson(info));
      Image image = getDragImage();
      Dragboard dbDragboard = this.startDragAndDrop(TransferMode.ANY);
      dbDragboard.setDragView(image, image.getWidth() / 2, image.getHeight() / 2);
      dbDragboard.setContent(content);
    }
    event.consume();
  }

  protected Image getDragImage() {
    return ((ImageView) nameLabel.getGraphic()).getImage();
  }

  protected boolean editCropData(Resolution resolution, CropData cropData) {
    UndecoratedDialog<ButtonType> dialog = new UndecoratedDialog<>();
    dialog.setTitle(Bundle.NbBundle.getMessage("signalCut.title"));
    dialog.initOwner(this.getScene().getWindow());
    dialog.initModality(Modality.APPLICATION_MODAL);
    SignalCuttingConfigView configView =
        new SignalCuttingConfigView(cropData.getName(), resolution.getWidth() == 0 ? 1920 : resolution.getWidth(),
            resolution.getHeight() == 0 ? 1080 : resolution.getHeight(), cropData.getLeft(),
            cropData.getTop(), cropData.getRight(), cropData.getBottom());
    dialog.getDialogPane().setContent(configView.getView());
    dialog.getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);
    Node okButton = dialog.getDialogPane().lookupButton(ButtonType.OK);
    okButton.disableProperty().bind(configView.getPresenter().validProperty().not());
    dialog.showAndWait();
    if (dialog.getResult() == ButtonType.OK) {
      cropData.setTop(configView.getPresenter().getTop());
      cropData.setRight(configView.getPresenter().getRight());
      cropData.setBottom(configView.getPresenter().getBottom());
      cropData.setLeft(configView.getPresenter().getLeft());
      cropData.setName(configView.getPresenter().getName());
      return true;
    } else {
      return false;
    }
  }

  protected void onAddSignalCutAction(ActionEvent event) {
    SourceCropData cropData = deviceController.getDataModel().getConfigDataManager().getFreeSourceCropData();
    if (cropData == null) {
      AlertEx alert = new AlertEx(AlertEx.AlertExType.WARNING);
      alert.setContentText(Bundle.NbBundle.getMessage("signalCut.noFreeClipData"));
      alert.showAndWait();
      return;
    }
    VisualEditNode node = getItem();
    if (node instanceof CaesarCpuTerminalWrapper) {
      CaesarCpuTerminalWrapper wrapper = (CaesarCpuTerminalWrapper) node;
      CaesarCpuTerminal terminal = wrapper.getTerminal();
      if (terminal != null && terminal.getCpuData() != null) {
        // 编辑数据
        Resolution res = terminal.getResolution(wrapper.getIndex());
        final CropData editableCropData = new CropData();
        if (editCropData(res, editableCropData)) {
          // 发送数据
          deviceController.execute(() -> {
            cropData.setLeft(editableCropData.getLeft());
            cropData.setTop(editableCropData.getTop());
            cropData.setRight(editableCropData.getRight());
            cropData.setBottom(editableCropData.getBottom());
            cropData.setCpuIndex(terminal.getCpuData().getOid() + 1);
            cropData.setSourceIndex(wrapper.getIndex());
            cropData.setName(editableCropData.getName());
            try {
              deviceController.getDataModel().sendSourceCropData(Arrays.asList(cropData));
            } catch (DeviceConnectionException e) {
              log.error("Failed to send source crop data", e);
            } catch (BusyException e) {
              log.error("Failed to send source crop data", e);
            }
          });
        }
      }
    }
  }


  protected void onEditSignalCutAction(ActionEvent event) {
    VisualEditNode item = getItem();
    if (!(item instanceof CaesarCpuTerminalCropWrapper)) {
      return;
    }
    CaesarCpuTerminalCropWrapper wrapper = (CaesarCpuTerminalCropWrapper) item;
    int sourceCropIndex = wrapper.getSourceCropIndex();
    SourceCropData cropData = deviceController.getDataModel().getConfigData().getSourceCropData(sourceCropIndex);
    if (cropData == null) {
      return;
    }
    CaesarCpuTerminal terminal = wrapper.getTerminalWrapper().getTerminal();
    if (terminal != null && terminal.getCpuData() != null) {
      // 编辑数据
      final CropData editableCropData = new CropData();
      editableCropData.setLeft(cropData.getLeft());
      editableCropData.setTop(cropData.getTop());
      editableCropData.setRight(cropData.getRight());
      editableCropData.setBottom(cropData.getBottom());
      editableCropData.setName(cropData.getName());
      Resolution res = terminal.getResolution(wrapper.getTerminalWrapper().getIndex());
      if (editCropData(res, editableCropData)) {
        // 发送数据
        deviceController.execute(() -> {
          cropData.setName(editableCropData.getName());
          cropData.setLeft(editableCropData.getLeft());
          cropData.setTop(editableCropData.getTop());
          cropData.setRight(editableCropData.getRight());
          cropData.setBottom(editableCropData.getBottom());
          try {
            deviceController.getDataModel().sendSourceCropData(Arrays.asList(cropData));
          } catch (DeviceConnectionException e) {
            log.error("Failed to send source crop data", e);
          } catch (BusyException e) {
            log.error("Failed to send source crop data", e);
          }
        });
      }
    }
  }

  protected void onDeleteSignalCutAction(ActionEvent event) {
    VisualEditNode item = getItem();
    if (!(item instanceof CaesarCpuTerminalCropWrapper)) {
      return;
    }
    CaesarCpuTerminalCropWrapper wrapper = (CaesarCpuTerminalCropWrapper) item;
    int sourceCropIndex = wrapper.getSourceCropIndex();
    SourceCropData cropData = deviceController.getDataModel().getConfigData().getSourceCropData(sourceCropIndex);
    if (cropData == null) {
      return;
    }
    // 发送数据
    deviceController.execute(() -> {
      cropData.setCpuIndex(0);
      cropData.setSourceIndex(0);
      try {
        deviceController.getDataModel().sendSourceCropData(Arrays.asList(cropData));
      } catch (DeviceConnectionException e) {
        log.error("Failed to send source crop data", e);
      } catch (BusyException e) {
        log.error("Failed to send source crop data", e);
      }
    });
  }
}
