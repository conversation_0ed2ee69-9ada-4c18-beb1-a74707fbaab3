package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Setter;

/**
 * 全光拼接屏图层信息.
 */
@Setter
@JsonPropertyOrder({
    VideoWallLayer.JSON_PROPERTY_NAME,
    VideoWallLayer.JSON_PROPERTY_ID,
    VideoWallLayer.JSON_PROPERTY_TYPE,
    VideoWallLayer.JSON_PROPERTY_STATUS,
    VideoWallLayer.JSON_PROPERTY_ZORDER,
    VideoWallLayer.JSON_PROPERTY_WINDOW,
    VideoWallLayer.JSON_PROPERTY_SOURCE
})
public class VideoWallLayer {
  public static final String JSON_PROPERTY_NAME = "name";
  private String name;

  public static final String JSON_PROPERTY_ID = "id";
  private Integer id;

  public static final String JSON_PROPERTY_TYPE = "type";
  private Integer type;

  public static final String JSON_PROPERTY_STATUS = "status";
  private Integer status;

  public static final String JSON_PROPERTY_ZORDER = "zorder";
  private Integer zorder;

  public static final String JSON_PROPERTY_WINDOW = "window";
  private LayerWindow window;

  public static final String JSON_PROPERTY_SOURCE = "source";
  private List<LayerSource> source = null;

  /**
   * .
   */
  public VideoWallLayer name(String name) {
    this.name = name;
    return this;
  }

  /**
   * 图层名称，与诺瓦/layer/detailList接口的name匹配.
   *
   * @return name
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public String getName() {
    return name;
  }

  /**
   * .
   */
  public VideoWallLayer id(Integer id) {
    this.id = id;
    return this;
  }

  /**
   * 图层ID,与诺瓦/layer/detailList接口的layerId匹配.
   *
   * @return id
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getId() {
    return id;
  }

  /**
   * .
   */
  public VideoWallLayer type(Integer type) {
    this.type = type;
    return this;
  }

  /**
   * 图层类型，0表示普通信号图层，1表示凯中TX信号全屏图层，2表示凯中TX信号四宫格图层.
   *
   * @return type
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getType() {
    return type;
  }

  /**
   * .
   */
  public VideoWallLayer status(Integer status) {
    this.status = status;
    return this;
  }

  /**
   * 图层状态，0表示正常，1表示无资源可用.
   *
   * @return status
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getStatus() {
    return status;
  }

  /**
   * .
   */
  public VideoWallLayer zorder(Integer zorder) {
    this.zorder = zorder;
    return this;
  }

  /**
   * 上下层位置.
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_ZORDER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getZorder() {
    return zorder;
  }

  /**
   * .
   */
  public VideoWallLayer window(LayerWindow window) {
    this.window = window;
    return this;
  }

  /**
   * Get window.
   **/
  @Nullable
  @Valid
  @JsonProperty(JSON_PROPERTY_WINDOW)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public LayerWindow getWindow() {
    return window;
  }

  /**
   * .
   */
  public VideoWallLayer source(List<LayerSource> source) {
    this.source = source;
    return this;
  }

  /**
   * .
   */
  public VideoWallLayer addSourceItem(LayerSource sourceItem) {
    if (this.source == null) {
      this.source = new ArrayList<>();
    }
    this.source.add(sourceItem);
    return this;
  }

  /**
   * 图层信号源.
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_SOURCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public List<LayerSource> getSource() {
    return source;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    VideoWallLayer videoWallLayer = (VideoWallLayer) o;
    return Objects.equals(this.name, videoWallLayer.name)
        && Objects.equals(this.id, videoWallLayer.id)
        && Objects.equals(this.type, videoWallLayer.type)
        && Objects.equals(this.status, videoWallLayer.status)
        && Objects.equals(this.zorder, videoWallLayer.zorder)
        && Objects.equals(this.window, videoWallLayer.window)
        && Objects.equals(this.source, videoWallLayer.source);
  }

  @Override
  public int hashCode() {
    return Objects.hash(name, id, type, status, zorder, window, source);
  }


  @Override
  public String toString() {
    return "VideoWallLayer {\n"
        + "    name: " + toIndentedString(name) + "\n"
        + "    id: " + toIndentedString(id) + "\n"
        + "    type: " + toIndentedString(type) + "\n"
        + "    status: " + toIndentedString(status) + "\n"
        + "    zorder: " + toIndentedString(zorder) + "\n"
        + "    window: " + toIndentedString(window) + "\n"
        + "    source: " + toIndentedString(source) + "\n"
        + "}";
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
