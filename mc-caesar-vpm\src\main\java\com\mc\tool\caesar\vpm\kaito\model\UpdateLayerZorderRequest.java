package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import javax.annotation.Nullable;
import lombok.Setter;

/**
 * UpdateLayerZorderRequest.
 */
@Setter
@JsonPropertyOrder({
    UpdateLayerZorderRequest.JSON_PROPERTY_ACTION
})
public class UpdateLayerZorderRequest {
  public static final String JSON_PROPERTY_ACTION = "action";
  private Integer action;

  /**
   * .
   */
  public UpdateLayerZorderRequest action(Integer action) {
    this.action = action;
    return this;
  }

  /**
   * 0:none,1:up,2:down,3:top,4:bottom.
   *
   * @return action
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_ACTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getAction() {
    return action;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    UpdateLayerZorderRequest updateLayerZorderRequest = (UpdateLayerZorderRequest) o;
    return Objects.equals(this.action, updateLayerZorderRequest.action);
  }

  @Override
  public int hashCode() {
    return Objects.hash(action);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("UpdateLayerZorderRequest {\n");
    sb.append("    action: ").append(toIndentedString(action)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
