package com.mc.tool.caesar.vpm.pages.systemlog;

import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.MatrixDefinitionData;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.systemlog.Bundle.NbBundle;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.UndecoratedAlert;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;
import java.util.ResourceBundle;
import javafx.application.Platform;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredList;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.Button;
import javafx.scene.control.CheckBox;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.stage.DirectoryChooser;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class SystemLogPageController implements Initializable, ViewControllable {

  @FXML private Button downloadButton;
  @FXML private TableView<SystemLogData> tableView;
  @FXML private TableColumn<SystemLogData, String> nameCol;
  @FXML private TableColumn<SystemLogData, String> typeCol;
  @FXML private TableColumn<SystemLogData, String> portCol;
  @FXML private TableColumn<SystemLogData, Boolean> checkBoxCol;

  private CheckBox selectAllCheckBox = new CheckBox();

  private CaesarDeviceController deviceController = null;

  ObservableList<SystemLogData> logLists = FXCollections.observableArrayList();

  private SimpleBooleanProperty isDownloadlog = new SimpleBooleanProperty(false);
  private SimpleBooleanProperty selectedAll = new SimpleBooleanProperty(false);

  private WeakAdapter weakAdapter = new WeakAdapter();

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
      this.deviceController
          .submitAsync(() -> Utilities.getMatrixModels(this.deviceController.getDataModel()))
          .whenComplete(
              (data, throwable) -> {
                if (throwable != null) {
                  log.warn("Fail to get matrix models!", throwable);
                  return;
                }
                PlatformUtility.runInFxThread(() -> nodesImpl(data));
              });
    } else {
      log.warn("Error device controller type : {} !", deviceController.getClass().getName());
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    downloadButton.setText(NbBundle.getMessage("SystemLog.downloadButton.text"));

    downloadButton.disableProperty().bind(isDownloadlog);

    selectedAll.addListener(
        weakAdapter.wrap(
            (observable, oldValue, newValue) -> {
              if (newValue != null) {
                for (SystemLogData item : logLists) {
                  item.setSelected(newValue);
                }
              }
            }));

    nameCol.setText(NbBundle.getMessage("SystemLog.nameCol.text"));
    typeCol.setText(NbBundle.getMessage("SystemLog.typeCol.text"));
    portCol.setText(NbBundle.getMessage("SystemLog.portCol.text"));

    selectedAll.bind(selectAllCheckBox.selectedProperty());
    checkBoxCol.setGraphic(selectAllCheckBox);

    nameCol
        .prefWidthProperty()
        .bind(
            tableView.widthProperty().subtract(checkBoxCol.prefWidthProperty().add(20)).divide(3));
    typeCol
        .prefWidthProperty()
        .bind(
            tableView.widthProperty().subtract(checkBoxCol.prefWidthProperty().add(20)).divide(3));
    portCol
        .prefWidthProperty()
        .bind(
            tableView.widthProperty().subtract(checkBoxCol.prefWidthProperty().add(20)).divide(3));

    nameCol.setCellValueFactory((item) -> item.getValue().getNameProperty());
    typeCol.setCellValueFactory((item) -> item.getValue().getTypeProperty());
    portCol.setCellValueFactory((item) -> item.getValue().getPortsProperty());
    checkBoxCol.setCellFactory((col) -> new SystemLogTableCell());
    tableView.setItems(logLists);
    initDownloadButton();
  }

  private void nodesImpl(
      Collection<Pair<MatrixDefinitionData, CaesarSwitchDataModel>> matricesModels) {
    CaesarSwitchDataModel model = this.deviceController.getDataModel();
    if (!logLists.isEmpty()) {
      logLists.clear();
    }
    for (Pair<MatrixDefinitionData, CaesarSwitchDataModel> pair : matricesModels) {
      MatrixDefinitionData mdd = pair.getKey();
      CaesarSwitchDataModel matModel = pair.getValue();
      if (null == mdd) {
        continue;
      }
      String address = mdd.getAddress();
      if (matModel == null) {
        log.warn("Fail to connect to {}.", address);
        continue;
      }

      StringProperty property =
          matModel.getConfigData().getSystemConfigData().getSystemData().getDeviceProperty();
      SystemLogData mainBoard = new SystemLogData((byte) 0, (byte) 0, address);
      mainBoard.getNameProperty().bind(property);
      mainBoard.setType(NbBundle.getMessage("SystemLog.type.main_board"));
      logLists.add(mainBoard);
      for (ModuleData moduleData : matModel.getSwitchModuleData().getModuleDatas()) {
        if (moduleData == null) {
          continue;
        }
        if (moduleData.isStatusActive()
            && mdd.getFirstModule() <= moduleData.getOid()
            && moduleData.getOid() <= mdd.getLastModule()) {
          byte localLevel2 = 0;
          Pair<Integer, Integer> portRange = Utilities.getModulePortRange(model, moduleData);
          int startIndex = portRange.getKey() - 1;
          int endIndex = portRange.getValue();

          for (int i = startIndex; i < endIndex; i++) {
            localLevel2 = (byte) (localLevel2 + 1);
            PortData portData = model.getConfigData().getPortData(i);
            ExtenderData extenderData = portData.getExtenderData();
            if (extenderData != null
                && !extenderData.isStatusFixPort()
                && !extenderData.isUniType()) {
              int localPort = 0;
              if (isPortInMatix(extenderData.getPort(), mdd)
                  && portData.getOid() + 1 == extenderData.getPort()) {
                localPort = extenderData.getPort();
              } else if (isPortInMatix(extenderData.getRdPort(), mdd)
                  && !isPortInMatix(extenderData.getPort(), mdd)) {
                localPort = extenderData.getRdPort();
              }
              if (localPort != 0) {
                SystemLogData data =
                    new SystemLogData((byte) moduleData.getOid(), localLevel2, address);
                String type = "";
                if (Utilities.areBitsSet(
                    extenderData.getType(), true, CaesarConstants.Extender.Type.CON)) {
                  type = NbBundle.getMessage("SystemLog.type.rx");
                } else if (Utilities.areBitsSet(
                    extenderData.getType(), true, CaesarConstants.Extender.Type.CPU)) {
                  type = NbBundle.getMessage("SystemLog.type.tx");
                }
                data.setName(extenderData.getName());
                data.setType(type);
                data.setPorts(String.valueOf(extenderData.getPort()));
                // logLists.add(data);
              }
            }
          }
        }
      }
    }
  }

  private void startReclog(String directory) {
    log.info(String.format("User(%s) download system log to %s.", deviceController.getLoginUser(), directory));
    ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    Task<Void> task =
        new Task<Void>() {

          @Override
          protected Void call() throws Exception {
            this.updateMessage(NbBundle.getMessage("SystemLog.task.msg"));
            Thread.sleep(1000);
            CaesarSwitchDataModel model = deviceController.getDataModel();
            isDownloadlog.setValue(true);
            FilteredList<SystemLogData> syslogList = logLists.filtered(SystemLogData::getSelected);
            for (SystemLogData sldata : syslogList) {
              deviceController.execute(
                  () -> {
                    try {
                      model.startRecSyslog((byte) sldata.getLevel1(), sldata.getLevel2());
                      saveReclog(directory, sldata);
                    } catch (ConfigException | BusyException ex) {
                      log.error(String.format("Request for %s log failed", sldata.getName()));
                    }
                  });
            }
            return null;
          }
        };
    task.setOnSucceeded(
        (event) ->
            Platform.runLater(
                () -> {
                  UndecoratedAlert alert = new UndecoratedAlert(AlertExType.INFORMATION);
                  if (tableView != null && tableView.getScene() != null) {
                    alert.initOwner(tableView.getScene().getWindow());
                  }
                  alert.setTitle(NbBundle.getMessage("SystemLog.Alert.information.title"));
                  alert.setHeaderText(null);
                  alert.setContentText(NbBundle.getMessage("SystemLog.Alert.information.text"));
                  alert.showAndWait();
                  isDownloadlog.setValue(false);
                }));
    app.getTaskManager().addForegroundTask(task);
  }

  private void saveReclog(String directory, SystemLogData sldata) {
    DatagramSocket socket = null;
    FileOutputStream fos = null;
    DatagramPacket packet;
    try {
      Date date = new Date();
      SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
      String dateNowStr = sdf.format(date);
      String filename =
          directory
              + "\\"
              + dateNowStr
              + "\\"
              + sldata.getName()
              + "_"
              + sldata.getType()
              + "_"
              + sldata.getPorts()
              + ".txt";
      File file = new File(filename);
      File fileParent = file.getParentFile();
      if (!fileParent.exists() && !fileParent.mkdirs()) {
        log.warn("can not make dirs");
      }
      if (!file.exists() && !file.createNewFile()) {
        log.warn("can not create new file");
      }
      byte[] syslogEnd = {0x1b, 0x30, 0x29};

      socket = new DatagramSocket(50060);

      try {
        fos = new FileOutputStream(file, true);
        while (true) {
          byte[] buf = new byte[1024];
          packet = new DatagramPacket(buf, buf.length);
          socket.setSoTimeout(2000);
          try {
            socket.receive(packet);
          } catch (IOException ex) {
            log.warn("Fail to receive packet!", ex);
            socket.close();
          }
          byte[] mess = packet.getData();
          if (mess[0] == syslogEnd[0] && mess[1] == syslogEnd[1] && mess[2] == syslogEnd[2]) {
            log.info(sldata.getName() + " REC OK!");
            break;
          }
          for (int i = mess.length - 1; mess[i] == 0 && i > 0; i--) {
            mess[i] = 0x2d;
          }
          fos.write(mess);
        }
      } catch (FileNotFoundException fex) {
        log.warn("Fail to find file " + file.getName(), fex);
      } finally {
        if (fos != null) {
          fos.close();
        }
      }
    } catch (IOException ex) {
      log.warn("Fail to save log!", ex);
    } finally {
      if (socket != null) {
        socket.close();
      }
    }
  }

  private void initDownloadButton() {
    downloadButton.setOnAction(
        event -> {
          FilteredList<SystemLogData> syslogList = logLists.filtered(SystemLogData::getSelected);
          if (!syslogList.isEmpty()) {
            DirectoryChooser chooser = new DirectoryChooser();
            File filepath = chooser.showDialog(null);
            if (filepath != null) {
              final String filePath = filepath.getPath();
              final String path = filePath.replaceAll("\\\\", "\\\\\\\\");
              startReclog(path);
            }
          } else {
            UndecoratedAlert alert = new UndecoratedAlert(AlertExType.WARNING);
            if (tableView != null && tableView.getScene() != null) {
              alert.initOwner(tableView.getScene().getWindow());
            }
            alert.setTitle(NbBundle.getMessage("SystemLog.Alert.warning.title"));
            alert.setHeaderText(null);
            alert.setContentText(NbBundle.getMessage("SystemLog.Alert.warning.text"));
            alert.showAndWait();
          }
        });
  }

  private boolean isPortInMatix(int port, MatrixDefinitionData mdd) {
    int minPort = mdd.getFirstPort();
    int maxPort = mdd.getLastPort();

    return port >= minPort && port <= maxPort;
  }

  public boolean isDownload() {
    return isDownloadlog.get();
  }

  private static class SystemLogTableCell extends TableCell<SystemLogData, Boolean> {
    @Override
    public void updateItem(Boolean item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);

      if (!empty) {
        Object rowItem = this.getTableRow().getItem();
        SystemLogData data = null;
        if (rowItem instanceof SystemLogData) {
          data = (SystemLogData) rowItem;
        }

        CheckBox checkBox = new CheckBox();
        this.setGraphic(checkBox);
        if (data != null) {
          checkBox.selectedProperty().bindBidirectional(data.getSelectedProperty());
        }
      }
    }
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceController;
  }
}
