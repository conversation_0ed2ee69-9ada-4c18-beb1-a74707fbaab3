package com.mc.tool.caesar.vpm.pages.operation.videowall.vp;

import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoData;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.util.vp.Vp6Constants;
import com.mc.tool.caesar.vpm.util.vp.VpVideoData;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;

/**
 * .
 */
public class CaesarVpVideoData implements VpVideoData {

  private CaesarVideoData videoData;

  public CaesarVpVideoData(CaesarVideoData videoData) {
    this.videoData = videoData;
  }

  @Override
  public String getName() {
    return videoData.getName().get();
  }

  @Override
  public int getTxId() {
    VisualEditTerminal terminal = videoData.getSource().get();
    if (terminal instanceof CaesarCpuTerminal) {
      CaesarCpuTerminal tx = (CaesarCpuTerminal) terminal;
      return tx.getExtenderData() == null ? 0 : tx.getExtenderData().getId();
    } else {
      return 0;
    }
  }

  @Override
  public int getConfigCropIndex() {
    return videoData.getCropIndex().get();
  }

  @Override
  public int getGridCropIndex() {
    switch (videoData.getSourceType().get()) {
      case GB28181: {
        if (videoData.getGb28181SourceData().get() != null) {
          return videoData.getGb28181SourceData().get().getGridIndex().get();
        } else {
          return -1;
        }
      }
      default:
        return -1;
    }
  }

  @Override
  public int getSourceIndex() {
    return videoData.getSourceIndex().get();
  }

  @Override
  public int getLeft() {
    return videoData.getXpos().get();
  }

  @Override
  public int getTop() {
    return videoData.getYpos().get();
  }

  @Override
  public int getWidth() {
    return videoData.getWidth().get();
  }

  @Override
  public int getHeight() {
    return videoData.getHeight().get();
  }

  @Override
  public int getAlpha() {
    return (int) (videoData.getAlpha().get() * Vp6Constants.ALPHA_FACTOR);
  }

  @Override
  public VpCropTpe getCropType() {
    switch (videoData.getSourceType().get()) {
      case NORMAL: {
        if (videoData.getCropIndex().get() > 0) {
          return VpCropTpe.CONFIG;
        } else {
          return VpCropTpe.NONE;
        }
      }
      case GB28181: {
        return VpCropTpe.GRID;
      }
      default:
        return VpCropTpe.NONE;
    }
  }

  @Override
  public boolean isVisible() {
    switch (videoData.getSourceType().get()) {
      case GB28181:
        return videoData.getGb28181SourceData().get() != null
            && videoData.getGb28181SourceData().get().getStatus().get() == CaesarVideoData.Gb28181SourceData.Status.OK;
      default:
        return true;
    }
  }
}
