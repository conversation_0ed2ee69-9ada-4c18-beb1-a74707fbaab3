package com.mc.tool.caesar.vpm.pages.hostconfiguration;

import cn.hutool.core.io.StreamProgress;
import cn.hutool.http.HttpUtil;
import com.jfoenix.controls.JFXDatePicker;
import com.jfoenix.controls.JFXTimePicker;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.utility.InjectorProvider;
import java.io.File;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ResourceBundle;
import javafx.beans.binding.Bindings;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.stage.DirectoryChooser;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 *
 * <AUTHOR>
 */
@Slf4j
public class EventFormController implements Initializable {

  @FXML
  private JFXDatePicker startDatePicker;
  @FXML
  private JFXTimePicker startTimePicker;
  @FXML
  private JFXDatePicker endDatePicker;
  @FXML
  private JFXTimePicker endTimePicker;

  @FXML
  private Button exportReport;

  private BooleanProperty hasStartDate = new SimpleBooleanProperty(false);
  private BooleanProperty hasEndDate = new SimpleBooleanProperty(false);
  private BooleanProperty hasStartTime = new SimpleBooleanProperty(false);
  private BooleanProperty hasEndTime = new SimpleBooleanProperty(false);

  private final CaesarDeviceController deviceController;

  public EventFormController(CaesarDeviceController deviceController) {
    this.deviceController = deviceController;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    startTimePicker.set24HourView(true);
    endTimePicker.set24HourView(true);

    startDatePicker
        .dayCellFactoryProperty()
        .addListener((observable, oldValue, newValue) -> System.out.println());

    hasStartDate.bind(
        Bindings.createBooleanBinding(
            () -> startDatePicker.getValue() != null, startDatePicker.valueProperty()));
    hasStartTime.bind(
        Bindings.createBooleanBinding(
            () -> startTimePicker.getValue() != null, startTimePicker.valueProperty()));
    hasEndDate.bind(
        Bindings.createBooleanBinding(
            () -> endDatePicker.getValue() != null, endDatePicker.valueProperty()));
    hasEndTime.bind(
        Bindings.createBooleanBinding(
            () -> endTimePicker.getValue() != null, endTimePicker.valueProperty()));

    exportReport
        .disableProperty()
        .bind(
            hasStartDate.not().or(hasStartTime.not()).or(hasEndDate.not()).or(hasEndTime.not()));
    exportReport.setOnAction(
        (actionEvent) -> {
          DirectoryChooser chooser = new DirectoryChooser();
          ApplicationBase base = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
          File filepath = chooser.showDialog(base.getMainWindow());
          if (filepath != null) {
            final String filePath = filepath.getPath();
            String filename =
                filePath
                    + "\\"
                    + "trigger-event"
                    + startDatePicker.getValue()
                    + " "
                    + startTimePicker.getValue().toString().replace(":", "-")
                    + "_"
                    + endDatePicker.getValue()
                    + " "
                    + endTimePicker.getValue().toString().replace(":", "-")
                    + ".csv";
            String url =
                String.format(
                    "http://%s:%s/mediacomm/trigger-event?start-time=%tF %tR&end-time=%tF %tR",
                    deviceController.getDefaultIp(),
                    "8080",
                    startDatePicker.getValue(),
                    startTimePicker.getValue(),
                    endDatePicker.getValue(),
                    endTimePicker.getValue());
            String encodeUrl = HttpUtil.encodeParams(url, StandardCharsets.UTF_8);
            Task<Void> task = new DownloadTask(encodeUrl, filename);
            base.getTaskManager().addForegroundTask(task);
          }
        });
  }

  static class DownloadTask extends Task<Void> {
    private final String encodeUrl;
    private final String filename;

    /**
     * .
     */
    public DownloadTask(String encodeUrl, String filename) {
      this.encodeUrl = encodeUrl;
      this.filename = filename;
    }

    @Override
    protected Void call() throws Exception {
      updateMessage("Please Wait...");
      try {
        File file = new File(filename);
        boolean newFile = file.createNewFile();
        if (!newFile) {
          log.warn("the file " + filename + " already exists");
        }
        HttpUtil.downloadFile(
            encodeUrl,
            file,
            new StreamProgress() {
              @Override
              public void start() {
              }

              @Override
              public void progress(long total, long progressSize) {
                updateProgress(progressSize, total);
              }

              @Override
              public void finish() {
              }
            });
      } catch (Exception ex) {
        log.error("Download trigger-event fail", ex);
      }
      return null;
    }

  }
}
