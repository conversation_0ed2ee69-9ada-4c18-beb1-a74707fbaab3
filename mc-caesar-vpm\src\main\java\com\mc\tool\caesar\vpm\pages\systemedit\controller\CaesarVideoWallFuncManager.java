package com.mc.tool.caesar.vpm.pages.systemedit.controller;

import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarVideoWallFunc;

/**
 * .
 */
public class CaesarVideoWallFuncManager {
  private CaesarVideoWallFunc[] funcs;

  public CaesarVideoWallFuncManager() {
    funcs = new CaesarVideoWallFunc[VideoWallGroupData.GROUP_COUNT];
  }

  /** 设置视频墙组. */
  public void setVideoWallFunc(int index, CaesarVideoWallFunc func) {
    if (index < 0 || index >= funcs.length) {
      return;
    }
    funcs[index] = func;
  }

  /** 获取视频墙组. */
  public CaesarVideoWallFunc getVideoWallFunc(int index) {
    if (index < 0 || index >= funcs.length) {
      return null;
    }
    return funcs[index];
  }
}
