package com.mc.tool.caesar.vpm.pages.systemedit;

import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.utility.InjectorProvider;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.fxml.FXMLLoader;
import javafx.scene.layout.AnchorPane;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class CaesarSystemEditPageView extends AnchorPane {
  @Getter private SystemEditControllable controllable;

  /** Contructor. */
  public CaesarSystemEditPageView(VisualEditModel model) {
    try {
      controllable = InjectorProvider.getInjector().getInstance(SystemEditControllable.class);
      controllable.initModel(model);
      URL location =
          Thread.currentThread()
              .getContextClassLoader()
              .getResource("com/mc/tool/caesar/vpm/pages/systemedit/system_edit_view.fxml");
      FXMLLoader loader = new FXMLLoader(location);
      loader.setController(controllable);
      loader.setResources(ResourceBundle.getBundle("com/mc/tool/caesar/vpm/i18n/systemedit"));
      loader.setRoot(this);
      loader.load();
    } catch (IOException exc) {
      log.warn("Can not load system_edit_view.fxml", exc);
    }
  }
}
