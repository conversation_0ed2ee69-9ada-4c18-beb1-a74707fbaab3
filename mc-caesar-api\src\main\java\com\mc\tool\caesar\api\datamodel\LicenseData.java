package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.Utilities;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * .
 */
public class LicenseData {

  private static final Logger LOG = Logger.getLogger(LicenseData.class.getName());
  public static final String PROPERTY_BASE = "LicenseData.";
  public static final String FIELD_LICENCE_BITS = "LicenseBits";
  public static final String FIELD_SERIAL = "Serial";
  public static final String PROPERTY_LICENSE_BITS_JAVA = "LicenseData.LicenseBits.Java";
  public static final String PROPERTY_LICENSE_BITS_EXTENDED = "LicenseData.LicenseBits.Extended";
  public static final String PROPERTY_LICENSE_BITS_API = "LicenseData.LicenseBits.Api";
  public static final String PROPERTY_LICENSE_BITS_SYSLOG = "LicenseData.LicenseBits.Syslog";
  public static final String PROPERTY_LICENSE_BITS_SNMP = "LicenseData.LicenseBits.Snmp";
  public static final String PROPERTY_LICENSE_BITS_CASCADING = "LicenseData.LicenseBits.Cascading";
  public static final String PROPERTY_LICENSE_BITS_MSC =
      "LicenseData.LicenseBits.MultiScreenControl";
  public static final String PROPERTY_LICENSE_KEY = "LicenseData.LicenseKey";
  public static final String PROPERTY_SERIAL = "LicenseData.Serial";

  private static final int DATA_COUNT = 31;
  @Expose
  private int serial = 0;
  @Expose
  protected int refDate;
  @Expose
  private String licenseKey;
  @Expose
  private Map<Integer, Integer> functionDateMap = new HashMap<Integer, Integer>();
  //private DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");
  @Expose
  private List<Integer> rawDateList = new ArrayList<Integer>();
  @Expose
  private List<Integer> rawValueList = new ArrayList<Integer>();

  /**
   * .
   */
  public LicenseData() {
    for (int i = 0; i < DATA_COUNT; i++) {
      rawDateList.add(0);
      rawValueList.add(0);
    }
  }

  public boolean hasLicenseJava() {
    return isValid(this.functionDateMap.get(Integer.valueOf(CaesarConstants.Licence.Bits.JAVA)));
  }

  public boolean hasLicenseExtended() {
    return isValid(
        this.functionDateMap.get(Integer.valueOf(CaesarConstants.Licence.Bits.EXTENDED)));
  }

  public boolean hasLicenseApi() {
    return isValid(this.functionDateMap.get(Integer.valueOf(CaesarConstants.Licence.Bits.API)));
  }

  public boolean hasLicenseSyslog() {
    return isValid(this.functionDateMap.get(Integer.valueOf(CaesarConstants.Licence.Bits.SYSLOG)));
  }

  public boolean hasLicenseSnmp() {
    return isValid(this.functionDateMap.get(Integer.valueOf(CaesarConstants.Licence.Bits.SNMP)));
  }

  public boolean hasLicenseCascading() {
    return isValid(
        this.functionDateMap.get(Integer.valueOf(CaesarConstants.Licence.Bits.CASCADING)));
  }

  public boolean hasLicenseMultiScreenControl() {
    return isValid(
        this.functionDateMap.get(Integer.valueOf(CaesarConstants.Licence.Bits.MULTISCREENCONTROL)));
  }

  public boolean hasLicense(int key) {
    return isValid(this.functionDateMap.get(Integer.valueOf(key)));
  }

  public int getSerial() {
    return this.serial;
  }

  public void setSerial(int serial) {
    this.serial = serial;
  }

  public String getLicenseKey() {
    return this.licenseKey;
  }

  public void setLicenseKey(String licenseKey) {
    this.licenseKey = licenseKey;
  }

  public int getReferenceDate() {
    return this.refDate;
  }

  public void setReferenceDate(LocalDate date) {
    String sdate = date.toString();
    this.refDate = Integer.parseInt(sdate, 16);
  }

  private boolean isValid(Integer date) {
    System.out.println(date);
    return true;
  }

  private void addLicenseData(int bit, int data) {
    Integer oldData = this.functionDateMap.get(Integer.valueOf(bit));
    if (oldData == null || oldData.intValue() < data) {
      this.functionDateMap.put(Integer.valueOf(bit), Integer.valueOf(data));
    }
  }

  /**
   * .
   */
  public void read(CfgReader cfgReader) throws ConfigException {
    this.functionDateMap.clear();
    this.rawDateList.clear();
    this.rawValueList.clear();

    setSerial(cfgReader.readInteger());
    for (int i = 0; i < 31; i++) {
      int value = cfgReader.readInteger();
      int data = cfgReader.readInteger();

      this.rawValueList.add(Integer.valueOf(value));
      this.rawDateList.add(Integer.valueOf(data));
      if (data != -1) {
        if (Utilities.areBitsSet(value, CaesarConstants.Licence.Bits.JAVA)) {
          addLicenseData(CaesarConstants.Licence.Bits.JAVA, data);
        }
        if (Utilities.areBitsSet(value, CaesarConstants.Licence.Bits.EXTENDED)) {
          addLicenseData(CaesarConstants.Licence.Bits.EXTENDED, data);
        }
        if (Utilities.areBitsSet(value, CaesarConstants.Licence.Bits.API)) {
          addLicenseData(CaesarConstants.Licence.Bits.API, data);
        }
        if (Utilities.areBitsSet(value, CaesarConstants.Licence.Bits.SNMP)) {
          addLicenseData(CaesarConstants.Licence.Bits.SNMP, data);
        }
        if (Utilities.areBitsSet(value, CaesarConstants.Licence.Bits.SYSLOG)) {
          addLicenseData(CaesarConstants.Licence.Bits.SYSLOG, data);
        }
        if (Utilities.areBitsSet(value, CaesarConstants.Licence.Bits.CASCADING)) {
          addLicenseData(CaesarConstants.Licence.Bits.CASCADING, data);
        }
        if (Utilities.areBitsSet(value, CaesarConstants.Licence.Bits.MULTISCREENCONTROL)) {
          addLicenseData(CaesarConstants.Licence.Bits.MULTISCREENCONTROL, data);
        }
      }
    }
  }

  /**
   * .
   */
  public void write(CfgWriter writer) {
    try {
      writer.writeInteger(getSerial());
      for (int i = 0; i < 31; i++) {
        writer.writeInteger(this.rawValueList.get(i).intValue());
        writer.writeInteger(this.rawDateList.get(i).intValue());
      }
    } catch (ConfigException ex) {
      LOG.log(Level.WARNING, "Fail to write data!", ex);
    }
  }
}

