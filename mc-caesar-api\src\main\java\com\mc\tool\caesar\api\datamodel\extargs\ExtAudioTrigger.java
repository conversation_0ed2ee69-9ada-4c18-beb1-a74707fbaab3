package com.mc.tool.caesar.api.datamodel.extargs;

/**
 * .
 *
 * <AUTHOR>
 */
public enum ExtAudioTrigger implements ExtArgObject {
  DISABLE(-1, "OFF"),
  AT_0(0, "0s"),
  AT_1(1, "1s"),
  AT_2(2, "2s"),
  AT_3(3, "3s"),
  AT_4(4, "4s"),
  AT_5(5, "5s");

  private final int value;
  private final String name;

  ExtAudioTrigger(int value, String name) {
    this.value = value;
    this.name = name;
  }

  @Override
  public byte[] toBytes() {
    return new byte[] {(byte) getValue()};
  }

  public int getValue() {
    return value;
  }

  @Override
  public String toString() {
    return name;
  }
}
