package com.mc.tool.caesar.vpm.pages.systemdata;

import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.caesar.vpm.pages.systemdata.Bundle.NbBundle;
import com.mc.tool.framework.interfaces.Page;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.scene.layout.Pane;

/**
 * .
 */
public class CaesarSystemDataPage implements Page {

  private final SystemDataPageView view;
  private final BooleanProperty visibility = new SimpleBooleanProperty(true);
  public static final String NAME = "systemdata";

  public CaesarSystemDataPage(CaesarEntity entity) {
    view = new SystemDataPageView(entity.getController());
    view.getController().setDeviceController(entity.getController());
  }

  @Override
  public String getTitle() {
    return NbBundle.getMessage(CaesarSystemDataPage.class, "systemdata.pagetitle");
  }

  @Override
  public String getName() {
    return NAME;
  }

  @Override
  public Pane getView() {
    return view;
  }

  @Override
  public String getStyleClass() {
    return null;
  }

  @Override
  public BooleanProperty getVisibleProperty() {
    return visibility;
  }

  @Override
  public void showObject(Object object) {}
}
