package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;

/**
 * AccessData.
 */
public final class AccessData extends AbstractData {

  public static final String PROPERTY_BASE = "SystemConfigData.AccessData.";
  public static final String FIELD_TIMEOUT_DISPLAY = "TimeoutDisplay";
  public static final String PROPERTY_TIMEOUT_DISPLAY = PROPERTY_BASE + FIELD_TIMEOUT_DISPLAY;
  public static final String FIELD_TIMEOUT_LOGOUT = "TimeoutLogout";
  public static final String PROPERTY_TIMEOUT_LOGOUT = PROPERTY_BASE + FIELD_TIMEOUT_LOGOUT;
  public static final String PROPERTY_FORCE_BITS_LOGIN = PROPERTY_BASE + "ForceBits.Login";
  public static final String PROPERTY_FORCE_BITS_USERLOCK = PROPERTY_BASE + "ForceBits.UserLock";
  public static final String PROPERTY_FORCE_BITS_CONLOCK = PROPERTY_BASE + "ForceBits.ConLock";
  public static final String PROPERTY_FORCE_BITS_CPUDISCONNECT =
      PROPERTY_BASE + "ForceBits.CpuDisconnct";
  public static final String PROPERTY_FORCE_BITS_CONACCESS = PROPERTY_BASE + "ForceBits.ConAccess";
  public static final String PROPERTY_FORCE_BITS_USERACCESS =
      PROPERTY_BASE + "ForceBits.UserAccess";
  public static final String PROPERTY_FORCE_BITS_USERCONOR =
      "SystemConfigData.AccessData.ForceBits.UserConOr";
  public static final String PROPERTY_FORCE_BITS_USERCONAND =
      "SystemConfigData.AccessData.ForceBits.UserConAnd";
  private final SystemConfigData systemConfigData;
  @Expose
  private int timeoutDisplay; // specify inactivity time to quit OSD
  // automatically(0=deactivited),单位为秒
  @Expose
  private int timeoutLogout; // specify inactivity time for automatic user
  // logout(0=deactivited,-1=unlimited)
  // 单位为分钟

  /**
   * .
   */
  public AccessData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager,
      String fqn, SystemConfigData systemConfigData) {
    super(pcs, configDataManager, -1, fqn);
    this.systemConfigData = systemConfigData;
    initCommitRollback();
  }

  @Override
  public void initDefaults() {
    setTimeoutDisplay(0);
    setTimeoutLogout(0);
  }

  public int getTimeoutLogout() {
    return this.timeoutLogout;
  }

  /**
   * .
   */
  public void setTimeoutLogout(int timeoutLogout) {
    int oldValue = this.timeoutLogout;
    this.timeoutLogout = timeoutLogout;
    firePropertyChange(AccessData.PROPERTY_TIMEOUT_LOGOUT, oldValue, timeoutLogout);
  }

  public int getTimeoutDisplay() {
    return this.timeoutDisplay;
  }

  /**
   * .
   */
  public void setTimeoutDisplay(int timeoutDisplay) {
    int oldValue = this.timeoutDisplay;
    this.timeoutDisplay = timeoutDisplay;
    firePropertyChange(AccessData.PROPERTY_TIMEOUT_DISPLAY, oldValue, timeoutDisplay);
  }

  public boolean isConLock() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.CONLOCK);
  }

  /**
   * .
   */
  public void setConLock(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, CaesarConstants.System.Forcebits.CONLOCK));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isUserLock() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.USERLOCK);
  }

  /**
   * .
   */
  public void setUserLock(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, CaesarConstants.System.Forcebits.USERLOCK));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isLogin() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.LOGIN);
  }

  /**
   * .
   */
  public void setLogin(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, CaesarConstants.System.Forcebits.LOGIN));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isConAccess() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.CONACCESS);
  }

  /**
   * .
   */
  public void setConAccess(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, CaesarConstants.System.Forcebits.CONACCESS));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isUserAccess() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.USERACCESS);
  }

  /**
   * .
   */
  public void setUserAccess(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, CaesarConstants.System.Forcebits.USERACCESS));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isCpuDisconnect() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.CPUDISCONNECT);
  }

  /**
   * .
   */
  public void setCpuDisconnect(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, CaesarConstants.System.Forcebits.CPUDISCONNECT));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isUserConOr() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.USERCONOR);
  }

  /**
   * .
   */
  public void setUserConOr(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, CaesarConstants.System.Forcebits.USERCONOR));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  public boolean isUserConAnd() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.USERCONAND);
  }

  /**
   * .
   */
  public void setUserConAnd(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, CaesarConstants.System.Forcebits.USERCONAND));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (AccessData.PROPERTY_TIMEOUT_DISPLAY.equals(propertyName)) {
      setTimeoutDisplay((Integer) value);
    } else if (AccessData.PROPERTY_TIMEOUT_LOGOUT.equals(propertyName)) {
      setTimeoutLogout((Integer) value);
    }
  }
}

