package com.mc.tool.caesar.api;

import com.mc.tool.caesar.api.CaesarConstants.Module.Type;

/**
 * .
 */
public abstract class CaesarCpuTypeProperty {

  public abstract int getPortsPerIo();

  public abstract boolean isIoVirtual();

  public abstract boolean hasTrunk();

  public abstract int getIoUpdateStartAddress(int matrixPorts);

  public abstract int getTrunkUpdateStartAddress();

  public abstract CaesarConstants.Module.Type getType();

  static final int IO_36_PORT_START_ADDR = 0x1000000;
  static final int IO_96_PORT_START_ADDR = 0x2000000;

  static final CaesarCpuTypeProperty[] CPU_TYPES = new CaesarCpuTypeProperty[]{
      new SingleCpuTypeProperty(),
      new Stack36T9x36B(),
      new Stack96T1x36B(),
      new Stack96T12x24B(),
      new Plugin144(),
      new Plugin384(),
      new Plugin816()
  };

  /**
   * 获取CPU类型的属性.
   *
   * @param value 类型的值.
   * @return 属性
   */
  public static CaesarCpuTypeProperty getProperty(int value) {
    for (CaesarCpuTypeProperty item : CPU_TYPES) {
      if (item.getType().getValue() == value) {
        return item;
      }
    }
    return null;
  }


  static class SingleCpuTypeProperty extends CaesarCpuTypeProperty {

    @Override
    public int getPortsPerIo() {
      return CaesarConstants.Module.DEFAULT_PORTS;
    }

    @Override
    public boolean isIoVirtual() {
      return true;
    }

    @Override
    public int getIoUpdateStartAddress(int matrixPorts) {
      if (matrixPorts == 96) {
        return IO_96_PORT_START_ADDR;
      } else {
        return IO_36_PORT_START_ADDR;
      }
    }

    @Override
    public int getTrunkUpdateStartAddress() {
      return 0;
    }

    @Override
    public Type getType() {
      return CaesarConstants.Module.Type.SINGLE_CPU;
    }

    @Override
    public boolean hasTrunk() {
      return false;
    }
  }

  static class Stack36T9x36B extends CaesarCpuTypeProperty {

    @Override
    public int getPortsPerIo() {
      return CaesarConstants.Module.COMBINDED_PORTS;
    }

    @Override
    public boolean isIoVirtual() {
      return false;
    }

    @Override
    public int getIoUpdateStartAddress(int matrixPorts) {
      return IO_36_PORT_START_ADDR;
    }

    @Override
    public int getTrunkUpdateStartAddress() {
      return IO_36_PORT_START_ADDR;
    }

    @Override
    public Type getType() {
      return CaesarConstants.Module.Type.STACK_36T_9x36B;
    }

    @Override
    public boolean hasTrunk() {
      return true;
    }

  }

  static class Stack96T1x36B extends Stack36T9x36B {

    @Override
    public int getTrunkUpdateStartAddress() {
      return IO_96_PORT_START_ADDR;
    }

    @Override
    public Type getType() {
      return CaesarConstants.Module.Type.STACK_96T_1x36B;
    }
  }

  static class Stack96T12x24B extends Stack96T1x36B {

    @Override
    public int getPortsPerIo() {
      return 24;
    }

    @Override
    public Type getType() {
      return CaesarConstants.Module.Type.STACK_96T_12x24B;
    }
  }

  static class Plugin384 extends Stack96T12x24B {

    @Override
    public Type getType() {
      return CaesarConstants.Module.Type.PLUGIN_384;
    }
  }

  static class Plugin816 extends Stack96T12x24B {

    @Override
    public Type getType() {
      return CaesarConstants.Module.Type.PLUGIN_816;
    }
  }

  static class Plugin144 extends Stack96T1x36B {

    @Override
    public int getPortsPerIo() {
      return 8;
    }

    @Override
    public Type getType() {
      return CaesarConstants.Module.Type.PLUGIN_144;
    }

    @Override
    public int getTrunkUpdateStartAddress() {
      return 0x1000000;
    }
  }
}
