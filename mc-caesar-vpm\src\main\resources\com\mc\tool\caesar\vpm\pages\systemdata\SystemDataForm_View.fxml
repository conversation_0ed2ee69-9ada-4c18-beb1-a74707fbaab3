<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<VBox fx:id="pane" stylesheets="@style.css" xmlns="http://javafx.com/javafx/8"
  xmlns:fx="http://javafx.com/fxml/1">
  <children>
    <HBox>
      <children>
        <Label id="device-form-label" layoutX="22.0" layoutY="77.0" prefHeight="33.0"
          prefWidth="250.0" text="Label"/>
        <VBox>
          <children>
            <TextField id="device-form-editor" prefHeight="15.0" prefWidth="250.0"/>
            <Label id="device-form-tooltip" prefHeight="20.0" prefWidth="250.0" text="Label"
              wrapText="true" VBox.vgrow="ALWAYS"/>
          </children>
        </VBox>
      </children>
      <VBox.margin>
        <Insets left="10.0"/>
      </VBox.margin>
    </HBox>
    <HBox>
      <children>
        <Label id="name-form-label" layoutX="22.0" layoutY="137.0" prefHeight="33.0"
          prefWidth="250.0" text="Label"/>
        <VBox>
          <children>
            <TextField id="name-form-editor" prefHeight="20.0" prefWidth="250.0"/>
            <Label id="name-form-tooltip" prefHeight="20.0" prefWidth="250.0" text="Label"
              wrapText="true"/>
          </children>
        </VBox>
      </children>
      <VBox.margin>
        <Insets left="10.0"/>
      </VBox.margin>
    </HBox>
    <HBox>
      <children>
        <Label id="info-form-label" layoutX="22.0" layoutY="194.0" prefHeight="33.0"
          prefWidth="250.0" text="Label"/>
        <VBox>
          <children>
            <TextArea id="info-form-editor" prefHeight="70.0" prefWidth="250.0" wrapText="true"/>
            <Label id="info-form-tooltip" prefHeight="20.0" prefWidth="250.0" text="Label"
              wrapText="true"/>
          </children>
        </VBox>
      </children>
      <VBox.margin>
        <Insets left="10.0"/>
      </VBox.margin>
    </HBox>
    <HBox>
      <children>
        <Label id="masterIp-form-label" layoutX="22.0" layoutY="253.0" prefHeight="33.0"
          prefWidth="250.0" text="Label"/>
        <VBox>
          <children>
            <TextField id="masterIp-form-editor" prefHeight="20.0" prefWidth="250.0"/>
            <Label id="masterIp-form-tooltip" prefHeight="20.0" prefWidth="250.0" text="Label"
              wrapText="true"/>
          </children>
        </VBox>
      </children>
      <VBox.margin>
        <Insets left="10.0"/>
      </VBox.margin>
    </HBox>

  </children>
</VBox>
