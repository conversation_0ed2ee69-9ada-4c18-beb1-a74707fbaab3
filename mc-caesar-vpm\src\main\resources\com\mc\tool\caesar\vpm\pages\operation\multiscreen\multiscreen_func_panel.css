#root {
  -fx-background-color: #ffffff;
}

#menu-btn {
  -fx-graphic: url("../menu_normal.png");
  -fx-text-fill: #333333;
}

#menu-btn:hover {
  -fx-graphic: url("../menu_hover.png");
  -fx-text-fill: #f08519;
}

#switch-btn {
  -fx-graphic: url("../switch_normal.png");
  -fx-text-fill: #333333;
}

#switch-btn:hover {
  -fx-graphic: url("../switch_hover.png");
  -fx-text-fill: #f08519;
}

.titleBox {
  -fx-background-color: #e6e6e6;
}

.titleLabel {
  -fx-padding: 0 0 0 10;
}

.property-box {
  -fx-alignment: center_left;
  -fx-padding: 12px;
  -fx-spacing: 4px;
}

#button-box {
  -fx-padding: 10px;
  -fx-spacing: 5px;
}


#rxList .column-header-background {
  -fx-border-color: -fx-table-cell-border-color;
  -fx-border-width: 0 0 1 0;
}

#rxList .column-header {
  -fx-background-color: white;
  -fx-border-color: transparent -fx-table-cell-border-color transparent transparent;
  -fx-border-width: 1;
}

#rxList .column-header:last-visible {
  -fx-background-color: white;
  -fx-border-width: 0;
}

.color-box {
  -fx-padding: 0 0 0 200;
  -fx-spacing: 5;
}

#fullRegion {
  -fx-background-color: #00e7d4;
}

#videoRegion {
  -fx-background-color: #f2bd00;
}

