package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.Setter;

/**
 * UpdateLayerSourceRequest.
 */
@Setter
@JsonPropertyOrder({
  UpdateLayerSourceRequest.JSON_PROPERTY_LAYER_TYPE,
  UpdateLayerSourceRequest.JSON_PROPERTY_SOURCES
})
public class UpdateLayerSourceRequest {
  public static final String JSON_PROPERTY_LAYER_TYPE = "layerType";
  private Integer layerType;

  public static final String JSON_PROPERTY_SOURCES = "sources";
  private List<List<Object>> sources = new ArrayList<>();

  /**
   * .
   */
  public UpdateLayerSourceRequest layerType(Integer layerType) {
    this.layerType = layerType;
    return this;
  }

  /**
   * .
   */
  @JsonProperty(JSON_PROPERTY_LAYER_TYPE)
  @JsonInclude()
  public Integer getLayerType() {
    return layerType;
  }


  public UpdateLayerSourceRequest sources(List<List<Object>> sources) {
    this.sources = sources;
    return this;
  }

  public UpdateLayerSourceRequest addSourcesItem(List<Object> sourcesItem) {
    this.sources.add(sourcesItem);
    return this;
  }

  /**
   * .
   */
  @JsonProperty(JSON_PROPERTY_SOURCES)
  @JsonInclude()
  public List<List<Object>> getSources() {
    return sources;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    UpdateLayerSourceRequest updateLayerSourceRequest = (UpdateLayerSourceRequest) o;
    return Objects.equals(this.layerType, updateLayerSourceRequest.layerType)
        && Objects.equals(this.sources, updateLayerSourceRequest.sources);
  }

  @Override
  public int hashCode() {
    return Objects.hash(layerType, sources);
  }


  @Override
  public String toString() {
    return "UpdateLayerSourceRequest {\n"
        + "    layerType: " + toIndentedString(layerType) + "\n"
        + "    sources: " + toIndentedString(sources) + "\n" + "}";
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
