package com.mc.tool.caesar.vpm.devices;

import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import lombok.Getter;

/**
 * .
 */
public class CaesarDemoDeviceController extends CaesarDeviceController {

  @Getter private String model;

  public CaesarDemoDeviceController(
      CaesarSwitchDataModel dataModel, String user, String pwd, String defaultIp, String model) {
    super(dataModel, user, pwd, defaultIp);
    this.model = model;
  }

  @Override
  public void init() {}

  @Override
  public boolean isDemo() {
    return true;
  }

  @Override
  public void close() {
    dataModel = null;
    if (executor != null) {
      executor.shutdown();
      executor = null;
    }
  }
}
