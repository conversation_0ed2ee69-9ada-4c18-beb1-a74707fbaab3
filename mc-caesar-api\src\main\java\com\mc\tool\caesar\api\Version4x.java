package com.mc.tool.caesar.api;

import com.mc.tool.caesar.api.datamodel.ConfigMetaData;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.api.datamodel.MatrixData;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.datamodel.SystemConfigData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.datamodel.UserGroupData;
import com.mc.tool.caesar.api.version.v3x.ExtenderDataConverter3x;
import com.mc.tool.caesar.api.version.v3x.UserDataConverter3x;
import com.mc.tool.caesar.api.version.v4x.ConsoleDataConverter4x;
import com.mc.tool.caesar.api.version.v4x.CpuDataConverter4x;
import com.mc.tool.caesar.api.version.v4x.FunctionKeyDataConverter4x;
import com.mc.tool.caesar.api.version.v4x.GroupDataConverter4x;
import com.mc.tool.caesar.api.version.v4x.MultiScreenDataConverter4x;
import java.util.Map;

/**
 * .
 */
public class Version4x extends Version3x {

  public static final int VERSION_VALUE = 0x40000;

  public static final int CONFIG_DATA_SIZE = 3511076;
  public static final int META_DATA_SIZE = 0x80;
  public static final int SYSTEM_DATA_SIZE = 0x358;
  public static final int CPU_DATA_SIZE = 0x68;
  public static final int CON_DATA_SIZE = 0x2a2;
  public static final int EXT_DATA_SIZE = 0xab;
  public static final int USER_DATA_SIZE = 0x2d1;
  public static final int USER_GROUP_DATA_SIZE = 0x262;
  public static final int MULTI_SCREEN_DATA_SIZE = 0x9e;
  public static final int MACRO_DATA_SIZE = 0x6b;
  public static final int MATRIX_DATA_SIZE = 0x52;
  public static final int PORT_DATA_SIZE = 0x14;
  public static final int BOARD_DATA_SIZE = 0x35;
  public static final int GROUP_DATA_SIZE = 0x2e;

  public static final int MAX_USER_NUMBER = 256;
  public static final int MAX_USER_GROUP_NUMBER = 64;
  public static final int MAX_MULTI_SCREEN_GROUP_NUMBER = 150;
  public static final int MAX_EXT_NUMBER = 4000;
  public static final int MAX_CON_NUMBER = 2000;
  public static final int MAX_CPU_NUMBER = 2000;
  public static final int MAX_PORT_NUMBER = 6096;
  public static final int MAX_MACROT_NUMBER = 8192;
  public static final int MAX_MATRIX_GRID_NUMBER = 16;
  public static final int MAX_TX_GROUP_NUMBER = 500;

  public static final int SYSTEMCONFIGDATA_OFFSET = 0x80;
  public static final int CPUDATA_OFFSET = 0x3d8;
  public static final int CONDATA_OFFSET = 0x33058;
  public static final int EXTDATA_OFFSET = 0x17c1f8;
  public static final int USERDATA_OFFSET = 0x2231d8;
  public static final int USERGROUPDATA_OFFSET = 0x2502d8;
  public static final int MULTISCREENDATA_OFFSET = 0x259b58;
  public static final int MACRODATA_OFFSET = 0x25f7ec;
  public static final int MATRIXGRIDDATA_OFFSET = 0x3357ec;
  public static final int GROUPDATA_OFFSET = 0x335d0c;
  public static final int PORTDATA_OFFSET = 0x33b6e4;

  protected Version4x() {
    Map<Class, Integer> v4x = classSizes;
    v4x.clear();
    v4x.put(ConfigMetaData.class, META_DATA_SIZE);
    v4x.put(SystemConfigData.class, SYSTEM_DATA_SIZE);
    v4x.put(CpuData.class, CPU_DATA_SIZE);
    v4x.put(ConsoleData.class, CON_DATA_SIZE);
    v4x.put(ExtenderData.class, EXT_DATA_SIZE);
    v4x.put(UserData.class, USER_DATA_SIZE);
    v4x.put(UserGroupData.class, USER_GROUP_DATA_SIZE);
    v4x.put(MultiScreenData.class, MULTI_SCREEN_DATA_SIZE);
    v4x.put(FunctionKeyData.class, MACRO_DATA_SIZE);
    v4x.put(MatrixData.class, MATRIX_DATA_SIZE);
    v4x.put(PortData.class, PORT_DATA_SIZE);
    v4x.put(ModuleData.class, BOARD_DATA_SIZE);
    v4x.put(TxRxGroupData.class, GROUP_DATA_SIZE);

    v4x = classCounts;
    v4x.clear();
    v4x.put(CpuData.class, MAX_CPU_NUMBER);
    v4x.put(ConsoleData.class, MAX_CON_NUMBER);
    v4x.put(ExtenderData.class, MAX_EXT_NUMBER);
    v4x.put(UserData.class, MAX_USER_NUMBER);
    v4x.put(UserGroupData.class, MAX_USER_GROUP_NUMBER);
    v4x.put(MultiScreenData.class, MAX_MULTI_SCREEN_GROUP_NUMBER);
    v4x.put(FunctionKeyData.class, MAX_MACROT_NUMBER);
    v4x.put(MatrixData.class, MAX_MATRIX_GRID_NUMBER);
    v4x.put(PortData.class, MAX_PORT_NUMBER);
    v4x.put(TxRxGroupData.class, MAX_TX_GROUP_NUMBER);

    v4x = classOffsets;
    v4x.clear();
    v4x.put(SystemConfigData.class, SYSTEMCONFIGDATA_OFFSET);
    v4x.put(CpuData.class, CPUDATA_OFFSET);
    v4x.put(ConsoleData.class, CONDATA_OFFSET);
    v4x.put(ExtenderData.class, EXTDATA_OFFSET);
    v4x.put(UserData.class, USERDATA_OFFSET);
    v4x.put(UserGroupData.class, USERGROUPDATA_OFFSET);
    v4x.put(MultiScreenData.class, MULTISCREENDATA_OFFSET);
    v4x.put(FunctionKeyData.class, MACRODATA_OFFSET);
    v4x.put(MatrixData.class, MATRIXGRIDDATA_OFFSET);
    v4x.put(PortData.class, PORTDATA_OFFSET);
    v4x.put(TxRxGroupData.class, GROUPDATA_OFFSET);

    classConverter.put(FunctionKeyData.class,
        new FunctionKeyDataConverter4x(getClassSize(FunctionKeyData.class)));
    classConverter
        .put(ExtenderData.class, new ExtenderDataConverter3x(getClassSize(ExtenderData.class)));
    classConverter.put(CpuData.class, new CpuDataConverter4x(getClassSize(CpuData.class)));
    classConverter.put(MultiScreenData.class, new MultiScreenDataConverter4x());
    classConverter
        .put(TxRxGroupData.class, new GroupDataConverter4x(getClassSize(TxRxGroupData.class)));
    classConverter.put(ConsoleData.class, new ConsoleDataConverter4x());
    classConverter.put(UserData.class, new UserDataConverter3x(getClassSize(UserData.class)));
  }

  @Override
  public String getName() {
    return "DKM4";
  }

  @Override
  public int getVersionValue() {
    return VERSION_VALUE;
  }

  @Override
  public int getMetaDataSize() {
    return META_DATA_SIZE;
  }

  @Override
  public int getNetworkCount() {
    return 4;
  }

  @Override
  public int getConfigDataSize() {
    return CONFIG_DATA_SIZE;
  }

  @Override
  public int getMaxExtNumber() {
    return MAX_EXT_NUMBER;
  }

  @Override
  public boolean hasExtenderStatusInfo() {
    return true;
  }

  @Override
  public boolean hasExtResolution() {
    return true;
  }

  @Override
  public boolean hasExtSerial() {
    return true;
  }

  @Override
  public boolean hasExtVersion() {
    return true;
  }

  @Override
  public boolean hasTxOffset() {
    return true;
  }

  @Override
  public boolean hasMacroKeyName() {
    return true;
  }

  @Override
  public int getFkeySize() {
    return 128;
  }

  @Override
  public int getMacroSize() {
    return 128;
  }

  @Override
  public boolean hasIrregularCrossScreen() {
    return true;
  }

  @Override
  public boolean hasMacroDualHdmi() {
    return true;
  }

  @Override
  public boolean hasTxRxGroupData() {
    return true;
  }

  @Override
  public boolean hasTxRemoteSwitching() {
    return true;
  }

  @Override
  public boolean isOnlyReadValidData() {
    return true;
  }

  @Override
  public boolean hasDoubleBackupMatrix() {
    return true;
  }
}
