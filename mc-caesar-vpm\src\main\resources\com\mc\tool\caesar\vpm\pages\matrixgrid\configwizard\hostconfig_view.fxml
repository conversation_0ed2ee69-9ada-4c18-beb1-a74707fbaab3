<?xml version="1.0" encoding="UTF-8"?>

<?import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridWizardIndicator?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<VBox stylesheets="@hostconfig_view.css" xmlns="http://javafx.com/javafx/8"
  xmlns:fx="http://javafx.com/fxml/1"
  prefWidth="700" prefHeight="464" id="main-container">
  <children>
    <MatrixGridWizardIndicator currentIndex="1"/>
    <VBox id="sub-container">
      <Region prefHeight="45"/>
      <Label styleClass="title-label" text="%hostconfig.title1" id="title1"/>
      <Region prefHeight="10"/>
      <VBox styleClass="info-box">
        <Label text="%hostconfig.info1_1"/>
        <Label text="%hostconfig.info1_2"/>
        <Label text="%hostconfig.info1_3"/>
      </VBox>
      <Region prefHeight="10"/>
      <Label styleClass="title-label" text="%hostconfig.title2" id="title2"/>
      <Region prefHeight="10"/>
      <VBox styleClass="info-box">
        <Label text="%hostconfig.info2_1"/>
      </VBox>
      <Region prefHeight="10"/>
      <TableView prefHeight="149" fx:id="tableView">
        <columns>
          <TableColumn fx:id="categoryCol" text="%hostconfig.table.category"/>
          <TableColumn fx:id="ipCol" text="%hostconfig.table.ip"/>
          <TableColumn fx:id="userCol" text="%hostconfig.table.user"/>
          <TableColumn fx:id="pwdCol" text="%hostconfig.table.pwd"/>
          <TableColumn fx:id="validateCol" text="%hostconfig.table.validate"/>
          <TableColumn fx:id="deleteCol" text="%hostconfig.table.delete"/>
          <TableColumn fx:id="validCol" text="%hostconfig.table.valid"/>
        </columns>
      </TableView>
      <Region prefHeight="10"/>
      <Button fx:id="addMatrixBtn" styleClass="common-button" text="%hostconfig.add-matrix"
        onAction="#onAddMatrix"/>
    </VBox>

  </children>
</VBox>
