package com.mc.tool.caesar.api.datamodel.vp;

import java.util.Collection;

/**
 * .
 */
public interface LayoutInfoGetter {

  int getHorzCount();

  int getVertCount();

  int getResolutionHeight();

  int getResolutionWidth();

  Collection<Integer> getResolutionWidths();

  Collection<Integer> getResolutionHeights();

  boolean isMultipleResolutionEnable();

  int getCompensationScaleThreshold();

  int getLeftCompensation();

  int getRightCompensation();

  int getTopCompensation();

  int getBottomCompensation();

  Collection<Integer> getHorzMargins();

  Collection<Integer> getVertMargins();
}
