<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.canvas.Canvas?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TreeView?>
<?import javafx.scene.Group?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import org.controlsfx.control.MasterDetailPane?>
<?import javafx.scene.control.TextField?>
<AnchorPane xmlns="http://javafx.com/javafx"
            xmlns:fx="http://javafx.com/fxml"
            stylesheets="@splicingscreen.css"
            fx:controller="com.mc.tool.caesar.vpm.pages.splicingscreen.CaesarSplicingScreenController"
            id="root">
  <HBox fx:id="mainContainer" AnchorPane.topAnchor="0"
        AnchorPane.bottomAnchor="0" AnchorPane.leftAnchor="0"
        AnchorPane.rightAnchor="0">
    <VBox id="root" prefHeight="720" prefWidth="1280" HBox.hgrow="ALWAYS">
      <HBox id="header-pane" alignment="CENTER" minHeight="40" prefHeight="40">
        <HBox id="header-toolbox" alignment="CENTER_RIGHT" HBox.Hgrow="ALWAYS">
          <Button id="menu-btn" onAction="#onNewDecoder"
                  styleClass="image-button" text="%toolbar.new_decoder"/>
          <Button id="menu-btn" onAction="#onNewSplicingScreen"
                  styleClass="image-button" text="%toolbar.new_splicing_screen"/>
          <Button id="menu-btn" onAction="#onDeleteSplicingScreen"
                  styleClass="image-button" text="%toolbar.delete_splicing_screen"/>
        </HBox>
      </HBox>
      <Region id="separator" minHeight="1"/>
      <MasterDetailPane fx:id="masterDetailPane" VBox.vgrow="ALWAYS">
        <masterNode>
          <HBox id="body-pane" HBox.hgrow="ALWAYS">
            <HBox HBox.hgrow="ALWAYS" id="main-container">
              <VBox id="function-source" prefWidth="200" minWidth="200">
                <HBox id="source-list-title-container" alignment="CENTER_LEFT" prefHeight="23.0"
                      prefWidth="198.0">
                  <Label id="source-list-title" text="%decoder_group"/>
                </HBox>
                <TreeView VBox.vgrow="ALWAYS" fx:id="sourceTree"/>
                <HBox id="tool-box" minHeight="24" alignment="CENTER_LEFT">
                  <Button text="%create_decoder_group.btn" styleClass="common-button"
                          style="-fx-font-size: 8px;" onAction="#onCreateDecoderGroup"/>
                  <Button text="%delete_decoder_group.btn" styleClass="common-button"
                          style="-fx-font-size: 8px;" fx:id="deleteGroupBtn" onAction="#onDeleteDecoderGroup"/>
                  <Button text="%config_decoder_group.btn" styleClass="common-button"
                          style="-fx-font-size: 8px;" fx:id="decoderGroupConfigBtn" onAction="#onDecoderGroupConfig"/>
                </HBox>
              </VBox>
              <VBox id="function-content" HBox.hgrow="ALWAYS" fx:id="functionContainer">
                <ScrollPane VBox.vgrow="ALWAYS" fitToHeight="true"
                            fitToWidth="true" fx:id="screenScroller">
                  <Group>
                    <StackPane fx:id="screenStack">
                      <Group fx:id="screenGroup">
                        <Canvas fx:id="canvas"/>
                      </Group>
                    </StackPane>
                  </Group>
                </ScrollPane>
                <HBox prefHeight="36" minHeight="36" alignment="CENTER" id="graph-tool-bar">
                  <Label id="zoomin-btn" fx:id="zoominBtn" prefWidth="18"
                         prefHeight="18" onMouseClicked="#onZoomin"/>
                  <Label id="zoomout-btn" fx:id="zoomoutBtn" prefWidth="18"
                         prefHeight="18" onMouseClicked="#onZoomout"/>
                  <Label id="restore-btn" prefWidth="18" prefHeight="18"
                         onMouseClicked="#onRestore"/>
                </HBox>
              </VBox>
            </HBox>
            <VBox id="show-btn-container" fx:id="showBtnContainer"
                  maxWidth="26" prefWidth="26">
              <VBox id="show-btn-sub-container">
                <HBox alignment="CENTER_RIGHT" minHeight="10" prefHeight="10">
                  <Button id="show-property-btn" onAction="#onShowProperty"
                          styleClass="image-button"/>
                </HBox>
                <Region minHeight="1" styleClass="separator"/>
                <Button id="property-btn" styleClass="image-button"/>
                <Region minHeight="1" styleClass="separator"/>
                <Button id="icon-btn" styleClass="image-button"/>
                <Region minHeight="1" styleClass="separator"/>
              </VBox>
              <Region VBox.vgrow="ALWAYS"/>
            </VBox>
          </HBox>
        </masterNode>
        <detailNode>
          <VBox id="right-panel" minWidth="240" prefWidth="240"
                styleClass="right-panel">
            <HBox id="hide-btn-container" minHeight="10" prefHeight="10"
                  styleClass="right-title">
              <Button id="hide-property-btn" onAction="#onHideProperty"
                      styleClass="image-button"/>
            </HBox>
            <VBox VBox.vgrow="ALWAYS">
              <Region minHeight="1" styleClass="separator"/>
              <HBox minHeight="20" prefHeight="20" styleClass="right-title">
                <Label text="%host_label"/>
              </HBox>
              <Region minHeight="1" styleClass="separator"/>
              <VBox>
                <HBox>
                  <Label text="IP" prefWidth="50"/>
                  <TextField fx:id="ipTextField" editable="false" disable="true"/>
                </HBox>
                <HBox>
                  <Label text="%name_label" prefWidth="50"/>
                  <TextField fx:id="nameTextField" editable="false" disable="true"/>
                </HBox>
                <HBox>
                  <Label text="%host_type_label" prefWidth="50"/>
                  <TextField fx:id="typeTextField" editable="false" disable="true"/>
                </HBox>
              </VBox>
              <Region minHeight="1" styleClass="separator"/>
              <HBox minHeight="20" prefHeight="20" styleClass="right-title">
                <Label text="%kaito_input_list"/>
              </HBox>
              <Region minHeight="1" styleClass="separator"/>
              <TableView fx:id="allOpticalList" prefHeight="300">
                <columns>
                  <TableColumn text="%index_column_text" fx:id="allOpticalIndexColumn"/>
                  <TableColumn text="%caesar_card_name_column_text" fx:id="caesarCardNameColumn"/>
                  <TableColumn text="%caesar_card_id_column_text" fx:id="caesarCardIdColumn"/>
                  <TableColumn text="%caesar_card_port_column_text" fx:id="caesarCardPortColumn"/>
                  <TableColumn text="%nowa_card_sn_column_text" fx:id="nowaCardSnColumn"/>
                  <TableColumn text="%nowa_card_id_column_text" fx:id="nowaCardIdColumn"/>
                  <TableColumn text="%nowa_card_port_column_text" fx:id="nowaCardPortColumn"/>
                </columns>
              </TableView>
              <Region minHeight="1" styleClass="separator"/>
              <HBox minHeight="20" prefHeight="20" styleClass="right-title">
                <Label text="%rx_list.label"/>
              </HBox>
              <Region minHeight="1" styleClass="separator"/>
              <TableView fx:id="rxList" prefHeight="300">
                <columns>
                  <TableColumn text="%rx_list.index" fx:id="rxListIndexColumn"
                               style="-fx-alignment: top-left;"/>
                  <TableColumn text="%rx_list.name" fx:id="rxListNameColumn"
                               style="-fx-alignment: top-left;"/>
                  <TableColumn text="RX ID" fx:id="rxListIdColumn"
                               style="-fx-alignment: top-left;"/>
                  <TableColumn text="%rx_list.port" fx:id="rxListPortColumn"
                               style="-fx-alignment: top-left;"/>
                  <TableColumn text="%rx_list.input_id" fx:id="rxListInputIdColumn"
                               style="-fx-alignment: top-left;"/>
                  <TableColumn text="%rx_list.slot_id" fx:id="rxListSlotIdColumn"
                               style="-fx-alignment: top-left;"/>
                </columns>
              </TableView>
            </VBox>
          </VBox>
        </detailNode>
      </MasterDetailPane>
    </VBox>
  </HBox>
  <ComboBox fx:id="videoWallGroupComboBox" prefWidth="200.0"
            AnchorPane.topAnchor="8" AnchorPane.leftAnchor="10" id="screen-combobox"/>
</AnchorPane>
