package com.mc.tool.caesar.vpm;

import com.mc.common.io.ResourceFile;
import com.mc.tool.caesar.api.DemoSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.ConfigData;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.PortData;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class ExportApp {

  private static void export(String path) {
    File file = new File(path);
    DemoSwitchDataModel model = new DemoSwitchDataModel(false);
    model.readStatus(ResourceFile.fromFile(file));

    String portFile = file.getParent() + "/portdata.csv";
    exportPort(model.getConfigData(), portFile);
    String extenderFile = file.getParent() + "/extender.csv";
    exportExtender(model.getConfigData(), extenderFile);
    String txFile = file.getParent() + "/tx.csv";
    exportTx(model.getConfigData(), txFile);
    String rxFile = file.getParent() + "/rx.csv";
    exportRx(model.getConfigData(), rxFile);
  }

  private static void exportPort(ConfigData configData, String path) {
    try (BufferedWriter writer =
        new BufferedWriter(
            new OutputStreamWriter(new FileOutputStream(path), StandardCharsets.UTF_8))) {
      writer.write("index, status, use, output, extender(index+1)\n");
      for (PortData data : configData.getPortDatas()) {
        StringBuilder builder = new StringBuilder();
        builder.append(data.getOid() + 1).append(',');
        builder.append(data.getStatus()).append(',');
        builder.append(data.getUse()).append(',');
        builder.append(data.getOutput()).append(',');
        builder.append(
            data.getExtender()
                + "|"
                + (data.getExtenderData() == null ? "null" : data.getExtenderData().getName()));
        builder.append('\n');
        writer.write(builder.toString());
      }
    } catch (IOException exception) {
      log.warn("Fail export port data!", exception);
    }
  }

  private static void exportTx(ConfigData configData, String path) {
    try (BufferedWriter writer =
        new BufferedWriter(
            new OutputStreamWriter(new FileOutputStream(path), StandardCharsets.UTF_8))) {
      writer.write(
          "index, status, name, id, console(index+1), extender0(index+1), extender1(index+1)\n");
      for (CpuData data : configData.getCpuDatas()) {
        StringBuilder builder = new StringBuilder();
        builder.append(data.getOid()).append(',');
        builder.append(data.getStatus()).append(',');
        builder.append(data.getName()).append(',');
        builder.append(data.getId()).append(',');

        String console = "null";
        if (data.getConsoleData() != null) {
          console = data.getConsoleData().getName();
        }
        builder.append(data.getConsole() + "|" + console).append(',');
        builder
            .append(
                data.getExtender(0)
                    + "|"
                    + (data.getExtenderData(0) == null
                        ? "null"
                        : data.getExtenderData(0).getName()))
            .append(',');
        builder
            .append(
                data.getExtender(1)
                    + "|"
                    + (data.getExtenderData(1) == null
                        ? "null"
                        : data.getExtenderData(1).getName()))
            .append(',');
        builder.append('\n');
        writer.write(builder.toString());
      }
    } catch (IOException exception) {
      log.warn("Fail export port data!", exception);
    }
  }

  private static void exportRx(ConfigData configData, String path) {
    try (BufferedWriter writer =
        new BufferedWriter(
            new OutputStreamWriter(new FileOutputStream(path), StandardCharsets.UTF_8))) {
      writer.write(
          "index, status, name, id, cpu(index+1), extender0(index+1), extender1(index+1)\n");
      for (ConsoleData data : configData.getConsoleDatas()) {
        StringBuilder builder = new StringBuilder();
        builder.append(data.getOid()).append(',');
        builder.append(data.getStatus()).append(',');
        builder.append(data.getName()).append(',');
        builder.append(data.getId()).append(',');

        String cpu = "null";
        if (data.getCpuData() != null) {
          cpu = data.getCpuData().getName();
        }
        builder.append(data.getCpu() + "|" + cpu).append(',');
        builder
            .append(
                data.getExtender(0)
                    + "|"
                    + (data.getExtenderData(0) == null
                        ? "null"
                        : data.getExtenderData(0).getName()))
            .append(',');
        builder
            .append(
                data.getExtender(1)
                    + "|"
                    + (data.getExtenderData(1) == null
                        ? "null"
                        : data.getExtenderData(1).getName()))
            .append(',');
        builder.append('\n');
        writer.write(builder.toString());
      }
    } catch (IOException exception) {
      log.warn("Fail export port data!", exception);
    }
  }

  private static void exportExtender(ConfigData configData, String path) {
    try (BufferedWriter writer =
        new BufferedWriter(
            new OutputStreamWriter(new FileOutputStream(path), StandardCharsets.UTF_8))) {
      writer.write(
          "index, status, name, port, remoteport, rdport, remoterdport, type, cpu/con(index+1)\n");
      for (ExtenderData data : configData.getExtenderDatas()) {
        StringBuilder builder = new StringBuilder();
        builder.append(data.getOid()).append(',');
        builder.append(data.getStatus()).append(',');
        builder.append(data.getName()).append(',');
        builder.append(data.getPort()).append(',');
        builder.append(data.getRemotePort()).append(',');
        builder.append(data.getRdPort()).append(',');
        builder.append(data.getRdRemotePort()).append(',');

        String type = "";
        if (data.isCpuType()) {
          type = "cpu";
        } else if (data.isConType()) {
          type = "con";
        }

        builder.append(data.getType() + "|" + type).append(',');

        String name = "null";
        if (data.isConType() && data.getConsoleData() != null) {
          name = data.getConsoleData().getName();
        } else if (data.isCpuType() && data.getCpuData() != null) {
          name = data.getCpuData().getName();
        }
        builder.append(data.getCpuCon() + "|" + name);
        builder.append('\n');
        writer.write(builder.toString());
      }
    } catch (IOException exception) {
      log.warn("Fail export port data!", exception);
    }
  }

  /** Main. */
  public static void main(String[] args) {
    System.setProperty("no-fx-mode", "1");
    System.setProperty("default.charset", "utf-8");
    export("");
  }
}
