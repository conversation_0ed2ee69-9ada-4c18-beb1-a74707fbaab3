package com.mc.tool.caesar.vpm.util.splicingscreen;

import com.mc.tool.framework.utility.UndecoratedAlert;
import feign.FeignException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import javafx.application.Platform;
import javafx.scene.Node;
import javafx.scene.control.AlertEx;
import lombok.extern.slf4j.Slf4j;

/**
 * Utils.
 */
@Slf4j
public class Utils {

  /**
   * Handle FeignException.
   */
  public static void handleFeignException(FeignException e, Node node) {
    log.error("FeignException: ", e);
    Optional<ByteBuffer> body = e.responseBody();
    String response = body.map(byteBuffer -> new String(byteBuffer.array(), StandardCharsets.UTF_8))
        .orElse("");
    Platform.runLater(() -> {
      UndecoratedAlert alert = new UndecoratedAlert(AlertEx.AlertExType.WARNING);
      alert.initOwner(node.getScene().getWindow());
      alert.setTitle("WARNING");
      alert.setHeaderText(null);
      alert.setContentText(response);
      alert.showAndWait();
    });
  }
}
