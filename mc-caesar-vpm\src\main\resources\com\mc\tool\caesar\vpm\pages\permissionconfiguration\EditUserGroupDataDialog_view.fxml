<?xml version="1.0" encoding="UTF-8"?>


<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<VBox minWidth="250.0" xmlns:fx="http://javafx.com/fxml/1" xmlns="http://javafx.com/javafx/8">
  <children>
    <HBox alignment="CENTER" maxHeight="40.0" minHeight="40.0">
      <children>
        <Label fx:id="idLabel" text="Label"/>
        <Region HBox.hgrow="ALWAYS"/>
        <TextField fx:id="idField" disable="true">
          <HBox.margin>
            <Insets right="10.0"/>
          </HBox.margin>
        </TextField>
      </children>
    </HBox>
    <HBox alignment="CENTER" maxHeight="40.0" minHeight="40.0">
      <children>
        <Label fx:id="nameLabel" text="Label"/>
        <Region HBox.hgrow="ALWAYS"/>
        <TextField fx:id="nameField">
          <HBox.margin>
            <Insets right="10.0"/>
          </HBox.margin>
        </TextField>
      </children>
    </HBox>
  </children>
</VBox>
