package com.mc.tool.caesar.api.datamodel.vp;

import javafx.scene.paint.Color;

/**
 * .
 */
public interface OsdInfoGetter {

  int getOsdLeft();

  int getOsdTop();

  int getOsdWidth();

  int getOsdHeight();

  double getOsdAlpha();

  Color getOsdColor();

  Color getBgColor();

  boolean isShowLogo();

  boolean isEnableBgImg();

  int getBgImgWidth();

  int getBgImgHeight();

  boolean isDisableSyncData();

  boolean isEnableRedundant();

  boolean isEnableBanner();

  boolean isEnableBannerBg();

  int getAnalogAudioVolume();
}
