<?xml version="1.0" encoding="UTF-8"?>

<?import com.dooapp.fxform.view.control.ConstraintLabel?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.control.CheckBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<VBox stylesheets="@snmpForm.css" xmlns="http://javafx.com/javafx/8">
  <GridPane VBox.vgrow="ALWAYS">
    <columnConstraints>
      <ColumnConstraints hgrow="SOMETIMES" maxWidth="164.0" minWidth="10.0" prefWidth="164.0"/>
      <ColumnConstraints hgrow="ALWAYS"/>
    </columnConstraints>
    <rowConstraints>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="ALWAYS"/>
      <RowConstraints maxHeight="55.0" minHeight="10.0" prefHeight="33.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="160.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      <RowConstraints minHeight="10.0" prefHeight="160.0" vgrow="SOMETIMES"/>
    </rowConstraints>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="1">
      <Label id="snmpPort-form-label" text="端口"/>
      <TextField id="snmpPort-form-editor" disable="true" editable="false" prefWidth="50.0">
        <HBox.margin>
          <Insets left="10.0"/>
        </HBox.margin>
      </TextField>
    </HBox>
    <Label text="%snmp.configuration"/>
    <Label text="%snmp.tap1" GridPane.rowIndex="3"/>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="4">
      <Label id="snmp1Address-form-label" prefWidth="150.0" text="Trap1服务 IP设置"/>
      <TextField id="snmp1Address-form-editor" prefWidth="150.0">
        <HBox.margin>
          <Insets left="10.0" right="10.0"/>
        </HBox.margin>
      </TextField>
      <Label id="snmp1Address-form-tooltip" text="（修改IP需要重启主机）"/>
      <ConstraintLabel id="snmp1Address-form-constraint"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="5">
      <Label id="snmp1TrapPort-form-label" text="端口"/>
      <TextField id="snmp1TrapPort-form-editor" disable="true" editable="false"
                 prefWidth="50.0">
        <HBox.margin>
          <Insets left="10.0"/>
        </HBox.margin>
      </TextField>
    </HBox>
    <Label text="%snmp.tap2" GridPane.rowIndex="7"/>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="8">
      <Label id="snmp2Address-form-label" prefWidth="150.0" text="Trap2服务 IP设置"/>
      <TextField id="snmp2Address-form-editor" prefWidth="150.0">
        <HBox.margin>
          <Insets left="10.0" right="10.0"/>
        </HBox.margin>
      </TextField>
      <Label id="snmp2Address-form-tooltip" text="（修改IP需要重启主机）" HBox.hgrow="ALWAYS"/>
      <ConstraintLabel id="snmp2Address-form-constraint"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="9">
      <Label id="snmp2TrapPort-form-label" text="端口"/>
      <TextField id="snmp2TrapPort-form-editor" disable="true" editable="false"
                 prefWidth="50.0">
        <HBox.margin>
          <Insets left="1.0"/>
        </HBox.margin>
      </TextField>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1" GridPane.rowIndex="3">
      <CheckBox id="snmp1IsTrap-form-editor" mnemonicParsing="false"/>
      <Label id="snmp1IsTrap-form-label" text="允许过滤"/>
    </HBox>
    <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="1"
          GridPane.rowIndex="7">
      <CheckBox id="snmp2IsTrap-form-editor" mnemonicParsing="false"/>
      <Label id="snmp2IsTrap-form-label" text="允许过滤"/>
    </HBox>
    <HBox id="tool-box" GridPane.columnIndex="1">
      <CheckBox id="isSnmp-form-editor" mnemonicParsing="false"/>
      <Label id="isSnmp-form-label" text="Label"/>
    </HBox>
    <GridPane GridPane.columnIndex="1" GridPane.rowIndex="6">
      <columnConstraints>
        <ColumnConstraints hgrow="SOMETIMES" maxWidth="-Infinity" minWidth="10.0"
                           prefWidth="160.0"/>
        <ColumnConstraints hgrow="SOMETIMES" maxWidth="-Infinity" minWidth="10.0"
                           prefWidth="160.0"/>
        <ColumnConstraints hgrow="SOMETIMES" maxWidth="-Infinity" minWidth="10.0"
                           prefWidth="160.0"/>
      </columnConstraints>
      <rowConstraints>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      </rowConstraints>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="2"
            GridPane.rowIndex="3">
        <CheckBox id="snmp1IsPowerSupply2-form-editor" mnemonicParsing="false">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </CheckBox>
        <Label id="snmp1IsPowerSupply2-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="1"
            GridPane.rowIndex="3">
        <CheckBox id="snmp1IsPowerSupply1-form-editor" mnemonicParsing="false">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </CheckBox>
        <Label id="snmp1IsPowerSupply1-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.rowIndex="3">
        <CheckBox id="snmp1IsFanTray2-form-editor" mnemonicParsing="false"/>
        <Label id="snmp1IsFanTray2-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="2"
            GridPane.rowIndex="2">
        <CheckBox id="snmp1IsFanTray1-form-editor" mnemonicParsing="false">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </CheckBox>
        <Label id="snmp1IsFanTray1-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.rowIndex="2">
        <CheckBox id="snmp1IsRemoveExtender-form-editor" mnemonicParsing="false"/>
        <Label id="snmp1IsRemoveExtender-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="1"
            GridPane.rowIndex="2">
        <CheckBox id="snmp1IsSwitchCommand-form-editor" mnemonicParsing="false">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </CheckBox>
        <Label id="snmp1IsSwitchCommand-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="2"
            GridPane.rowIndex="1">
        <CheckBox id="snmp1IsInsertExtender-form-editor" mnemonicParsing="false">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </CheckBox>
        <Label id="snmp1IsInsertExtender-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="1"
            GridPane.rowIndex="1">
        <CheckBox id="snmp1IsInvalidBoard-form-editor" mnemonicParsing="false">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </CheckBox>
        <Label id="snmp1IsInvalidBoard-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.rowIndex="1">
        <CheckBox id="snmp1IsRemoveBoard-form-editor" mnemonicParsing="false"/>
        <Label id="snmp1IsRemoveBoard-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0">
        <CheckBox id="snmp1IsStatus-form-editor" mnemonicParsing="false"/>
        <Label id="snmp1IsStatus-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="2">
        <CheckBox id="snmp1IsInsertBoard-form-editor" mnemonicParsing="false">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </CheckBox>
        <Label id="snmp1IsInsertBoard-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="1">
        <CheckBox id="snmp1IsTemperature-form-editor" mnemonicParsing="false">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </CheckBox>
        <Label id="snmp1IsTemperature-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.rowIndex="4">
        <CheckBox id="snmp1IsPowerSupply3-form-editor" mnemonicParsing="false"/>
        <Label id="snmp1IsPowerSupply3-form-label" text="Label"/>
      </HBox>
    </GridPane>
    <GridPane GridPane.columnIndex="1" GridPane.rowIndex="10">
      <columnConstraints>
        <ColumnConstraints hgrow="SOMETIMES" maxWidth="-Infinity" minWidth="10.0"
                           prefWidth="160.0"/>
        <ColumnConstraints hgrow="SOMETIMES" maxWidth="-Infinity" minWidth="10.0"
                           prefWidth="160.0"/>
        <ColumnConstraints hgrow="SOMETIMES" maxWidth="-Infinity" minWidth="10.0"
                           prefWidth="160.0"/>
      </columnConstraints>
      <rowConstraints>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
      </rowConstraints>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="2"
            GridPane.rowIndex="3">
        <CheckBox id="snmp2IsPowerSupply2-form-editor" mnemonicParsing="false">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </CheckBox>
        <Label id="snmp2IsPowerSupply2-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="1"
            GridPane.rowIndex="3">
        <CheckBox id="snmp2IsPowerSupply1-form-editor" mnemonicParsing="false">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </CheckBox>
        <Label id="snmp2IsPowerSupply1-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.rowIndex="3">
        <CheckBox id="snmp2IsFanTray2-form-editor" mnemonicParsing="false"/>
        <Label id="snmp2IsFanTray2-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="2"
            GridPane.rowIndex="2">
        <CheckBox id="snmp2IsFanTray1-form-editor" mnemonicParsing="false">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </CheckBox>
        <Label id="snmp2IsFanTray1-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.rowIndex="2">
        <CheckBox id="snmp2IsRemoveExtender-form-editor" mnemonicParsing="false"/>
        <Label id="snmp2IsRemoveExtender-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="1"
            GridPane.rowIndex="2">
        <CheckBox id="snmp2IsSwitchCommand-form-editor" mnemonicParsing="false">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </CheckBox>
        <Label id="snmp2IsSwitchCommand-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="2"
            GridPane.rowIndex="1">
        <CheckBox id="snmp2IsInsertExtender-form-editor" mnemonicParsing="false">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </CheckBox>
        <Label id="snmp2IsInsertExtender-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="1"
            GridPane.rowIndex="1">
        <CheckBox id="snmp2IsInvalidBoard-form-editor" mnemonicParsing="false">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </CheckBox>
        <Label id="snmp2IsInvalidBoard-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.rowIndex="1">
        <CheckBox id="snmp2IsRemoveBoard-form-editor" mnemonicParsing="false"/>
        <Label id="snmp2IsRemoveBoard-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0">
        <CheckBox id="snmp2IsStatus-form-editor" mnemonicParsing="false"/>
        <Label id="snmp2IsStatus-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="2">
        <CheckBox id="snmp2IsInsertBoard-form-editor" mnemonicParsing="false">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </CheckBox>
        <Label id="snmp2IsInsertBoard-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="1">
        <CheckBox id="snmp2IsTemperature-form-editor" mnemonicParsing="false">
          <HBox.margin>
            <Insets/>
          </HBox.margin>
        </CheckBox>
        <Label id="snmp2IsTemperature-form-label" text="Label"/>
      </HBox>
      <HBox id="tool-box" prefHeight="100.0" prefWidth="200.0" GridPane.rowIndex="4">
        <CheckBox id="snmp2IsPowerSupply3-form-editor" mnemonicParsing="false"/>
        <Label id="snmp2IsPowerSupply3-form-label" text="Label"/>
      </HBox>
    </GridPane>
  </GridPane>
</VBox>
