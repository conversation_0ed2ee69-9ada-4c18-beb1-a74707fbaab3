package com.mc.tool.caesar.api.communication;

import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import java.beans.PropertyChangeListener;
import java.beans.PropertyChangeSupport;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import javafx.application.Platform;

/**
 * 基本的设备控制.
 *
 * <AUTHOR>
 */
public class BasicController {

  public static final String PROPERTY_IO_CAPABLE = "BasicController.ioCapable";
  private final Lock lock = new ReentrantLock(true);
  private final AtomicInteger ioUsages = new AtomicInteger(0);
  private final PropertyChangeSupport pcs = new PropertyChangeSupport(this);
  private IoController.ReadWriteController ioController = null;

  protected final IoController.ReadWriteController getIoController()
      throws DeviceConnectionException {
    this.lock.lock();
    try {
      if (null == this.ioController) {
        throw new DeviceConnectionException("Io controller is null!");
      }
      return this.ioController;
    } finally {
      this.lock.unlock();
    }
  }

  protected final Lock getLock() {
    return this.lock;
  }

  /**
   * .
   */
  public final boolean isIoCapable() {
    this.lock.lock();
    try {
      return null != this.ioController;
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   */
  public final void setConnection(long key, String address) {
    this.lock.lock();
    try {
      if (0 != this.ioUsages.get()) {
        throw new IllegalStateException("Cannot change portName! IOController is still in use!");
      }
      if (null == address) {
        if (this.ioController != null) {
          this.ioController.disconnect(key);
          this.ioController = null;
          this.pcs.firePropertyChange(BasicController.PROPERTY_IO_CAPABLE, null, Boolean.FALSE);
        }
      } else {
        int pndel = address.lastIndexOf(':');

        String ip = address.substring(0, pndel);
        int ipport = Integer.parseInt(address.substring(pndel + 1));
        this.ioController = IoController.createIp(ip, ipport);

        this.pcs.firePropertyChange(BasicController.PROPERTY_IO_CAPABLE, null, isIoCapable());
      }
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   */
  public final void disconnect(long key) throws DeviceConnectionException {
    this.lock.lock();
    try {
      getIoController().disconnect(key);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   */
  public final void ensureConnection(long key) throws DeviceConnectionException {
    if (Platform.isFxApplicationThread()) {
      throw new IllegalStateException("Should not be in javafx thread!");
    }
    this.lock.lock();
    try {
      getIoController().connect(key);
    } finally {
      this.lock.unlock();
    }
  }

  public synchronized void removePropertyChangeListener(String propertyName,
      PropertyChangeListener listener) {
    this.pcs.removePropertyChangeListener(propertyName, listener);
  }

  public synchronized void removePropertyChangeListener(PropertyChangeListener listener) {
    this.pcs.removePropertyChangeListener(listener);
  }

  public synchronized void addPropertyChangeListener(String propertyName,
      PropertyChangeListener listener) {
    this.pcs.addPropertyChangeListener(propertyName, listener);
  }

  public synchronized void addPropertyChangeListener(PropertyChangeListener listener) {
    this.pcs.addPropertyChangeListener(listener);
  }
}

