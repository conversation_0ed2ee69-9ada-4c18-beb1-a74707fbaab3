package com.mc.tool.caesar.api.datamodel;

import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import lombok.Getter;

/**
 * 分支数据.
 */
@Getter
public class BranchData extends AbstractData implements CaesarCommunicatable {
  public static final String PROPERTY_TYPE = "BranchData.Type";
  public static final int RESERVED_COUNT = 8;

  /**
   * 分支类型.
   */
  public enum BranchType {
    SPEED_1G(0),
    SPEED_2_5G(1),
    SPEED_10G(2);

    private final int value;

    int getValue() {
      return value;
    }

    BranchType(int value) {
      this.value = value;
    }

    static BranchType valueOf(int value) {
      for (BranchType type : BranchType.values()) {
        if (type.getValue() == value) {
          return type;
        }
      }
      return SPEED_1G;
    }
  }

  private BranchType branchType = BranchType.SPEED_1G;

  public BranchData(CustomPropertyChangeSupport pcs,
                    ConfigDataManager configDataManager, int oid, String fqn) {
    super(pcs, configDataManager, oid, fqn);
  }

  /**
   * .
   */
  public void setBranchType(BranchType branchType) {
    BranchType oldData = this.branchType;
    this.branchType = branchType;
    firePropertyChange(PROPERTY_TYPE, oldData, branchType);
  }

  @Override
  public void readData(CfgReader paramCfgReader) throws ConfigException {
    int value = paramCfgReader.readByteValue();
    BranchType branchType = BranchType.valueOf(value);
    setBranchType(branchType);
    paramCfgReader.readByteArray(RESERVED_COUNT);
  }

  @Override
  public void writeData(CfgWriter paramCfgWriter) throws ConfigException {
    paramCfgWriter.writeByte((byte) (branchType.getValue() & 0xff));
    paramCfgWriter.writeByteArray(new byte[RESERVED_COUNT]);
  }
}
