package com.mc.tool.caesar.api.utils;

import com.mc.tool.caesar.api.datamodel.CpuData;

/**
 * .
 */
public class CpuDataStateWrapper {

  private CpuData cpuData;
  private Access accessMode;

  /**
   * .
   */
  public enum Access {
    FULL, // full access or disconnect
    VIDEO,
    PRIVATE;

    Access() {

    }
  }

  public CpuDataStateWrapper(CpuData cpuData, Access accessMode) {
    this.cpuData = cpuData;
    this.accessMode = accessMode;
  }

  public CpuData getCpuData() {
    return this.cpuData;
  }

  public Access getAccessMode() {
    return this.accessMode;
  }

  public boolean isPrivateAccess() {
    return this.accessMode == Access.PRIVATE;
  }

  public boolean isVideoAccess() {
    return this.accessMode == Access.VIDEO;
  }

  public boolean isFullAccess() {
    return this.accessMode == Access.FULL;
  }
}
