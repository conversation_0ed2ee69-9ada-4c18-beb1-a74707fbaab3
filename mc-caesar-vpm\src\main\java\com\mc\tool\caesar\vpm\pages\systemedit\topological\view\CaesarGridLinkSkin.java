package com.mc.tool.caesar.vpm.pages.systemedit.topological.view;

import com.mc.graph.DefaultLinkSkin;
import com.mc.graph.interfaces.LinkObject;
import com.mc.graph.interfaces.SkinManager;
import com.mc.tool.caesar.vpm.pages.systemedit.topological.TopologicalUtility;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import javafx.scene.Parent;
import javafx.scene.shape.ArcTo;
import javafx.scene.shape.LineTo;
import javafx.scene.shape.MoveTo;
import javafx.scene.shape.Path;

/**
 * .
 */
public class CaesarGridLinkSkin extends DefaultLinkSkin {

  /**
   * Contructor.
   *
   * @param link link to be skin.
   * @param parent parent to add components.
   * @param container TODO
   * @param skinManager skin manager
   */
  public CaesarGridLinkSkin(
      LinkObject link, Parent parent, Parent container, SkinManager skinManager) {
    super(link, parent, container, skinManager);
  }

  @Override
  protected void initConnnectionPath() {
    double firstAngle = link.getConnectors().getKey().getCell().getAngleProperty().get();
    double secondAngle = link.getConnectors().getValue().getCell().getAngleProperty().get();
    // 创建link时已保证firstAngle小于secondAngle
    double crossAngle = secondAngle - firstAngle;
    double delta = Math.abs(crossAngle / 180 - Math.round(crossAngle / 180));

    double firstX = TopologicalUtility.getCircleInnerXposByAngle(firstAngle);
    double firstY = TopologicalUtility.getCircleInnerYposByAngle(firstAngle);
    double secondX = TopologicalUtility.getCircleInnerXposByAngle(secondAngle);
    double secondY = TopologicalUtility.getCircleInnerYposByAngle(secondAngle);
    if (delta < 1e-6) {
      connectionPath = new Path(new MoveTo(firstX, firstY), new LineTo(secondX, secondY));
    } else {
      double arcRadius =
          Math.abs(
              Math.tan(Math.toRadians(crossAngle / 2))
                  * (TopologicalUtility.RADIUS - TopologicalUtility.CIRCLE_RADIUS));

      connectionPath =
          new Path(
              new MoveTo(firstX, firstY),
              new ArcTo(arcRadius, arcRadius, 0, secondX, secondY, false, crossAngle > 180));
    }

    connectionPath.setStroke(SystemEditDefinition.LINK_NORMAL_COLOR);
    connectionPath.setStrokeWidth(2);

    link.highLightProperty()
        .addListener(
            (obs, oldVal, newVal) -> {
              if (newVal) {
                connectionPath.setStroke(SystemEditDefinition.LINK_HIGHLIGHT_COLOR);
              } else {
                connectionPath.setStroke(SystemEditDefinition.LINK_NORMAL_COLOR);
              }
            });
  }
}
