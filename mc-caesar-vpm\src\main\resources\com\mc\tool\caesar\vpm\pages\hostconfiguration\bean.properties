autoRejectTime-label=Enable push grab authorization in OSD,after
autoRejectTime-tooltip=seconds,Automatic rejection
buzzerWarning-label=sound alert
configurationName-label=Configuration Name\uFF1A
gateway1-label=Gateway:
gateway2-label=Gateway:
hostName-label=Host Name\uFF1A
ipAddress1-label=IP Address:
ipAddress1-tooltip=Changing device IP requires the host to be restarted
ipAddress2-label=IP Address:
ipAddress2-tooltip=Changing device IP requires the host to be restarted
isComEcho-label=Allow RS232 serial port to return switching commands
isConAccess-label=Allow new connection control to have all permissions on the access side
isConDisconnect-label=Activate the control terminal in perfect access condition, and force the connection of other control terminals to be disconnected
isConLock-label=Allow OSD authority management on the control side (global configuration)
isCpuConnect-label=The activation control side supports full access and other control side supports video mode viewing
isCpuDisconnect-label=Allows open OSD to automatically disconnect the access end signal source
isCpuWatch-label=Enable video sharing
isDebug-label=Debug
isDhcp-label=DHCP
isEchoOnly-tooltip=Only synchronize the return message from the host
isEnableRemoteLog-label=Enable Remote Log
isEnableLocalLog-label=Enable Local Log
isError-label=Error
isForcePushGet-label=Enable advanced user privileges in OSD Push interface to directly push video and grab actions to low-level user privileges
isInfo-label=Info
isLanEcho-label=Allow network port to return switching commands
connectMode-label=RX No default mode for push and crawl under login:
isLogin-label=Force user login into OSD (global configuration)
isNotice-label=Notice
isRedundancy-tooltip=Activate the automatic switch function of access end control end redundant link
isSnmp-label=Activate the SNMP function
isSynchronize-tooltip=Synchronize network backup information between the backup host and the primary host
isUart-label=Activate third party serial port control
isUserAccess-label=Allow new users to have all permissions on the access side
isUserLock-label=Allow user OSD privilege management (global configuration)
isWarning-label=Warning
logServerIp-label=RemoteServerAddress:
macAddress1-label=MAC Address:
masterIp-label=Primary/secondary host IP address Settings
isDefaultBackup-tooltip=Backup as default
virtualIp-label=Virtual IP
virtualRouteId-label=Virtual Route ID
virtualRouteId-tooltip=Virtual Route ID, Master and Backup machines use the same ID.
netMask1-label=net Mask:
netMask2-label=net Mask:
notes-label=notes\uFF1A
osdWarning-label=OSD popup
snmp1Address-label=SNMP Trap1 IP:
snmp1Address-tooltip=(you need to restart the host to modify the IP)
snmp1IsFanTray1-label=FAN1
snmp1IsFanTray2-label=FAN2
snmp1IsInsertBoard-label=Insert I/O board
snmp1IsInsertExtender-label=Insert Extender
snmp1IsInvalidBoard-label=Invalid I/O board
snmp1IsPowerSupply1-label=PowerSupply1
snmp1IsPowerSupply2-label=PowerSupply2
snmp1IsPowerSupply3-label=PowerSupply3
snmp1IsRemoveBoard-label=Remove I/O board
snmp1IsRemoveExtender-label=Remove Extender
snmp1IsStatus-label=Status
snmp1IsSwitchCommand-label=Switch Command
snmp1IsTemperature-label=Temperature
snmp1IsTrap-label=Allow the filtering
snmp1TrapPort-label=Port
snmp2Address-label=SNMP Trap2 IP:
snmp2Address-tooltip=(you need to restart the host to modify the IP)
snmp2IsFanTray1-label=FAN1
snmp2IsFanTray2-label=FAN2
snmp2IsInsertBoard-label=Insert I/O board
snmp2IsInsertExtender-label=Insert Extender
snmp2IsInvalidBoard-label=Invalid I/O board
snmp2IsPowerSupply1-label=PowerSupply1
snmp2IsPowerSupply2-label=PowerSupply2
snmp2IsPowerSupply3-label=PowerSupply3
snmp2IsRemoveBoard-label=Remove I/O board
snmp2IsRemoveExtender-label=Remove Extender
snmp2IsStatus-label=Status
snmp2IsSwitchCommand-label=Switch Command
snmp2IsTemperature-label=Temperature
snmp2IsTrap-label=Allow the filtering
snmp2TrapPort-label=Port
snmpPort-label=Port
time-label=Time:
time-tooltip=Get local time
timeoutDisconnect-label=After Access terminal does not operate
timeoutDisconnect-tooltip=minutes,disconnect(0 = nonactivated)
timeoutDisplay-label=After callout OSD
timeoutDisplay-tooltip=second ,exit OSD(0 = nonactivated)
timeoutLogout-label=After user inactivity
timeoutLogout-tooltip=minute,logout(0 = nonactivated, -1 = unconstrained)
timeoutShare-label=Keyboard and mouse permission occupied, permission release time is
timeoutShare-tooltip=s
isPwdLogin-label=Password
isFaceLogin-label=Face Recognition
dualMasterVirtualIp-label=Control board data synchronization IP:
dualMasterVirtualIp-tooltip=Data synchronization of active / standby control board card on the same host
dualMasterVirtualRouteId-label=Control board data synchronous routing ID:
