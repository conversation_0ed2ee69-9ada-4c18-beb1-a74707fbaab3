package com.mc.tool.caesar.vpm.pages.systemedit.datamodel;

import com.google.common.collect.BiMap;
import com.google.common.collect.Lists;
import com.google.gson.annotations.Expose;
import com.mc.graph.connector.AnomymousConnectorObject;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.MatrixDefinitionData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.AudioGroup;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.CpuGroup;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.framework.systemedit.datamodel.DefaultVisualEditMatrix;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javafx.beans.Observable;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredListEx;
import javafx.util.Pair;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarMatrix extends DefaultVisualEditMatrix {
  private static final String DISCONNECTED_PORT = "0";
  private ObservableList<ConnectorIdentifier> onlineConnectedConnector;
  @Expose @Getter private String ip = "";

  @Getter private MatrixDefinitionData matrixData;

  @Override
  protected void initConnectorIds() {
    super.initConnectorIds();
    FilteredListEx<ConnectorIdentifier> onlineConnectedFilterConnectors =
        new FilteredListEx<>(onlineConnectorIds, CaesarMatrix::isConnectorConnected);
    onlineConnectedFilterConnectors.setExtractor((ci) -> new Observable[] {ci.textProperty()});
    onlineConnectedConnector = onlineConnectedFilterConnectors;
  }

  @Override
  public int getPortCount() {
    return 1000;
  }

  /**
   * 查找cpudata对应的terminal.
   *
   * @param cpuData cpudata
   * @return 如果找到，返回terminal
   */
  public CaesarTerminalBase findTerminal(CpuData cpuData) {
    if (cpuData == null) {
      return null;
    }
    ExtenderData data = cpuData.getExtenderData(0);
    return findTerminal(data);
  }

  /**
   * 查找consoleData对应的terminal.
   *
   * @param consoleData consoleData
   * @return 如果找到，返回terminal
   */
  public CaesarTerminalBase findTerminal(ConsoleData consoleData) {
    if (consoleData == null) {
      return null;
    }
    ExtenderData data = consoleData.getExtenderData(0);
    return findTerminal(data);
  }

  /**
   * 查找extenderdata对应的terminal.
   *
   * @param extenderData extender data.
   * @return 如果找到，返回对应的terminal，否则返回null.
   */
  public CaesarTerminalBase findTerminal(ExtenderData extenderData) {
    if (extenderData == null) {
      return null;
    } else {
      return findTerminal(extenderData.getId(), extenderData.isConType(), extenderData.isCpuType());
    }
  }

  /**
   * 查找extender id对应的terminal.
   *
   * @param extenderId extender的id
   * @return 如果找到，返回对应的terminal，否则返回null.
   */
  public CaesarTerminalBase findTerminal(int extenderId, boolean isRx, boolean isTx) {
    ObservableList<VisualEditTerminal> children = getAllTerminalChild();
    for (VisualEditTerminal terminal : children) {
      if (terminal instanceof CaesarTerminalBase
          && terminal.isRx() == isRx
          && terminal.isTx() == isTx) {
        CaesarTerminalBase item = (CaesarTerminalBase) terminal;
        if (item.getId() != 0 && item.getId() == extenderId) {
          return item;
        }
      }
    } // end for
    return null;
  }

  /**
   * 查找级联线的terminal.
   *
   * @param port 端口号.
   * @return terminal
   */
  public CaesarGridLineTerminal findGridLine(int port) {
    ObservableList<VisualEditTerminal> children = getAllTerminalChild();
    for (VisualEditTerminal terminal : children) {
      if (terminal instanceof CaesarGridLineTerminal) {
        CaesarGridLineTerminal item = (CaesarGridLineTerminal) terminal;
        if (item.getPortIndex() == port) {
          return item;
        }
      }
    }
    return null;
  }

  /**
   * 查找vp组.
   *
   * @param vpConsoleData vpcon的数据
   * @return 如果找到，返回vp组，否则返回null
   */
  public VpGroup findVpGroup(VpConsoleData vpConsoleData) {
    VisualEditNode visualEditNode =
        rxGroup.findChild(
            (node) -> {
              if (node instanceof VpGroup) {
                return ((VpGroup) node).getId() == vpConsoleData.getId();
              }
              return false;
            },
            true);
    if (visualEditNode instanceof VpGroup) {
      return (VpGroup) visualEditNode;
    } else {
      return null;
    }
  }

  /**
   * 查找vpgroup.
   *
   * @param id vpgroup 的id
   * @return vpgroup
   */
  public VpGroup findVpGroup(int id) {
    VisualEditNode visualEditNode =
        rxGroup.findChild(
            (node) -> {
              if (node instanceof VpGroup) {
                return ((VpGroup) node).getId() == id;
              }
              return false;
            },
            true);
    if (visualEditNode instanceof VpGroup) {
      return (VpGroup) visualEditNode;
    } else {
      return null;
    }
  }

  /**
   * 获取全部的vpGroup.
   *
   * @return 全部的vpGroup的列表
   */
  public Collection<VpGroup> getAllVpGroup() {
    List<VpGroup> result = Lists.newArrayList();
    rxGroup.recursiveAllChild((node) -> true, result, VpGroup.class);
    return result;
  }

  /**
   * 获取全部的cpuGroup.
   *
   * @return 全部的cpuGroup的列表
   */
  public Collection<CpuGroup> getAllCpuGroup() {
    List<CpuGroup> result = Lists.newArrayList();
    txGroup.recursiveAllChild((node) -> true, result, CpuGroup.class);
    return result;
  }

  /**
   * 获取全部的audioGroup.
   */
  public Collection<AudioGroup> getAllAudioGroup() {
    List<AudioGroup> result = Lists.newArrayList();
    rxGroup.recursiveAllChild(node -> true, result, AudioGroup.class);
    return result;
  }

  @Override
  public ObservableList<ConnectorIdentifier> getConnectorId() {
    return onlineConnectedConnector;
  }

  @Override
  public void insertTerminal(VisualEditTerminal terminal, int matrixPort, int terminalPort) {
    AnomymousConnectorObject matrixConnector = new AnomymousConnectorObject();
    matrixConnector.textProperty().set(getMatrixPortName(matrixPort));
    super.insertTerminlImpl(
        terminal, matrixConnector, ConnectorIdentifier.getIdentifier(terminalPort));
  }

  /**
   * 更新终端的端口.
   *
   * @param terminal terminal
   * @param matrixPort 矩阵端口.
   * @param terminalPort 终端端口.
   */
  public void updateTerminalPort(VisualEditTerminal terminal, int matrixPort, int terminalPort) {
    BiMap<ConnectorIdentifier, ConnectorIdentifier> map = terminalLinkPorts.get(terminal);
    if (map == null) {
      return;
    }
    ConnectorIdentifier identifier = map.get(ConnectorIdentifier.getIdentifier(terminalPort));
    if (identifier == null && matrixPort > 0) {
      insertTerminal(terminal, matrixPort, terminalPort);
      identifier = map.get(ConnectorIdentifier.getIdentifier(terminalPort));
      identifier.textProperty().set(getMatrixPortName(matrixPort));
    } else if (identifier != null) {
      if (matrixPort > 0) {
        identifier.textProperty().set(getMatrixPortName(matrixPort));
      } else {
        map.remove(ConnectorIdentifier.getIdentifier(terminalPort));
      }
    }
  }

  @Override
  public Collection<Pair<ConnectorIdentifier, ConnectorIdentifier>> getChildConnectorPair(
      VisualEditTerminal child) {
    Collection<Pair<ConnectorIdentifier, ConnectorIdentifier>> pairs =
        super.getChildConnectorPair(child);
    List<Pair<ConnectorIdentifier, ConnectorIdentifier>> newPairs = new ArrayList<>();

    for (Pair<ConnectorIdentifier, ConnectorIdentifier> pair : pairs) {
      if (isConnectorConnected(pair.getKey())) {
        newPairs.add(pair);
      }
    }
    return newPairs;
  }

  public void setMatrixData(MatrixDefinitionData matrixData) {
    this.matrixData = matrixData;
    this.ip = matrixData.getAddress();
  }

  private static boolean isConnectorConnected(ConnectorIdentifier ci) {
    return ci.textProperty().get() != null
        && !ci.textProperty().get().isEmpty()
        && !ci.textProperty().get().equals(DISCONNECTED_PORT);
  }

  private String getMatrixPortName(int matrixPort) {
    if (matrixData != null) {
      if (matrixData.isGridEnabled()) {
        return String.format("%d(%d)", matrixPort - matrixData.getFirstPort() + 1, matrixPort);
      } else {
        return matrixPort + "";
      }
    } else {
      return matrixPort + "";
    }
  }
}
