package com.mc.tool.caesar.vpm.pages.permissionconfiguration;

import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarConstants.User;
import com.mc.tool.caesar.api.datamodel.AbstractData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.api.datamodel.SystemConfigData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.datamodel.UserGroupData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.devices.CaesarUserRightGetter;
import com.mc.tool.caesar.vpm.pages.permissionconfiguration.Bundle.NbBundle;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.control.SearchField;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.utility.UndecoratedAlert;
import java.beans.PropertyChangeListener;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.ResourceBundle;
import java.util.function.Predicate;
import javafx.beans.binding.Bindings;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.control.SelectionMode;
import javafx.scene.control.Tab;
import javafx.scene.control.TabPane;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.VBox;
import javafx.stage.Modality;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class CaesarPermissionConfigurationController
    implements Initializable, ViewControllable {

  @Getter @FXML private TabPane tabPane;
  @FXML private TableView<UserTableData> tableView;
  @FXML private TableColumn<UserTableData, String> indexCol;
  @FXML private TableColumn<UserTableData, String> idCol;
  @FXML private TableColumn<UserTableData, String> usernameCol;
  @FXML private TableColumn<UserTableData, String> permissionCol;
  @FXML private TableColumn<UserTableData, String> usergroupCol;
  @FXML private TableColumn<UserTableData, String> editCol;
  @FXML private Button newAction;
  @FXML private Button deleteAction;

  @FXML private VBox searchFieldBox;
  private SearchField<UserTableData> searchField;
  private ObservableList<UserTableData> sourceRawList = FXCollections.observableArrayList();
  private FilteredList<UserTableData> sourceFilteredList = new FilteredList<>(sourceRawList);

  @Getter private UserCpuRightView userCpuRightView;
  @Getter private ConCpuRightView conCpuRightView;
  @Getter private UserGroupCpuRightView userGroupCpuRightView;
  @Getter private Tab conCpuRightTab;

  private WeakAdapter weakAdapter = new WeakAdapter();
  private CaesarDeviceController caesarDeviceController = null;
  private final String[] changeStatus = {
    UserData.PROPERTY_STATUS,
    UserData.PROPERTY_NAME,
    UserData.PROPERTY_VIDEOWALL_RIGHTS,
    UserData.PROPERTY_FULLNAME,
    UserData.PROPERTY_ID,
    UserData.PROPERTY_PASSWORD,
    UserData.PROPERTY_RIGHTS
  };

  private BooleanProperty userEditableProperty = new SimpleBooleanProperty(true);

  private BooleanProperty updatingProperty = new SimpleBooleanProperty(false);

  private BooleanProperty operableProperty = new SimpleBooleanProperty(false);

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      caesarDeviceController = (CaesarDeviceController) deviceController;
    } else {
      log.warn("Error device controller type : {} !", deviceController.getClass().getName());
      return;
    }
    listImpl();
    caesarDeviceController
        .getDataModel()
        .addPropertyChangeListener(
            changeStatus,
            weakAdapter.wrap(
                (PropertyChangeListener) evt -> PlatformUtility.runInFxThread(this::listImpl)));
    userCpuRightView = new UserCpuRightView();
    userCpuRightView.setDeviceController(deviceController);
    tabPane
        .getTabs()
        .add(new Tab(CaesarI18nCommonResource.getString("page.user_cpu_right"), userCpuRightView));
    conCpuRightView = new ConCpuRightView();
    conCpuRightView.setDeviceController(deviceController);
    conCpuRightTab =
        new Tab(CaesarI18nCommonResource.getString("page.con_cpu_right"), conCpuRightView);
    tabPane.getTabs().add(conCpuRightTab);
    userGroupCpuRightView = new UserGroupCpuRightView();
    userGroupCpuRightView.setDeviceController(deviceController);
    tabPane
        .getTabs()
        .add(
            new Tab(
                CaesarI18nCommonResource.getString("page.user_group_cpu_right"),
                userGroupCpuRightView));

    updateUserRight();
    updateOperableRight();
  }

  /**
   * For administrator.
   */
  public void setAuthorityDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      caesarDeviceController = (CaesarDeviceController) deviceController;
    } else {
      log.warn("Error device controller type : {} !", deviceController.getClass().getName());
      return;
    }
    listImpl();
    caesarDeviceController
        .getDataModel()
        .addPropertyChangeListener(
            changeStatus,
            weakAdapter.wrap(
                (PropertyChangeListener) evt -> PlatformUtility.runInFxThread(this::listImpl)));
    CaesarUserRightGetter userRightGetter = caesarDeviceController.getCaesarUserRight();
    userEditableProperty.set(userRightGetter.isUserEditable());
    updateOperableRight();
  }

  @Override
  public DeviceControllable getDeviceController() {
    return caesarDeviceController;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    // 检索框
    searchField = new SearchField<>();
    searchFieldBox.getChildren().add(0, searchField);

    indexCol.setText(NbBundle.getMessage("PermissionConfiguration.user.index"));
    idCol.setText(NbBundle.getMessage("PermissionConfiguration.user.id"));
    usernameCol.setText(NbBundle.getMessage("PermissionConfiguration.user.username"));
    permissionCol.setText(NbBundle.getMessage("PermissionConfiguration.user.permission"));
    usergroupCol.setText(NbBundle.getMessage("PermissionConfiguration.user.usergroup"));
    editCol.setText(NbBundle.getMessage("PermissionConfiguration.user.edit"));

    tableView
        .widthProperty()
        .addListener(
            weakAdapter.wrap(
                (observable, oldValue, newValue) -> {
                  idCol.setPrefWidth((newValue.doubleValue() - indexCol.getWidth() - 20) / 6);
                  usernameCol.setPrefWidth((newValue.doubleValue() - indexCol.getWidth() - 20) / 6);
                  permissionCol.setPrefWidth(
                      (newValue.doubleValue() - indexCol.getWidth() - 20) * 2 / 6);
                  usergroupCol.setPrefWidth(
                      (newValue.doubleValue() - indexCol.getWidth() - 20) / 6);
                  editCol.setPrefWidth((newValue.doubleValue() - indexCol.getWidth() - 20) / 6);
                }));

    indexCol.setCellFactory(col -> new IndexCell());
    idCol.setCellValueFactory(item -> item.getValue().getIdProperty());
    usernameCol.setCellValueFactory(item -> item.getValue().getNameProperty());
    permissionCol.setCellValueFactory(item -> item.getValue().getPermissionProperty());
    usergroupCol.setCellValueFactory(item -> item.getValue().getUsergroupProperty());

    tableView.setItems(sourceFilteredList);

    deleteAction.setDisable(true);
    tableView.getSelectionModel().setSelectionMode(SelectionMode.MULTIPLE);

    editCol.setCellFactory(
        (cel) -> {
          TableCell<UserTableData, String> cell = new EditTableCell();
          cell.disableProperty().bind(userEditableProperty.not().or(operableProperty.not()));
          return cell;
        });

    initButton();

    searchField
        .filterPredicateProperty()
        .addListener(
            weakAdapter.wrap(
                (obs, oldVal, newVal) ->
                    sourceFilteredList.setPredicate(
                        searchField
                            .getFilterPredicate()
                            .or(getSourcePrediate(searchField.getSearchText())))));
    sourceFilteredList.setPredicate(getSourcePrediate(""));
  }

  private Predicate<UserTableData> getSourcePrediate(String text) {
    return (item) -> {
      if (item == null) {
        return false;
      }
      return item.getPermission().contains(text) || item.getUsergroup().contains(text);
    };
  }

  private void initButton() {
    newAction
        .disableProperty()
        .bind(userEditableProperty.not().or(operableProperty.not()).or(updatingProperty));
    newAction.setOnAction(
        (event) -> {
          UserData userData =
              caesarDeviceController.getDataModel().getConfigDataManager().getFreeUser();
          Collection<UserData> users =
              caesarDeviceController.getDataModel().getConfigDataManager().getActiveUsers();
          for (UserData user : users) {
            if (CaesarConstants.User.ADMIN_NAME.equalsIgnoreCase(user.getName())) {
              users.remove(user);
              break;
            }
          }
          if (userData == null) {
            UndecoratedAlert alert = new UndecoratedAlert(AlertExType.ERROR);
            if (tableView != null && tableView.getScene() != null) {
              alert.initOwner(tableView.getScene().getWindow());
            }
            alert.setTitle("用户数量超过最大数目");
            alert.setHeaderText(null);
            alert.setContentText("不能创建新用户");
            alert.showAndWait();
          } else {

            EditUserDataDialog editUserDataDialog =
                new EditUserDataDialog(
                    userData,
                    Arrays.asList(
                        caesarDeviceController.getDataModel().getVpDataModel().getAllVideoWalls()),
                    false);
            editUserDataDialog.setTitle(
                NbBundle.getMessage("PermissionConfiguration.new_user_dialog.title"));
            editUserDataDialog.setDeviceController(caesarDeviceController);
            if (tableView != null && tableView.getScene() != null) {
              editUserDataDialog.initOwner(tableView.getScene().getWindow());
            }
            editUserDataDialog.initModality(Modality.APPLICATION_MODAL);
            editUserDataDialog.showAndWait();
            UserDataInfo userDataInfo = editUserDataDialog.getResult();
            if (userDataInfo != null) {
              updatingProperty.set(true);
              caesarDeviceController
                  .submitAsync(
                      () -> {
                        userData.setInitMode(true);
                        userData.setStatusNew(true);
                        SystemConfigData systemConfigData =
                            caesarDeviceController
                                .getDataModel()
                                .getConfigData()
                                .getSystemConfigData();
                        if (systemConfigData.getAccessData().isUserAccess()) {
                          for (CpuData cpuData : userData.getConfigDataManager().getActiveCpus()) {
                            userData.setNoAccess(cpuData, false);
                            userData.setVideoAccess(cpuData, false);
                          }
                        } else {
                          userData.setNoAccessCpus(userData.getConfigDataManager().getCpus());
                        }
                        userData.setInitMode(false);

                        List<FunctionKeyData> functionKeyChangedList = new ArrayList<>();
                        for (FunctionKeyData functionKeyData :
                            caesarDeviceController
                                .getDataModel()
                                .getConfigData()
                                .getFunctionKeyDatas()) {
                          if (functionKeyData.equals(AbstractData.THRESHOLD_UI_LOCAL_CHANGES)) {
                            functionKeyChangedList.add(functionKeyData);
                          }
                        }

                        userData.setInitMode(true);
                        if (userData.isStatusNew()) {
                          userData.setStatusActive(true);
                          userData.setStatusNew(false);
                        }
                        userData.setName(userDataInfo.getUsername());
                        userData.setPassword(userDataInfo.getPassword());
                        userData.setRights(userDataInfo.getRights());
                        userData.setVideoWallRights(userDataInfo.getVideoWallRights());
                        for (UserGroupData udata : userDataInfo.getUserGroupDatas()) {
                          userData.setUserGroup(udata, true);
                        }
                        for (UserGroupData udata : userDataInfo.getUnSelectUserGroupDatas()) {
                          userData.setUserGroup(udata, false);
                        }
                        userData.setMouseSpeed(userDataInfo.getMouseSpeed());
                        userData.setAutoConnect(userDataInfo.getAutoConnect());
                        userData.setInitMode(false);
                        try {
                          if (!functionKeyChangedList.isEmpty()) {
                            caesarDeviceController
                                .getDataModel()
                                .sendFunctionKeyData(functionKeyChangedList);
                          }
                          caesarDeviceController
                              .getDataModel()
                              .sendUserData(Collections.singletonList(userData));
                          log.info("User[{}] Create a new user named {}", caesarDeviceController.getLoginUser(), userData.getName());
                        } catch (DeviceConnectionException | BusyException ex) {
                          log.warn("Can not send user data!");
                        }
                      })
                  .whenComplete(
                      (result, throwable) -> {
                        if (throwable != null) {
                          log.warn("Can not send user data!", throwable);
                        }
                        PlatformUtility.runInFxThread(
                            () -> {
                              updatingProperty.set(false);
                              listImpl();
                            });
                      });
            }
          }
        });

    deleteAction
        .disableProperty()
        .bind(
            userEditableProperty
                .not()
                .or(operableProperty.not())
                .or(updatingProperty)
                .or(Bindings.isEmpty(tableView.getSelectionModel().getSelectedItems()))
                .or(
                    Bindings.createBooleanBinding(
                        () -> {
                          for (UserTableData userTableData :
                              tableView.getSelectionModel().getSelectedItems()) {
                            if (userTableData.getName().equals(User.ADMIN_NAME)) {
                              return true;
                            }
                          }
                          return false;
                        },
                        tableView.getSelectionModel().getSelectedItems())));
    deleteAction.setOnAction(
        (event) -> {
          UndecoratedAlert alert = new UndecoratedAlert(AlertExType.WARNING);
          if (deleteAction != null && deleteAction.getScene() != null) {
            alert.initOwner(deleteAction.getScene().getWindow());
          }
          alert.getButtonTypes().add(ButtonType.CANCEL);
          alert.setTitle(NbBundle.getMessage("PermissionConfiguration.delete_alert.title"));
          alert.setHeaderText(null);
          alert.setContentText(NbBundle.getMessage("PermissionConfiguration.delete_alert.text"));
          alert
              .showAndWait()
              .ifPresent(
                  (type) -> {
                    if (type.equals(ButtonType.OK)) {
                      List<UserTableData> userTableDatas =
                          new ArrayList<>(tableView.getSelectionModel().getSelectedItems());
                      updatingProperty.set(true);
                      caesarDeviceController
                          .submitAsync(
                              () -> {
                                List<FunctionKeyData> functionKeyDatas = new ArrayList<>();
                                List<UserData> userDatas = new ArrayList<>();
                                for (UserTableData userTableData : userTableDatas) {
                                  UserData userData = userTableData.getUserData();
                                  for (FunctionKeyData functionKeyData :
                                      caesarDeviceController
                                          .getDataModel()
                                          .getConfigData()
                                          .getFunctionKeyDatas()) {
                                    if (functionKeyData.getHostSubscript()
                                        == userData.getOid() + 1) {
                                      functionKeyData.reset();
                                      functionKeyDatas.add(functionKeyData);
                                    }
                                  }
                                  log.info("User({}) delete a user named {}", caesarDeviceController.getLoginUser(), userData.getName());
                                  userData.delete();
                                  userDatas.add(userData);
                                }
                                try {
                                  caesarDeviceController
                                      .getDataModel()
                                      .sendFunctionKeyData(functionKeyDatas);
                                  caesarDeviceController.getDataModel().sendUserData(userDatas);

                                } catch (DeviceConnectionException | BusyException ex) {
                                  log.warn("Can not delete function key data or user data!");
                                }
                              })
                          .whenComplete(
                              (result, throwable) -> {
                                if (throwable != null) {
                                  log.warn("Can not delete user data!", throwable);
                                }
                                PlatformUtility.runInFxThread(
                                    () -> {
                                      updatingProperty.set(false);
                                      listImpl();
                                    });
                              });
                    }
                  });
          tableView.getSelectionModel().clearSelection();
        });
  }

  private static class IndexCell extends TableCell<UserTableData, String> {
    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);

      if (!empty) {
        int rowIndex = this.getIndex() + 1;
        this.setText(String.valueOf(rowIndex));
      }
    }
  }

  private class EditTableCell extends TableCell<UserTableData, String> {
    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);

      if (!empty) {
        Object rowItem = this.getTableRow().getItem();
        UserTableData data;
        if (rowItem instanceof UserTableData) {
          data = (UserTableData) rowItem;
          ImageView iv =
              new ImageView(
                  new Image("/com/mc/tool/caesar/vpm/pages/permissionconfiguration/edit.png"));
          Button button = new Button();
          button.getStyleClass().add("image-button");
          button.setGraphic(iv);
          this.setGraphic(button);
          button.setOnAction(
              (event) -> {
                UserData userData = data.getUserData();
                EditUserDataDialog editUserDataDialog =
                    new EditUserDataDialog(
                        userData,
                        Arrays.asList(
                            caesarDeviceController
                                .getDataModel()
                                .getVpDataModel()
                                .getAllVideoWalls()),
                        true);
                editUserDataDialog.setTitle(
                    NbBundle.getMessage("PermissionConfiguration.edit_user_dialog.title"));
                editUserDataDialog.setDeviceController(caesarDeviceController);
                if (tableView != null && tableView.getScene() != null) {
                  editUserDataDialog.initOwner(tableView.getScene().getWindow());
                }
                editUserDataDialog.initModality(Modality.APPLICATION_MODAL);
                editUserDataDialog.showAndWait();
                UserDataInfo userDataInfo = editUserDataDialog.getResult();
                if (userDataInfo != null) {
                  updatingProperty.set(true);
                  caesarDeviceController
                      .submitAsync(
                          () -> {
                            List<FunctionKeyData> functionKeyChangedList = new ArrayList<>();
                            for (FunctionKeyData functionKeyData :
                                caesarDeviceController
                                    .getDataModel()
                                    .getConfigData()
                                    .getFunctionKeyDatas()) {
                              if (functionKeyData.equals(AbstractData.THRESHOLD_UI_LOCAL_CHANGES)) {
                                functionKeyChangedList.add(functionKeyData);
                              }
                            }

                            userData.setInitMode(true);
                            if (userData.isStatusNew()) {
                              userData.setStatusActive(true);
                              userData.setStatusNew(false);
                            }
                            if (!userData.getName().equals(userDataInfo.getUsername())) {
                              log.info(
                                  "Change user name from {} to {}.",
                                  userData.getName(),
                                  userDataInfo.getUsername());
                            }
                            userData.setName(userDataInfo.getUsername());
                            if (!userData.getPassword().equals(userDataInfo.getPassword())) {
                              log.info("User({}) change password.", userData.getName());
                            }
                            userData.setPassword(userDataInfo.getPassword());
                            if (userData.getRights() != userDataInfo.getRights()) {
                              log.info(
                                  "Change user right from {} to {}.",
                                  userData.getRights(),
                                  userDataInfo.getRights());
                            }
                            userData.setRights(userDataInfo.getRights());
                            userData.setVideoWallRights(userDataInfo.getVideoWallRights());
                            for (UserGroupData udata : userDataInfo.getUserGroupDatas()) {
                              userData.setUserGroup(udata, true);
                            }
                            for (UserGroupData udata : userDataInfo.getUnSelectUserGroupDatas()) {
                              userData.setUserGroup(udata, false);
                            }
                            userData.setMouseSpeed(userDataInfo.getMouseSpeed());
                            userData.setAutoConnect(userDataInfo.getAutoConnect());
                            userData.setInitMode(false);
                            try {
                              if (!functionKeyChangedList.isEmpty()) {
                                caesarDeviceController
                                    .getDataModel()
                                    .sendFunctionKeyData(functionKeyChangedList);
                              }
                              caesarDeviceController
                                  .getDataModel()
                                  .sendUserData(Collections.singletonList(userData));
                            } catch (DeviceConnectionException | BusyException ex) {
                              log.warn("Can not send user data!");
                            }
                          })
                      .whenComplete(
                          (result, throwable) -> {
                            if (throwable != null) {
                              log.warn("Can not send user data!", throwable);
                            }
                            PlatformUtility.runInFxThread(
                                () -> {
                                  updatingProperty.set(false);
                                  listImpl();
                                });
                          });
                }
              });
        }
      }
    }
  }

  /** 更新表格. */
  private void listImpl() {
    if (!sourceRawList.isEmpty()) {
      sourceRawList.clear();
    }
    for (UserData data : caesarDeviceController.getDataModel().getConfigData().getUserDatas()) {
      if (data.isStatusActive()) {
        UserTableData tableData = new UserTableData(data);
        sourceRawList.add(tableData);
      }
    }
  }

  /** 更新权限信息. */
  public void updateUserRight() {
    if (caesarDeviceController == null) {
      return;
    }
    CaesarUserRightGetter userRightGetter = caesarDeviceController.getCaesarUserRight();
    userEditableProperty.set(userRightGetter.isUserEditable());

    userCpuRightView.updateEditableStatus();
    userGroupCpuRightView.updateEditableStatus();
    conCpuRightView.updateEditableStatus();
  }

  /**
   * updateOperableRight.
   */
  public void updateOperableRight() {
    if (caesarDeviceController == null) {
      return;
    }
    operableProperty.set(caesarDeviceController.isAdminUser(caesarDeviceController.getLoginUser()));
  }
}
