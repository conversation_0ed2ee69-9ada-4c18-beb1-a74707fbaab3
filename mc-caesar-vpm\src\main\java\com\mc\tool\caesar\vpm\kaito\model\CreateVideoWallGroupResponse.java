package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import javax.annotation.Nullable;
import lombok.Setter;

/**
 * CreateVideoWallGroupResponse.
 */
@Setter
@JsonPropertyOrder({
    CreateVideoWallGroupResponse.JSON_PROPERTY_ID
})
public class CreateVideoWallGroupResponse {
  public static final String JSON_PROPERTY_ID = "id";
  private Integer id;

  /**
   * .
   */
  public CreateVideoWallGroupResponse id(Integer id) {
    this.id = id;
    return this;
  }

  /**
   * 全光拼接组ID.
   *
   * @return id
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getId() {
    return id;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CreateVideoWallGroupResponse createVideoWallGroupResponse = (CreateVideoWallGroupResponse) o;
    return Objects.equals(this.id, createVideoWallGroupResponse.id);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("CreateVideoWallGroupResponse {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
