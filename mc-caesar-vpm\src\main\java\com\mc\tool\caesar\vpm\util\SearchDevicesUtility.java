package com.mc.tool.caesar.vpm.util;

import com.mc.tool.caesar.vpm.util.searchdevices.SearchDevicesDialog;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.Entity;
import java.util.function.Consumer;
import javafx.stage.Modality;

/**
 * .
 */
public class SearchDevicesUtility {

  /** 设备查找. */
  public static void searchForDevices(ApplicationBase app, Consumer<Entity> postAction) {
    SearchDevicesDialog searchDevicesDialog = new SearchDevicesDialog();
    SearchDevicesDialog.setApp(app);
    SearchDevicesDialog.setConnectedAction(postAction);
    searchDevicesDialog.initModality(Modality.APPLICATION_MODAL);
    searchDevicesDialog.initOwner(app.getMainWindow());
    searchDevicesDialog.showAndWait();
    searchDevicesDialog.destroy();
  }
}
