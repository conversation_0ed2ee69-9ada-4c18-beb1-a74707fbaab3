package com.mc.tool.caesar.api.datamodel;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import lombok.Getter;

/**
 * SourceCropData.
 */
@Getter
public class SourceCropData extends AbstractData implements CaesarCommunicatable {
  public static final String PROPERTY_BASE = "SourceCropData.";
  public static final String PROPERTY_CPU_INDEX = PROPERTY_BASE + "CpuIndex";
  public static final String PROPERTY_NAME = PROPERTY_BASE + "Name";
  public static final String PROPERTY_LEFT = PROPERTY_BASE + "Left";
  public static final String PROPERTY_TOP = PROPERTY_BASE + "Top";
  public static final String PROPERTY_RIGHT = PROPERTY_BASE + "Right";
  public static final String PROPERTY_BOTTOM = PROPERTY_BASE + "Bottom";

  public static final String PROPERTY_SOURCE_INDEX = PROPERTY_BASE + "SourceIndex";
  public static final int RESERVED_COUNT = 7;

  private int cpuIndex;
  private String name;
  private int left;
  private int top;
  private int right;
  private int bottom;

  private int sourceIndex;

  /**
   * .
   */
  public SourceCropData(CustomPropertyChangeSupport pcs,
                        ConfigDataManager configDataManager, int oid,
                        String fqn) {
    super(pcs, configDataManager, oid, fqn);
  }

  @Override
  public void readData(CfgReader cfgReader) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    setCpuIndex(cfgReader.read2ByteValue());
    setName(cfgReader.readString(CaesarConstants.NAME_LEN));
    setLeft(cfgReader.read2ByteValue());
    setTop(cfgReader.read2ByteValue());
    setRight(cfgReader.read2ByteValue());
    setBottom(cfgReader.read2ByteValue());
    setSourceIndex(cfgReader.readByteValue());
    cfgReader.readByteArray(RESERVED_COUNT);
  }

  @Override
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    cfgWriter.write2ByteSmallEndian(getCpuIndex());
    cfgWriter.writeString(getName(), CaesarConstants.NAME_LEN);
    cfgWriter.write2ByteSmallEndian(getLeft());
    cfgWriter.write2ByteSmallEndian(getTop());
    cfgWriter.write2ByteSmallEndian(getRight());
    cfgWriter.write2ByteSmallEndian(getBottom());
    cfgWriter.writeByte((byte) getSourceIndex());
    cfgWriter.writeByteArray(new byte[RESERVED_COUNT]);
  }

  /**
   * .
   */
  public void setCpuIndex(int cpuIndex) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.cpuIndex;
    if (oldValue != cpuIndex) {
      this.cpuIndex = cpuIndex;
      //PlatformUtility.runInFxThread(() -> cpuIndexProperty.set(cpuIndex));
      firePropertyChange(PROPERTY_CPU_INDEX, oldValue, cpuIndex);
    }
  }

  /**
   * .
   */
  public void setName(String name) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    String oldValue = this.name;
    this.name = name;
    //PlatformUtility.runInFxThread(() -> nameProperty.set(name));
    firePropertyChange(PROPERTY_NAME, oldValue, name);
  }

  /**
   * .
   */
  public void setLeft(int left) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.left;
    this.left = left;
    //PlatformUtility.runInFxThread(() -> leftProperty.set(left));
    firePropertyChange(PROPERTY_LEFT, oldValue, left);
  }

  /**
   * .
   */
  public void setTop(int top) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.top;
    this.top = top;
    //PlatformUtility.runInFxThread(() -> topProperty.set(top));
    firePropertyChange(PROPERTY_TOP, oldValue, top);
  }

  /**
   * .
   */
  public void setRight(int right) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.right;
    this.right = right;
    //PlatformUtility.runInFxThread(() -> rightProperty.set(right));
    firePropertyChange(PROPERTY_RIGHT, oldValue, right);
  }

  /**
   * .
   */
  public void setBottom(int bottom) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.bottom;
    this.bottom = bottom;
    //PlatformUtility.runInFxThread(() -> bottomProperty.set(bottom));
    firePropertyChange(PROPERTY_BOTTOM, oldValue, bottom);
  }

  /**
   * 设置信号源的HDMI索引.
   */
  public void setSourceIndex(int sourceIndex) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.sourceIndex;
    this.sourceIndex = sourceIndex;
    firePropertyChange(PROPERTY_SOURCE_INDEX, oldValue, sourceIndex);
  }


}
