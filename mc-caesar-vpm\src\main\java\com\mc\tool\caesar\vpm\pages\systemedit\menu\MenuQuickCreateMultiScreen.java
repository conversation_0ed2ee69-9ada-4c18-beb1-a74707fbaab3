package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMultiScreenFunc;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.util.stream.Collectors;

/**
 * .
 */
public class MenuQuickCreateMultiScreen extends MenuQuickCreate {

  /** 快速创建预案组. */
  public MenuQuickCreateMultiScreen(SystemEditControllable controllable) {
    super(controllable);
    this.setText(Bundle.NbBundle.getMessage("menu.grouping.multiscreen"));
  }

  @Override
  protected void initTerminal() {
    nodes =
        controllable.getModel().getAllTerminals().stream()
            .filter(VisualEditTerminal::isRx)
            .filter(VisualEditTerminal::isOnline)
            .filter(item -> item.getParent() instanceof CaesarMatrix)
            .collect(Collectors.toList());
  }

  @Override
  protected void handleAction() {
    dialog
        .showAndWait()
        .ifPresent(
            nodeList -> {
              if (!nodeList.isEmpty()) {
                String groupName = nameField.getText();
                CaesarMultiScreenFunc func =
                    controllable.addGroup(
                        groupName,
                        CaesarMultiScreenFunc.class,
                        nodeList.toArray(new VisualEditNode[0]));
                if (func != null) {
                  if (nodeList.size() < CaesarMultiScreenFunc.DEFAULT_COLUMN_SIZE) {
                    func.setLayout(1, nodeList.size());
                  } else {
                    func.setLayout(
                        (int)
                            Math.ceil(
                                nodeList.size()
                                    / (double) CaesarMultiScreenFunc.DEFAULT_COLUMN_SIZE),
                        CaesarMultiScreenFunc.DEFAULT_COLUMN_SIZE);
                  }
                }
              }
            });
  }

  @Override
  protected void initBundle() {
    groupNameLabel.setText(Bundle.NbBundle.getMessage("menu.grouping.multiscreen.name"));
    dialog.setTitle(Bundle.NbBundle.getMessage("menu.grouping.multiscreen"));
  }

  @Override
  protected void setNameFieldText() {
    nameField.setText("Group");
  }
}
