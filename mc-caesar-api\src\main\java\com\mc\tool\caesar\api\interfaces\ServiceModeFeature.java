package com.mc.tool.caesar.api.interfaces;

import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;

/**
 * .
 */
public interface ServiceModeFeature {

  void setServiceMode(int paramInt, boolean paramBoolean)
      throws ConfigException, BusyException;

  void setServiceMode(int paramInt1, int paramInt2, boolean paramBoolean)
      throws ConfigException, BusyException;
}

