package com.mc.tool.caesar.api;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

import com.google.gson.GsonBuilder;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.utils.Utilities;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.TestName;

/**
 * .
 */
public class TeraControllerSystemRequestTest extends TestBase {

  @BeforeClass
  public static void setUpBeforeClass() {
    TestBase.setupBeforeClass();
  }

  @AfterClass
  public static void tearDownAfterClass() {
    TestBase.tearDownAfterClass();
  }

  @Rule
  public TestName testName = new TestName();

  @Before
  public void before() {
    System.out.println("Starting Test: " + testName.getMethodName());
  }

  @After
  public void after() {
    System.out.println("End      Test: " + testName.getMethodName());
  }

  @Test
  public void testGetConfigData() {
    try {
      model.getController().getConfigData();
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }


  @Test
  public void testGetSetSystemData() {
    try {
      model.getController().getSystemData();
      model.getController().setSystemData();
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testGetSetPorts() {
    try {
      model.getController().getPortData(model.getConfigData().getPortDatas());
      model.getController().setPortData(model.getConfigData().getPortDatas());
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testGetSetCpuData() {
    try {
      model.getController().getCpuData(model.getConfigData().getCpuDatas());
      for (CpuData cpuData : model.getConfigData().getCpuDatas()) {
        if (cpuData.isStatusActive()) {
          System.out.println(cpuData.getId());
        }
      }
      model.getController().setCpuData(model.getConfigData().getCpuDatas());
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testGetSetConData() {
    try {
      model.getController().getConsoleData(model.getConfigData().getConsoleDatas());
      model.getController().setConsoleData(model.getConfigData().getConsoleDatas());
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testGetSetExtData() {
    try {
      model.getController().getExtenderData(model.getConfigData().getExtenderDatas());
      model.getController().setExtenderData(model.getConfigData().getExtenderDatas());
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testGetSetUserData() {
    try {
      model.getController().getUserData(model.getConfigData().getUserDatas());
      model.getController().setUserData(model.getConfigData().getUserDatas());
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testGetSetUserGroupData() {
    try {
      model.getController().getUserGroupData(model.getConfigData().getUserGroupDatas());
      model.getController().setUserGroupData(model.getConfigData().getUserGroupDatas());
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testGetSetMultiScreenData() {
    try {
      model.getController().getMultiScreenData(model.getConfigData().getMultiScreenDatas());
      model.getController().setMultiScreenData(model.getConfigData().getMultiScreenDatas());
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testGetModuleData() {
    try {
      model.getSwitchModuleData().reloadModules();
    } catch (ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testGetGridInfo() {
    try {
      model.getController().getGridInfo(Utilities.getMatrixOffset(model));
      GsonBuilder builder = new GsonBuilder().excludeFieldsWithoutExposeAnnotation();
      System.out.println(builder.create().toJson(model.getConfigData().getGridData()));
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testGetSetSystemFile() {
    try {
      String url = String.format("ftp://admin:admin@%s/config01.dtc", TestBase.ip);
      model.getConfigData().getSystemConfigData().getSystemData().setName("MediaCommTest1");
      model.writeFtp(url);
      model.readFtp(url);
      assertEquals("MediaCommTest1",
          model.getConfigData().getSystemConfigData().getSystemData().getName());

    } catch (ConfigException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }
}
