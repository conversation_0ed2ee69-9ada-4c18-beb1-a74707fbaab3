package com.mc.tool.caesar.api.datamodel.db;

import com.google.gson.annotations.SerializedName;

/**
 * 人脸识别设备.
 */
public class FrDevice {
  @SerializedName("deviceid")
  String deviceId;
  @SerializedName("ip")
  String ip;
  @SerializedName("port")
  Integer port;
  @SerializedName("mask")
  String mask;
  @SerializedName("gateway")
  String gateway;
  @SerializedName("kvm_ip")
  String kvmIp;
  @SerializedName("kvm_port")
  String kvmPort;
  @SerializedName("alias")
  String alias;
  @SerializedName("bound_rx")
  Integer boundRx;
}
