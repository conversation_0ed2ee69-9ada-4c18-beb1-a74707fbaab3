package com.mc.tool.caesar.vpm.pages.operation.crossscreen.controller;

import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.MultiviewData;
import com.mc.tool.caesar.api.datamodel.MultiviewLayoutType;
import com.mc.tool.caesar.api.datamodel.MultiviewSource;
import com.mc.tool.caesar.api.datamodel.extargs.DhdmiSelection;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.utils.CpuDataStateWrapper;
import com.mc.tool.caesar.api.utils.CpuDataStateWrapper.Access;
import com.mc.tool.caesar.api.utils.ExtendedSwitchUtility;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.crossscreen.view.CaesarScreenItem;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminalWrapper;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCrossScreenFunc;
import com.mc.tool.caesar.vpm.util.TerminalUtility;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.operation.crossscreen.controller.CrossScreenController;
import com.mc.tool.framework.operation.crossscreen.controller.ScreenItem;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.TypeWrapper;
import java.beans.PropertyChangeListener;
import java.net.URL;
import java.util.Collection;
import java.util.Collections;
import java.util.Optional;
import java.util.ResourceBundle;
import javafx.beans.property.ObjectProperty;
import javafx.event.WeakEventHandler;
import javafx.scene.Node;
import javafx.scene.canvas.Canvas;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.MenuItem;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.AnchorPane;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarCrossScreenController extends CrossScreenController {
  private CaesarDeviceController deviceController;
  protected WeakAdapter weakAdapter = new WeakAdapter();

  protected final String[] updateData = {
      ConsoleData.PROPERTY_MULTIVIEW_INDEX, MultiviewData.PROPERTY_LAYOUT_TYPE, MultiviewSource.PROPERTY_US_CONNECTED_CPU_INDEX
  };

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    super.initialize(location, resources);
    screenList.addEventFilter(MouseEvent.MOUSE_PRESSED, event -> {
      if (event.getButton() == MouseButton.SECONDARY && event.getClickCount() == 1
          && event.getPickResult().getIntersectedNode() instanceof Canvas
          && event.getPickResult().getIntersectedNode().getParent() instanceof AnchorPane
          && event.getPickResult().getIntersectedNode().getParent()
          .getParent() instanceof CaesarScreenItem) {
        CaesarScreenItem screenItem =
            (CaesarScreenItem) event.getPickResult().getIntersectedNode().getParent()
                .getParent();
        if (screenItem.getTarget().get() instanceof CaesarConTerminal) {
          ConsoleData conData =
              ((CaesarConTerminal) screenItem.getTarget().get()).getConsoleData();
          if (conData.isMultiview()) {
            ContextMenu contextMenu = new ContextMenu();
            for (MultiviewLayoutType type : MultiviewLayoutType.values()) {
              MenuItem menuItem = getMenuItem(type, conData);
              contextMenu.getItems().add(menuItem);
            }
            contextMenu.show(screenItem.getScene().getWindow(), event.getScreenX(),
                event.getScreenY());
          }
        }
        event.consume();
      }
    });
  }

  @Override
  public CaesarDeviceController getDeviceController() {
    return deviceController;
  }

  private MenuItem getMenuItem(MultiviewLayoutType type, ConsoleData conData) {
    MenuItem menuItem = new MenuItem(type.getName());
    menuItem.setOnAction(
        new WeakEventHandler<>(actionEvent -> deviceController.execute(() -> {
          try {
            deviceController.getDataModel().setMultiviewLayout(conData.getId(), type);
            MultiviewData multiviewData = conData.getMultiviewData();
            Optional.ofNullable(multiviewData).ifPresent(data -> data.setLayoutType(type));
          } catch (DeviceConnectionException | BusyException exception) {
            log.error("Fail to set multiviewData LayoutType!", exception);
          }
        })));
    return menuItem;
  }

  @Override
  public void connectForCrossScreen(VisualEditTerminal tx, VisualEditTerminal rx) {
    int sourceIndex = -1;
    VisualEditTerminal realTx = tx;
    if (tx instanceof CaesarCpuTerminalWrapper) {
      realTx = ((CaesarCpuTerminalWrapper) tx).getTerminal();
      sourceIndex = ((CaesarCpuTerminalWrapper) tx).getIndex();
    }
    if (realTx instanceof CaesarCpuTerminal && rx instanceof CaesarConTerminal) {
      CaesarCpuTerminal cpu = (CaesarCpuTerminal) realTx;
      CaesarConTerminal con = (CaesarConTerminal) rx;
      CpuData cpuData = cpu.getCpuData();
      ConsoleData consoleData = con.getConsoleData();
      if (cpuData == null) {
        log.warn("Cpu data is null!");
        return;
      }
      if (consoleData == null) {
        log.warn("Console data is null!");
        return;
      }
      final int sourceIndexFinal = sourceIndex;
      deviceController.execute(() -> {
        ExtendedSwitchUtility.fullAccess(
            deviceController.getDataModel(),
            consoleData,
            new CpuDataStateWrapper(cpuData, Access.FULL),
            true);
        if (sourceIndexFinal >= 0) {
          consoleData.setCpuHdmi(
              sourceIndexFinal == 0 ? DhdmiSelection.HDMI1 : DhdmiSelection.HDMI2);
          try {
            deviceController.getDataModel()
                .sendConsoleData(Collections.singletonList(consoleData));
          } catch (DeviceConnectionException | BusyException exc) {
            log.warn("Fail to send console data.", exc);
          }
        }
      });
    } else if (realTx == null && rx instanceof CaesarConTerminal) {
      CaesarConTerminal con = (CaesarConTerminal) rx;
      ConsoleData consoleData = con.getConsoleData();
      deviceController.execute(() ->
          ExtendedSwitchUtility.disconnect(deviceController.getDataModel(), consoleData, true));
    }
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
      updateMultiviewMode();
      //注意并发问题
      this.deviceController.getDataModel().addPropertyChangeListener(updateData,
          weakAdapter.wrap((PropertyChangeListener) evt -> updateMultiviewMode()));
    }
  }

  /**
   * 更新Multiview模式.
   */
  public void updateMultiviewMode() {
    for (Node child : screenList.getChildren()) {
      if (child instanceof CaesarScreenItem) {
        ((CaesarScreenItem) child).updateMultiviewMode();
      }
    }
  }

  protected void updateToDevice() {
    CaesarCrossScreenFunc caesarCrossScreenFunc = (CaesarCrossScreenFunc) func;
    if (isUpdating()
        || caesarCrossScreenFunc.getDeviceData() == null
        || caesarCrossScreenFunc.getDeviceData().isStatusNew()) {
      return;
    }
    caesarCrossScreenFunc.commit();
    deviceController.execute(
        () -> {
          try {
            deviceController
                .getDataModel()
                .sendMultiscreenData(
                    Collections.singletonList(caesarCrossScreenFunc.getDeviceData()));
          } catch (DeviceConnectionException | BusyException exception) {
            log.warn("Fail to send multi screen data!", exception);
          }
        });
  }

  @Override
  protected ScreenItem createScreenItem(
      int row, int column, ObjectProperty<VisualEditTerminal> target) {
    return new CaesarScreenItem(row, column, target, model, func, this);
  }

  @Override
  public boolean isConnetable(VisualEditTerminal tx, VisualEditTerminal rx) {
    if (deviceController == null) {
      return false;
    }
    if (!deviceController.getCaesarUserRight().isCrossScreenConnectable()) {
      return false;
    }

    if (tx != null && rx != null) {
      return !TerminalUtility.getConnectableType(deviceController, rx, tx).isEmpty();
    }
    return true;
  }

  /**
   * 获取Multiview通道列表.
   */
  @Override
  public Collection<TypeWrapper> getMultiviewChannel(VisualEditTerminal rx) {
    return TerminalUtility.getMultiviewChannel(rx);
  }

  @Override
  public void connectMultiview(VisualEditTerminal tx, VisualEditTerminal rx, int mode,
                               int channel) {
    if (tx instanceof CaesarCpuTerminal && rx instanceof CaesarConTerminal) {
      CaesarCpuTerminal cpu = (CaesarCpuTerminal) tx;
      CaesarConTerminal con = (CaesarConTerminal) rx;
      CpuData cpuData = cpu.getCpuData();
      ConsoleData consoleData = con.getConsoleData();
      if (cpuData == null) {
        log.warn("Cpu data is null!");
        return;
      }
      if (consoleData == null) {
        log.warn("Console data is null!");
        return;
      }
      deviceController.execute(() -> {
        try {
          deviceController.getDataModel().switchMultiviewTx(cpuData, consoleData, channel, mode);
          //切换成功后,更新数据
          deviceController.getDataModel().reloadMultiviewData();
        } catch (ConfigException | BusyException | DeviceConnectionException ex) {
          log.error("switchMultiviewTx failed", ex);
        }
      });
    } else if (tx == null && rx instanceof CaesarConTerminal) {
      CaesarConTerminal con = (CaesarConTerminal) rx;
      ConsoleData consoleData = con.getConsoleData();
      //断开连接
      deviceController.execute(() -> {
        try {
          deviceController.getDataModel().switchMultiviewTx(null, consoleData, channel, mode);
          //切换成功后,更新数据
          deviceController.getDataModel().reloadMultiviewData();
        } catch (ConfigException | BusyException | DeviceConnectionException ex) {
          log.error("switchMultiviewTx failed", ex);
        }
      });
    } else {
      log.warn("Error terminal child!");
    }
  }
}
