package com.mc.tool.caesar.vpm.pages.hostconfiguration;

import com.dooapp.fxform.FXForm;
import com.mc.tool.caesar.api.datamodel.SystemTimeData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.caesar.vpm.pages.hostconfiguration.Bundle.NbBundle;
import com.mc.tool.framework.interfaces.Page;
import javafx.application.Platform;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.scene.layout.Pane;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarHostConfigurationPage implements Page {
  public static final String NAME = "hostconfiguration";
  private final CaesarHostConfigurationPageView view;
  private final BooleanProperty visibility = new SimpleBooleanProperty(true);

  /** . */
  public CaesarHostConfigurationPage(CaesarEntity entity) {
    view = new CaesarHostConfigurationPageView(entity.getController());
    view.getController().setDeviceController(entity.getController());
    view.getController().setEntity(entity);
  }

  @Override
  public String getTitle() {
    return NbBundle.getMessage("HostConfigurationPage.title");
  }

  @Override
  public String getName() {
    return NAME;
  }

  @Override
  public Pane getView() {
    return view;
  }

  @Override
  public String getStyleClass() {
    return "caesar-hostconfiguration-page";
  }

  @Override
  public BooleanProperty getVisibleProperty() {
    return visibility;
  }

  @Override
  public void showObject(Object object) {}

  @Override
  public void refresh() {
    reloadTime();
    view.getController().getActivateConfigPane().getController().reload();
  }

  protected void reloadTime() {
    final SystemTimeData time;
    try {
      time = view.getController().getDeviceController().getDataModel().getTime();
      Platform.runLater(
          () -> {
            FXForm<GeneralConfigurationBean> generalForm = view.getController().getGeneralForm();
            generalForm.getSource().setTime(time.getTime());
          });
    } catch (ConfigException | BusyException ex) {
      log.warn("Fail to load system time！", ex);
    }
  }

  @Override
  public void refreshOnShow() {
    reloadTime();
  }

  @Override
  public void close() {
    view.getController().close();
  }
}
