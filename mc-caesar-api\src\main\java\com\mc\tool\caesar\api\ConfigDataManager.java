package com.mc.tool.caesar.api;

import com.mc.tool.caesar.api.datamodel.ConfigData;
import com.mc.tool.caesar.api.datamodel.ConfigMetaData;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.api.datamodel.MatrixData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.datamodel.MultiviewData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.datamodel.SourceCropData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.datamodel.UserGroupData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.interfaces.CommitRollback;
import com.mc.tool.caesar.api.utils.DefaultCommitRollback;
import com.mc.tool.caesar.api.utils.Utilities;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 *
 * @brief 配置数据管理器，提供对ConfigData的加工后的数据的获取的接口
 */
public final class ConfigDataManager extends DefaultCommitRollback {

  private ConfigData configData = null;
  @Setter
  @Getter
  private ConfigMetaData configMetaData = null;

  void setConfigData(ConfigData configData) {
    this.configData = configData;
    initCommitRollback();
  }

  /**
   * .
   */
  public Collection<ConsoleData> getConsoles(int... status) {
    Collection<ConsoleData> consoles = new ArrayList<>();
    for (ConsoleData consoleData : this.configData.getConsoleDatas()) {
      if (Utilities.areBitsSet(consoleData.getStatus(), status)) {
        consoles.add(consoleData);
      }
    }
    return consoles;
  }

  public Collection<ConsoleData> getActiveConsoles() {
    return getConsoles(CaesarConstants.Console.Status.ACTIVE);
  }

  /**
   * 获取所有con.
   */
  public Collection<ConsoleData> getAllConsoles() {
    Collection<ConsoleData> consoles = new ArrayList<>();
    for (ConsoleData consoleData : this.configData.getConsoleDatas()) {
      consoles.add(consoleData);
    }
    return consoles;
  }

  /**
   * 获取不包含vpcon的con列表.
   */
  public Collection<ConsoleData> getConsolesWithoutVpcon() {
    Collection<ConsoleData> consoles = new ArrayList<>();
    for (ConsoleData consoleData : this.configData.getConsoleDatas()) {
      if (consoleData.isStatusVpcon()) {
        continue;
      }
      consoles.add(consoleData);
    }
    return consoles;
  }

  /**
   * 获取不包含vpcon的有效的con列表.
   *
   * @return con列表
   */
  public Collection<ConsoleData> getActiveConsolesWithoutVpcon() {
    List<ConsoleData> result =
        new ArrayList<>(getConsoles(CaesarConstants.Console.Status.ACTIVE));
    result.removeIf(ConsoleData::isStatusVpcon);
    return result;
  }

  /**
   * .
   */
  public Collection<VpConsoleData> getActiveVpconsolses() {
    Collection<VpConsoleData> vpConsoleDatas = new ArrayList<>();
    for (VpConsoleData vpConsoleData : this.configData.getVpConsoleDatas()) {
      if (vpConsoleData.isStatusActive()) {
        vpConsoleDatas.add(vpConsoleData);
      }
    }
    return vpConsoleDatas;
  }

  /**
   * 获取终端的vpcon，有子vpcon的不算.
   */
  public Collection<VpConsoleData> getActiveEndpointVpconsoles() {
    Collection<VpConsoleData> vpConsoleDatas = new ArrayList<>();
    for (VpConsoleData vpConsoleData : this.configData.getVpConsoleDatas()) {
      if (!vpConsoleData.isStatusActive()) {
        continue;
      }
      if (vpConsoleData.getOutPortCount() > 1) {
        for (VpConsoleData sub : vpConsoleData.getOutPortList()) {
          if (sub == null) {
            continue;
          }
          vpConsoleDatas.add(sub);
        }
      } else {
        vpConsoleDatas.add(vpConsoleData);
      }
    }
    return vpConsoleDatas;
  }

  /**
   * .
   */
  public Collection<CpuData> getCpus(int... status) {
    Collection<CpuData> cpus = new ArrayList<>();
    for (CpuData cpuData : this.configData.getCpuDatas()) {
      if (Utilities.areBitsSet(cpuData.getStatus(), status)) {
        cpus.add(cpuData);
      }
    }
    return cpus;
  }

  /**
   * .
   */
  public Collection<CpuData> getCpus() {
    Collection<CpuData> cpus = new ArrayList<>();
    for (CpuData cpuData : this.configData.getCpuDatas()) {
      cpus.add(cpuData);
    }
    return cpus;
  }

  /**
   * .
   */
  public Collection<TxRxGroupData> getTxRxGroups(int... status) {
    Collection<TxRxGroupData> groups = new ArrayList<>();
    for (TxRxGroupData txRxGroupData : this.configData.getTxRxGroupDatas()) {
      if (Utilities.areBitsSet(txRxGroupData.getStatus(), status)) {
        groups.add(txRxGroupData);
      }
    }
    return groups;
  }

  /**
   * .
   */
  public Collection<TxRxGroupData> getTxRxGroups() {
    Collection<TxRxGroupData> groups = new ArrayList<>();
    for (TxRxGroupData txRxGroupData : this.configData.getTxRxGroupDatas()) {
      groups.add(txRxGroupData);
    }
    return groups;
  }

  public Collection<CpuData> getActiveCpus() {
    return getCpus(CaesarConstants.Cpu.Status.ACTIVE);
  }

  public Collection<TxRxGroupData> getActiveTxRxGroups() {
    return getTxRxGroups(CaesarConstants.TxRxGroup.Status.ACTIVE);
  }

  /**
   * 获取未使用的txrx分组.
   */
  public TxRxGroupData getUnActiveTxRxGroup() {
    for (TxRxGroupData txRxGroupData : this.configData.getTxRxGroupDatas()) {
      if (!Utilities.areBitsSet(txRxGroupData.getStatus(), CaesarConstants.TxRxGroup.Status.ACTIVE)) {
        return txRxGroupData;
      }
    }
    return null;
  }

  /**
   * .
   */
  public Collection<ExtenderData> getExtenders(int... status) {
    Collection<ExtenderData> extenders = new ArrayList<>();
    for (ExtenderData extenderData : this.configData.getExtenderDatas()) {
      if (Utilities.areBitsSet(extenderData.getStatus(), status)) {
        extenders.add(extenderData);
      }
    }
    return extenders;
  }

  public Collection<ExtenderData> getActiveExtenders() {
    return getExtenders(CaesarConstants.Extender.Status.ACTIVE);
  }

  /**
   * .
   */
  public Collection<MatrixData> getMatrices(int... status) {
    Collection<MatrixData> matrices = new ArrayList<>();
    for (MatrixData matrixData : this.configData.getMatrixDatas()) {
      if (Utilities.areBitsSet(matrixData.getStatus(), status)) {
        matrices.add(matrixData);
      }
    }
    return matrices;
  }

  public Collection<MatrixData> getActiveMatrices() {
    return getMatrices(CaesarConstants.Matrix.Status.ACTIVE);
  }

  /**
   * .
   */
  public Collection<PortData> getPorts(int... status) {
    Collection<PortData> ports = new ArrayList<>();
    for (PortData portData : this.configData.getPortDatas()) {
      if (Utilities.areBitsSet(portData.getStatus(), status)) {
        ports.add(portData);
      }
    }
    return ports;
  }

  public Collection<PortData> getActivePorts() {
    return getPorts(CaesarConstants.Port.Status.ACTIVE);
  }

  public Collection<PortData> getAvailablePorts() {
    return getPorts(1);
  }

  /**
   * .
   */
  public Collection<UserData> getUsers(int... status) {
    Collection<UserData> users = new ArrayList<>();
    for (UserData userData : this.configData.getUserDatas()) {
      if (Utilities.areBitsSet(userData.getStatus(), status)) {
        users.add(userData);
      }
    }
    return users;
  }

  /**
   * .
   */
  public Collection<UserGroupData> getUserGroups(int... status) {
    Collection<UserGroupData> users = new ArrayList<>();
    for (UserGroupData userGroupData : this.configData.getUserGroupDatas()) {
      if (Utilities.areBitsSet(userGroupData.getStatus(), status)) {
        users.add(userGroupData);
      }
    }
    return users;
  }

  public Collection<UserGroupData> getActiveUserGroups() {
    return getUserGroups(CaesarConstants.UserGroup.Status.ACTIVE);
  }

  /**
   * .
   */
  public Collection<MultiScreenData> getMultiScreens(int... status) {
    Collection<MultiScreenData> multiScreens = new ArrayList<>();
    for (MultiScreenData multiScreen : this.configData.getMultiScreenDatas()) {
      if (Utilities.areBitsSet(multiScreen.getStatus(), status)) {
        multiScreens.add(multiScreen);
      }
    }
    return multiScreens;
  }

  public Collection<MultiScreenData> getActiveMultiScreen() {
    return getMultiScreens(CaesarConstants.MultiScreen.Status.ACTIVE);
  }

  public Collection<UserData> getActiveUsers() {
    return getUsers(CaesarConstants.User.Status.ACTIVE);
  }

  /**
   * 获取有效的sourceCropData.
   *
   * @return 有效的数据列表.
   */
  public Collection<SourceCropData> getActiveSourceCropDatas() {
    Collection<SourceCropData> sourceCropDataList = new ArrayList<>();
    for (SourceCropData sourceCropData : this.configData.getSourceCropDatas()) {
      if (sourceCropData.getCpuIndex() > 0) {
        sourceCropDataList.add(sourceCropData);
      }
    }
    return sourceCropDataList;
  }

  /**
   * .
   */
  public ConsoleData getConsoleData(int oid) {
    if (0 <= oid && oid < getConfigMetaData().getConsoleCount()) {
      return this.configData.getConsoleData(oid);
    }
    return null;
  }

  public List<ConsoleData> getConsoleData(int... oids) {
    return Utilities.getObjects(this.configData.getConsoleDatas(), oids);
  }

  /**
   * .
   */
  public ConsoleData getConsoleData4Id(int id) {
    if (id <= 0) {
      return null;
    }
    for (ConsoleData consoleData : this.configData.getConsoleDatas()) {
      if (consoleData.getId() == id) {
        return consoleData;
      }
    }
    return null;
  }

  /**
   * 通过oid获取CpuData.
   */
  public CpuData getCpuData(int oid) {
    if (0 <= oid && oid < getConfigMetaData().getCpuCount()) {
      return this.configData.getCpuData(oid);
    }
    return null;
  }

  /**
   * 通过oid获取CpuData.
   */
  public List<CpuData> getCpuData(int... oids) {
    return Utilities.getObjects(this.configData.getCpuDatas(), oids);
  }

  /**
   * 通过id获取CpuData.
   */
  public CpuData getCpuData4Id(int id) {
    if (id <= 0) {
      return null;
    }
    for (CpuData cpuData : this.configData.getCpuDatas()) {
      if (cpuData.getId() == id) {
        return cpuData;
      }
    }
    return null;
  }

  /**
   * 通过id获取CpuData.
   */
  public List<CpuData> getCpuData4TxGroupIndex(int index) {
    List<CpuData> cpuDataList = new ArrayList<>();
    if (index <= 0) {
      return cpuDataList;
    }
    for (CpuData cpuData : this.configData.getCpuDatas()) {
      if (cpuData.getTxGroupIndex() == index) {
        cpuDataList.add(cpuData);
      }
    }
    return cpuDataList;
  }

  /**
   * 通过txRxGroupIndex获取CpuData.
   */
  public List<ConsoleData> getConsoleDataByTxRxGroupIndex(int index) {
    List<ConsoleData> consoleDataList = new ArrayList<>();
    if (index <= 0) {
      return consoleDataList;
    }
    for (ConsoleData consoleData : this.configData.getConsoleDatas()) {
      if (consoleData.getGroupIndex() == index) {
        consoleDataList.add(consoleData);
      }
    }
    return consoleDataList;
  }

  /**
   * .
   */
  public ExtenderData getExtenderData(int oid) {
    if (0 <= oid && this.configMetaData != null && oid < this.configMetaData.getExtenderCount()) {
      return this.configData.getExtenderData(oid);
    }
    return null;
  }

  /**
   * .
   */
  public List<ExtenderData> getExtenderData(int... oids) {
    return Utilities.getObjects(this.configData.getExtenderDatas(), oids);
  }

  /**
   * .
   */
  public MultiScreenData getMultiScreenData(int oid) {
    if (0 <= oid && this.configMetaData != null && oid
        < this.configMetaData.getMultiScreenCount()) {
      return this.configData.getMultiScreenData(oid);
    }
    return null;
  }


  /**
   * .
   */
  public ExtenderData getExtenderDataById(int id) {
    for (ExtenderData extenderData : this.configData.getExtenderDatas()) {
      if (id == extenderData.getId()) {
        return extenderData;
      }
    }
    return null;
  }

  /**
   * .
   */
  public ExtenderData getExtenderDataByIdAndType(int id, int type) {
    for (ExtenderData extenderData : this.configData.getExtenderDatas()) {
      if (id == extenderData.getId() && type == extenderData.getType()) {
        return extenderData;
      }
    }
    return null;
  }

  /**
   * .
   */
  public MultiviewData getMultiviewData(int oid) {
    if (0 <= oid && this.configMetaData != null && oid
        < this.configMetaData.getMultiviewDataCount()) {
      return this.configData.getMultiviewData(oid);
    }
    return null;
  }

  /**
   * .
   */
  public PortData getPortData(int oid) {
    if (0 <= oid && oid < getConfigMetaData().getPortCount()) {
      return this.configData.getPortData(oid);
    }
    return null;
  }

  public List<PortData> getPortData(int... oids) {
    return Utilities.getObjects(this.configData.getPortDatas(), oids);
  }

  /**
   * .
   */
  public VpConsoleData getEndPointVpconsoleData4Id(int id) {
    for (VpConsoleData consoleData : getActiveEndpointVpconsoles()) {
      if (consoleData.getId() == id) {
        return consoleData;
      }
    }
    return null;
  }

  /**
   * .
   */
  public UserData getUserData(int oid) {
    if (0 <= oid && oid < getConfigMetaData().getUserCount()) {
      return this.configData.getUserData(oid);
    }
    return null;
  }

  public List<UserData> getUserData(int... oids) {
    return Utilities.getObjects(this.configData.getUserDatas(), oids);
  }

  /**
   * .
   */
  public UserGroupData getUserGroupData(int oid) {
    if (0 <= oid && oid < getConfigMetaData().getUserGroupCount()) {
      return this.configData.getUserGroupData(oid);
    }
    return null;
  }

  public List<UserGroupData> getUserGroupData(int... oids) {
    return Utilities.getObjects(this.configData.getUserGroupDatas(), oids);
  }

  /**
   * .
   */
  public FunctionKeyData getFunctionKeyData(int oid) {
    if (0 <= oid && oid < getConfigMetaData().getFunctionKeyCount()) {
      return this.configData.getFunctionKeyData(oid);
    }
    return null;
  }

  public List<FunctionKeyData> getFunctionKeyData(int... oids) {
    return Utilities.getObjects(this.configData.getFunctionKeyDatas(), oids);
  }

  /**
   * .
   */
  public ConsoleData getFreeConsole() {
    for (ConsoleData consoleData : this.configData.getConsoleDatas()) {
      if (0 == consoleData.getStatus()) {
        return consoleData;
      }
    }
    return null;
  }

  /**
   * .
   */
  public MultiScreenData getFreeMultiScreenData() {
    for (MultiScreenData msd : this.configData.getMultiScreenDatas()) {
      if (0 == msd.getStatus()) {
        return msd;
      }
    }
    return null;
  }

  /**
   * .
   */
  public CpuData getFreeCpu() {
    for (CpuData cpuData : this.configData.getCpuDatas()) {
      if (0 == cpuData.getStatus()) {
        return cpuData;
      }
    }
    return null;
  }


  /**
   * .
   */
  public ExtenderData getFreeExtender() {
    for (ExtenderData extenderData : this.configData.getExtenderDatas()) {
      if (0 == extenderData.getStatus()) {
        return extenderData;
      }
    }
    return null;
  }

  /**
   * .
   */
  public UserData getFreeUser() {
    for (UserData userData : this.configData.getUserDatas()) {
      if (!userData.isStatusActive() && !userData.isStatusNew()) {
        return userData;
      }
    }
    return null;
  }

  /**
   * .
   */
  public UserGroupData getFreeUserGroup() {
    for (UserGroupData userGroupData : this.configData.getUserGroupDatas()) {
      if (0 == userGroupData.getStatus()) {
        return userGroupData;
      }
    }
    return null;
  }

  /**
   * .
   */
  public FunctionKeyData getFreeFunctionKeyData() {
    for (FunctionKeyData functionKeyData : this.configData.getFunctionKeyDatas()) {
      if (!functionKeyData.isStatusActive()) {
        return functionKeyData;
      }
    }
    return null;
  }

  /**
   * 获取空的宏数据.
   *
   * @param exclusion 要排除的宏数据.
   * @return 空的宏数据.
   */
  public FunctionKeyData getFreeFunctionKeyData(Collection<FunctionKeyData> exclusion) {
    for (FunctionKeyData functionKeyData : this.configData.getFunctionKeyDatas()) {
      if (!functionKeyData.isStatusActive() && !exclusion.contains(functionKeyData)) {
        return functionKeyData;
      }
    }
    return null;
  }

  /**
   * 获取一个空闲的sourceCropData.
   *
   * @return 空闲的数据，如果没有找到返回null
   */
  public SourceCropData getFreeSourceCropData() {
    for (SourceCropData sourceCropData : this.configData.getSourceCropDatas()) {
      if (0 == sourceCropData.getCpuIndex()) {
        return sourceCropData;
      }
    }
    return null;
  }

  /**
   * 根据索引获取sourceCropData.
   */
  public SourceCropData getSourceCropData(int oid) {
    if (0 <= oid && oid < getConfigMetaData().getSourceCropDataCount()) {
      return this.configData.getSourceCropData(oid);
    }
    return null;
  }

  public int getAutoIdRealCpu() {
    return getAutoId(0);
  }

  public int getAutoIdVirtualCpu() {
    return getAutoId(1);
  }

  public int getAutoIdRealCon() {
    return getAutoId(2);
  }

  public int getAutoIdVirtualCon() {
    return getAutoId(3);
  }

  public int getAutoIdExtender() {
    return getAutoId(4);
  }

  /**
   * .
   */
  public int getAutoId(int type) {
    switch (type) {
      case 0:
      case 1:
      case 2:
      case 3:
        boolean virtual = 1 == type % 2;
        Collection<Integer> ids = new HashSet<>();
        if (0 == type / 2) {
          for (CpuData cpuData : this.configData.getCpuDatas()) {
            if (cpuData.isStatusActive() && virtual == cpuData.isStatusVirtual()) {
              ids.add(cpuData.getId());
            }
          }
        } else {
          for (ConsoleData consoleData : this.configData.getConsoleDatas()) {
            if (consoleData.isStatusActive() && virtual == consoleData.isStatusVirtual()) {
              ids.add(consoleData.getId());
            }
          }
        }
        for (int autoId = this.configData.getSystemConfigData().getAutoIdData()
            .getAutoId(type); autoId < CaesarConstants.MAX_ID; autoId++) {
          if (!ids.contains(autoId)) {
            return autoId;
          }
        }
        return -1;
      case 4:
        Collection<Integer> extenderIds = new HashSet<>();
        for (ExtenderData extenderData : this.configData.getExtenderDatas()) {
          extenderIds.add(extenderData.getId());
        }
        for (int autoId = 90000000; autoId < 99999999; autoId++) {
          if (!extenderIds.contains(autoId)) {
            return autoId;
          }
        }
        return -1;
      default:
        break;
    }
    return -1;
  }

  @Override
  protected Collection<? extends CommitRollback> getDependentCommitRollbacks() {
    return Collections.singletonList(this.configData);
  }
}

