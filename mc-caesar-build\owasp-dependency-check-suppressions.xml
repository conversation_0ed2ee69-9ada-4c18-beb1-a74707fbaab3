<?xml version="1.0" encoding="UTF-8"?>
<suppressions xmlns="https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd ">
  <suppress>
    <notes><![CDATA[
   file name: hibernate-validator-5.1.3.Final.jar
   ]]></notes>
    <packageUrl regex="true">^pkg:maven/org\.hibernate/hibernate\-validator@.*$</packageUrl>
    <vulnerabilityName>CVE-2017-7536</vulnerabilityName>
  </suppress>
  <suppress>
    <notes><![CDATA[
   file name: hibernate-validator-5.1.3.Final.jar
   ]]></notes>
    <packageUrl regex="true">^pkg:maven/org\.hibernate/hibernate\-validator@.*$</packageUrl>
    <cve>CVE-2020-25638</cve>
  </suppress>
  <suppress>
    <notes><![CDATA[
   file name: hutool-http-5.8.29.jar
   ]]></notes>
    <packageUrl regex="true">^pkg:maven/cn\.hutool/hutool\-http@.*$</packageUrl>
    <vulnerabilityName>CVE-2022-22885</vulnerabilityName>
  </suppress>
  <suppress>
    <notes><![CDATA[
   file name: jackson-databind-2.15.2.jar
   ]]></notes>
    <packageUrl regex="true">^pkg:maven/com\.fasterxml\.jackson\.core/jackson\-databind@.*$</packageUrl>
    <cve>CVE-2023-35116</cve>
  </suppress>
</suppressions>