package com.mc.tool.caesar.vpm.pages.hostconfiguration;

import com.mc.common.validation.constraints.StringFormat;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.SystemConfigData;
import com.mc.tool.caesar.api.utils.IpUtil;

/**
 * .
 */
public class SnmpConfigurationBean {
  private SystemConfigData systemConfigData;

  private Boolean isSnmp;
  private Integer snmpPort;

  private Boolean snmp1IsTrap;
  private String snmp1Address;
  private Integer snmp1TrapPort;
  private Boolean snmp1IsStatus;
  private Boolean snmp1IsTemperature;
  private Boolean snmp1IsInsertBoard;
  private Boolean snmp1IsRemoveBoard;
  private Boolean snmp1IsInvalidBoard;
  private Boolean snmp1IsInsertExtender;
  private Boolean snmp1IsRemoveExtender;
  private Boolean snmp1IsSwitchCommand;
  private Boolean snmp1IsFanTray1;
  private Boolean snmp1IsFanTray2;
  private Boolean snmp1IsPowerSupply1;
  private Boolean snmp1IsPowerSupply2;
  private Boolean snmp1IsPowerSupply3;

  private Boolean snmp2IsTrap;
  private String snmp2Address;
  private Integer snmp2TrapPort;
  private Boolean snmp2IsStatus;
  private Boolean snmp2IsTemperature;
  private Boolean snmp2IsInsertBoard;
  private Boolean snmp2IsRemoveBoard;
  private Boolean snmp2IsInvalidBoard;
  private Boolean snmp2IsInsertExtender;
  private Boolean snmp2IsRemoveExtender;
  private Boolean snmp2IsSwitchCommand;
  private Boolean snmp2IsFanTray1;
  private Boolean snmp2IsFanTray2;
  private Boolean snmp2IsPowerSupply1;
  private Boolean snmp2IsPowerSupply2;
  private Boolean snmp2IsPowerSupply3;

  public SnmpConfigurationBean(SystemConfigData systemConfigData) {
    this.systemConfigData = systemConfigData;
  }

  public Boolean getIsSnmp() {
    isSnmp = systemConfigData.getNetworkDataPreset1().isSnmp();
    return isSnmp;
  }

  public void setIsSnmp(Boolean enabled) {
    systemConfigData.getNetworkDataPreset1().setSnmp(enabled);
  }

  public Integer getSnmpPort() {
    snmpPort = CaesarConstants.SNMP_AGENT_PORT;
    return snmpPort;
  }

  public void setSnmpPort(Integer port) {}

  // snmp1
  public Boolean getSnmp1IsTrap() {
    snmp1IsTrap = systemConfigData.getSnmpData().isTrapEnabled();
    return snmp1IsTrap;
  }

  public void setSnmp1IsTrap(Boolean enabled) {
    systemConfigData.getSnmpData().setTrapEnabled(enabled);
  }

  @StringFormat(format = "[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}")
  public String getSnmp1Address() {
    snmp1Address = IpUtil.getAddressString(systemConfigData.getSnmpData().getAddress());
    return snmp1Address;
  }

  public void setSnmp1Address(String address) {
    systemConfigData.getSnmpData().setAddress(IpUtil.getAddressByte(address));
  }

  public Integer getSnmp1TrapPort() {
    snmp1TrapPort = CaesarConstants.SNMP_TRAP_PORT;
    return snmp1TrapPort;
  }

  public void setSnmp1TrapPort(Integer port) {}

  public Boolean getSnmp1IsStatus() {
    snmp1IsStatus = systemConfigData.getSnmpData().isStatusEnabled();
    return snmp1IsStatus;
  }

  public void setSnmp1IsStatus(Boolean enabled) {
    systemConfigData.getSnmpData().setStatusEnabled(enabled);
  }

  public Boolean getSnmp1IsTemperature() {
    snmp1IsTemperature = systemConfigData.getSnmpData().isTemperatureEnabled();
    return snmp1IsTemperature;
  }

  public void setSnmp1IsTemperature(Boolean enabled) {
    systemConfigData.getSnmpData().setTemperatureEnabled(enabled);
  }

  public Boolean getSnmp1IsInsertBoard() {
    snmp1IsInsertBoard = systemConfigData.getSnmpData().isInsertBoardEnabled();
    return snmp1IsInsertBoard;
  }

  public void setSnmp1IsInsertBoard(Boolean enabled) {
    systemConfigData.getSnmpData().setInsertBoardEnabled(enabled);
  }

  public Boolean getSnmp1IsRemoveBoard() {
    snmp1IsRemoveBoard = systemConfigData.getSnmpData().isRemoveBoardEnabled();
    return snmp1IsRemoveBoard;
  }

  public void setSnmp1IsRemoveBoard(Boolean enabled) {
    systemConfigData.getSnmpData().setRemoveBoardEnabled(enabled);
  }

  public Boolean getSnmp1IsInvalidBoard() {
    snmp1IsInvalidBoard = systemConfigData.getSnmpData().isInvalidBoardEnabled();
    return snmp1IsInvalidBoard;
  }

  public void setSnmp1IsInvalidBoard(Boolean enabled) {
    systemConfigData.getSnmpData().setInvalidBoardEnabled(enabled);
  }

  public Boolean getSnmp1IsInsertExtender() {
    snmp1IsInsertExtender = systemConfigData.getSnmpData().isInsertExtenderEnabled();
    return snmp1IsInsertExtender;
  }

  public void setSnmp1IsInsertExtender(Boolean enabled) {
    systemConfigData.getSnmpData().setInsertExtenderEnabled(enabled);
  }

  public Boolean getSnmp1IsRemoveExtender() {
    snmp1IsRemoveExtender = systemConfigData.getSnmpData().isRemoveExtenderEnabled();
    return snmp1IsRemoveExtender;
  }

  public void setSnmp1IsRemoveExtender(Boolean enabled) {
    systemConfigData.getSnmpData().setRemoveExtenderEnabled(enabled);
  }

  public Boolean getSnmp1IsSwitchCommand() {
    snmp1IsSwitchCommand = systemConfigData.getSnmpData().isSwitchCommandEnabled();
    return snmp1IsSwitchCommand;
  }

  public void setSnmp1IsSwitchCommand(Boolean enabled) {
    systemConfigData.getSnmpData().setSwitchCommandEnabled(enabled);
  }

  public Boolean getSnmp1IsFanTray1() {
    snmp1IsFanTray1 = systemConfigData.getSnmpData().isFanTray1Enabled();
    return snmp1IsFanTray1;
  }

  public void setSnmp1IsFanTray1(Boolean enabled) {
    systemConfigData.getSnmpData().setFanTray1Enabled(enabled);
  }

  public Boolean getSnmp1IsFanTray2() {
    snmp1IsFanTray2 = systemConfigData.getSnmpData().isFanTray2Enabled();
    return snmp1IsFanTray2;
  }

  public void setSnmp1IsFanTray2(Boolean enabled) {
    systemConfigData.getSnmpData().setFanTray2Enabled(enabled);
  }

  public Boolean getSnmp1IsPowerSupply1() {
    snmp1IsPowerSupply1 = systemConfigData.getSnmpData().isPowerSupply1Enabled();
    return snmp1IsPowerSupply1;
  }

  public void setSnmp1IsPowerSupply1(Boolean enabled) {
    systemConfigData.getSnmpData().setPowerSupply1Enabled(enabled);
  }

  public Boolean getSnmp1IsPowerSupply2() {
    snmp1IsPowerSupply2 = systemConfigData.getSnmpData().isPowerSupply2Enabled();
    return snmp1IsPowerSupply2;
  }

  public void setSnmp1IsPowerSupply2(Boolean enabled) {
    systemConfigData.getSnmpData().setPowerSupply2Enabled(enabled);
  }

  public Boolean getSnmp1IsPowerSupply3() {
    snmp1IsPowerSupply3 = systemConfigData.getSnmpData().isPowerSupply3Enabled();
    return snmp1IsPowerSupply3;
  }

  public void setSnmp1IsPowerSupply3(Boolean enabled) {
    systemConfigData.getSnmpData().setPowerSupply3Enabled(enabled);
  }

  // snmp2
  public Boolean getSnmp2IsTrap() {
    snmp2IsTrap = systemConfigData.getSnmpData2().isTrapEnabled();
    return snmp2IsTrap;
  }

  public void setSnmp2IsTrap(Boolean enabled) {
    systemConfigData.getSnmpData2().setTrapEnabled(enabled);
  }

  @StringFormat(format = "[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}")
  public String getSnmp2Address() {
    snmp2Address = IpUtil.getAddressString(systemConfigData.getSnmpData2().getAddress());
    return snmp2Address;
  }

  public void setSnmp2Address(String address) {
    systemConfigData.getSnmpData2().setAddress(IpUtil.getAddressByte(address));
  }

  public Integer getSnmp2TrapPort() {
    snmp2TrapPort = CaesarConstants.SNMP_TRAP_PORT;
    return snmp2TrapPort;
  }

  public void setSnmp2TrapPort(Integer port) {}

  public Boolean getSnmp2IsStatus() {
    snmp2IsStatus = systemConfigData.getSnmpData2().isStatusEnabled();
    return snmp2IsStatus;
  }

  public void setSnmp2IsStatus(Boolean enabled) {
    systemConfigData.getSnmpData2().setStatusEnabled(enabled);
  }

  public Boolean getSnmp2IsTemperature() {
    snmp2IsTemperature = systemConfigData.getSnmpData2().isTemperatureEnabled();
    return snmp2IsTemperature;
  }

  public void setSnmp2IsTemperature(Boolean enabled) {
    systemConfigData.getSnmpData2().setTemperatureEnabled(enabled);
  }

  public Boolean getSnmp2IsInsertBoard() {
    snmp2IsInsertBoard = systemConfigData.getSnmpData2().isInsertBoardEnabled();
    return snmp2IsInsertBoard;
  }

  public void setSnmp2IsInsertBoard(Boolean enabled) {
    systemConfigData.getSnmpData2().setInsertBoardEnabled(enabled);
  }

  public Boolean getSnmp2IsRemoveBoard() {
    snmp2IsRemoveBoard = systemConfigData.getSnmpData2().isRemoveBoardEnabled();
    return snmp2IsRemoveBoard;
  }

  public void setSnmp2IsRemoveBoard(Boolean enabled) {
    systemConfigData.getSnmpData2().setRemoveBoardEnabled(enabled);
  }

  public Boolean getSnmp2IsInvalidBoard() {
    snmp2IsInvalidBoard = systemConfigData.getSnmpData2().isInvalidBoardEnabled();
    return snmp2IsInvalidBoard;
  }

  public void setSnmp2IsInvalidBoard(Boolean enabled) {
    systemConfigData.getSnmpData2().setInvalidBoardEnabled(enabled);
  }

  public Boolean getSnmp2IsInsertExtender() {
    snmp2IsInsertExtender = systemConfigData.getSnmpData2().isInsertExtenderEnabled();
    return snmp2IsInsertExtender;
  }

  public void setSnmp2IsInsertExtender(Boolean enabled) {
    systemConfigData.getSnmpData2().setInsertExtenderEnabled(enabled);
  }

  public Boolean getSnmp2IsRemoveExtender() {
    snmp2IsRemoveExtender = systemConfigData.getSnmpData2().isRemoveExtenderEnabled();
    return snmp2IsRemoveExtender;
  }

  public void setSnmp2IsRemoveExtender(Boolean enabled) {
    systemConfigData.getSnmpData2().setRemoveExtenderEnabled(enabled);
  }

  public Boolean getSnmp2IsSwitchCommand() {
    snmp2IsSwitchCommand = systemConfigData.getSnmpData2().isSwitchCommandEnabled();
    return snmp2IsSwitchCommand;
  }

  public void setSnmp2IsSwitchCommand(Boolean enabled) {
    systemConfigData.getSnmpData2().setSwitchCommandEnabled(enabled);
  }

  public Boolean getSnmp2IsFanTray1() {
    snmp2IsFanTray1 = systemConfigData.getSnmpData2().isFanTray1Enabled();
    return snmp2IsFanTray1;
  }

  public void setSnmp2IsFanTray1(Boolean enabled) {
    systemConfigData.getSnmpData2().setFanTray1Enabled(enabled);
  }

  public Boolean getSnmp2IsFanTray2() {
    snmp2IsFanTray2 = systemConfigData.getSnmpData2().isFanTray2Enabled();
    return snmp2IsFanTray2;
  }

  public void setSnmp2IsFanTray2(Boolean enabled) {
    systemConfigData.getSnmpData2().setFanTray2Enabled(enabled);
  }

  public Boolean getSnmp2IsPowerSupply1() {
    snmp2IsPowerSupply1 = systemConfigData.getSnmpData2().isPowerSupply1Enabled();
    return snmp2IsPowerSupply1;
  }

  public void setSnmp2IsPowerSupply1(Boolean enabled) {
    systemConfigData.getSnmpData2().setPowerSupply1Enabled(enabled);
  }

  public Boolean getSnmp2IsPowerSupply2() {
    snmp2IsPowerSupply2 = systemConfigData.getSnmpData2().isPowerSupply2Enabled();
    return snmp2IsPowerSupply2;
  }

  public void setSnmp2IsPowerSupply2(Boolean enabled) {
    systemConfigData.getSnmpData2().setPowerSupply2Enabled(enabled);
  }

  public Boolean getSnmp2IsPowerSupply3() {
    snmp2IsPowerSupply3 = systemConfigData.getSnmpData2().isPowerSupply3Enabled();
    return snmp2IsPowerSupply3;
  }

  public void setSnmp2IsPowerSupply3(Boolean enabled) {
    systemConfigData.getSnmpData2().setPowerSupply3Enabled(enabled);
  }
}
