package com.mc.tool.caesar.vpm.pages.operation.multiscreen.view;

import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.MultiviewData;
import com.mc.tool.caesar.api.datamodel.MultiviewSource;
import com.mc.tool.caesar.api.utils.SwitchType;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.multiscreen.controller.CaesarMultiScreenControllable;
import com.mc.tool.caesar.vpm.pages.operation.multiscreen.datamodel.CaesarMultiScreenData;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMultiScreenFunc;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.TerminalUtility;
import com.mc.tool.framework.operation.crossscreen.controller.MultiScreenItem;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.VisualEditConnection;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.TypeWrapper;
import java.net.URL;
import java.util.Collection;
import java.util.List;
import java.util.ResourceBundle;
import javafx.beans.property.ObjectProperty;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.control.Menu;
import javafx.scene.control.MenuItem;
import javafx.scene.image.Image;
import javafx.scene.input.DragEvent;
import javafx.scene.layout.Background;
import javafx.scene.layout.BackgroundFill;
import javafx.scene.layout.BackgroundImage;
import javafx.scene.layout.BackgroundPosition;
import javafx.scene.layout.BackgroundRepeat;
import javafx.scene.layout.BackgroundSize;
import javafx.scene.paint.Color;
import javafx.scene.text.Font;
import javafx.util.Pair;

/**
 * .
 */
public class CaesarMultiScreenItem
    extends MultiScreenItem<
        CaesarMultiScreenData, CaesarMultiScreenFunc, CaesarMultiScreenControllable> {

  /**
   * Constructor.
   *
   * @param row 行
   * @param column 列
   * @param target 对应rx
   * @param model 数据模型
   * @param func func
   * @param controllable controllable
   */
  public CaesarMultiScreenItem(
      int row,
      int column,
      ObjectProperty<VisualEditTerminal> target,
      VisualEditModel model,
      CaesarMultiScreenFunc func,
      CaesarMultiScreenControllable controllable) {
    super(row, column, target, model, func, controllable);
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    super.initialize(location, resources);
  }

  /**
   * 更新Multiview模式.
   */
  public void updateMultiviewMode() {
    if (target.get() instanceof CaesarConTerminal) {
      CaesarConTerminal conTerminal = (CaesarConTerminal) target.get();
      ConsoleData conData = conTerminal.getConsoleData();
      MultiviewData multiviewData = conData.getMultiviewData();
      if (multiviewData != null) {
        drawDividingLine(multiviewData);
      }
    }
  }

  private void drawDividingLine(MultiviewData multiviewData) {
    GraphicsContext gc = canvas.getGraphicsContext2D();
    double width = canvas.getWidth();
    double halfWidth = width / 2.0;
    double height = canvas.getHeight();
    double halfHeight = height / 2.0;
    gc.clearRect(0, 0, width, height);
    gc.setFill(Color.CADETBLUE);
    gc.setStroke(Color.LIGHTGRAY);
    gc.strokeRect(0, 0, width, height);
    gc.setFont(new Font(10));
    gc.beginPath();
    if (multiviewData != null) {
      switch (multiviewData.getLayoutType()) {

        case FULL_SCREEN:
          if (multiviewData.getSourceCount() > 0) {
            MultiviewSource source = multiviewData.getSource(0);
            if (source != null) {
              CpuData cpuData = source.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 5, 10);
              }
            }
          }
          break;
        case TWO_GRID:
          gc.strokeRect(0, 0, halfWidth, height);
          gc.strokeRect(halfWidth, 0, width, height);
          if (multiviewData.getSourceCount() > 1) {
            MultiviewSource source1 = multiviewData.getSource(0);
            if (source1 != null) {
              CpuData cpuData = source1.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 5, 10);
              }
            }
            MultiviewSource source2 = multiviewData.getSource(1);
            if (source2 != null) {
              CpuData cpuData = source2.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 75, 10);
              }
            }
          }
          break;
        case FOUR_GRID:
          gc.strokeRect(0, 0, halfWidth, halfHeight);
          gc.strokeRect(halfWidth, 0, width, halfHeight);
          gc.strokeRect(0, halfHeight, halfWidth, height);
          gc.strokeRect(halfWidth, halfHeight, width, height);
          if (multiviewData.getSourceCount() > 3) {
            MultiviewSource source1 = multiviewData.getSource(0);
            if (source1 != null) {
              CpuData cpuData = source1.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 5, 10);
              }
            }
            MultiviewSource source2 = multiviewData.getSource(1);
            if (source2 != null) {
              CpuData cpuData = source2.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 75, 10);
              }
            }
            MultiviewSource source3 = multiviewData.getSource(2);
            if (source3 != null) {
              CpuData cpuData = source3.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 5, 48);
              }
            }
            MultiviewSource source4 = multiviewData.getSource(3);
            if (source4 != null) {
              CpuData cpuData = source4.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 75, 48);
              }
            }
          }
          break;
        case SIDE_BAR:
          double twoThirdWidth = width / 3.0 * 2;
          double oneThirdHeight = height / 3.0;
          double twoThirdHeight = height / 3.0 * 2;
          gc.strokeRect(0, 0, twoThirdWidth, height);
          gc.strokeRect(twoThirdWidth, 0, width, oneThirdHeight);
          gc.strokeRect(twoThirdWidth, oneThirdHeight, width, twoThirdHeight);
          gc.strokeRect(twoThirdWidth, twoThirdHeight, width, height);
          if (multiviewData.getSourceCount() > 3) {
            MultiviewSource source1 = multiviewData.getSource(0);
            if (source1 != null) {
              CpuData cpuData = source1.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 5, 10);
              }
            }
            MultiviewSource source2 = multiviewData.getSource(1);
            if (source2 != null) {
              CpuData cpuData = source2.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 98, 10);
              }
            }
            MultiviewSource source3 = multiviewData.getSource(2);
            if (source3 != null) {
              CpuData cpuData = source3.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 98, 35);
              }
            }
            MultiviewSource source4 = multiviewData.getSource(3);
            if (source4 != null) {
              CpuData cpuData = source4.getCpuData();
              if (cpuData != null && cpuData.isOnline()) {
                gc.fillText(cpuData.getName(), 98, 60);
              }
            }
          }
          break;
        default:
          break;
      }
    }
    gc.stroke();
    gc.closePath();
  }

  @Override
  protected void onDragDrop(DragEvent event) {
    VisualEditTerminal terminal = getTerminal(event);
    if (terminal != null && terminal.isRx() && func.isScreenMovable()) {
      Pair<Integer, Integer> pos = func.posOfScreen(terminal);
      func.moveScreen(pos.getKey(), pos.getValue(), getRow(), getColumn());
    } else if (terminal != null && target.get() != null && terminal.isTx()
        && controllable.isConnetable()) {
      menu.getItems().clear();
      Collection<TypeWrapper> channels = controllable.getMultiviewChannel(target.get());
      if (!channels.isEmpty()) {
        channels.forEach(channel -> {
          Menu channelMenu = new Menu(channel.getName());
          MenuItem fullItem =
              new MenuItem(CaesarI18nCommonResource.getString("multiscreen.connection.full"));
          fullItem.setOnAction(evt -> controllable.connectMultiview(terminal, target.get(), 0,
              Integer.parseInt(channel.getType())));
          MenuItem videoItem =
              new MenuItem(CaesarI18nCommonResource.getString("multiscreen.connection.video"));
          videoItem.setOnAction(evt -> controllable.connectMultiview(terminal, target.get(), 1,
              Integer.parseInt(channel.getType())));
          channelMenu.getItems().addAll(fullItem, videoItem);
          menu.getItems().add(channelMenu);
        });
      } else {
        List<TypeWrapper> connectableType = TerminalUtility.getConnectableType(
            (CaesarDeviceController) controllable.getDeviceController(), target.get(), terminal);
        connectableType.forEach(type -> {
          MenuItem item = new MenuItem(type.getName());
          item.setOnAction(evt -> controllable.connectScreen(terminal, target.get(), type.getType()));
          menu.getItems().add(item);
        });
      }
      menu.show(this.screenImage, event.getScreenX(), event.getScreenY());
    } else if (terminal == null
        && event.getDragboard().getString().equals(SystemEditDefinition.EMPTY_TERMINAL_GUID)
        && controllable.isConnetable()) {
      Collection<TypeWrapper> channels = controllable.getMultiviewChannel(target.get());
      if (!channels.isEmpty()) {
        menu.getItems().clear();
        channels.forEach(channel -> {
          Menu channelMenu = new Menu(channel.getName());
          MenuItem disconnectItem =
              new MenuItem(CaesarI18nCommonResource.getString("multiscreen.connection.disconnect"));
          disconnectItem.setOnAction(evt -> controllable.connectMultiview(null, target.get(), 0,
              Integer.parseInt(channel.getType())));
          channelMenu.getItems().addAll(disconnectItem);
          menu.getItems().add(channelMenu);
        });
        menu.show(this.screenImage, event.getScreenX(), event.getScreenY());
      } else {
        controllable.connectScreen(null, target.get(), SwitchType.NOT_SWITCHED.toString());
      }
    }
    event.setDropCompleted(true);
    event.consume();
  }

  @Override
  protected void updateImage() {

    String noConnect = "com/mc/tool/framework/operation/crossscreen/screen.png";
    String videoConnect = "com/mc/tool/framework/operation/crossscreen/screen_video.png";
    String fullConnect = "com/mc/tool/framework/operation/crossscreen/screen_full_control.png";

    String image = noConnect;
    if (getConnectedTerminal().get() != null && getConnectedTerminal().get().getKey() != null) {
      SwitchType switchType = SwitchType.valueOf(getConnectedTerminal().get().getValue());
      switch (switchType) {
        case FULL:
        case FULL_SHARED:
        case PRIVATE:
          image = fullConnect;
          break;
        case VIDEO:
        case SHARED:
          image = videoConnect;
          break;
        default:
          break;
      }
    }

    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    BackgroundFill backgroundFill = new BackgroundFill(Color.web("#f8f9fb"), null, null);
    BackgroundImage screenImageBg;
    Image screenImageInstance = new Image(classLoader.getResourceAsStream(image));
    screenImageBg =
        new BackgroundImage(
            screenImageInstance,
            BackgroundRepeat.NO_REPEAT,
            BackgroundRepeat.NO_REPEAT,
            BackgroundPosition.CENTER,
            new BackgroundSize(-1, -1, false, false, false, false));
    Background background =
        new Background(
            new BackgroundFill[] {backgroundFill}, new BackgroundImage[] {screenImageBg});
    screenImage.setBackground(background);
  }

  @Override
  protected void updateConnectedTerminal() {
    if (target.get() == null) {
      getConnectedTerminal().set(null);
    } else {
      Collection<VisualEditConnection> conns = model.getTerminalConnections(target.get());
      if (conns.isEmpty()) {
        getConnectedTerminal().set(null);
        controllable.updateConnection(target.get(), null);
      } else {
        VisualEditConnection conn = conns.iterator().next();
        getConnectedTerminal().set(new Pair<>(conn.getTxTerminal(), conn.getMode()));
        controllable.updateConnection(target.get(), conn);
      }
    }
  }
}
