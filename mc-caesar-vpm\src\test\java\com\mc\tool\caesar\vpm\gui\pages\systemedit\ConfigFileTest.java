package com.mc.tool.caesar.vpm.gui.pages.systemedit;

import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import java.io.File;
import java.io.IOException;
import javafx.scene.control.TextField;
import org.junit.Assert;
import org.junit.Test;

/**
 * .
 */
public class ConfigFileTest extends AppGuiTestBase {

  @Override
  public void beforeAll() {}

  @Test
  public void testLoadMatrix() {
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      clickOn(findMatrixNode());
      TextField field = lookup(GuiTestConstants.RXNUM_PROP).query();
      Assert.assertTrue(Integer.valueOf(field.getText()) > 0);
      field = lookup(GuiTestConstants.TXNUM_PROP).query();
      Assert.assertTrue(Integer.valueOf(field.getText()) > 0);

      clickOn(GuiTestConstants.SWITCH_GRAPH);
      clickOn(lookupGraphAnotherPageMenu());

      clickOn(GuiTestConstants.RESTORE_GRAPH);
      clickOn(findMatrixNode());
      field = lookup(GuiTestConstants.RXNUM_PROP).query();
      Assert.assertTrue(Integer.valueOf(field.getText()) > 0);
      field = lookup(GuiTestConstants.TXNUM_PROP).query();
      Assert.assertTrue(Integer.valueOf(field.getText()) > 0);

      File tempFile = loadResourceCfgx("com/mc/tool/caesar/vpm/36_hw1.2_grid_full.status.cfgx");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testLoadSingle() {
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      clickOn(findMatrixNode());
      TextField field = lookup(GuiTestConstants.RXNUM_PROP).query();
      Assert.assertTrue(Integer.valueOf(field.getText()) > 0);
      field = lookup(GuiTestConstants.TXNUM_PROP).query();
      Assert.assertTrue(Integer.valueOf(field.getText()) > 0);
      File tempFile = loadResourceCfgx("com/mc/tool/caesar/vpm/single_36_hw1.2_sample.status.cfgx");
      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }
}
