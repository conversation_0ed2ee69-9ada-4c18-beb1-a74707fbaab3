package com.mc.tool.caesar.vpm.util;

import com.google.common.base.Charsets;
import com.mc.common.util.ZipUtility;
import com.mc.tool.caesar.api.CaesarConfigDataModel;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarControllerConstants.SystemRequest;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.Version;
import com.mc.tool.caesar.api.datamodel.ConfigData.IoMode;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.vpm.pages.activateconfig.ActivateConfigMetaCdm;
import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class ConfigListUtility {

  /**
   * 读取文件.
   *
   * @param model model
   * @param fileType 文件类型
   * @param bufferLen buffer长度
   * @return 文件数据
   * @throws DeviceConnectionException 设备连接异常
   * @throws ConfigException 配置内容异常
   * @throws BusyException 设备忙异常
   * @throws IOException io异常
   */
  public static byte[] readFile(CaesarSwitchDataModel model, int fileType, int bufferLen)
      throws DeviceConnectionException, ConfigException, BusyException, IOException {
    ByteArrayOutputStream bos = new ByteArrayOutputStream();
    model.getController().setSystemFileOpen(fileType);
    byte[] data;
    do {
      data = model.getController().readSystemFile(fileType, bufferLen);
      if (data != null) {
        bos.write(data);
      }
    } while (data != null && data.length == bufferLen);
    model.getController().setSystemFileClose(fileType);
    return bos.toByteArray();
  }

  private static int getFileType(String configname) {
    int index = 1;
    for (String config : CaesarConstants.CONFIG_NAMES) {
      if (config.equals(configname)) {
        return index;
      }
      index++;
    }
    return 0;
  }

  private static String dhcpRow(Boolean isDhcp, byte[] address) {
    if (isDhcp) {
      return "";
    }
    return IpUtil.getAddressString(address);
  }

  /**
   * 读取配置列表.
   *
   * @param model model
   * @return 配置列表
   */
  public static Collection<ActivateConfigMetaCdm> readConfigList(CaesarSwitchDataModel model) {
    int type;
    InputStream is;
    List<ActivateConfigMetaCdm> activateConfigList = new ArrayList<>();
    for (String configName : CaesarConstants.CONFIG_NAMES) {
      // Fix bug #1374.
      CaesarConfigDataModel activeConfigModel = new CaesarConfigDataModel();
      try {
        type = getFileType(configName);
        byte[] bytes = readFile(model, type, 1024);
        is = new ByteArrayInputStream(bytes);

        boolean error;
        int tryCounter = 0;
        do {
          try {
            tryCounter++;
            error = false;
            activeConfigModel.initDefaults();
            activeConfigModel.readData(is, IoMode.Available);
          } catch (ConfigException ex) {
            log.warn("Failed to read activeConfig data!");
            error = true;
          }
        } while (error && tryCounter < 5);
        if (error) {
          activateConfigList.add(
              new ActivateConfigMetaCdm("无效配置", "无效配置", "s0.0.0.0".substring(1), "", "", 0));
        } else {
          // 保存配置的基本信息
          String filename = configName + CaesarConstants.CFG_FILE_EXTENSION;
          String name =
              activeConfigModel.getConfigData().getSystemConfigData().getSystemData().getName();
          String info =
              activeConfigModel.getConfigData().getSystemConfigData().getSystemData().getInfo();
          byte[] address =
              activeConfigModel
                  .getConfigData()
                  .getSystemConfigData()
                  .getNetworkDataPreset1()
                  .getAddress();
          Boolean isdhcp =
              activeConfigModel
                  .getConfigData()
                  .getSystemConfigData()
                  .getNetworkDataPreset1()
                  .isDhcp();
          int version = activeConfigModel.getConfigData().getConfigMetaData().getVersion();
          activateConfigList.add(
              new ActivateConfigMetaCdm(
                  filename,
                  name,
                  info,
                  dhcpRow(isdhcp, address),
                  Version.getVersion(version).getName(),
                  type));
        }

      } catch (ConfigException | BusyException | IOException ex) {
        log.warn("Failed to initialize activeConfig list!");
      }
    }
    return activateConfigList;
  }

  /**
   * 保存cfgx文件.
   *
   * @param model 数据
   * @param filePath 保存路径.
   * @return 如果保存成功，返回true
   */
  public static boolean saveCfgx(CaesarSwitchDataModel model, String filePath) {
    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    try {
      model.writeData(baos, IoMode.Available);
    } catch (ConfigException exception) {
      log.warn("Fail to write model data!", exception);
      return false;
    }
    String extraConfFile = System.getProperty("java.io.tmpdir") + "temp.zip";
    ByteArrayOutputStream extraConfBaos = new ByteArrayOutputStream();
    if (saveExtraConfig(model, extraConfFile)) {
      try (FileInputStream fis = new FileInputStream(extraConfFile)) {
        byte[] data = new byte[1024];
        int readed;
        do {
          readed = fis.read(data);
          if (readed > 0) {
            extraConfBaos.write(data, 0, readed);
          }
        } while (readed > 0);
      } catch (IOException exception) {
        log.warn("Fail to write extra config data!", exception);
      }
    }

    return saveConfigFile(
        filePath, baos.toByteArray(), extraConfBaos.size() > 0 ? extraConfBaos.toByteArray() : null);
  }

  /**
   * 保存所有配置到文件.
   *
   * @param filePath 文件路径.
   * @param cfgData 配置数据
   * @param extraConfData 额外的配置数据
   * @return 如果保存成功，返回true
   */
  public static boolean saveConfigFile(String filePath, byte[] cfgData, byte[] extraConfData) {
    String tempPath = System.getProperty("java.io.tmpdir");
    File cfgFile = new File(tempPath + CaesarConstants.CFGX_CONFIG_NAME);
    try (FileOutputStream fos = new FileOutputStream(cfgFile)) {
      fos.write(cfgData);
    } catch (IOException exception) {
      log.warn("Fail to write cfg file!", exception);
      return false;
    }

    File extraConfFile = null;
    // 兼容没有额外配置文件的情况
    if (extraConfData != null) {
      extraConfFile = new File(tempPath + CaesarConstants.CFGX_EXTRA_CONF_NAME);
      try (FileOutputStream fos = new FileOutputStream(extraConfFile)) {
        fos.write(extraConfData);
      } catch (IOException exception) {
        log.warn("Fail to write extra config file!", exception);
        extraConfFile = null;
      }
    }

    try (ZipOutputStream zos =
        new ZipOutputStream(new BufferedOutputStream(new FileOutputStream(filePath)))) {
      ZipUtility.zipFile(zos, cfgFile);
      if (extraConfFile != null) {
        ZipUtility.zipFile(zos, extraConfFile);
      }
    } catch (IOException exception) {
      log.warn("Fail to write cfgx file!", exception);
      return false;
    }
    return true;
  }

  /**
   * 保存额外的配置数据到zip文件.
   *
   * @param source 数据模型
   * @param zipFilePath zip文件路径.
   * @return 是否保存成功
   */
  public static boolean saveExtraConfig(CaesarSwitchDataModel source, String zipFilePath) {
    try (ZipOutputStream zos =
             new ZipOutputStream(new FileOutputStream(zipFilePath))) {
      source.getDbDataModel().reloadAll();
      if (!saveVpConfig(source, zos)) {
        return false;
      }
      // 为了兼容，不保存dbmodel的数据也是成功的
      saveDbData(source, zos);
    } catch (RuntimeException | IOException exception) {
      return false;
    }
    return true;
  }

  private static boolean saveDbData(CaesarSwitchDataModel source, ZipOutputStream zos) {
    String path = System.getProperty("java.io.tmpdir");
    String backupFile = path + CaesarConstants.CFGX_DB_BK;
    if (source.getDbDataModel().getDataBackup().isEmpty()) {
      return true;
    }
    try (Writer writer = new OutputStreamWriter(new FileOutputStream(backupFile), Charsets.UTF_8)) {
      writer.write(source.getDbDataModel().getDataBackup());
    } catch (IOException exception) {
      log.warn("Fail to open file " + backupFile);
      return false;
    }
    try {
      String directory = "db/";
      zos.putNextEntry(new ZipEntry(directory));
      zos.closeEntry();
      ZipUtility.zipFile(zos, new File(backupFile), directory);
    } catch (IOException exception) {
      log.warn("Fail write db zip file!", exception);
      return false;
    }
    return true;
  }

  /**
   * 保存vp配置到文件.
   *
   * @param source data
   * @param zos 保存位置
   * @return 如果保存成功，返回true
   */
  private static boolean saveVpConfig(CaesarSwitchDataModel source, ZipOutputStream zos) {
    // 导出vp数据
    String path = System.getProperty("java.io.tmpdir");
    String vpBackupFile = path + CaesarConstants.CFGX_VP_BACKUP_NAME;
    String vpScenarioFils = path + CaesarConstants.CFGX_VP_PRE_NAME;
    if (!saveVpBackupData(source, vpBackupFile)) {
      return false;
    }

    if (!saveVpScenarios(source, vpScenarioFils)) {
      return false;
    }

    try {
      String directory = "vp/";
      zos.putNextEntry(new ZipEntry(directory));
      zos.closeEntry();
      ZipUtility.zipFile(zos, new File(vpBackupFile), directory);
      ZipUtility.zipFile(zos, new File(vpScenarioFils), directory);
    } catch (IOException exception) {
      log.warn("Fail write vp zip file!", exception);
      return false;
    }
    return true;
  }

  private static boolean saveVpScenarios(CaesarSwitchDataModel source, String vpScenarioFils) {
    try (FileOutputStream fos = new FileOutputStream(vpScenarioFils)) {
      for (int i = 0; i < CaesarConstants.SCENARIO_SIZE; i++) {
        ByteArrayOutputStream scenarioBaos = new ByteArrayOutputStream();
        if (source.getVpDataModel().getScenarioData(i) != null) {

          ByteArrayOutputStream realData = new ByteArrayOutputStream();
          CfgWriter writer = new CfgWriter(realData);
          source.getVpDataModel().getScenarioData(i).writeData(writer);
          scenarioBaos.write(new byte[] {1}); // enable
          scenarioBaos.write(
              new byte[] {
                0x1b,
                (byte) SystemRequest.SET_SCENARIO_DATA.getServerRequestByte(),
                (byte) SystemRequest.SET_SCENARIO_DATA.getByteValue()
              }); // esc + sevType + reqType
          // 写入长度
          int size = realData.size();
          size += 8;
          scenarioBaos.write(size & 0xff);
          scenarioBaos.write((size & 0xff00) >> 8);
          scenarioBaos.write((size & 0xff0000) >> 16);
          scenarioBaos.write((size & 0xff0000) >> 24);
          // index
          scenarioBaos.write(new byte[] {(byte) i});
          scenarioBaos.write(realData.toByteArray());
          fos.write(scenarioBaos.toByteArray());
        }
        byte[] reservedData = new byte[128];
        int leftDataCount =
            VideoWallGroupData.VIDEOWALL_SCENARIO_RESERVED_DATA_COUNT - scenarioBaos.size();
        while (leftDataCount > 0) {
          int count = Math.min(128, leftDataCount);
          fos.write(reservedData, 0, count);
          leftDataCount -= count;
        }
      }
    } catch (IOException | ConfigException exception) {
      log.warn("Fail to write vp scenario file!", exception);
      return false;
    }
    return true;
  }

  private static boolean saveVpBackupData(CaesarSwitchDataModel source, String vpBackupFile) {
    try (FileOutputStream fos = new FileOutputStream(vpBackupFile)) {
      source.getVpDataModel().getVideoWallGroupData().write(fos);
    } catch (IOException exception) {
      log.warn("Fail to write vp back file!", exception);
      return false;
    }
    return true;
  }

}
