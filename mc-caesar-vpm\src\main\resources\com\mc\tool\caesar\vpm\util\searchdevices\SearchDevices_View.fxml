<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.VBox?>
<javafx.scene.layout.VBox id="main-container" maxHeight="-Infinity" maxWidth="-Infinity"
  minHeight="-Infinity" minWidth="-Infinity"
  stylesheets="@SearchDevices_View.css" xmlns="http://javafx.com/javafx/8"
  xmlns:fx="http://javafx.com/fxml/1">
  <TableView fx:id="tableView" focusTraversable="false" prefHeight="270.0" prefWidth="830.0">
    <columns>
      <TableColumn fx:id="indexCol" prefWidth="30.0" resizable="false" text="C1"/>
      <TableColumn fx:id="deviceCol" prefWidth="99.0" text="C2"/>
      <TableColumn fx:id="versionCol" prefWidth="50.0" text="C9"/>
      <TableColumn fx:id="statusCol" prefWidth="70.0" text="C3"/>
      <TableColumn fx:id="asCol" prefWidth="50.0" text="C4"/>
      <TableColumn fx:id="nameCol" prefWidth="111.0" text="C5"/>
      <TableColumn fx:id="addressCol" prefWidth="134.0" text="C6"/>
      <TableColumn fx:id="macCol" prefWidth="142.0" text="C7"/>
      <TableColumn fx:id="connectCol" prefWidth="76.0" text="C8"/>
    </columns>
    <VBox.margin>
      <Insets/>
    </VBox.margin>
  </TableView>
</javafx.scene.layout.VBox>
