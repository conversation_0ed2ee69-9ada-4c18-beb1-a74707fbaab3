package com.mc.tool.caesar.vpm.pages.hotkeyconfiguration;

import impl.org.controlsfx.skin.ListSelectionViewSkinEx;
import java.util.ArrayList;
import java.util.List;
import javafx.beans.binding.Bindings;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.SimpleIntegerProperty;
import org.controlsfx.control.ListSelectionViewEx;

/**
 * .
 */
public class CopyListSelectionViewSkin<T> extends ListSelectionViewSkinEx<T> {

  private IntegerProperty targetMaxSize = new SimpleIntegerProperty(Integer.MAX_VALUE);

  /** Construct. */
  public CopyListSelectionViewSkin(ListSelectionViewEx<T> view, int maxSize) {
    super(view);
    this.moveToTarget
        .disableProperty()
        .bind(
            Bindings.size(getTargetListView().getItems())
                .greaterThanOrEqualTo(targetMaxSize)
                .or(Bindings.isEmpty(getSourceListView().getSelectionModel().getSelectedItems())));
    this.moveToTargetAll
        .disableProperty()
        .bind(
            Bindings.size(getTargetListView().getItems())
                .greaterThanOrEqualTo(targetMaxSize)
                .or(Bindings.isEmpty(getSourceListView().getItems())));
    this.targetMaxSize.set(maxSize);

    this.gridPane.setStyle("-fx-vgap: 0px;");
    this.sourceListView.setStyle("-fx-border-width:1px;-fx-border-color:#e5e5e5;");
    this.targetListView.setStyle("-fx-border-width:1px;-fx-border-color:#e5e5e5;");
  }

  @Override
  protected void moveToTarget() {
    List<T> selectedItems =
        new ArrayList<>(getSourceListView().getSelectionModel().getSelectedItems());
    if (getTargetListView().getItems().size() + selectedItems.size() <= getTargetMaxSize()) {
      move(getSourceListView(), getTargetListView(), selectedItems);
    } else if (getTargetListView().getItems().size() < getTargetMaxSize()) {
      List<T> items =
          selectedItems.subList(0, getTargetMaxSize() - getTargetListView().getItems().size());
      move(getSourceListView(), getTargetListView(), items);
    }
    getSourceListView().getSelectionModel().clearSelection();
  }

  @Override
  protected void moveToTargetAll() {
    List<T> allItems = new ArrayList<>(getSourceListView().getItems());
    if (getTargetListView().getItems().size() + allItems.size() <= getTargetMaxSize()) {
      move(getSourceListView(), getTargetListView(), allItems);
    } else if (getTargetListView().getItems().size() < getTargetMaxSize()) {
      List<T> items =
          allItems.subList(0, getTargetMaxSize() - getTargetListView().getItems().size());
      move(getSourceListView(), getTargetListView(), items);
    }
    getSourceListView().getSelectionModel().clearSelection();
  }

  @Override
  protected void moveToSource() {
    move(getTargetListView(), getSourceListView());
    getTargetListView().getSelectionModel().clearSelection();
  }

  @Override
  protected void moveToSourceAll() {
    move(getTargetListView(), getSourceListView(), new ArrayList<>(getTargetListView().getItems()));
    getTargetListView().getSelectionModel().clearSelection();
  }

  public void setTargetMaxSize(int size) {
    targetMaxSize.set(size);
  }

  public int getTargetMaxSize() {
    return targetMaxSize.get();
  }
}
