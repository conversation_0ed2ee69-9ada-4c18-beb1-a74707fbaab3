package com.mc.tool.caesar.api.datamodel;

import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData.VideoWallData;
import com.mc.tool.caesar.api.datamodel.vp.Vp6OutputData;
import com.mc.tool.caesar.api.datamodel.vp.VpConConfigData;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javafx.util.Pair;


/**
 * .
 */
public final class ScenarioData {

  public static final String PROPERTY_DATA_CHANGED = "ScenarioData.dataChanged";
  public static final int SCENARIO_NAME_LENGTH = 32;
  /**
   * 预案索引.
   */
  private int index = -1;
  /**
   * 预案的逻辑配置.
   */
  private VideoWallData videoWallData;
  /**
   * 预案中所有的连接关系.
   */
  private Map<ConsoleData, CpuData> connections;
  /**
   * 预案中所有的配置信息.
   */
  private Map<ConsoleData, Pair<VpConConfigData, Vp6OutputData>> configs;

  private String fileName = ""; // 加载或保存的文件路径
  private String deviceName = ""; // 设备名
  private String configName = ""; // 当前配置的名字
  private long lastModified; // 文件的最后的一次修改的信息

  public int getIndex() {
    return index;
  }

  public void setIndex(int index) {
    this.index = index;
  }

  public Map<ConsoleData, CpuData> getConnections() {
    return connections;
  }

  public void setConnections(Map<ConsoleData, CpuData> connections) {
    this.connections = connections;
  }

  public Map<ConsoleData, Pair<VpConConfigData, Vp6OutputData>> getConfigs() {
    return configs;
  }

  public void setConfigs(Map<ConsoleData, Pair<VpConConfigData, Vp6OutputData>> configs) {
    this.configs = configs;
  }

  public VideoWallData getVideoWallData() {
    return videoWallData;
  }

  public void setVideoWallData(VideoWallData videoWallData) {
    this.videoWallData = videoWallData;
  }

  /**
   * .
   */
  public void readData(CfgReader reader, CaesarSwitchDataModel model)
      throws ConfigException, IOException {
    configName = reader.readString(SCENARIO_NAME_LENGTH, 0);
    // horz count
    reader.read2ByteValue();
    // vert count
    reader.read2ByteValue();
    final int connectCount = reader.read2ByteValue();
    final int configCount = reader.read2ByteValue();

    final int backupSegCount = reader.read2ByteValue();

    // 读取连接信息
    connections = new HashMap<>();
    for (int i = 0; i < connectCount; i++) {
      int conId = reader.read2ByteValue();
      int cpuId = reader.read2ByteValue();
      ConsoleData con = model.getConfigDataManager().getConsoleData4Id(conId);
      CpuData cpu = model.getConfigDataManager().getCpuData4Id(cpuId);
      connections.put(con, cpu);
    }
    // 读取配置信息
    configs = new HashMap<>();
    for (int i = 0; i < configCount; i++) {
      int conId = reader.read2ByteValue();
      int configSize = reader.read2ByteValue();
      ConsoleData con = model.getConfigDataManager().getConsoleData4Id(conId);
      if (con == null || con.getExtenderData(0) == null
          || con.getExtenderData(0).getVpType() == null) {
        continue;
      }
      VpConConfigData configData = con.getExtenderData(0).getVpType().createConfigData();
      byte[] datas = reader.readByteArray(configSize);
      configData.read(new ByteArrayInputStream(datas));

      int outputSize = reader.read2ByteValue();
      Vp6OutputData outputData = new Vp6OutputData();
      datas = reader.readByteArray(outputSize);
      outputData.read(new ByteArrayInputStream(datas));
      configs.put(con, new Pair<VpConConfigData, Vp6OutputData>(configData, outputData));
    }
    // 读取备份数据
    videoWallData = new VideoWallData();
    byte[] videoDataBytes = new byte[videoWallData.size()];
    for (int i = 0; i < backupSegCount; i++) {
      int offset = reader.readInteger();
      int size = reader.read2ByteValue();
      byte[] temp = reader.readByteArray(size);
      System.arraycopy(temp, 0, videoDataBytes, offset, size);
    }
    videoWallData.read(new ByteArrayInputStream(videoDataBytes));
  }

  /**
   * .
   */
  public void writeData(CfgWriter writer) throws ConfigException, IOException {
    if (videoWallData != null && connections != null && configs != null) {
      // 备份数据整理
      int[] baseRanges = videoWallData.getBaseDataRange();
      int[] ranges = videoWallData.getValidDataRange();

      List<Integer> rangeList = new ArrayList<>();
      for (int i : baseRanges) {
        rangeList.add(i);
      }
      for (int i : ranges) {
        rangeList.add(i);
      }
      //
      writer.writeString(configName, SCENARIO_NAME_LENGTH, 0);
      writer.write2ByteSmallEndian(videoWallData.getColumns());
      writer.write2ByteSmallEndian(videoWallData.getRows());
      writer.write2ByteSmallEndian(connections.size());
      writer.write2ByteSmallEndian(configs.size());

      writer.write2ByteSmallEndian(rangeList.size() / 2);
      // 写入连接信息
      for (Map.Entry<ConsoleData, CpuData> entry : connections.entrySet()) {
        writer.write2ByteSmallEndian(entry.getKey().getId());
        if (entry.getValue() == null) {
          writer.write2ByteSmallEndian(0);
        } else {
          writer.write2ByteSmallEndian(entry.getValue().getId());
        }
      }
      for (Map.Entry<ConsoleData, Pair<VpConConfigData, Vp6OutputData>> entry : configs.entrySet()) {
        writer.write2ByteSmallEndian(entry.getKey().getId());
          // config
          {
          writer.write2ByteSmallEndian(entry.getValue().getKey().size() + 2); // 带checksum
          ByteArrayOutputStream output = new ByteArrayOutputStream();
          entry.getValue().getKey().write(output);
          byte[] datas = output.toByteArray();
          int checksum = getCheckSum(datas);
          writer.writeByteArray(datas);
          writer.writeByte((byte) (checksum & 0xff));
          writer.writeByte((byte) ((checksum >> 8) & 0xff));
          }
          // output
          {
          writer.write2ByteSmallEndian(entry.getValue().getValue().size() + 2); // 带checksum
          ByteArrayOutputStream output = new ByteArrayOutputStream();
          entry.getValue().getValue().write(output);
          byte[] datas = output.toByteArray();
          int checksum = getCheckSum(datas);
          writer.writeByteArray(datas);
          writer.writeByte((byte) (checksum & 0xff));
          writer.writeByte((byte) ((checksum >> 8) & 0xff));
          }

      }

      // 写入备份信息

      ByteArrayOutputStream videoOutput = new ByteArrayOutputStream();
      videoWallData.write(videoOutput);
      byte[] bytes = videoOutput.toByteArray();
      Integer[] finalRanges = rangeList.toArray(new Integer[0]);
      for (int i = 0; i < finalRanges.length; i += 2) {
        writer.writeInteger(finalRanges[i]);
        writer.write2ByteSmallEndian(finalRanges[i + 1]);
        byte[] tempBytes = new byte[finalRanges[i + 1]];
        System.arraycopy(bytes, finalRanges[i], tempBytes, 0, finalRanges[i + 1]);
        writer.writeByteArray(tempBytes);
      }
    }

  }

  protected int getCheckSum(byte[] datas) {
    int checkSum = 0;
    for (byte data : datas) {
      checkSum += 0xff & data;
    }
    return checkSum;
  }

  public String getFileName() {
    return fileName;
  }

  public void setFileName(String fileName) {
    this.fileName = fileName;
  }

  public String getDeviceName() {
    return deviceName;
  }

  public void setDeviceName(String deviceName) {
    this.deviceName = deviceName;
  }

  public String getConfigName() {
    return configName;
  }

  public void setConfigName(String configName) {
    this.configName = configName;
  }

  public long getLastModified() {
    return lastModified;
  }

  public void setLastModified(long lastModified) {
    this.lastModified = lastModified;
  }

  /**
   * .
   */
  public boolean load() {
    File file = new File(getFileName());
    if (!file.exists()) {
      return false;
    }
    try {
      FileInputStream fileInputStream = new FileInputStream(file);
      CfgReader reader = new CfgReader(fileInputStream);
      setIndex(reader.read2ByteValue());
      setConfigName(reader.readString(16));
      setDeviceName(reader.readString(16));
      byte[] bytes = reader.readByteArray();
      ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
      VideoWallData videoWallData = new VideoWallData();
      videoWallData.read(bais);

      setVideoWallData(videoWallData);
      setLastModified(file.lastModified());

    } catch (ConfigException | IOException ex) {
      return false;
    }
    return true;
  }


}
