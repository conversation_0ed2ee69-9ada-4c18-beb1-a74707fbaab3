package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.api.utils.BitFieldEntry;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;
import java.nio.ByteBuffer;
import java.nio.charset.CharacterCodingException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.StringTokenizer;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.xml.bind.DatatypeConverter;

/**
 * 设备模块数据，模块包括cpu板与io板等，oid为0的是cpu板.
 */
public class ModuleData extends AbstractData
    implements DataObject, CaesarCommunicatable {

  private static final Logger LOG = Logger.getLogger(ModuleData.class.getName());
  public static final String PROPERTY_BASE = "SwitchDataModel.ModuleData.";
  public static final String FIELD_PORTS = "Ports";
  public static final String PROPERTY_PORTS = "SwitchDataModel.ModuleData.Ports";
  public static final String FIELD_STATUS = "Status";
  public static final String PROPERTY_STATUS = "SwitchDataModel.ModuleData.Status";
  public static final String PROPERTY_STATUS_AVAILABLE =
      "SwitchDataModel.ModuleData.Status.Available";
  public static final String PROPERTY_STATUS_ONLINE = "SwitchDataModel.ModuleData.Status.Online";
  public static final String PROPERTY_STATUS_CFG_DATA =
      "SwitchDataModel.ModuleData.Status.ConfigData";
  public static final String PROPERTY_STATUS_ACTIVE = "SwitchDataModel.ModuleData.Status.Active";
  public static final String PROPERTY_STATUS_READY = "SwitchDataModel.ModuleData.Status.Ready";
  public static final String FIELD_TYPE = "Type";
  public static final String PROPERTY_TYPE = "SwitchDataModel.ModuleData.Type";
  public static final String FIELD_VERSION = "Version";
  public static final String PROPERTY_VERSION = "SwitchDataModel.ModuleData.Version";
  public static final String PROPERTY_SERIAL = "Serial";

  public static final int RESERVED_COUNT = 20;

  public static final Map<String, Collection<BitFieldEntry>> BIT_FIELD_MAP;
  @Expose
  private int type;
  @Expose
  private int ports;
  @Expose
  private int status;
  @Expose
  transient VersionSet version = new VersionSet();
  @Expose
  private String serial;

  static {
    Collection<BitFieldEntry> bitMapStatus = new HashSet<BitFieldEntry>();

    bitMapStatus.add(new BitFieldEntry(ModuleData.PROPERTY_STATUS_AVAILABLE,
        new int[]{CaesarConstants.Module.Status.AVAILABLE}));
    bitMapStatus.add(new BitFieldEntry(ModuleData.PROPERTY_STATUS_ONLINE,
        new int[]{CaesarConstants.Module.Status.ONLINE}));
    bitMapStatus.add(new BitFieldEntry(ModuleData.PROPERTY_STATUS_CFG_DATA,
        new int[]{CaesarConstants.Module.Status.CFGDATA}));
    bitMapStatus.add(new BitFieldEntry(ModuleData.PROPERTY_STATUS_ACTIVE,
        new int[]{CaesarConstants.Module.Status.ACTIVE}));
    bitMapStatus.add(new BitFieldEntry(ModuleData.PROPERTY_STATUS_READY,
        new int[]{CaesarConstants.Module.Status.READY}));

    Map<String, Collection<BitFieldEntry>> bitMaps =
        new HashMap<String, Collection<BitFieldEntry>>();

    bitMaps.put(ModuleData.PROPERTY_STATUS, Collections.unmodifiableCollection(bitMapStatus));

    BIT_FIELD_MAP = Collections.unmodifiableMap(bitMaps);
  }

  public ModuleData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager, int oid) {
    super(pcs, configDataManager, oid, "ModuleData[" + oid + "]");
    initCommitRollback();
  }

  @Override
  public void initDefaults() {
    this.type = 0;
    this.ports = 0;
    this.status = 0;
    this.version = new VersionSet();
    this.serial = "";
  }

  public String getSerial() {
    return this.serial;
  }

  /**
   * .
   */
  public void setSerial(String serial) {
    String oldValue = this.serial;
    try {
      byte[] bytes = DatatypeConverter.parseHexBinary(serial);
      String value =
          Charset.forName("ascii").newDecoder().decode(ByteBuffer.wrap(bytes)).toString();
      this.serial = value;
    } catch (CharacterCodingException exception) {
      this.serial = CaesarConstants.INVALID_SERIAL;
    }
    firePropertyChange(ModuleData.PROPERTY_SERIAL, oldValue, serial, new int[0]);
  }

  public int getPorts() {
    return this.ports;
  }

  /**
   * .
   */
  public void setPorts(int ports) {
    int oldValue = this.ports;
    this.ports = ports;
    firePropertyChange(ModuleData.PROPERTY_PORTS, Integer.valueOf(oldValue),
        Integer.valueOf(this.ports), new int[0]);
  }

  public int getStatus() {
    return this.status;
  }

  /**
   * .
   */
  public void setStatus(int status) {
    int oldValue = this.status;
    this.status = status;
    firePropertyChange(ModuleData.PROPERTY_STATUS, Integer.valueOf(oldValue),
        Integer.valueOf(this.status), new int[0]);
  }

  public boolean isStatusAvailable() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Module.Status.AVAILABLE);
  }

  public boolean isStatusConfigData() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Module.Status.CFGDATA);
  }

  public boolean isStatusActive() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Module.Status.ACTIVE);
  }

  public boolean isStatusOnline() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Module.Status.ONLINE);
  }

  public boolean isStatusReady() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Module.Status.READY);
  }

  public void setStatusAvailable(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled,
        new int[]{CaesarConstants.Module.Status.AVAILABLE}));
  }

  public void setStatusDelete(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, new int[]{CaesarConstants.Module.Status.CFGDATA}));
  }

  public void setStatusActive(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, new int[]{CaesarConstants.Module.Status.ACTIVE}));
  }

  public void setStatusOnline(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, new int[]{CaesarConstants.Module.Status.ONLINE}));
  }

  public void setStatusReady(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, new int[]{CaesarConstants.Module.Status.READY}));
  }

  public boolean isStatusServiceMode() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Module.Status.SERVICEMODE);
  }

  public boolean isStatusInvalid() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Module.Status.INVALID);
  }

  public boolean isStatusOnHold() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Module.Status.ONHOLD);
  }

  public int getType() {
    return this.type;
  }

  /**
   * .
   */
  public void setType(int type) {
    int oldValue = this.type;
    this.type = type;
    firePropertyChange(ModuleData.PROPERTY_TYPE, Integer.valueOf(oldValue),
        Integer.valueOf(this.type), new int[0]);
  }

  public VersionSet getVersion() {
    return this.version;
  }

  /**
   * .
   */
  public void setVersion(String version) {
    // TODO add this later
    // String oldValue = this.version;
    // this.version = version;
    // firePropertyChange(ModuleData.PROPERTY_VERSION, oldValue, this.version, new int[0]);
  }

  /**
   * .
   */
  public void setVersion(VersionSet version) {
    VersionSet oldValue = this.version;
    this.version = version;
    firePropertyChange(ModuleData.PROPERTY_VERSION, oldValue, this.version, new int[0]);
  }

  /**
   * .
   */
  public static String getVersionToken(int idx, String version) {
    if (null == version || version.isEmpty()) {
      return null;
    }
    StringTokenizer st = new StringTokenizer(version, " \r\n\t");
    List<String> versionTokens = new ArrayList<String>();
    while (st.hasMoreTokens()) {
      versionTokens.add(st.nextToken());
    }
    if (idx < versionTokens.size()) {
      return versionTokens.get(idx);
    }
    return null;
  }

  public String getVersionName() {
    return "";
  }

  /**
   * .
   */
  public static String getVersionName(String version) {
    String name = getVersionToken(0, version);
    if (name != null) {
      name = name.toUpperCase(Locale.ENGLISH);
    }
    return name;
  }

  public String getVersionType() {
    return "";
  }

  public static String getVersionType(String version) {
    return getVersionToken(1, version);
  }

  public String getVersionPorts() {
    return "";
  }

  public static String getVersionPorts(String version) {
    return getVersionToken(2, version);
  }

  public String getVersionVersion() {
    return getVersion().toString();
  }

  public static String getVersionVersion(String version) {
    return getVersionToken(3, version);
  }

  public String getVersionDate() {
    return "";
  }


  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (ModuleData.PROPERTY_PORTS.equals(propertyName)) {
      setPorts(Integer.class.cast(value).intValue());
    } else if (ModuleData.PROPERTY_STATUS.equals(propertyName)) {
      setStatus(Integer.class.cast(value).intValue());
    } else if (ModuleData.PROPERTY_TYPE.equals(propertyName)) {
      setType(Integer.class.cast(value).intValue());
    } else if (ModuleData.PROPERTY_VERSION.equals(propertyName)) {
      setVersion(String.class.cast(value));
    }
  }

  @Override
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    Level logLevel = Level.FINER;
    boolean isLoggable = LOG.isLoggable(logLevel);
    if (isLoggable) {
      LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), "Type"});
    }
    cfgWriter.writeInteger(getType());
    if (isLoggable) {
      LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), "Ports"});
    }
    cfgWriter.writeInteger(getPorts());
    if (isLoggable) {
      LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), "Status"});
    }
    cfgWriter.writeInteger(getStatus());
    if (isLoggable) {
      LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), "Version"});
    }
    getVersion().writeData(cfgWriter);
  }

  @Override
  public void readData(CfgReader cfgReader) throws ConfigException {
    Level logLevel = Level.FINER;
    boolean isLoggable = LOG.isLoggable(logLevel);
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), "Type"});
    }
    setType(cfgReader.readInteger());
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), "Ports"});
    }
    setPorts(cfgReader.readInteger());
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), "Status"});
    }
    setStatus(cfgReader.readInteger());
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), "Version"});
    }
    getVersion().readData(cfgReader);
  }

  @Override
  public void delete() {
    throw new UnsupportedOperationException("Not supported yet.");
  }

  @Override
  public void delete(boolean bln) {
    throw new UnsupportedOperationException("Not supported yet.");
  }

  @Override
  public int getId() {
    return getOid();
  }

  @Override
  public String getName() {
    return "";
  }
}

