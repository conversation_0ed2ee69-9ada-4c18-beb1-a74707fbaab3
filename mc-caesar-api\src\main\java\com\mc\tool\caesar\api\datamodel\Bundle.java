package com.mc.tool.caesar.api.datamodel;

import java.util.ResourceBundle;

class Bundle {

  static class NbBundle {

    public static String getMessage(Class<?> clazz, String id) {
      return ResourceBundle.getBundle("Bundle").getString(id);
    }
  }

  public static String configData_checkArrays(String info) {
    return info + " check array false!";
  }

  public static String configData_warning_unsupported_message() {
    return NbBundle.getMessage(Bundle.class, "ConfigData.warning.unsupported.message");
  }

  public static String configData_warning_unsupported_title() {
    return NbBundle.getMessage(Bundle.class, "ConfigData.warning.unsupported.title");
  }

  public static String configData_warning_tooNew_message() {
    return NbBundle.getMessage(Bundle.class, "ConfigData.warning.tooNew.message");
  }

  public static String configData_warning_tooNew_title() {
    return NbBundle.getMessage(Bundle.class, "ConfigData.warning.tooNew.title");
  }

  static String consoleData_Cpu() {
    return NbBundle.getMessage(Bundle.class, ConsoleData.PROPERTY_CPU);
  }

  static String consoleData_Cpu_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ConsoleData.CPU.Tooltip");
  }

  static String consoleData_ConsoleVirtual() {
    return NbBundle.getMessage(Bundle.class, ConsoleData.PROPERTY_CONSOLE_VIRTUAL);
  }

  static String consoleData_ConsoleVirtual_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ConsoleData.ConsoleVirtual.Tooltip");
  }

  static String consoleData_Id() {
    return NbBundle.getMessage(Bundle.class, ConsoleData.PROPERTY_ID);
  }

  static String consoleData_Id_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ConsoleData.ID.Tooltip");
  }

  static String consoleData_Name() {
    return NbBundle.getMessage(Bundle.class, ConsoleData.PROPERTY_NAME);
  }

  static String consoleData_Name_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ConsoleData.Name.Tooltip");
  }

  static String consoleData_RdCpu() {
    return NbBundle.getMessage(Bundle.class, ConsoleData.PROPERTY_RDCPU);
  }

  static String consoleData_RdCpu_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ConsoleData.RDCPU.Tooltip");
  }

  static String consoleData_ScanTime() {
    return NbBundle.getMessage(Bundle.class, ConsoleData.PROPERTY_SCAN_TIME);
  }

  static String consoleData_ScanTime_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ConsoleData.ScanTime.Tooltip");
  }

  static String consoleData_Status_AllowLogin() {
    return NbBundle.getMessage(Bundle.class, ConsoleData.PROPERTY_STATUS_ALLOWLOGIN);
  }

  static String consoleData_Status_AllowLogin_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ConsoleData.Status.AllowLogin.Tooltip");
  }

  static String consoleData_Status_AllowScan() {
    return NbBundle.getMessage(Bundle.class, ConsoleData.PROPERTY_STATUS_ALLOWSCAN);
  }

  static String consoleData_Status_AllowScan_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ConsoleData.Status.AllowScan.Tooltip");
  }

  static String consoleData_Status_ForceLogin() {
    return NbBundle.getMessage(Bundle.class, ConsoleData.PROPERTY_STATUS_FORCELOGIN);
  }

  static String consoleData_Status_ForceLogin_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ConsoleData.Status.ForceLogin.Tooltip");
  }

  static String consoleData_Status_ForceMacro() {
    return NbBundle.getMessage(Bundle.class, ConsoleData.PROPERTY_STATUS_FORCEMACRO);
  }

  static String consoleData_Status_ForceMacro_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ConsoleData.Status.ForceMacro.Tooltip");
  }

  static String consoleData_Status_ForceScan() {
    return NbBundle.getMessage(Bundle.class, ConsoleData.PROPERTY_STATUS_FORCESCAN);
  }

  static String consoleData_Status_ForceScan_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ConsoleData.Status.ForceScan.Tooltip");
  }

  static String consoleData_Status_LosFrame() {
    return NbBundle.getMessage(Bundle.class, ConsoleData.PROPERTY_STATUS_LOSFRAME);
  }

  static String consoleData_Status_LosFrame_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ConsoleData.Status.LOSFrame.Tooltip");
  }

  static String consoleData_Status_OsdDisabled() {
    return NbBundle.getMessage(Bundle.class, ConsoleData.PROPERTY_STATUS_OSDDISABLED);
  }

  static String consoleData_Status_OsdDisabled_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ConsoleData.Status.OsdDisabled.Tooltip");
  }

  static String consoleData_Status_PortMode() {
    return NbBundle.getMessage(Bundle.class, ConsoleData.PROPERTY_STATUS_PORTMODE);
  }

  static String consoleData_Status_PortMode_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ConsoleData.Status.PortMode.Tooltip");
  }

  static String consoleData_Status_Redundant() {
    return NbBundle.getMessage(Bundle.class, ConsoleData.PROPERTY_STATUS_REDUNDANT);
  }

  static String consoleData_Status_Redundant_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ConsoleData.Status.Redundant.Tooltip");
  }

  static String consoleData_Status_Virtual() {
    return NbBundle.getMessage(Bundle.class, ConsoleData.PROPERTY_STATUS_VIRTUAL);
  }

  static String consoleData_Status_Virtual_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ConsoleData.Status.Virtual.Tooltip");
  }

  static String cpuData_Console() {
    return NbBundle.getMessage(Bundle.class, CpuData.PROPERTY_CONSOLE);
  }

  static String cpuData_Console_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "CpuData.Console.Tooltip");
  }

  static String cpuData_Id() {
    return NbBundle.getMessage(Bundle.class, CpuData.PROPERTY_ID);
  }

  static String cpuData_Id_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "CpuData.ID.Tooltip");
  }

  static String cpuData_Name() {
    return NbBundle.getMessage(Bundle.class, CpuData.PROPERTY_NAME);
  }

  static String cpuData_Name_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "CpuData.Name.Tooltip");
  }

  static String cpuData_RealCpu() {
    return NbBundle.getMessage(Bundle.class, CpuData.PROPERTY_REAL_CPU);
  }

  static String cpuData_RealCpu_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "CpuData.RealCpu.Tooltip");
  }

  static String cpuData_Status_AllowPrivate() {
    return NbBundle.getMessage(Bundle.class, CpuData.PROPERTY_STATUS_ALLOWPRIVATE);
  }

  static String cpuData_Status_AllowPrivate_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "CpuData.Status.AllowPrivate.Tooltip");
  }

  static String cpuData_Status_FixFrame() {
    return NbBundle.getMessage(Bundle.class, CpuData.PROPERTY_STATUS_FIXFRAME);
  }

  static String cpuData_Status_FixFrame_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "CpuData.Status.FixFrame.Tooltip");
  }

  static String cpuData_Status_ForcePrivate() {
    return NbBundle.getMessage(Bundle.class, CpuData.PROPERTY_STATUS_FORCEPRIVATE);
  }

  static String cpuData_Status_ForcePrivate_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "CpuData.Status.ForcePrivate.Tooltip");
  }

  static String cpuData_Status_Virtual() {
    return NbBundle.getMessage(Bundle.class, CpuData.PROPERTY_STATUS_VIRTUAL);
  }

  static String cpuData_Status_Virtual_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "CpuData.Status.Virtual.Tooltip");
  }

  static String displayData_C_Speed_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "DisplayData.C_Speed.Tooltip");
  }

  static String displayData_H_Speed_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "DisplayData.H_Speed.Tooltip");
  }

  static String displayData_Keyboard_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "DisplayData.Keyboard.Tooltip");
  }

  static String displayData_V_Speed_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "DisplayData.V_Speed.Tooltip");
  }

  static String displayData_VideoMode_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "DisplayData.VideoMode.Tooltip");
  }

  static String extenderData_Con() {
    return NbBundle.getMessage(Bundle.class, "ExtenderData.Con");
  }

  static String extenderData_Con_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ExtenderData.Con.Tooltip");
  }

  static String extenderData_Cpu() {
    return NbBundle.getMessage(Bundle.class, "ExtenderData.Cpu");
  }

  static String extenderData_CpuCon() {
    return NbBundle.getMessage(Bundle.class, ExtenderData.PROPERTY_CPU_CON);
  }

  static String extenderData_CpuCon_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ExtenderData.CpuCon.Tooltip");
  }

  static String extenderData_Cpu_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ExtenderData.Cpu.Tooltip");
  }

  static String extenderData_Id() {
    return NbBundle.getMessage(Bundle.class, ExtenderData.PROPERTY_ID);
  }

  static String extenderData_Id_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ExtenderData.ID.Tooltip");
  }

  static String extenderData_Name() {
    return NbBundle.getMessage(Bundle.class, ExtenderData.PROPERTY_NAME);
  }

  static String extenderData_Name_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ExtenderData.Name.Tooltip");
  }

  static String extenderData_Port() {
    return NbBundle.getMessage(Bundle.class, ExtenderData.PROPERTY_PORT);
  }

  static String extenderData_Port_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ExtenderData.Port.Tooltip");
  }

  static String extenderData_RdPort() {
    return NbBundle.getMessage(Bundle.class, ExtenderData.PROPERTY_RDPORT);
  }

  static String extenderData_RdPort_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ExtenderData.RDPort.Tooltip");
  }

  static String extenderData_Status_FixPort() {
    return NbBundle.getMessage(Bundle.class, ExtenderData.PROPERTY_STATUS_FIX_PORT);
  }

  static String extenderData_Status_FixPort_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "ExtenderData.Status.FixPort.Tooltip");
  }

  static String matrixData_Active() {
    return NbBundle.getMessage(Bundle.class, "MatrixData.Active");
  }

  static String matrixData_Device() {
    return NbBundle.getMessage(Bundle.class, MatrixData.PROPERTY_DEVICE);
  }

  static String matrixData_Ports() {
    return NbBundle.getMessage(Bundle.class, "MatrixData.Ports");
  }

  static String systemConfigData_AccessData_ForceBits_ConAccess() {
    return NbBundle.getMessage(Bundle.class, AccessData.PROPERTY_FORCE_BITS_CONACCESS);
  }

  /**
   * System - Access窗口标题下的是否选中框 提示 Enable CPU access for new CON devices.
   */
  static String systemConfigData_AccessData_ForceBits_ConAccess_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.AccessData.ForceBits.ConAccess.Tooltip");
  }

  static String systemConfigData_AccessData_ForceBits_ConLock() {
    return NbBundle.getMessage(Bundle.class, AccessData.PROPERTY_FORCE_BITS_CONLOCK);
  }

  /**
   * System - Access窗口标题下的是否选中框 提示 Enable CPU Access Control List for all consoles.
   */
  static String systemConfigData_AccessData_ForceBits_ConLock_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.AccessData.ForceBits.ConLock.Tooltip");
  }

  static String systemConfigData_AccessData_ForceBits_CpuDisconnct() {
    return NbBundle.getMessage(Bundle.class, AccessData.PROPERTY_FORCE_BITS_CPUDISCONNECT);
  }

  /**
   * System - Access窗口标题下的是否选中框 提示Disconnect console from current CPU upon opening the OSD.
   */
  static String systemConfigData_AccessData_ForceBits_CpuDisconnct_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.AccessData.ForceBits.CpuDisconnct.Tooltip");
  }

  static String systemConfigData_AccessData_ForceBits_Login() {
    return NbBundle.getMessage(Bundle.class, AccessData.PROPERTY_FORCE_BITS_LOGIN);
  }

  /**
   * System - Access窗口标题下的是否选中框 提示 Require user login to enter OSD.
   */
  static String systemConfigData_AccessData_ForceBits_Login_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.AccessData.ForceBits.Login.Tooltip");
  }

  static String systemConfigData_AccessData_ForceBits_UserAccess() {
    return NbBundle.getMessage(Bundle.class, AccessData.PROPERTY_FORCE_BITS_USERACCESS);
  }

  /**
   * System - Access窗口标题下的是否选中框 提示 Enable CPU access for new users.
   */
  static String systemConfigData_AccessData_ForceBits_UserAccess_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.AccessData.ForceBits.UserAccess.Tooltip");
  }

  static String systemConfigData_AccessData_ForceBits_UserConAnd() {
    return NbBundle.getMessage(Bundle.class, AccessData.PROPERTY_FORCE_BITS_USERCONAND);
  }

  static String systemConfigData_AccessData_ForceBits_UserConAnd_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.AccessData.ForceBits.UserConAnd.Tooltip");
  }

  static String systemConfigData_AccessData_ForceBits_UserConOr() {
    return NbBundle.getMessage(Bundle.class, AccessData.PROPERTY_FORCE_BITS_USERCONOR);
  }

  static String systemConfigData_AccessData_ForceBits_UserConOr_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.AccessData.ForceBits.UserConOr.Tooltip");
  }

  static String systemConfigData_AccessData_ForceBits_UserLock() {
    return NbBundle.getMessage(Bundle.class, AccessData.PROPERTY_FORCE_BITS_USERLOCK);
  }

  /**
   * System - Access窗口标题 下的是否选中框 提示 Enable CPU Access Control List for all users.
   */
  static String systemConfigData_AccessData_ForceBits_UserLock_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.AccessData.ForceBits.UserLock.Tooltip");
  }

  static String systemConfigData_AccessData_TimeoutDisplay() {
    return NbBundle.getMessage(Bundle.class, AccessData.PROPERTY_TIMEOUT_DISPLAY);
  }

  /**
   * Access窗口标题 下的填写框 提示Specify inactivity time to quit OSD automatically (0 = deactivated).
   */
  static String systemConfigData_AccessData_TimeoutDisplay_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.AccessData.TimeoutDisplay.Tooltip");
  }

  static String systemConfigData_AccessData_TimeoutLogout() {
    return NbBundle.getMessage(Bundle.class, AccessData.PROPERTY_TIMEOUT_LOGOUT);
  }

  /**
   * Access窗口标题 下的填写框 提示Specify inactivity time for automatic user logout (0 = deactivated, -1 =
   * unlimited).
   */
  static String systemConfigData_AccessData_TimeoutLogout_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.AccessData.TimeoutLogout.Tooltip");
  }

  static String systemConfigData_AutoIdData_AutoId_RealConId() {
    return NbBundle.getMessage(Bundle.class, AutoIdData.PROPERTY_AUTO_ID_REAL_CON_ID);
  }

  /**
   * System Data 下的 Automatic窗口标题下的 ID Real CPU Device 自动分配真正的CON组设备启动标识.
   */
  static String systemConfigData_AutoIdData_AutoId_RealConId_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.AutoIdData.AutoId.RealConId.Tooltip");
  }

  static String systemConfigData_AutoIdData_AutoId_RealCpuId() {
    return NbBundle.getMessage(Bundle.class, AutoIdData.PROPERTY_AUTO_ID_REAL_CPU_ID);
  }

  /**
   * System Data 下的 Automatic窗口标题下的 ID Virtual CPU Device 提示 自动分配实际CPU设备的启动ID.
   */
  static String systemConfigData_AutoIdData_AutoId_RealCpuId_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.AutoIdData.AutoId.RealCpuId.Tooltip");
  }

  static String systemConfigData_AutoIdData_AutoId_VirtualConId() {
    return NbBundle.getMessage(Bundle.class, AutoIdData.PROPERTY_AUTO_ID_VIRTUAL_CON_ID);
  }

  /**
   * System Data 下的 Automatic窗口标题下的ID Virtual CON Device 提示 自动分配虚拟CON组设备的启动标识.
   */
  static String systemConfigData_AutoIdData_AutoId_VirtualConId_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.AutoIdData.AutoId.VirtualConId.Tooltip");
  }

  static String systemConfigData_AutoIdData_AutoId_VirtualCpuId() {
    return NbBundle.getMessage(Bundle.class, AutoIdData.PROPERTY_AUTO_ID_VIRTUAL_CPU_ID);
  }

  /**
   * System Data 下的 Automatic窗口标题下的ID Virtual CON Device 提示自动分配虚拟CPU设备的启动标识.
   */
  static String systemConfigData_AutoIdData_AutoId_VirtualCpuId_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.AutoIdData.AutoId.VirtualCpuId.Tooltip");
  }

  static String systemConfigData_AutoIdData_ForceBits_AutoConfig() {
    return NbBundle.getMessage(Bundle.class, AutoIdData.PROPERTY_FORCE_BITS_AUTO_CONFIG);
  }

  /**
   * System Data 下的 Automatic窗口标题下的Enable Auto Config 提示是否选中 分配新的外景单元到一个新的CPU或控制装置.
   */
  static String systemConfigData_AutoIdData_ForceBits_AutoConfig_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.AutoIdData.ForceBits.AutoConfig.Tooltip");
  }

  static String systemConfigData_LdapData_Address() {
    return NbBundle.getMessage(Bundle.class, LdapData.PROPERTY_ADDRESS);
  }

  static String systemConfigData_LdapData_Address_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.LdapData.Address.Tooltip");
  }

  static String systemConfigData_LdapData_BaseDn() {
    return NbBundle.getMessage(Bundle.class, LdapData.PROPERTY_BASE_DN);
  }

  static String systemConfigData_LdapData_BaseDn_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.LdapData.BaseDN.Tooltip");
  }

  static String systemConfigData_LdapData_Port() {
    return NbBundle.getMessage(Bundle.class, LdapData.PROPERTY_PORT);
  }

  static String systemConfigData_LdapData_Port_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.LdapData.Port.Tooltip");
  }

  static String systemConfigData_MatrixGridData_ForceBits_Grid() {
    return NbBundle.getMessage(Bundle.class, MatrixGridData.PROPERTY_FORCE_BITS_GRID);
  }

  static String systemConfigData_MatrixGridData_ForceBits_Grid_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.MatrixGridData.ForceBits.Grid.Tooltip");
  }

  static String systemConfigData_NetworkData_Address() {
    return NbBundle.getMessage(Bundle.class, NetworkData.PROPERTY_ADDRESS);
  }

  static String systemConfigData_NetworkData_Address_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.NetworkData.Address.Tooltip");
  }

  static String systemConfigData_NetworkData_Gateway() {
    return NbBundle.getMessage(Bundle.class, NetworkData.PROPERTY_GATEWAY);
  }

  static String systemConfigData_NetworkData_Gateway_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.NetworkData.Gateway.Tooltip");
  }

  static String systemConfigData_NetworkData_MacAddress() {
    return NbBundle.getMessage(Bundle.class, NetworkData.PROPERTY_MACADDRESS);
  }

  static String systemConfigData_NetworkData_MacAddress_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.NetworkData.MacAddress.Tooltip");
  }

  static String systemConfigData_NetworkData_Netmask() {
    return NbBundle.getMessage(Bundle.class, NetworkData.PROPERTY_NETMASK);
  }

  static String systemConfigData_NetworkData_Netmask_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.NetworkData.Netmask.Tooltip");
  }

  static String systemConfigData_NetworkData_NetworkBits_Dhcp() {
    return NbBundle.getMessage(Bundle.class, NetworkData.PROPERTY_NETWORK_BITS_DHCP);
  }

  /**
   * DBCP 帮助提示 通过DHCP服务器动态配置网络参数.
   */
  static String systemConfigData_NetworkData_NetworkBits_Dhcp_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.NetworkData.NetworkBits.Dhcp.Tooltip");
  }

  static String systemConfigData_NetworkData_NetworkBits_Api() {
    return NbBundle.getMessage(Bundle.class, NetworkData.PROPERTY_NETWORK_BITS_API);
  }

  static String systemConfigData_NetworkData_NetworkBits_Api_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.NetworkData.NetworkBits.Api.Tooltip");
  }

  static String systemConfigData_NetworkData_NetworkBits_Ftp() {
    return NbBundle.getMessage(Bundle.class, NetworkData.PROPERTY_NETWORK_BITS_FTP);
  }

  static String systemConfigData_NetworkData_NetworkBits_Ftp_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.NetworkData.NetworkBits.Ftp.Tooltip");
  }

  static String systemConfigData_NetworkData_NetworkBits_Ldap() {
    return NbBundle.getMessage(Bundle.class, NetworkData.PROPERTY_NETWORK_BITS_LDAP);
  }

  static String systemConfigData_NetworkData_NetworkBits_Ldap_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.NetworkData.NetworkBits.Ldap.Tooltip");
  }

  static String systemConfigData_NetworkData_NetworkBits_Snmp() {
    return NbBundle.getMessage(Bundle.class, NetworkData.PROPERTY_NETWORK_BITS_SNMP);
  }

  static String systemConfigData_NetworkData_NetworkBits_Snmp_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.NetworkData.NetworkBits.Snmp.Tooltip");
  }

  static String systemConfigData_NetworkData_NetworkBits_Sntp() {
    return NbBundle.getMessage(Bundle.class, NetworkData.PROPERTY_NETWORK_BITS_SNTP);
  }

  /**
   * Date and Time 下的SNTP是否选中的提示 Enable network time server synchronisation.
   */
  static String systemConfigData_NetworkData_NetworkBits_Sntp_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.NetworkData.NetworkBits.Sntp.Tooltip");
  }

  static String systemConfigData_NetworkData_NetworkBits_Syslog() {
    return NbBundle.getMessage(Bundle.class, NetworkData.PROPERTY_NETWORK_BITS_SYSLOG);
  }

  static String systemConfigData_NetworkData_NetworkBits_Syslog_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.NetworkData.NetworkBits.Syslog.Tooltip");
  }

  static String systemConfigData_NetworkData_SnmpAgentPort() {
    return NbBundle.getMessage(Bundle.class, NetworkData.PROPERTY_SNMPAGENT_PORT);
  }

  static String systemConfigData_NetworkData_SnmpAgentPort_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.NetworkData.SnmpAgentPort.Tooltip");
  }

  static String systemConfigData_SnmpData_Address() {
    return NbBundle.getMessage(Bundle.class, SnmpData.PROPERTY_ADDRESS);
  }

  static String systemConfigData_SnmpData_Address_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SnmpData.Address.Tooltip");
  }

  static String systemConfigData_SnmpData_Port() {
    return NbBundle.getMessage(Bundle.class, SnmpData.PROPERTY_PORT);
  }

  static String systemConfigData_SnmpData_Port_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SnmpData.Port.Tooltip");
  }

  static String systemConfigData_SnmpData_SnmpBits_FanTray1() {
    return NbBundle.getMessage(Bundle.class, SnmpData.PROPERTY_SNMP_FANTRAY1_ENABLED);
  }

  static String systemConfigData_SnmpData_SnmpBits_FanTray1_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SnmpData.SnmpBits.FanTray1.Tooltip");
  }

  static String systemConfigData_SnmpData_SnmpBits_FanTray2() {
    return NbBundle.getMessage(Bundle.class, SnmpData.PROPERTY_SNMP_FANTRAY2_ENABLED);
  }

  static String systemConfigData_SnmpData_SnmpBits_FanTray2_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SnmpData.SnmpBits.FanTray2.Tooltip");
  }

  static String systemConfigData_SnmpData_SnmpBits_InsertBoard() {
    return NbBundle.getMessage(Bundle.class, SnmpData.PROPERTY_SNMP_INSERT_BOARD_ENABLED);
  }

  static String systemConfigData_SnmpData_SnmpBits_InsertBoard_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SnmpData.SnmpBits.InsertBoard.Tooltip");
  }

  static String systemConfigData_SnmpData_SnmpBits_InsertExtender() {
    return NbBundle.getMessage(Bundle.class, SnmpData.PROPERTY_SNMP_INSERT_EXTENDER_ENABLED);
  }

  static String systemConfigData_SnmpData_SnmpBits_InsertExtender_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SnmpData.SnmpBits.InsertExtender.Tooltip");
  }

  static String systemConfigData_SnmpData_SnmpBits_InvalidBoard() {
    return NbBundle.getMessage(Bundle.class, SnmpData.PROPERTY_SNMP_INVALID_BOARD_ENABLED);
  }

  static String systemConfigData_SnmpData_SnmpBits_InvalidBoard_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SnmpData.SnmpBits.InvalidBoard.Tooltip");
  }

  static String systemConfigData_SnmpData_SnmpBits_PowerSupply1() {
    return NbBundle.getMessage(Bundle.class, SnmpData.PROPERTY_SNMP_POWERSUPPLY1_ENABLED);
  }

  static String systemConfigData_SnmpData_SnmpBits_PowerSupply1_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SnmpData.SnmpBits.PowerSupply1.Tooltip");
  }

  static String systemConfigData_SnmpData_SnmpBits_PowerSupply2() {
    return NbBundle.getMessage(Bundle.class, SnmpData.PROPERTY_SNMP_POWERSUPPLY2_ENABLED);
  }

  static String systemConfigData_SnmpData_SnmpBits_PowerSupply2_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SnmpData.SnmpBits.PowerSupply2.Tooltip");
  }

  static String systemConfigData_SnmpData_SnmpBits_PowerSupply3() {
    return NbBundle.getMessage(Bundle.class, SnmpData.PROPERTY_SNMP_POWERSUPPLY3_ENABLED);
  }

  static String systemConfigData_SnmpData_SnmpBits_PowerSupply3_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SnmpData.SnmpBits.PowerSupply3.Tooltip");
  }

  static String systemConfigData_SnmpData_SnmpBits_RemoveBoard() {
    return NbBundle.getMessage(Bundle.class, SnmpData.PROPERTY_SNMP_REMOVE_BOARD_ENABLED);
  }

  static String systemConfigData_SnmpData_SnmpBits_RemoveBoard_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SnmpData.SnmpBits.RemoveBoard.Tooltip");
  }

  static String systemConfigData_SnmpData_SnmpBits_RemoveExtener() {
    return NbBundle.getMessage(Bundle.class, SnmpData.PROPERTY_SNMP_REMOVE_EXTENDER_ENABLED);
  }

  static String systemConfigData_SnmpData_SnmpBits_RemoveExtener_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SnmpData.SnmpBits.RemoveExtener.Tooltip");
  }

  static String systemConfigData_SnmpData_SnmpBits_Status() {
    return NbBundle.getMessage(Bundle.class, SnmpData.PROPERTY_SNMP_STATUS_ENABLED);
  }

  static String systemConfigData_SnmpData_SnmpBits_Status_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SnmpData.SnmpBits.Status.Tooltip");
  }

  static String systemConfigData_SnmpData_SnmpBits_SwitchCommand() {
    return NbBundle.getMessage(Bundle.class, SnmpData.PROPERTY_SNMP_SWITCH_COMMAND_ENABLED);
  }

  static String systemConfigData_SnmpData_SnmpBits_SwitchCommand_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SnmpData.SnmpBits.SwitchCommand.Tooltip");
  }

  static String systemConfigData_SnmpData_SnmpBits_Temperature() {
    return NbBundle.getMessage(Bundle.class, SnmpData.PROPERTY_SNMP_TEMPERATURE_ENABLED);
  }

  static String systemConfigData_SnmpData_SnmpBits_Temperature_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SnmpData.SnmpBits.Temperature.Tooltip");
  }

  static String systemConfigData_SnmpData_SnmpBits_Trap() {
    return NbBundle.getMessage(Bundle.class, SnmpData.PROPERTY_SNMP_TRAP_ENABLED);
  }

  static String systemConfigData_SnmpData_SnmpBits_Trap_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SnmpData.SnmpBits.Trap.Tooltip");
  }

  static String systemConfigData_SntpData_Address() {
    return NbBundle.getMessage(Bundle.class, SntpData.PROPERTY_ADDRESS);
  }

  static String systemConfigData_SntpData_Address_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SntpData.Address.Tooltip");
  }

  static String systemConfigData_SntpData_RealTimeClock() {
    return NbBundle.getMessage(Bundle.class, SntpData.PROPERTY_REALTIMECLOCK);
  }

  static String systemConfigData_SntpData_RealTimeClock_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SntpData.RealTimeClock.Tooltip");
  }

  static String systemConfigData_SntpData_TimeZone() {
    return NbBundle.getMessage(Bundle.class, SntpData.PROPERTY_TIMEZONE);
  }

  static String systemConfigData_SntpData_TimeZone_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SntpData.TimeZone.Tooltip");
  }

  static String systemConfigData_SwitchData_ForceBits_AutoConnect() {
    return NbBundle.getMessage(Bundle.class, SwitchData.PROPERTY_FORCE_BITS_AUTO_CONNECT);
  }

  /**
   * System - Switch窗口CPU Auto Connect 的提示Connect to next available CPU, requires keyboard or
   * mouse.
   */
  static String systemConfigData_SwitchData_ForceBits_AutoConnect_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SwitchData.ForceBits.AutoConnect.Tooltip");
  }

  static String systemConfigData_SwitchData_ForceBits_ConDisconnect() {
    return NbBundle.getMessage(Bundle.class, SwitchData.PROPERTY_FORCE_BITS_CON_DISCONNECT);
  }

  /**
   * System - Switch窗口 Force Disconnect的提示Enforce full KVM access to CPU, other consoles are
   * disconnected.
   */
  static String systemConfigData_SwitchData_ForceBits_ConDisconnect_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SwitchData.ForceBits.ConDisconnect.Tooltip");
  }

  static String systemConfigData_SwitchData_ForceBits_CpuConnect() {
    return NbBundle.getMessage(Bundle.class, SwitchData.PROPERTY_FORCE_BITS_CPU_CONNECT);
  }

  /**
   * System - Switch窗口 Force Connect的提示Enforce full KVM access to CPU, other consoles retain video.
   */
  static String systemConfigData_SwitchData_ForceBits_CpuConnect_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SwitchData.ForceBits.CpuConnect.Tooltip");
  }

  static String systemConfigData_SwitchData_ForceBits_CpuWatch() {
    return NbBundle.getMessage(Bundle.class, SwitchData.PROPERTY_FORCE_BITS_CPU_WATCH);
  }

  /**
   * System - Switch窗口 Enable Video Sharing的提示 Allow shared video access to CPU.
   */
  static String systemConfigData_SwitchData_ForceBits_CpuWatch_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SwitchData.ForceBits.CpuWatch.Tooltip");
  }

  static String systemConfigData_SwitchData_ForceBits_FkeySingle() {
    return NbBundle.getMessage(Bundle.class, SwitchData.PROPERTY_FORCE_BITS_FKEYSINGLE);
  }

  /**
   * System - Switch窗口Macro Single Step提示Execute macros in a single step mode.
   */
  static String systemConfigData_SwitchData_ForceBits_FkeySingle_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SwitchData.ForceBits.FKeySingle.Tooltip");
  }

  static String systemConfigData_SwitchData_ForceBits_KeyboardConnect() {
    return NbBundle.getMessage(Bundle.class, SwitchData.PROPERTY_FORCE_BITS_KEYBOARD_MOUSE_CONNECT);
  }

  /**
   * System - Switch窗口 Keyboard Connect的提示 Enable CPU control request by keyboard activity.
   */
  static String systemConfigData_SwitchData_ForceBits_KeyboardConnect_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SwitchData.ForceBits.KeyboardConnect.Tooltip");
  }

  /**
   * System - Switch窗口Mouse Connect提示Enable CPU control request by mouse activity.
   */
  static String systemConfigData_SwitchData_ForceBits_MouseConnect_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SwitchData.ForceBits.MouseConnect.Tooltip");
  }

  static String systemConfigData_SwitchData_TimeoutDisconnect() {
    return NbBundle.getMessage(Bundle.class, SwitchData.PROPERTY_TIMEOUT_DISCONNECT);
  }

  /**
   * System - Switch窗口CPU Timeout [min]的提示Specify inactivity period at currently connected CPU after
   * which CPU will be disconnected automatically (0 = deactivated).
   */
  static String systemConfigData_SwitchData_TimeoutDisconnect_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SwitchData.TimeoutDisconnect.Tooltip");
  }

  static String systemConfigData_SwitchData_TimeoutShare() {
    return NbBundle.getMessage(Bundle.class, SwitchData.PROPERTY_TIMEOUT_SHARE);
  }

  /**
   * System - Switch窗口Release Time [sec]提示Specify inactivity time to accept CPU control request from
   * another console.
   */
  static String systemConfigData_SwitchData_TimeoutShare_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SwitchData.TimeoutShare.Tooltip");
  }

  static String systemConfigData_SyslogData_Address() {
    return NbBundle.getMessage(Bundle.class, SyslogData.PROPERTY_ADDRESS);
  }

  static String systemConfigData_SyslogData_Address_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SyslogData.Address.Tooltip");
  }

  static String systemConfigData_SyslogData_Facility() {
    return NbBundle.getMessage(Bundle.class, SyslogData.PROPERTY_FACILITY);
  }

  static String systemConfigData_SyslogData_Facility_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SyslogData.Facility.Tooltip");
  }

  static String systemConfigData_SyslogData_Port() {
    return NbBundle.getMessage(Bundle.class, SyslogData.PROPERTY_PORT);
  }

  static String systemConfigData_SyslogData_Port_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SyslogData.Port.Tooltip");
  }

  static String systemConfigData_SyslogData_SysLevel() {
    return NbBundle.getMessage(Bundle.class, SyslogData.PROPERTY_SYSLEVEL);
  }

  static String systemConfigData_SyslogData_SysLevel_Debug() {
    return NbBundle.getMessage(Bundle.class, SyslogData.PROPERTY_SYSLEVEL_DEBUG);
  }

  static String systemConfigData_SyslogData_SysLevel_Debug_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SyslogData.SysLevel.Debug.Tooltip");
  }

  static String systemConfigData_SyslogData_SysLevel_Error() {
    return NbBundle.getMessage(Bundle.class, SyslogData.PROPERTY_SYSLEVEL_ERROR);
  }

  static String systemConfigData_SyslogData_SysLevel_Error_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SyslogData.SysLevel.Error.Tooltip");
  }

  static String systemConfigData_SyslogData_SysLevel_Info() {
    return NbBundle.getMessage(Bundle.class, SyslogData.PROPERTY_SYSLEVEL_INFO);
  }

  static String systemConfigData_SyslogData_SysLevel_Info_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SyslogData.SysLevel.Info.Tooltip");
  }

  static String systemConfigData_SyslogData_SysLevel_Notice() {
    return NbBundle.getMessage(Bundle.class, SyslogData.PROPERTY_SYSLEVEL_NOTICE);
  }

  static String systemConfigData_SyslogData_SysLevel_Notice_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SyslogData.SysLevel.Notice.Tooltip");
  }

  static String systemConfigData_SyslogData_SysLevel_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SyslogData.SysLevel.Tooltip");
  }

  static String systemConfigData_SyslogData_SysLevel_Warning() {
    return NbBundle.getMessage(Bundle.class, SyslogData.PROPERTY_SYSLEVEL_WARNING);
  }

  static String systemConfigData_SyslogData_SysLevel_Warning_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SyslogData.SysLevel.Warning.Tooltip");
  }

  static String systemConfigData_SyslogData_Syslog_Enabled() {
    return NbBundle.getMessage(Bundle.class, SyslogData.PROPERTY_SYSLOG_ENABLED);
  }

  static String systemConfigData_SyslogData_Syslog_Enabled_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SyslogData.Syslog.Enabled.Tooltip");
  }

  static String systemConfigData_SystemData_Device() {
    return NbBundle.getMessage(Bundle.class, SystemData.PROPERTY_DEVICE);
  }

  /**
   * System Data 下的 General窗口标题下的显示帮助 网络环境下的主机名（推荐：a-z，A-Z，数字0-9，-）.
   */
  static String systemConfigData_SystemData_Device_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SystemData.Device.Tooltip");
  }

  static String systemConfigData_SystemData_ForceBits_AutoSave() {
    return NbBundle.getMessage(Bundle.class, SystemData.PROPERTY_FORCE_BITS_AUTO_SAVE);
  }

  /**
   * System Data 下的 General窗口标题下的显示帮助 自动保存矩阵状态.
   */
  static String systemConfigData_SystemData_ForceBits_AutoSave_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SystemData.ForceBits.AutoSave.Tooltip");
  }

  static String systemConfigData_SystemData_ForceBits_ComEcho() {
    return NbBundle.getMessage(Bundle.class, SystemData.PROPERTY_FORCE_BITS_COM_ECHO);
  }

  /**
   * System Data 下的 General窗口标题下的显示帮助 是否选中 通过通信端口返回所有开关命令.
   */
  static String systemConfigData_SystemData_ForceBits_ComEcho_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SystemData.ForceBits.ComEcho.Tooltip");
  }

  static String systemConfigData_SystemData_ForceBits_Default() {
    return NbBundle.getMessage(Bundle.class, SystemData.PROPERTY_FORCE_BITS_DEFAULT);
  }

  /**
   * System Data 下的 General窗口标题下的显示帮助 是否选中 当执行冷启动或重新启动的矩阵，配置默认存储将永远激活.
   */
  static String systemConfigData_SystemData_ForceBits_Default_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SystemData.ForceBits.Default.Tooltip");
  }

  static String systemConfigData_SystemData_ForceBits_EchoOnly() {
    return NbBundle.getMessage(Bundle.class, SystemData.PROPERTY_FORCE_BITS_ECHOONLY);
  }

  /**
   * System Data 下的 General窗口标题下的显示帮助是否选中 仅与回声同步矩阵.
   */
  static String systemConfigData_SystemData_ForceBits_EchoOnly_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SystemData.ForceBits.EchoOnly.Tooltip");
  }

  static String systemConfigData_SystemData_ForceBits_Invalid() {
    return NbBundle.getMessage(Bundle.class, SystemData.PROPERTY_FORCE_BITS_INVALID);
  }

  /**
   * System Data 下的 General窗口标题下的显示帮助是否选中 在操作期间必须/必须关闭，在矩阵更新期间启用.
   */
  static String systemConfigData_SystemData_ForceBits_Invalid_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SystemData.ForceBits.Invalid.Tooltip");
  }

  static String systemConfigData_SystemData_ForceBits_LanEcho() {
    return NbBundle.getMessage(Bundle.class, SystemData.PROPERTY_FORCE_BITS_LAN_ECHO);
  }

  /**
   * System Data 下的 General窗口标题下的显示帮助 通过局域网端口返回所有开关命令 是否选中.
   */
  static String systemConfigData_SystemData_ForceBits_LanEcho_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SystemData.ForceBits.LanEcho.Tooltip");
  }

  static String systemConfigData_SystemData_ForceBits_OldEcho() {
    return NbBundle.getMessage(Bundle.class, SystemData.PROPERTY_FORCE_BITS_OLDECHO);
  }

  /**
   * System Data 下的 General窗口标题下的显示帮助 是否选中 回声内部开关命令与旧格式.
   */
  static String systemConfigData_SystemData_ForceBits_OldEcho_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SystemData.ForceBits.OldEcho.Tooltip");
  }

  static String systemConfigData_SystemData_ForceBits_OnlineConfig() {
    return NbBundle.getMessage(Bundle.class, SystemData.PROPERTY_FORCE_BITS_ONLINECONFIG);
  }

  static String systemConfigData_SystemData_ForceBits_OnlineConfig_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SystemData.ForceBits.OnlineConfig.Tooltip");
  }

  static String systemConfigData_SystemData_ForceBits_Redundancy() {
    return NbBundle.getMessage(Bundle.class, SystemData.PROPERTY_FORCE_BITS_REDUNDANCY);
  }

  /**
   * System Data 下的 General窗口标题下的显示帮助 是否选中 使多余的填充自动切换.
   */
  static String systemConfigData_SystemData_ForceBits_Redundancy_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SystemData.ForceBits.Redundancy.Tooltip");
  }

  static String systemConfigData_SystemData_ForceBits_RemoveSlave() {
    return NbBundle.getMessage(Bundle.class, SystemData.PROPERTY_FORCE_BITS_REMOVESLAVE);
  }

  static String systemConfigData_SystemData_ForceBits_RemoveSlave_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SystemData.ForceBits.RemoveSlave.Tooltip");
  }

  static String systemConfigData_SystemData_ForceBits_Slave() {
    return NbBundle.getMessage(Bundle.class, SystemData.PROPERTY_FORCE_BITS_SLAVE);
  }

  /**
   * System Data 下的 General窗口标题下的显示帮助 允许在级联环境热键控制 是否选中.
   */
  static String systemConfigData_SystemData_ForceBits_Slave_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SystemData.ForceBits.Slave.Tooltip");
  }

  static String systemConfigData_SystemData_ForceBits_Synchronize() {
    return NbBundle.getMessage(Bundle.class, SystemData.PROPERTY_FORCE_BITS_SYNCHRONIZE);
  }

  /**
   * System Data 下的 General窗口标题下的显示帮助是否选中 主矩阵同步矩阵.
   */
  static String systemConfigData_SystemData_ForceBits_Synchronize_Tooltip() {
    return NbBundle.getMessage(Bundle.class,
        "SystemConfigData.SystemData.ForceBits.Synchronize.Tooltip");
  }

  static String systemConfigData_SystemData_Info() {
    return NbBundle.getMessage(Bundle.class, SystemData.PROPERTY_INFO);
  }

  /**
   * System Data 下的 General窗口标题下的显示帮助 电流矩阵配置描述.
   */
  static String systemConfigData_SystemData_Info_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SystemData.Info.Tooltip");
  }

  static String systemConfigData_SystemData_MasterIp() {
    return NbBundle.getMessage(Bundle.class, SystemData.PROPERTY_MASTER_IP);
  }

  /**
   * System Data 下的 General窗口标题下的显示帮助 设置主矩阵的网络地址 填写ip.
   */
  static String systemConfigData_SystemData_MasterIp_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SystemData.MasterIP.Tooltip");
  }

  static String systemConfigData_SystemData_Name() {
    return NbBundle.getMessage(Bundle.class, SystemData.PROPERTY_NAME);
  }

  /**
   * System Data 下的 General窗口标题下的显示帮助 电流矩阵配置名称.
   */
  static String systemConfigData_SystemData_Name_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemConfigData.SystemData.Name.Tooltip");
  }

  static String systemData_InternalLogLevel() {
    return NbBundle.getMessage(Bundle.class, SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL);
  }

  static String systemData_InternalLogLevel_Debug() {
    return NbBundle.getMessage(Bundle.class, SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL_DEBUG);
  }

  static String systemData_InternalLogLevel_Debug_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemData.InternalLogLevel.Debug.Tooltip");
  }

  static String systemData_InternalLogLevel_Error() {
    return NbBundle.getMessage(Bundle.class, SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL_ERROR);
  }

  static String systemData_InternalLogLevel_Error_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemData.InternalLogLevel.Error.Tooltip");
  }

  static String systemData_InternalLogLevel_Info() {
    return NbBundle.getMessage(Bundle.class, SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL_INFO);
  }

  static String systemData_InternalLogLevel_Info_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemData.InternalLogLevel.Info.Tooltip");
  }

  static String systemData_InternalLogLevel_Notice() {
    return NbBundle.getMessage(Bundle.class, SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL_NOTICE);
  }

  static String systemData_InternalLogLevel_Notice_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemData.InternalLogLevel.Notice.Tooltip");
  }

  static String systemData_InternalLogLevel_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemData.InternalLogLevel.Tooltip");
  }

  static String systemData_InternalLogLevel_Warning() {
    return NbBundle.getMessage(Bundle.class, SystemConfigData.PROPERTY_INTERNAL_LOGLEVEL_WARNING);
  }

  static String systemData_InternalLogLevel_Warning_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemData.InternalLogLevel.Warning.Tooltip");
  }

  static String systemData_Multicast() {
    return NbBundle.getMessage(Bundle.class, SystemConfigData.PROPERTY_MULTICAST);
  }

  static String systemData_Multicast_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "SystemData.Multicast.Tooltip");
  }

  static String userData_FullName() {
    return NbBundle.getMessage(Bundle.class, UserData.PROPERTY_FULLNAME);
  }

  static String userData_FullName_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "UserData.FullName.Tooltip");
  }

  static String userData_Id() {
    return NbBundle.getMessage(Bundle.class, UserData.PROPERTY_ID);
  }

  static String userData_Id_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "UserData.ID.Tooltip");
  }

  static String userData_Name() {
    return NbBundle.getMessage(Bundle.class, UserData.PROPERTY_NAME);
  }

  static String userData_Name_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "UserData.Name.Tooltip");
  }

  static String userData_Password() {
    return NbBundle.getMessage(Bundle.class, UserData.PROPERTY_PASSWORD);
  }

  static String userData_Password_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "UserData.Password.Tooltip");
  }

  static String userData_Rights_Super_Tooltip() {
    return NbBundle.getMessage(Bundle.class, "UserData.Rights.SUPER.Tooltip");
  }

  public static String multiviewOutputMode(int ch) {
    return ch + NbBundle.getMessage(Bundle.class, "multiview.output_mode");
  }

  //private void Bundle() {}
}

