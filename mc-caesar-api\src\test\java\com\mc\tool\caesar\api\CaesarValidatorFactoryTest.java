package com.mc.tool.caesar.api;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

/**
 * .
 */
public class CaesarValidatorFactoryTest {

  @Test
  public void testPassword() {
    assertTrue(CaesarValidatorFactory.testPassword("qwer.123"));
    assertTrue(CaesarValidatorFactory.testPassword("qwe#987456"));
    assertTrue(CaesarValidatorFactory.testPassword("21*ggg3e"));
    assertTrue(CaesarValidatorFactory.testPassword("@dfadg2325"));
    assertFalse(CaesarValidatorFactory.testPassword(""));
    assertFalse(CaesarValidatorFactory.testPassword("adfa"));
    assertFalse(CaesarValidatorFactory.testPassword("211"));
    assertFalse(CaesarValidatorFactory.testPassword("adg12"));
    assertFalse(CaesarValidatorFactory.testPassword("adg$12"));
  }
}