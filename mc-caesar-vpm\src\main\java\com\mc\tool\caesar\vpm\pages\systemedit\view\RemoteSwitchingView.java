package com.mc.tool.caesar.vpm.pages.systemedit.view;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarTerminalBase;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.TerminalPropertyConverter;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.PortComparator;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.UndecoratedAlert;
import com.mc.tool.framework.utility.UndecoratedDialog;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import javafx.beans.Observable;
import javafx.beans.binding.Bindings;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredListEx;
import javafx.collections.transformation.SortedList;
import javafx.geometry.Pos;
import javafx.scene.control.AlertEx;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonBar.ButtonData;
import javafx.scene.control.ButtonType;
import javafx.scene.control.ComboBox;
import javafx.scene.control.DialogPaneEx;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.control.ListView;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextField;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.HBox;
import javafx.util.Callback;
import javafx.util.StringConverter;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.validation.ValidationSupport;

/**
 * .
 */
@Slf4j
public class RemoteSwitchingView extends TableView<VisualEditTerminal> {
  private static final String DEMO_IP = "s0.0.0.0".substring(1);

  private CaesarDeviceController deviceController;

  private final TableColumn<VisualEditTerminal, String> indexCol;
  private final TableColumn<VisualEditTerminal, Number> idCol;
  private final TableColumn<VisualEditTerminal, String> nameCol;
  private final TableColumn<VisualEditTerminal, String> portCol;
  private final TableColumn<VisualEditTerminal, String> modeCol;
  private final TableColumn<VisualEditTerminal, String> pduIpCol;
  private final TableColumn<VisualEditTerminal, String> outletCol;
  private final TableColumn<VisualEditTerminal, String> operationCol;
  private FilteredListEx<VisualEditTerminal> filteredListEx;

  private static final int PDU_PORT_MAX = 8;

  /** 远程开关机. */
  public RemoteSwitchingView() {
    indexCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.switching.index"));
    indexCol.prefWidthProperty().bind(widthProperty().multiply(0.1));
    indexCol.setId("index-col");
    idCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.switching.id"));
    idCol.prefWidthProperty().bind(widthProperty().multiply(0.1));
    idCol.setId("id-col");
    nameCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.switching.device"));
    nameCol.prefWidthProperty().bind(widthProperty().multiply(0.1));
    nameCol.setId("name-col");
    portCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.switching.port"));
    portCol.prefWidthProperty().bind(widthProperty().multiply(0.1));
    portCol.setId("port-col");
    modeCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.switching.mode"));
    modeCol.prefWidthProperty().bind(widthProperty().multiply(0.15));
    modeCol.setId("mode-col");
    pduIpCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.switching.ip"));
    pduIpCol.prefWidthProperty().bind(widthProperty().multiply(0.15));
    pduIpCol.setId("pduIp-col");
    outletCol =
        new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.switching.outlet"));
    outletCol.prefWidthProperty().bind(widthProperty().multiply(0.075));
    outletCol.setId("outlet-col");
    operationCol =
        new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.switching.operation"));
    operationCol.prefWidthProperty().bind(widthProperty().multiply(0.2));
    operationCol.setId("operation-col");

    getColumns()
        .addAll(indexCol, idCol, nameCol, portCol, modeCol, pduIpCol, outletCol, operationCol);
    // setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);

    indexCol.setCellFactory(new IndexCellFactory());
    indexCol.setStyle("-fx-alignment:CENTER");
    indexCol.setSortable(false);

    idCol.setCellValueFactory(
        (cell) -> {
          VisualEditTerminal terminal = cell.getValue();
          return TerminalPropertyConverter.getIdProperty(terminal);
        });
    idCol.setStyle("-fx-alignment:CENTER");

    nameCol.setCellValueFactory(
        (cell) -> TerminalPropertyConverter.getNameProperty(cell.getValue()));

    portCol.setCellValueFactory(
        (cell) -> TerminalPropertyConverter.getPortProperty(cell.getValue()));
    portCol.setComparator(new PortComparator());

    modeCol.setCellFactory(cell -> new ModeTableCell(deviceController));
    pduIpCol.setCellFactory(cell -> new PduIpTableCell(deviceController));
    outletCol.setCellFactory(cell -> new OutletTableCell(deviceController, filteredListEx));
    operationCol.setCellFactory(cell -> new OperationTableCell(deviceController));
  }

  /** . */
  public void setDeviceController(CaesarDeviceController deviceController) {
    this.deviceController = deviceController;
  }

  /**
   * 初始化.
   *
   * @param model model
   */
  public void init(VisualEditModel model) {
    // 初始化列表
    filteredListEx =
        new FilteredListEx<>(
            model.getAllTerminals(),
            (item) -> {
              if (item instanceof CaesarTerminalBase && item.isTx()) {
                CaesarTerminalBase terminalBase = (CaesarTerminalBase) item;
                return terminalBase.getPort1ConnectedProperty().get()
                    || terminalBase.getPort2ConnectedProperty().get();
              }
              return false;
            });

    filteredListEx.setExtractor(
        (item) -> {
          if (item instanceof CaesarTerminalBase) {
            CaesarTerminalBase terminalBase = (CaesarTerminalBase) item;
            return new Observable[] {
              terminalBase.getPort1ConnectedProperty(), terminalBase.getPort2ConnectedProperty()
            };
          } else {
            return new Observable[0];
          }
        });

    SortedList<VisualEditTerminal> sortedList = new SortedList<>(filteredListEx);
    sortedList.comparatorProperty().bind(comparatorProperty());
    setItems(sortedList);
  }

  static class IndexCellFactory
      implements Callback<
          TableColumn<VisualEditTerminal, String>, TableCell<VisualEditTerminal, String>> {

    @Override
    public TableCell<VisualEditTerminal, String> call(
        TableColumn<VisualEditTerminal, String> param) {
      TableCell<VisualEditTerminal, String> cell = new TableCell<>();
      cell.textProperty()
          .bind(
              Bindings.createStringBinding(
                  () -> {
                    if (cell.isEmpty()) {
                      return null;
                    } else {
                      return String.format("%04d", cell.getIndex() + 1);
                    }
                  },
                  cell.indexProperty(),
                  cell.emptyProperty()));
      return cell;
    }
  }

  static class ModeTableCell extends TableCell<VisualEditTerminal, String> {

    private final CaesarDeviceController deviceController;

    public ModeTableCell(CaesarDeviceController deviceController) {
      this.deviceController = deviceController;
    }

    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);

      if (!empty && getTableRow() != null) {
        Object rowItem = this.getTableRow().getItem();
        if (rowItem instanceof CaesarCpuTerminal) {
          CpuData data = ((CaesarCpuTerminal) rowItem).getCpuData();
          String networkWake =
              CaesarI18nCommonResource.getString("systemedit.switching.networkwake");
          String pcPowerControl =
              CaesarI18nCommonResource.getString("systemedit.switching.pcpowercontrol");
          String disable = CaesarI18nCommonResource.getString("systemedit.switching.disable");
          List<String> modeList = Lists.newArrayList(disable, networkWake, pcPowerControl);
          ObservableList<String> observableList = FXCollections.observableArrayList(modeList);
          ComboBox<String> modeSelector = new ComboBox<>(observableList);
          modeSelector.getSelectionModel().select(data.getOpenMode());
          Bindings.bindBidirectional(
              modeSelector.valueProperty(),
              data.getOpenModeProperty(),
              new IndexStringConverter(modeList));
          modeSelector
              .getSelectionModel()
              .selectedIndexProperty()
              .addListener(
                  (observable, oldValue, newValue) -> {
                    if (!oldValue.equals(newValue) && newValue.intValue() != data.getOpenMode()) {
                      deviceController.execute(
                          () -> {
                            data.setOpenMode(newValue.intValue());
                            try {
                              deviceController
                                  .getDataModel()
                                  .sendCpuData(Collections.singletonList(data));
                            } catch (DeviceConnectionException | BusyException exception) {
                              log.error("Fail to send cpuData value!", exception);
                            }
                          });
                    }
                  });
          modeSelector.setStyle("-fx-border-width: 0;");
          this.setGraphic(modeSelector);
        }
      }
    }
  }

  static class IndexStringConverter extends StringConverter<Number> {

    private final List<String> list;

    public IndexStringConverter(List<String> list) {
      this.list = list;
    }

    @Override
    public String toString(Number object) {
      if (list.size() > object.intValue()) {
        return list.get(object.intValue());
      }
      return null;
    }

    @Override
    public Number fromString(String string) {
      return list.indexOf(string);
    }
  }

  static class PduIpTableCell extends TableCell<VisualEditTerminal, String> {

    private final CaesarDeviceController deviceController;

    public PduIpTableCell(CaesarDeviceController deviceController) {
      this.deviceController = deviceController;
    }

    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);
      if (!empty && getTableRow() != null) {
        Object rowItem = this.getTableRow().getItem();
        if (rowItem instanceof CaesarCpuTerminal) {
          CpuData data = ((CaesarCpuTerminal) rowItem).getCpuData();
          Label ipLabel = new Label(data.getPduIpString());
          deviceController
              .getDataModel()
              .addPropertyChangeListener(
                  CpuData.PROPERTY_PDU_IP,
                  pce -> {
                    if (!ipLabel.getText().equals(data.getPduIpString())) {
                      PlatformUtility.runInFxThread(() -> ipLabel.setText(data.getPduIpString()));
                    }
                  });
          ImageView iv =
              new ImageView(new Image("/com/mc/tool/caesar/vpm/pages/systemedit/edit.png"));
          Button button = new Button();
          button.getStyleClass().add("image-button");
          button.setGraphic(iv);
          HBox hbox = new HBox(ipLabel, button);
          hbox.setAlignment(Pos.CENTER_LEFT);
          this.setGraphic(hbox);
          button.setOnAction(
              event -> {
                EditPduIpDialog dialog = new EditPduIpDialog(data.getPduIpString());
                dialog.initOwner(button.getScene().getWindow());
                dialog.showAndWait();
                String ip = dialog.getResult();
                if (ip != null && !ip.equals(data.getPduIpString())) {
                  deviceController.execute(
                      () -> {
                        byte[] bytes = IpUtil.getAddressByte(ip);
                        data.setPduIp(bytes);
                        data.setPduPort(0);
                        try {
                          deviceController
                              .getDataModel()
                              .sendCpuData(Collections.singletonList(data));
                        } catch (DeviceConnectionException | BusyException exception) {
                          log.error("Fail to send cpuData value!", exception);
                        }
                      });

                }
              });
        }
      }
    }
  }

  static class EditPduIpDialog extends UndecoratedDialog<String> {

    public EditPduIpDialog(String initialIp) {
      setTitle(CaesarI18nCommonResource.getString("systemedit.switching.editPduIpDialog.title"));
      Label ipLabel = new Label("PDU IP");
      TextField ipField = new TextField(initialIp);
      HBox hbox = new HBox(ipLabel, ipField);
      hbox.setSpacing(10);
      hbox.setAlignment(Pos.CENTER);
      getDialogPane().setContent(hbox);

      final DialogPaneEx dialogPane = getDialogPane();
      dialogPane.getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);
      ValidationSupport validationSupport = new ValidationSupport();
      validationSupport.setValidationDecorator(null);
      validationSupport.registerValidator(ipField, new IpValidator());
      dialogPane
          .lookupButton(ButtonType.OK)
          .disableProperty()
          .bind(validationSupport.invalidProperty());
      setResultConverter(
          (dialogButton) -> {
            ButtonData data = dialogButton == null ? null : dialogButton.getButtonData();
            if (data == ButtonData.OK_DONE) {
              return ipField.getText();
            }
            return null;
          });
    }
  }

  static class OutletTableCell extends TableCell<VisualEditTerminal, String> {

    private final CaesarDeviceController deviceController;
    private final FilteredListEx<VisualEditTerminal> filteredListEx;
    private final ObservableList<String> disabledItems = FXCollections.observableArrayList();

    public OutletTableCell(
        CaesarDeviceController deviceController,
        FilteredListEx<VisualEditTerminal> filteredListEx) {
      this.deviceController = deviceController;
      this.filteredListEx = filteredListEx;
    }

    private void updateComboboxExistedList(CpuData data) {
      PlatformUtility.runInFxThread(
          () -> {
            String pduIpString = data.getPduIpString();
            if (DEMO_IP.equals(pduIpString)) {
              disabledItems.clear();
              return;
            }
            int pduPort = data.getPduPort();
            List<String> existedList = Lists.newArrayList();
            for (VisualEditTerminal terminal : filteredListEx) {
              if (terminal instanceof CaesarCpuTerminal) {
                CpuData cpuData = ((CaesarCpuTerminal) terminal).getCpuData();
                if (Objects.equals(cpuData.getPduIpString(), pduIpString)
                    && cpuData.getPduPort() > 0 && cpuData.getPduPort() != pduPort) {
                  existedList.add(String.valueOf(cpuData.getPduPort()));
                }
              }
            }
            if (!disabledItems.equals(existedList)) {
              disabledItems.setAll(existedList);
            }
          });
    }

    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);

      if (!empty) {
        Object rowItem = this.getTableRow().getItem();
        if (rowItem instanceof CaesarCpuTerminal) {
          ObservableList<String> ports = FXCollections.observableArrayList();
          ports.add("");
          for (int i = 1; i <= PDU_PORT_MAX; i++) {
            ports.add(String.valueOf(i));
          }
          ComboBox<String> outletSelector = new ComboBox<>(ports);
          outletSelector.setCellFactory(
              new Callback<ListView<String>, ListCell<String>>() {
                @Override
                public ListCell<String> call(ListView<String> param) {
                  return new ListCell<String>() {
                    @Override
                    protected void updateItem(String item, boolean empty) {
                      super.updateItem(item, empty);
                      if (item != null || !empty) {
                        this.textProperty()
                            .bind(
                                Bindings.createStringBinding(
                                    () -> disabledItems.contains(item) ? "" : item, disabledItems));
                        this.disableProperty()
                            .bind(
                                Bindings.createBooleanBinding(
                                    () -> disabledItems.contains(item), disabledItems));
                      }
                    }
                  };
                }
              });
          CpuData data = ((CaesarCpuTerminal) rowItem).getCpuData();
          deviceController
              .getDataModel()
              .addPropertyChangeListener(
                  CpuData.PROPERTY_PDU_PORT,
                  pce -> {
                    if (!Objects.equals(
                        outletSelector.getSelectionModel().getSelectedItem(),
                        String.valueOf(data.getPduPort()))) {
                      PlatformUtility.runInFxThread(
                          () -> {
                            if (data.getPduPort() > 0) {
                              outletSelector
                                  .getSelectionModel()
                                  .select(String.valueOf(data.getPduPort()));
                            } else {
                              outletSelector.getSelectionModel().select("");
                            }
                          });
                    }
                  });

          // 更新端口选项
          deviceController
              .getDataModel()
              .addPropertyChangeListener(
                  new String[] {CpuData.PROPERTY_PDU_PORT, CpuData.PROPERTY_PDU_IP},
                  pce -> updateComboboxExistedList(data));
          filteredListEx.addListener(
              (ListChangeListener<VisualEditTerminal>)
                  change -> {
                    if (change.next()) {
                      updateComboboxExistedList(data);
                    }
                  });

          outletSelector.setStyle("-fx-border-width: 0;");
          if (data.getPduPort() > 0) {
            outletSelector.getSelectionModel().select(String.valueOf(data.getPduPort()));
          } else {
            outletSelector.getSelectionModel().select("");
          }
          outletSelector
              .getSelectionModel()
              .selectedItemProperty()
              .addListener(
                  (observable, oldValue, newValue) -> {
                    if (!Objects.equals(newValue, oldValue)) {
                      if (Strings.isNullOrEmpty(newValue)) {
                        if (data.getPduPort() != 0) {
                          deviceController.execute(
                              () -> {
                                data.setPduPort(0);
                                try {
                                  deviceController
                                      .getDataModel()
                                      .sendCpuData(Collections.singletonList(data));
                                } catch (DeviceConnectionException | BusyException exception) {
                                  log.error("Fail to send cpuData value!", exception);
                                }
                              });
                        }
                      } else if (Integer.parseInt(newValue) > 0
                          && data.getPduPort() != Integer.parseInt(newValue)) {
                        deviceController.execute(
                            () -> {
                              data.setPduPort(Integer.parseInt(newValue));
                              try {
                                deviceController
                                    .getDataModel()
                                    .sendCpuData(Collections.singletonList(data));
                              } catch (DeviceConnectionException | BusyException exception) {
                                log.error("Fail to send cpuData value!", exception);
                              }
                            });
                      }
                    }
                  });
          this.setGraphic(outletSelector);
        }
      }
    }
  }

  class OperationTableCell extends TableCell<VisualEditTerminal, String> {

    private final CaesarDeviceController deviceController;

    public OperationTableCell(CaesarDeviceController deviceController) {
      this.deviceController = deviceController;
    }

    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);

      if (!empty) {
        Object rowItem = this.getTableRow().getItem();
        if (rowItem instanceof CaesarCpuTerminal) {
          CpuData data = ((CaesarCpuTerminal) rowItem).getCpuData();
          Button powerOn =
              new Button(CaesarI18nCommonResource.getString("systemedit.switching.poweron"));
          powerOn.getStyleClass().add("common-button");
          powerOn.setOnAction(event -> powerOnEvent(data));
          Button powerOff =
              new Button(CaesarI18nCommonResource.getString("systemedit.switching.poweroff"));
          powerOff.getStyleClass().add("common-button");
          powerOff.setOnAction(event -> powerOffEvent(data));
          Button reboot =
              new Button(CaesarI18nCommonResource.getString("systemedit.switching.reboot"));
          reboot.getStyleClass().add("common-button");
          reboot.setOnAction(event -> rebootEvent(data));
          BooleanProperty disable = new SimpleBooleanProperty();
          disable.set(data.getOpenMode() == 0);
          deviceController
              .getDataModel()
              .addPropertyChangeListener(
                  CpuData.PROPERTY_PDU_OPEN_MODE, pce -> disable.set(data.getOpenMode() == 0));
          powerOn.disableProperty().bind(disable);
          powerOff.disableProperty().bind(disable);
          reboot.disableProperty().bind(disable);
          HBox container = new HBox(powerOn, reboot, powerOff);
          container.setSpacing(10);
          this.setGraphic(container);
        }
      }
    }

    private void powerOnEvent(CpuData cpuData) {
      UndecoratedAlert alert = newAlert();
      alert.setTitle(CaesarI18nCommonResource.getString("systemedit.switching.poweron"));
      alert.setContentText(
          CaesarI18nCommonResource.getString("systemedit.switching.poweron.alert"));
      alert
          .showAndWait()
          .ifPresent(
              (type) -> {
                if (type.equals(ButtonType.OK)) {
                  deviceController.execute(() -> deviceController.switchPduPowerOn(cpuData));
                }
              });
    }

    private void powerOffEvent(CpuData cpuData) {
      UndecoratedAlert alert = newAlert();
      alert.setTitle(CaesarI18nCommonResource.getString("systemedit.switching.poweroff"));
      alert.setContentText(
          CaesarI18nCommonResource.getString("systemedit.switching.poweroff.alert"));
      alert
          .showAndWait()
          .ifPresent(
              (type) -> {
                if (type.equals(ButtonType.OK)) {
                  deviceController.execute(() -> deviceController.switchPduPowerOff(cpuData));
                }
              });
    }

    private void rebootEvent(CpuData cpuData) {
      UndecoratedAlert alert = newAlert();
      alert.setTitle(CaesarI18nCommonResource.getString("systemedit.switching.reboot"));
      alert.setContentText(CaesarI18nCommonResource.getString("systemedit.switching.reboot.alert"));
      alert
          .showAndWait()
          .ifPresent(
              (type) -> {
                if (type.equals(ButtonType.OK)) {
                  deviceController.execute(() -> deviceController.switchPduReboot(cpuData));
                }
              });
    }
  }

  private UndecoratedAlert newAlert() {
    UndecoratedAlert alert = new UndecoratedAlert(AlertEx.AlertExType.WARNING);
    alert.initOwner(getScene().getWindow());
    alert.setHeaderText(null);
    alert.getButtonTypes().add(ButtonType.CANCEL);
    return alert;
  }

}
