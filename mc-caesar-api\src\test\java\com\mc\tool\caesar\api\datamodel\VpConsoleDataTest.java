package com.mc.tool.caesar.api.datamodel;

import static org.junit.Assert.assertEquals;

import com.mc.tool.caesar.api.datamodel.vp.VpConConfigData;
import com.mc.tool.caesar.api.datamodel.vp.Vp6OutputData;
import com.mc.tool.caesar.api.datamodel.vp.VpType;
import org.junit.Test;

/**
 * .
 */
public class VpConsoleDataTest {

  @Test
  public void test() {
    VpConConfigData configData = VpType.VP6.createConfigData();
    assertEquals(0x400, configData.size());

    Vp6OutputData vp6OutputData = new Vp6OutputData();
    assertEquals(0x40, vp6OutputData.size());
  }

}
