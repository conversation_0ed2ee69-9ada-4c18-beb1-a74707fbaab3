package com.mc.tool.caesar.api.utils;

import java.beans.IndexedPropertyChangeEvent;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.beans.PropertyChangeSupport;
import java.util.Collection;
import java.util.Collections;

/**
 * BitFieldPropertyChangeListener.
 */
public class BitFieldPropertyChangeListener implements PropertyChangeListener {

  private final PropertyChangeSupport pcs;
  private final Collection<BitFieldEntry> bitFieldEntries;

  /**
   * .
   *
   * @param pcs             pcs
   * @param bitFieldEntries bitFieldEntries
   */
  public BitFieldPropertyChangeListener(PropertyChangeSupport pcs,
      Collection<BitFieldEntry> bitFieldEntries) {
    this.pcs = pcs;
    this.bitFieldEntries =
        null == bitFieldEntries || bitFieldEntries.isEmpty() ? Collections.emptyList()
            : bitFieldEntries;
  }

  @Override
  public void propertyChange(PropertyChangeEvent evt) {
    int index;
    int oldValue;
    int newValue;
    if (evt instanceof IndexedPropertyChangeEvent && !this.bitFieldEntries.isEmpty()) {
      index = ((IndexedPropertyChangeEvent) evt).getIndex();

      oldValue = getValue(evt.getOldValue());
      newValue = getValue(evt.getNewValue());
      for (BitFieldEntry bitFieldEntry : this.bitFieldEntries) {
        this.pcs.fireIndexedPropertyChange(bitFieldEntry.getPropertyName(), index,
            Utilities.areBitsSet(oldValue, bitFieldEntry.getBitField()),
            Utilities.areBitsSet(newValue, bitFieldEntry.getBitField()));
      }
    }
  }

  private int getValue(Object object) {
    if (object instanceof Number) {
      return ((Number) object).intValue();
    }
    return 0;
  }
}

