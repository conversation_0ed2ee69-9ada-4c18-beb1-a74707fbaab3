package com.mc.tool.caesar.api;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import com.mc.tool.caesar.api.datamodel.ConfigData.IoMode;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.api.datamodel.MatrixData;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.datamodel.ScenarioDescribeData;
import com.mc.tool.caesar.api.datamodel.SystemData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData.CompleteVideoData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData.VideoWallData;
import com.mc.tool.caesar.api.datamodel.vp.VpConConfigData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.interfaces.SwitchModuleData;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.Utilities;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.TestName;

/**
 * .
 */
public class TeraSwitchDataModelTest extends TestBase {


  @BeforeClass
  public static void setUpBeforeClass() {
    TestBase.setupBeforeClass();
  }

  @AfterClass
  public static void tearDownAfterClass() {
    TestBase.tearDownAfterClass();
  }

  @Rule
  public TestName testName = new TestName();

  @Before
  public void before() {
    System.out.println("Starting Test: " + testName.getMethodName());
  }

  @After
  public void after() {
    System.out.println("End      Test: " + testName.getMethodName());
  }

  @Test
  public void testGetMacAddress() {
    try {
      String mac = model.getMacAddress();
      System.out.printf("mac address : %s%n", mac);
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception.");
    }
  }

  /**
   * 重新启动.
   */
  @Test
  public void testRestart() {
    try {
      model.restart(0xff);
    } catch (ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception.");
    }
  }

  /**
   * 重启IO卡.
   */
  @Test
  @Ignore("not ready yet")
  public void testRestartIoCard1() throws Exception {
    // model.restartIOCard(36);
    assertNotNull(model);
  }

  /**
   * 重置.
   */
  @Test
  @Ignore("not ready yet")
  public void testReset() throws Exception {
    byte i = 19;
    byte t = 16;
    byte u = 18;
    model.reset(i, t, u);
    assertNotNull(model);
  }

  @Test
  public void testGetUiThreshold() {
    Threshold t = model.getUiThreshold();
    assertEquals(true, t.equals(Threshold.UI_ALL));
  }

  @Test
  public void testGetIdentifier() {
    long id = model.getIdentifier();
    assertTrue(id >= 0);
  }

  @Test
  public void testClearSerialCache() {
    model.clearSerialCache();
    java.util.Map<String, byte[]> map = model.getSerialCache();
    assertTrue(map.isEmpty());
  }

  @Test
  public void testSendSystemData() {
    // 先读，然后写，再读查看是否有所改变，然后再把最开始的值写进去
    try {
      model.reloadSystemData();
    } catch (ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }

    SystemData data = model.getConfigData().getSystemConfigData().getSystemData();

    String newName = "test";
    String newInfo = "just for test";
    String newDevice = "test_device";
    data.setName(newName);
    data.setInfo(newInfo);
    data.setDevice(newDevice);

    try {
      model.sendSystemData();
    } catch (DeviceConnectionException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }

    try {
      model.reloadSystemData();
    } catch (ConfigException | BusyException e) {
      fail("expect no exception");
    }

    data = model.getConfigData().getSystemConfigData().getSystemData();
    assertEquals(newName, data.getName());
    assertEquals(newInfo, data.getInfo());
    assertEquals(newDevice, data.getDevice());

    String saveName = data.getName();
    data.setName(saveName);
    String saveInfo = data.getInfo();
    data.setInfo(saveInfo);
    String saveDevice = data.getInfo();
    data.setDevice(saveDevice);

    try {
      model.sendSystemData();
    } catch (DeviceConnectionException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
    // MTODO 测试其他选项
  }

  @Test
  @Ignore("级联还没有实现")
  public void testSendMatrixData() {
    Iterable<MatrixData> datas = model.getConfigData().getMatrixDatas();
    try {
      model.sendMatrixData(datas);
    } catch (DeviceConnectionException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testSendUserData() {
    try {
      model.reloadUserData();
      Iterable<UserData> datas = model.getConfigData().getUserDatas();
      List<String> names = new ArrayList<>();
      for (UserData userData : datas) {
        names.add(userData.getFullName());
      }
      // 发送测试数据
      model.sendUserData(datas);
      // 读取并检查
      model.reloadUserData();
      datas = model.getConfigData().getUserDatas();
      int cnt = 0;
      for (UserData userData : datas) {
        cnt += 1;
        assertEquals("test_user", userData.getFullName());
      }
      assertEquals(names.size(), cnt);
      // 恢复数据
      @SuppressWarnings("unused")
      int index = 0;
      for (@SuppressWarnings("unused") UserData userData : datas) {
        index += 1;
      }
      model.sendUserData(datas);

    } catch (ConfigException | BusyException | DeviceConnectionException e) {
      e.printStackTrace();
      fail("expect no exception");
    }

  }

  @Test
  public void testSendExtenderData() {
    try {
      model.reloadExtenderData();
      Iterable<ExtenderData> datas = model.getConfigData().getExtenderDatas();

      model.sendExtenderData(datas);
    } catch (ConfigException | BusyException | DeviceConnectionException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
    // MTODO 测试各个选项
  }

  @Test
  public void testSendCpuData() {
    try {
      model.reloadCpuData();
      Iterable<CpuData> cpuDatas = model.getConfigData().getCpuDatas();
      model.sendCpuData(cpuDatas);
    } catch (ConfigException | BusyException | DeviceConnectionException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
    // MTODO 测试各个选项

  }

  @Test
  public void testSendConsoleData() {
    try {
      model.reloadConsoleData();
      Iterable<ConsoleData> consoleDatas = model.getConfigData().getConsoleDatas();
      model.sendConsoleData(consoleDatas);
    } catch (ConfigException | BusyException | DeviceConnectionException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
    // MTODO 测试各个选项
  }

  @Test
  public void testSendFunctionKeyData() {
    try {
      model.reloadFunctionKeyData();
      Iterable<FunctionKeyData> datas = model.getConfigData().getFunctionKeyDatas();
      model.sendFunctionKeyData(datas);
    } catch (ConfigException | BusyException | DeviceConnectionException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
    // MTODO 测试各个选项
  }

  @Test
  public void testSendPortData() {
    try {
      Iterable<PortData> datas = model.getConfigData().getPortDatas();
      model.sendPortData(datas);
    } catch (DeviceConnectionException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
    // MTODO 待搞清楚portdata的含义
  }

  @Test
  public void testGetSwitchModuleData() {
    SwitchModuleData data = model.getSwitchModuleData();
    assertNotNull(data);

    try {
      data.reloadModules();
    } catch (ConfigException | BusyException e) {
      fail("expect no exception");
    }

  }

  @Test
  public void testGetConfigVersion() {
    try {
      int version = model.getConfigVersion();
      assertNotEquals(0, version);
    } catch (ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testReloadConfigData() {
    try {
      model.reloadConfigData();
      ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
      model.writeData(byteArrayOutputStream, IoMode.All);
      FileOutputStream fos = new FileOutputStream("c://configout.bin");
      fos.write(byteArrayOutputStream.toByteArray());
      fos.close();
    } catch (ConfigException | BusyException | IOException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testIsIoCapable() {
    boolean capable = model.isIoCapable();
    assertTrue(capable);
  }

  @Test
  public void testActivateConfig() {
    try {
      model.activateConfig(0);
    } catch (ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testSave() {
    try {
      model.save();
    } catch (ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  @Ignore("not ready yet")
  public void testShutdown() {
    try {
      model.shutdown(1);
    } catch (ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  @Ignore("not ready yet")
  public void testFactoryReset() {
    try {
      model.factoryReset(0);
    } catch (ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testSetServiceModeIntBoolean() {
    try {
      model.setServiceMode(255, true);
      model.setServiceMode(255, false);
    } catch (ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testSendUpdateIoFlash() {
  }

  @Test
  public void testReloadCpuConsoleMatrix() {
    try {
      model.reloadCpuConsoleMatrix();
    } catch (ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
    // MTODO 测试跟reloadCpuData与reloadConsoleData的不同
    // 测试有没有加载成功
  }

  @Test
  public void testSendConsole() {
    Collection<ConsoleData> cons = model.getConfigData().getConfigDataManager().getActiveConsoles();
    Collection<CpuData> cpus = model.getConfigData().getConfigDataManager().getActiveCpus();
    assertTrue(!cons.isEmpty());
    assertTrue(!cpus.isEmpty());

    try {
      model.videoConnect(cons.iterator().next(), cpus.iterator().next());
      model.sendCpuConsoleConnection(null, cons.iterator().next());
    } catch (ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testSendCpuConsoleConnection() {
    Collection<ConsoleData> cons = model.getConfigData().getConfigDataManager().getActiveConsoles();
    Collection<CpuData> cpus = model.getConfigData().getConfigDataManager().getActiveCpus();
    assertTrue(!cons.isEmpty());
    assertTrue(!cpus.isEmpty());

    try {
      model.sendCpuConsoleConnection(cpus.iterator().next(), cons.iterator().next());
    } catch (ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testSendCpuConsolePrivate() {
    Collection<ConsoleData> cons = model.getConfigData().getConfigDataManager().getActiveConsoles();
    Collection<CpuData> cpus = model.getConfigData().getConfigDataManager().getActiveCpus();
    assertTrue(!cons.isEmpty());
    assertTrue(!cpus.isEmpty());

    try {
      model.sendCpuConsolePrivate(cpus.iterator().next(), cons.iterator().next());
    } catch (ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testSendCpuConsoleBlock() {
    try {
      model.sendCpuConsoleBlock();
    } catch (ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testSendCpuConsoleBlockMapOfConsoleDataCpuDataMapOfConsoleDataCpuData() {
    Collection<ConsoleData> cons = model.getConfigData().getConfigDataManager().getActiveConsoles();
    Collection<CpuData> cpus = model.getConfigData().getConfigDataManager().getActiveCpus();
    assertTrue(!cons.isEmpty());
    assertTrue(!cpus.isEmpty());
    java.util.Map<ConsoleData, CpuData> map = new HashMap<>();
    map.put(cons.iterator().next(), cpus.iterator().next());
    try {
      model.sendCpuConsoleBlock(map, Collections.emptyMap());
      model.sendCpuConsoleBlock(Collections.emptyMap(), map);
      model.sendCpuConsoleBlock(Collections.emptyMap(), Collections.emptyMap(), map);
    } catch (ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testSendDisconnectPorts() {
    try {
      model.sendDisconnectPorts();
    } catch (ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  /**
   * 获得序列号.
   */
  @Test
  public void testGetSerial() {
    try {

      String serial = model.getSerial((byte) 0, (byte) 0);
      System.out.println("cpu serial:" + serial);

      serial = model.getSerial((byte) 0, (byte) 1);
      System.out.println("matrix serial:" + serial);

      model.getSwitchModuleData().reloadModules();
      for (ModuleData moduleData : model.getSwitchModuleData().getModuleDatas()) {
        if (!moduleData.isStatusAvailable() || moduleData.getOid() == 0) {
          continue;
        }
        String key = moduleData.getOid() + "_0_0";
        System.out
            .println(key + ":" + model.getSerial((byte) moduleData.getOid(), (byte) 0));
      }
    } catch (Exception e) {
      fail("expect no exception");
    }
  }

  /**
   * 获取模块名称.
   */
  @Test
  public void testGetExtInfo() {
    try {

      Collection<ExtenderData> extenderDatas = model.getConfigDataManager().getActiveExtenders();
      for (ExtenderData extenderData : extenderDatas) {
        if (extenderData.getPort() <= 0) {
          continue;
        }
        byte level1 = (byte) Utilities.getLevel1(model, extenderData);
        byte level2 = (byte) Utilities.getLevel2(model, extenderData);
        String key = level1 + "_" + level2;
        System.out.print(key + ":");
        // System.out.println(model.getExtName(level1, level2));
        System.out.println(
            Arrays.toString(model.getController().getExtId(level1, level2, (byte) 1)));
      }
    } catch (Exception e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testGetVp6ExtInfo() {
    try {
      for (VpConsoleData vpConsoleData : model.getConfigDataManager().getActiveVpconsolses()) {
        if (!vpConsoleData.isStatusOnline()) {
          continue;
        }
        ExtenderData extenderData = vpConsoleData.getInPort(0).getExtenderData(0);
        byte level1 = (byte) Utilities.getLevel1(model, extenderData);
        byte level2 = (byte) Utilities.getLevel2(model, extenderData);
        String key = level1 + "_" + level2;
        System.out.print(key + ":");
        // System.out.println(model.getExtName(level1, level2));
        model.getController().getExtInfo(level1, level2);
      }
    } catch (Exception e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }


  /**
   * 检查连接.
   */
  @Test
  public void testCheckConnectivity() {
    try {
      model.checkConnectivity();
    } catch (Exception e) {
      e.printStackTrace();
      fail("expect no exception");
    }

  }

  /**
   * 判断是否连接.
   */
  @Test
  public void testIsConnected() {
    try {
      boolean connected = model.isConnected();
      assertTrue(connected);
    } catch (Exception e) {
      e.printStackTrace();
      fail("expect no exception");
    }

  }

  /**
   * 管理员用户.
   */
  @Test
  public void testIsAdminUser() {
    try {
      boolean user = model.isAdminUser("admin");
      assertTrue(user);
    } catch (Exception e) {
      e.printStackTrace();
      fail("expect no exception");
    }


  }

  /**
   * 判断用户是否有存在.
   */
  @Test
  public void testUserExists() {
    try {
      boolean b = model.userExists("admin");
      assertTrue(b);
    } catch (Exception e) {
      e.printStackTrace();
      fail("expect no exception");
    }

  }

  /**
   * 本地常量.
   */
  @Test
  public void testHasLocalChanges() {
    try {
      boolean changes = model.hasLocalChanges();
      assertTrue(changes);
    } catch (Exception e) {
      e.printStackTrace();
      fail("expect no exception");
    }

  }

  /**
   * 保存需求.
   */
  @Test
  public void testRequiresSave() {
    try {
      boolean save = model.requiresSave();
      assertTrue(save);
    } catch (Exception e) {
      e.printStackTrace();
      fail("expect no exception");
    }

  }

  @Test
  public void testVpBackup() {
    try {
      VideoWallGroupData groupData = new VideoWallGroupData();
      int length = 5000;
      byte[] testdata = new byte[length];
      int size = groupData.size();
      for (int i = 0; i < length; i++) {
        testdata[i] = (byte) (i & 0xff);
      }
      for (int i = 0; i < size - length; i += length) {
        model.getController().setVpBackup(i, testdata);
        byte[] result = model.getController().getVpBackup(i, length);
        for (int j = 0; j < length; j++) {
          assertEquals(testdata[j], result[j]);
        }
      }

    } catch (Exception e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testVideoWallData() {
    try {
      VideoWallData videoWallData = new VideoWallData();
      videoWallData.setRows(1);
      videoWallData.setColumns(2);
      videoWallData.setVideoCnt(2);
      videoWallData.setResolutionWidth(1920);
      videoWallData.setResolutionHeight(1080);
      CompleteVideoData completeVideoData = videoWallData.getCompleteVideoData(0);

      completeVideoData.setLeft(33);
      completeVideoData.setTop(34);
      completeVideoData.setWidth(35);
      completeVideoData.setHeight(36);
      completeVideoData.setAlpha(0.37);

      completeVideoData = videoWallData.getCompleteVideoData(1);
      completeVideoData.setLeft(38);
      completeVideoData.setTop(39);
      completeVideoData.setWidth(40);
      completeVideoData.setHeight(41);
      completeVideoData.setAlpha(0.42);

      for (int i = 0; i < 4; i++) {
        model.getVpDataModel().sendVideoWallData(i, videoWallData);

      }

      model.getVpDataModel().reloadVideoWallGroup();

      for (int i = 0; i < 4; i++) {
        VideoWallData newData = model.getVpDataModel().getVideoWallData(i);
        assertNotEquals(videoWallData, newData);
        assertEquals(videoWallData.toString(), newData.toString());
      }


    } catch (Exception e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testGetVpconResolution() {
    try {
      for (int i = 0; i < model.getConfigMetaData().getVpconsoleCountActive(); i++) {
        VpConsoleData vpConsoleData = model.getConfigData().getVpConsoleData(i);
        model.getVpconResolution(vpConsoleData);
        System.out.println(vpConsoleData.getOutResolution(0).getWidth());
        System.out.println(vpConsoleData.getOutResolution(0).getHeight());
        System.out.println(vpConsoleData.getInResolution(0).getWidth());
        System.out.println(vpConsoleData.getInResolution(0).getHeight());
        System.out.println(vpConsoleData.getInResolution(1).getWidth());
        System.out.println(vpConsoleData.getInResolution(1).getHeight());
        System.out.println();

      }
    } catch (Exception ex) {
      ex.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testGetVpconConfigData() {
    System.setProperty("no-fx-mode", "1");
    Collection<VpConsoleData> vpConsoleDatas =
        model.getConfigData().getConfigDataManager().getActiveVpconsolses();
    try {
      for (VpConsoleData vpConsoleData : vpConsoleDatas) {
        if (!vpConsoleData.isStatusOnline()) {
          continue;
        }
        System.out.println(vpConsoleData.getName() + "'s data:");
        model.getVpconConfigData(vpConsoleData);
        VpConConfigData vpConConfigData = vpConsoleData.getConfigData();
        vpConConfigData.print();
        System.out.println();
      }

    } catch (BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }

  }

  @Test
  public void testSetCpuConsoleMatrix() {
    try {
      model.getController().getConfigData();
      Collection<ConsoleData> consoleDatas = model.getConfigDataManager().getActiveConsoles();
      Collection<CpuData> cpuDatas = model.getConfigDataManager().getActiveCpus();
      if (!consoleDatas.isEmpty() && !cpuDatas.isEmpty()) {
        consoleDatas.iterator().next().setCpuData(cpuDatas.iterator().next());
      }
      model.getController().setCpuConsoleMatrix();
      model.getController().getCpuConsoleMatrix();
      if (!consoleDatas.isEmpty() && !cpuDatas.isEmpty()) {
        assertEquals(cpuDatas.iterator().next(), consoleDatas.iterator().next().getCpuData());
      }
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testSetAllOff() {
    try {
      model.getController().getConfigData();
      Collection<ConsoleData> consoleDatas = model.getConfigDataManager().getActiveConsoles();
      Collection<CpuData> cpuDatas = model.getConfigDataManager().getActiveCpus();
      if (!consoleDatas.isEmpty() && !cpuDatas.isEmpty()) {
        Map<ConsoleData, CpuData> map = new HashMap<>();
        map.put(consoleDatas.iterator().next(), cpuDatas.iterator().next());
        model.getController().setCpusConnectToCons(map);
      }
      model.getController().setAllOff();

    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testGetCpuConnectToCon() {
    try {
      Collection<ConsoleData> consoleDatas = model.getConfigDataManager().getActiveConsoles();
      for (ConsoleData consoleData : consoleDatas) {
        CpuData cpuData = model.getController().getCpuConnectToCon(consoleData);
        String cpuid = "null";
        if (cpuData != null) {
          cpuid = "" + cpuData.getId();
        }
        System.out.printf("con(%d) connects to cpu(%s)%n", consoleData.getId(), cpuid);
      }
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testSetCpuConnectToCon() {
    try {
      Collection<ConsoleData> consoleDatas = model.getConfigDataManager().getActiveConsoles();
      Collection<CpuData> cpuDatas = model.getConfigDataManager().getActiveCpus();
      if (!consoleDatas.isEmpty() && !cpuDatas.isEmpty()) {
        model.getController().setCpuConnectToCon(consoleDatas.iterator().next(),
            cpuDatas.iterator().next());
      } else {
        fail("expect con cpu.");
      }
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testGetCpusConnectToCons() {
    try {
      Collection<ConsoleData> consoleDatas = model.getConfigDataManager().getActiveConsoles();
      Map<ConsoleData, CpuData> result = model.getController().getCpusConnectToCons(consoleDatas);
      for (Map.Entry<ConsoleData, CpuData> entry : result.entrySet()) {
        String conId = "null";
        if (entry.getKey() != null) {
          conId = entry.getKey().getId() + "";
        }

        String cpuId = "null";
        if (entry.getValue() != null) {
          cpuId = entry.getValue().getId() + "";
        }
        System.out.printf("con(%s) connects to cpu(%s)%n", conId, cpuId);
      }
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testSetCpusConnectToCons() {
    try {
      Collection<ConsoleData> consoleDatas = model.getConfigDataManager().getActiveConsoles();
      Collection<CpuData> cpuDatas = model.getConfigDataManager().getActiveCpus();
      if (!consoleDatas.isEmpty() && !cpuDatas.isEmpty()) {
        Map<ConsoleData, CpuData> map = new HashMap<>();
        map.put(consoleDatas.iterator().next(), cpuDatas.iterator().next());
        model.getController().setCpusConnectToCons(map);
      }
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testGetCpuConnectToConBidirectional() {
    try {
      Collection<ConsoleData> consoleDatas = model.getConfigDataManager().getActiveConsoles();
      for (ConsoleData consoleData : consoleDatas) {
        CpuData cpuData = model.getController().getCpuConnectToConBidirectional(consoleData);
        String cpuid = "null";
        if (cpuData != null) {
          cpuid = "" + cpuData.getId();
        }
        System.out.printf("con(%d) connects to cpu(%s) full%n", consoleData.getId(), cpuid);
      }
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testGetCpusConnectToConsBidirectional() {
    try {
      Collection<ConsoleData> consoleDatas = model.getConfigDataManager().getActiveConsoles();
      Map<ConsoleData, CpuData> result =
          model.getController().getCpusConnectToConsBidirectional(consoleDatas);
      for (Map.Entry<ConsoleData, CpuData> entry : result.entrySet()) {
        String conId = "null";
        if (entry.getKey() != null) {
          conId = entry.getKey().getId() + "";
        }

        String cpuId = "null";
        if (entry.getValue() != null) {
          cpuId = entry.getValue().getId() + "";
        }
        System.out.printf("con(%s) connects to cpu(%s)%n", conId, cpuId);
      }
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }


  @Test
  public void testGetScenarioList() {
    try {
      Collection<ScenarioDescribeData> datas = model.getController().getScenarioList();
      System.out.println("count:" + datas.size());
      for (ScenarioDescribeData data : datas) {
        System.out.print(data.getIndex() + "\t");
        System.out.print(data.getName() + "\t");
        System.out.print(data.getHorizCount() + "\t");
        System.out.println(data.getVertCount());
      }


    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testGetScenarioData() {
    try {
      model.getController().getScenarioData(0, 0x01);
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testStartScenarioByMyself() {
    for (int i = 0; i < 1; i++) {
      model.getVpDataModel().startScenarioLocal(i);
      try {
        Thread.sleep(3000);
      } catch (InterruptedException e) {
        e.printStackTrace();
      }
    }
  }

  @Test
  public void testStartScenario() {
    try {
      // Collection<ScenarioDescribeData> datas = model.getController().getScenarioList();
      // for (int i = 0; i < 10; i++) {
      // for (ScenarioDescribeData data : datas) {
      // model.getController().startScenario(data.getIndex(), 0);
      // Thread.sleep(3000);
      // }
      // }
      for (int i = 0; i < 6; i++) {
        model.getController().startScenario(i, 0);
        Thread.sleep(3000);
      }
    } catch (DeviceConnectionException | ConfigException | BusyException | InterruptedException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }

  @Test
  public void testDeleteScenario() {
    try {
      model.getController().deleteScenarioData(0);
      Collection<ScenarioDescribeData> datas = model.getController().getScenarioList();
      System.out.println(datas.size());
    } catch (DeviceConnectionException | ConfigException | BusyException e) {
      e.printStackTrace();
      fail("expect no exception");
    }
  }
}
