package com.mc.tool.caesar.api.utils;

import java.beans.IndexedPropertyChangeEvent;
import java.util.Arrays;

/**
 * .
 */
public final class MultiIndexedPropertyChangeEvent extends IndexedPropertyChangeEvent {

  private static final long serialVersionUID = 3756123497514369L;
  private final int[] indexes;

  /**
   * MultiIndexedPropertyChangeEvent.
   */
  public MultiIndexedPropertyChangeEvent(Object source, String propertyName, Object oldValue,
      Object newValue, int... indexes) {
    super(source, propertyName, oldValue, newValue, indexes[0]);
    assert indexes.length > 1;

    this.indexes = Arrays.copyOfRange(indexes, 1, indexes.length);
  }

  public int[] getIndexes() {
    return Arrays.copyOf(this.indexes, this.indexes.length);
  }
}

