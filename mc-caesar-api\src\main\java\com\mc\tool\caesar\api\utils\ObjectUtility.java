package com.mc.tool.caesar.api.utils;

import java.lang.reflect.Array;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * .
 */
public final class ObjectUtility {

  /**
   * 把object格式化为字符串.
   *
   * @param object object
   */
  public static String formatValue(Object object) {
    StringBuilder sb = new StringBuilder(512);

    formatValue(sb, object, 1);

    return sb.toString();
  }

  private static void formatValue(StringBuilder sb, Object object, int indent) {
    if (null == object) {
      sb.append("null");
    } else if (object.getClass().isArray()) {
      int length = Array.getLength(object);

      sb.append(object.getClass().getComponentType().getName());
      sb.append('[');
      sb.append(length);
      sb.append("]@");
      sb.append(Integer.toHexString(System.identityHashCode(object)));
      sb.append(" {");

      String indexFormat = "[%" + Double.valueOf(1.0D + Math.log10(length)).intValue() + "d] = ";
      for (int i = 0; i < length; i++) {
        sb.append('\n');
        for (int idnt = 0; idnt < indent + 1; idnt++) {
          sb.append('\t');
        }
        sb.append(String.format(indexFormat, i));
        formatValue(sb, Array.get(object, i), indent + 1);
      }
      sb.append('\n');
      for (int idnt = 0; idnt < indent; idnt++) {
        sb.append('\t');
      }
      sb.append('}');
    } else if (object instanceof Collection) {
      int count = 0;

      sb.append(object.getClass().getName()).append('@');
      sb.append(Integer.toHexString(System.identityHashCode(object)));
      sb.append(" {");
      for (Object o : (Collection) object) {
        sb.append('\n');
        for (int idnt = 0; idnt < indent + 1; idnt++) {
          sb.append('\t');
        }
        sb.append('(');
        if (object instanceof List) {
          sb.append(count++);
        } else {
          sb.append('#');
        }
        sb.append(") = ");

        formatValue(sb, o, indent + 1);
      }
      sb.append('\n');
      for (int idnt = 0; idnt < indent; idnt++) {
        sb.append('\t');
      }
      sb.append('}');
    } else if (object instanceof Map) {
      sb.append(object.getClass().getName()).append('@');
      sb.append(Integer.toHexString(System.identityHashCode(object)));
      sb.append(" {");
      for (Map.Entry<?, ?> entry : (Iterable<Map.Entry<?, ?>>) ((Map) object).entrySet()) {
        sb.append('\n');
        for (int idnt = 0; idnt < indent + 1; idnt++) {
          sb.append('\t');
        }
        sb.append(entry.getClass().getName()).append(" {\n");
        for (int idnt = 0; idnt < indent + 2; idnt++) {
          sb.append('\t');
        }
        sb.append("  key = ");

        formatValue(sb, entry.getKey(), indent + 1);

        sb.append('\n');
        for (int idnt = 0; idnt < indent + 2; idnt++) {
          sb.append('\t');
        }
        sb.append("value = ");

        formatValue(sb, entry.getValue(), indent + 1);

        sb.append('\n');
        for (int idnt = 0; idnt < indent + 1; idnt++) {
          sb.append('\t');
        }
        sb.append('}');
      }
      sb.append('\n');
      for (int idnt = 0; idnt < indent; idnt++) {
        sb.append('\t');
      }
      sb.append('}');
    } else {
      sb.append('[').append(object.getClass().getName()).append("]@");
      sb.append(Integer.toHexString(System.identityHashCode(object)));
      sb.append(' ').append(object);
    }
  }
}

