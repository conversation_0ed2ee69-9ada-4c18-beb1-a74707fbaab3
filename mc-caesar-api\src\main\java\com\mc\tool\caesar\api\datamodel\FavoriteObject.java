package com.mc.tool.caesar.api.datamodel;

import java.util.Collection;

/**
 * .
 */
public interface FavoriteObject {

  String PROPERTY_FAVORITE = "FavoriteObject.Favorite";

  int getFavorite(int paramInt);

  void setFavorite(int paramInt1, int paramInt2);

  CpuData getFavoriteData(int paramInt);

  Collection<CpuData> getFavoriteDatas();

  void setFavoriteData(int paramInt, CpuData paramCpuData);

  int[] getFavoriteArray();
}

