package com.mc.tool.caesar.vpm.gui;

import com.mc.tool.caesar.vpm.GuiTestConstants;
import javafx.scene.Node;
import org.hamcrest.core.StringContains;
import org.junit.Assert;
import org.junit.Test;
import org.testfx.api.FxRobot;
import org.testfx.assertions.api.Assertions;

/**
 * .
 */
public class AppGuiTest extends AppGuiTestBase {

  @Override
  public void beforeAll() {}

  @Test
  public void testConfigDialog() {
    clickOn("#menuHelp");
    clickOn("#menuConfig");

    clickOn("#currentLanguage-form-editor");
    clickOn("English");
    clickOn("#currentLanguage-form-editor");
    clickOn("中文");
    clickOn(GuiTestConstants.BUTTON_APPLY);
    clickOn(GuiTestConstants.BUTTON_CONFIRM);
  }

  @Test
  public void testAboutDialog() {
    clickOn("#menuHelp");
    clickOn("#menuAbout");
    Assertions.assertThat(this.lookup("#companyLabel").queryLabeled()).isVisible();
    Assertions.assertThat(this.lookup("#versionLabel").queryLabeled()).isVisible();
    Assertions.assertThat(this.lookup("#javaVersionLabel").queryLabeled())
        .hasText(new StringContains("1.8"));
    Assertions.assertThat(this.lookup("#copyRightLabel").queryLabeled()).isVisible();
    Assertions.assertThat(this.lookup("#fullNameLabel").queryLabeled()).isVisible();
    Assertions.assertThat(this.lookup("#phoneBox").queryParent()).isVisible();
    clickOn(GuiTestConstants.BUTTON_CONFIRM);
  }

  @Test
  public void testUploadConfig() {
    login();

    Node menu = lookup(".menu").lookup("配置").query();
    clickOn(menu);
    clickOn(GuiTestConstants.UPLOAD_CONFIG);
    clickOn(GuiTestConstants.WIZARD_NEXT);
    FxRobot window = targetWindow(GuiTestConstants.DIALOG_UPLOAD);
    window.clickOn(GuiTestConstants.TEXT_UPLOAD_IP);
    eraseText(20);
    write(GuiTestConstants.INPUT_IP);
    window.clickOn(GuiTestConstants.TEXT_UPLOAD_USER);
    write(GuiTestConstants.INPUT_USER);
    window.clickOn(GuiTestConstants.TEXT_UPLOAD_PWD);
    write(GuiTestConstants.INPUT_PWD);
    window.clickOn(GuiTestConstants.BUTTON_VALIDATE);
    window.clickOn(GuiTestConstants.WIZARD_NEXT);
    //
    window.clickOn(GuiTestConstants.CHECKBOX_UPLOAD_ACTIVATE);
    clickOn(GuiTestConstants.BUTTON_CONFIRM);
    window.clickOn(lookupFirstTableCell(window, 0));
    clickOn(GuiTestConstants.BUTTON_UPLOAD_BTN);
    try {
      Thread.sleep(2000);
    } catch (InterruptedException e) {
      Assert.fail();
    }

    clickOn(GuiTestConstants.BUTTON_CONFIRM);
    clickOn(GuiTestConstants.BUTTON_FINISH);
  }

  @Test
  public void testClose() {
    clickOn("#menuFile");
    clickOn("#menuFile");
  }
}
