package com.mc.tool.caesar.api.version.v3x;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.ResolutionData;
import com.mc.tool.caesar.api.datamodel.VersionSet;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgError;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.version.ApiDataConverter;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.xml.bind.DatatypeConverter;

/**
 * .
 */
public class ExtenderDataConverter3x implements ApiDataConverter<ExtenderData> {

  private static final Logger LOG = Logger.getLogger(ExtenderDataConverter3x.class.getName());
  public static final int RESERVE1_CNT = 10;
  private final int dataSize;

  public ExtenderDataConverter3x(int dataSize) {
    this.dataSize = dataSize;
  }

  @Override
  public void readData(ExtenderData data, CfgReader cfgReader) throws ConfigException {
    final int startAvailable = cfgReader.available();
    String name = cfgReader.readString(CaesarConstants.NAME_LEN);
    data.setName(name);
    int status = cfgReader.readInteger();
    data.setStatus(status);
    int port = cfgReader.read2ByteValue();
    data.setPort(port);

    int remotePort = cfgReader.read2ByteValue();
    data.setRemotePort(remotePort);

    int rdport = cfgReader.read2ByteValue();
    data.setRdPort(rdport);

    int remoteRdPort = cfgReader.read2ByteValue();
    data.setRdRemotePort(remoteRdPort);

    int id = cfgReader.readInteger();
    data.setId(id);
    int type = cfgReader.readInteger();
    data.setType(type);
    int cpuCon = cfgReader.read2ByteValue();
    data.setCpuCon(cpuCon);
    cfgReader.readByteArray(RESERVE1_CNT);
    // 状态信息
    data.getExtenderStatusInfo().readData(cfgReader);
    // 2个分辨率
    for (int i = 0; i < 2; i++) {
      try {
        ResolutionData resolutionData = new ResolutionData();
        resolutionData
            .read(new ByteArrayInputStream(cfgReader.readByteArray(resolutionData.size())));
        data.setResolution(i, resolutionData);
      } catch (IOException exception) {
        LOG.log(Level.WARNING, "Fail to read resolution data!", exception);
        throw new ConfigException(CfgError.IO, exception.getMessage());
      }
    }
    // 版本
    VersionSet versionSet = new VersionSet();
    versionSet.readData(cfgReader);
    if (versionSet.getHwVersionDef().getMasterVersion() >= 5) {  // 四期外设版本号有3个数字
      versionSet.getAppVersion().setHasPatchVersion(true);
    }
    data.setVersion(versionSet);
    // SN
    long value = cfgReader.readLong();
    data.setSerial(DatatypeConverter.printHexBinary(
        Long.toUnsignedString(value).getBytes(StandardCharsets.UTF_8)));

    int endAvailable = cfgReader.available();
    int readedSize = startAvailable - endAvailable;
    int reservedSize = dataSize - readedSize;
    cfgReader.readByteArray(reservedSize);
  }

  @Override
  public void writeData(ExtenderData data, CfgWriter cfgWriter) throws ConfigException {
    final long startSize = cfgWriter.getSize();
    cfgWriter.writeString(data.getName(), CaesarConstants.NAME_LEN);
    cfgWriter.writeInteger(data.getStatus());
    cfgWriter.write2ByteSmallEndian(data.getPort());
    cfgWriter.write2ByteSmallEndian(data.getRemotePort());
    cfgWriter.write2ByteSmallEndian(data.getRdPort());
    cfgWriter.write2ByteSmallEndian(data.getRdRemotePort());
    cfgWriter.writeInteger(data.getId());
    cfgWriter.writeInteger(data.getType());
    cfgWriter.write2ByteSmallEndian(data.getCpuCon());

    //
    cfgWriter.writeByteArray(new byte[RESERVE1_CNT]);
    // 状态信息
    data.getExtenderStatusInfo().writeData(cfgWriter);
    // 分辨率
    for (int i = 0; i < 2; i++) {
      ResolutionData resolutionData = data.getResolution(i);
      ByteArrayOutputStream baos = new ByteArrayOutputStream();
      try {
        resolutionData.write(baos);
        cfgWriter.writeByteArray(baos.toByteArray());
      } catch (IOException exception) {
        LOG.log(Level.WARNING, "Fail to write resolution data!", exception);
        throw new ConfigException(CfgError.IO, exception.getMessage());
      }
    }
    // 版本
    data.getVersion().writeData(cfgWriter);
    // SN

    String serial = new String(data.getSerialBytes(), StandardCharsets.US_ASCII);

    long value;
    try {
      value = Long.parseLong(serial);
    } catch (RuntimeException exception) {
      value = 0;
    }
    cfgWriter.writeLong(value);

    long endSize = cfgWriter.getSize();
    int writedSize = (int) (endSize - startSize);
    int reservedSize = dataSize - writedSize;
    cfgWriter.writeByteArray(new byte[reservedSize]);
  }
}
