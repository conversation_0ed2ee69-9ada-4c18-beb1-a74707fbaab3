package com.mc.tool.caesar.api.datamodel;

import java.beans.PropertyChangeListener;
import java.beans.PropertyChangeSupport;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public class GridData {

  public static final String PROPERTY_HOSTNAME = "GridMasterData.hostname";
  public static final String PROPERTY_DEVICENAME = "GridMasterData.devicename";
  public static final String PROPERTY_INDEX = "GridMasterData.index";
  public static final String PROPERTY_GRIDNAME = "GridMasterData.gridname";
  public static final String PROPERTY_VERSION = "GridMasterData.version";
  public static final String PROPERTY_ADDRESS = "GridMasterData.address";
  public static final String PROPERTY_MACADDRESS = "GridMasterData.macaddress";
  public static final String PROPERTY_ACTIVE = "GridMasterData.active";
  public static final String PROPERTY_BACKUP = "GridMasterData.backup";
  private byte[] hostname;
  private int index; // master矩阵的索引，从1开始
  private String deviceName;
  private String gridname;
  private VersionSet version = new VersionSet();
  private byte[] address;
  private byte[] macaddress;

  //三期新增:双主控运行状态
  @Getter
  @Setter
  private boolean hasMatrixInfo = false;
  @Getter
  private boolean active; //指示当前主控板处于激活状态
  @Getter
  private boolean backup; //指示当前主控板是备份主控板

  private PropertyChangeSupport pcs = new PropertyChangeSupport(this);

  public void addPropertyChangeListener(String name, PropertyChangeListener listener) {
    this.pcs.addPropertyChangeListener(name, listener);
  }

  public void removePropertyChangeListener(String name, PropertyChangeListener listener) {
    this.pcs.removePropertyChangeListener(name, listener);
  }

  /**
   * .
   */
  public byte[] getHostname() {
    if (this.hostname != null) {
      return this.hostname.clone();
    }
    return new byte[0];

  }

  /**
   * .
   */
  public void setHostname(byte[] hostname) {
    byte[] oldValue = this.hostname;
    this.hostname = hostname.clone();
    this.pcs.firePropertyChange(GridData.PROPERTY_HOSTNAME, oldValue, hostname);
  }

  public int getIndex() {
    return this.index;
  }

  /**
   * .
   */
  public void setIndex(int index) {
    int oldValue = this.index;
    this.index = index;
    this.pcs.firePropertyChange(GridData.PROPERTY_INDEX, oldValue, index);
  }

  public String getDeviceName() {
    return this.deviceName;
  }

  /**
   * .
   */
  public void setDeviceName(String devicename) {
    String oldValue = this.deviceName;
    this.deviceName = devicename;
    this.pcs.firePropertyChange(GridData.PROPERTY_DEVICENAME, oldValue, devicename);
  }

  public String getGridname() {
    return this.gridname;
  }

  /**
   * .
   */
  public void setGridname(String gridname) {
    String oldValue = this.gridname;
    this.gridname = gridname;
    this.pcs.firePropertyChange(GridData.PROPERTY_GRIDNAME, oldValue, this.deviceName);
  }

  public VersionSet getVersion() {
    return this.version;
  }

  /**
   * .
   */
  public void setVersion(VersionSet version) {
    VersionSet oldValue = this.version;
    this.version = version;
    this.pcs.firePropertyChange(GridData.PROPERTY_VERSION, oldValue, this.deviceName);
  }

  /**
   * .
   */
  public byte[] getAddress() {
    if (this.address != null) {
      return this.address.clone();
    }
    return new byte[0];
  }

  /**
   * .
   */
  public void setAddress(byte[] address) {
    byte[] oldValue = this.address;
    this.address = address.clone();
    this.pcs.firePropertyChange(GridData.PROPERTY_ADDRESS, oldValue, address);
  }

  /**
   * .
   */
  public byte[] getMacAddress() {
    if (this.macaddress != null) {
      return this.macaddress.clone();
    }
    return new byte[0];
  }

  /**
   * .
   */
  public void setMacAddress(byte[] macaddress) {
    byte[] oldValue = this.macaddress;
    this.macaddress = macaddress.clone();
    this.pcs.firePropertyChange(GridData.PROPERTY_MACADDRESS, oldValue, macaddress);
  }

  /**
   * 设置是否为激活主控板卡.
   */
  public void setActive(boolean active) {
    boolean oldValue = this.active;
    this.active = active;
    this.pcs.firePropertyChange(GridData.PROPERTY_ACTIVE, oldValue, active);
  }

  /**
   * 设置是否为备用主控板卡.
   */
  public void setBackup(boolean backup) {
    boolean oldValue = this.backup;
    this.backup = backup;
    this.pcs.firePropertyChange(GridData.PROPERTY_BACKUP, oldValue, backup);
  }
}

