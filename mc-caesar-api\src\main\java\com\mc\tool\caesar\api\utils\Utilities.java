package com.mc.tool.caesar.api.utils;

import com.mc.tool.caesar.api.CaesarConfigDataModel;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarConstants.Module;
import com.mc.tool.caesar.api.CaesarControllerConstants;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.CaesarSwitchDataModelManager;
import com.mc.tool.caesar.api.CommunicationBuilder;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.communication.IoController;
import com.mc.tool.caesar.api.datamodel.AccessControlObject;
import com.mc.tool.caesar.api.datamodel.ConfigData;
import com.mc.tool.caesar.api.datamodel.ConfigData.IoMode;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.MatrixData;
import com.mc.tool.caesar.api.datamodel.MatrixDefinitionData;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.datamodel.extargs.ExtArgObject;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.interfaces.Oidable;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.InetAddress;
import java.net.InterfaceAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import javafx.util.Pair;

/**
 * .
 */
public final class Utilities {

  private static final Logger LOG = Logger.getLogger(Utilities.class.getName());

  /**
   * setBits.
   */
  public static int setBits(int value, boolean enabled, int... bits) {
    int bitComposite = getBitComposite(bits);
    if (areBitsSet(value, bitComposite) != enabled) {
      return enabled ? value | bitComposite : value & (bitComposite ^ 0xFFFFFFFF);
    }
    return value;
  }

  public static boolean areBitsSet(int value, int bits) {
    return areBitsSet(value, false, bits);
  }

  public static boolean areBitsSet(int value, int... bits) {
    return areBitsSet(value, false, getBitComposite(bits));
  }

  /**
   * areBitsSet.
   */
  public static boolean areBitsSet(int value, boolean allBitsSet, int bits) {
    if (allBitsSet) {
      return bits == (value & bits);
    }
    return 0 != (value & bits);
  }

  public static boolean areBitsSet(int value, boolean allBitsSet, int... bits) {
    return areBitsSet(value, allBitsSet, getBitComposite(bits));
  }

  /**
   * getBitComposite.
   *
   * @param bits bits
   */
  public static int getBitComposite(int... bits) {
    int value = 0;
    for (int bit : bits) {
      value |= bit;
    }
    return value;
  }

  /**
   * getObjects.
   *
   * @param iterable iterable
   * @param oids     oids
   */
  public static <T extends Oidable> List<T> getObjects(Iterable<T> iterable, int... oids) {
    Map<Integer, T> oidMap = getOidMap((null == oids) || (0 == oids.length) ? null : iterable);
    if (oidMap.isEmpty()) {
      return Collections.emptyList();
    }
    List<T> tl = new ArrayList<>();
    for (int oid : oids) {
      tl.add(oidMap.get(oid));
    }
    return CollectionUtil.getListWithoutNull(tl);
  }

  /**
   * getOidMap.
   *
   * @param iterable iterable
   */
  public static <T extends Oidable> Map<Integer, T> getOidMap(Iterable<T> iterable) {
    if (null == iterable) {
      return Collections.emptyMap();
    }
    Map<Integer, T> oidMap = new HashMap<>();
    for (T object : iterable) {
      if (null != object) {
        oidMap.put(object.getOid(), object);
      }
    }
    if (oidMap.isEmpty()) {
      return Collections.emptyMap();
    }
    return oidMap;
  }

  /**
   * 获取设备的模块的id在级联矩阵中的偏移.
   *
   * @param model model
   */
  public static int getModuleIdOffset(CaesarConfigDataModel model) {
    boolean isGridEnabled = model.getConfigData().getSystemConfigData().isMatrixGridEnabled();
    String device;
    if (isGridEnabled) {
      device = model.getConfigData().getSystemConfigData().getSystemData().getDevice();
      for (MatrixData matrixData : model.getConfigData().getMatrixDatas()) {
        if (device.equals(matrixData.getDevice())) {
          return matrixData.getFirstModule() - 1;
        }
      }
    }
    return 0;
  }

  /**
   * getMatrixOffset.
   *
   * @param model model
   */
  public static int getMatrixOffset(CaesarConfigDataModel model) {
    boolean isGridEnabled = model.getConfigData().getSystemConfigData().isMatrixGridEnabled();
    String device;
    if (isGridEnabled) {
      device = model.getConfigData().getSystemConfigData().getSystemData().getDevice();
      for (MatrixData matrixData : model.getConfigData().getMatrixDatas()) {
        if (device.equals(matrixData.getDevice())) {
          return matrixData.getOid();
        }
      }
    }
    return 0;
  }

  /**
   * getPortOffset.
   *
   * @param model model
   */
  public static int getPortOffset(CaesarConfigDataModel model) {
    boolean isGridEnabled = model.getConfigData().getSystemConfigData().isMatrixGridEnabled();
    String device;
    if (isGridEnabled) {
      device = model.getConfigData().getSystemConfigData().getSystemData().getDevice();
      for (MatrixData matrixData : model.getConfigData().getMatrixDatas()) {
        if (device.equals(matrixData.getDevice())) {
          return matrixData.getFirstPort() - 1;
        }
      }
    }
    return 0;
  }

  /**
   * 获取端口的所在板卡与位置.
   *
   * @param model model
   * @param port  端口值，从1开始
   */
  public static Pair<Integer, Integer> getPortLevel(CaesarSwitchDataModel model, int port) {
    if (model == null) {
      return new Pair<>(-1, -1);
    }
    Collection<MatrixDefinitionData> matrices = getActiveMatrices(model);
    for (MatrixDefinitionData mdd : matrices) {
      if (port >= mdd.getFirstPort() && port <= mdd.getLastPort()) {
        int localIndex = port - mdd.getFirstPort();
        int portsPerIo = getPortsPerIo(model, mdd);
        int level1 = localIndex / portsPerIo + mdd.getFirstModule();
        int level2 = localIndex % portsPerIo + 1;
        return new Pair<>(level1, level2);
      }
    }

    return new Pair<>(-1, -1);
  }

  private static int getPortsPerIo(CaesarSwitchDataModel model, MatrixDefinitionData mdd) {
    int portsPerIo = Module.DEFAULT_PORTS;
    try {
      portsPerIo = getExternalModel(model, mdd.getAddress()).getPortsPerIo();
    } catch (ConfigException | BusyException | RuntimeException exception) {
      LOG.warning("Fail to get external model for " + mdd.getAddress());
    }
    return portsPerIo;
  }

  /**
   * 获取level1+level2所对应的port的值.
   *
   * @param model  model
   * @param level1 level1
   * @param level2 level2
   * @return port的值，从1开始
   */
  public static int getLevelPort(CaesarSwitchDataModel model, int level1, int level2) {
    if (model == null) {
      return -1;
    }
    Collection<MatrixDefinitionData> matrices = getActiveMatrices(model);
    for (MatrixDefinitionData mdd : matrices) {
      if (level1 >= mdd.getFirstModule() && level1 <= mdd.getLastModule()) {
        int moduleOffset = level1 - mdd.getFirstModule();
        return moduleOffset * getPortsPerIo(model, mdd) + mdd.getFirstPort()
            + level2 - 1;
      }
    }
    return -1;
  }

  /**
   * 获取板卡的端口范围.
   *
   * @param model      model
   * @param moduleData moduleData
   * @return firstport与lastport的pair
   */
  public static Pair<Integer, Integer> getModulePortRange(CaesarSwitchDataModel model,
      ModuleData moduleData) {
    if (model == null) {
      return new Pair<>(-1, -1);
    }
    Collection<MatrixDefinitionData> matrices = getActiveMatrices(model);
    int level1 = moduleData.getOid();
    for (MatrixDefinitionData mdd : matrices) {
      if (level1 >= mdd.getFirstModule() && level1 <= mdd.getLastModule()) {
        int moduleOffset = level1 - mdd.getFirstModule();
        int startPort =
            mdd.getFirstPort() + moduleOffset * getPortsPerIo(model, mdd);
        int endPort = startPort + moduleData.getPorts() - 1;
        return new Pair<>(startPort, endPort);
      }
    }
    return new Pair<>(-1, -1);
  }

  /**
   * 获取module对应的matrix.
   *
   * @param model      model
   * @param moduleData moduleData
   * @return 如果有找到，返回对应的matrix，否则返回null
   */
  public static MatrixDefinitionData getMatrixByModule(CaesarSwitchDataModel model,
      ModuleData moduleData) {
    if (model == null) {
      return null;
    }
    Collection<MatrixDefinitionData> matrices = getActiveMatrices(model);
    int level1 = moduleData.getOid();
    for (MatrixDefinitionData mdd : matrices) {
      if (level1 >= mdd.getFirstModule() && level1 <= mdd.getLastModule()) {
        return mdd;
      }
    }
    return null;
  }

  /**
   * 获取外设输入端口的索引.
   *
   * @param extenderData extenderData
   * @return 如果为负数，无效
   */
  public static int getVpconInputIndex(ExtenderData extenderData) {
    if (extenderData.getVpType() == null) {
      return -1;
    }
    return extenderData.getVpType().getBuilder().getVpconInputIndex(extenderData.getId());
  }

  /**
   * 根据vpcon的其中一个extender返回此vpcon的id.
   *
   * @param extenderData extenderData
   */
  public static int getVpconId(ExtenderData extenderData) {
    if (extenderData == null || !extenderData.isVpConType()) {
      return 0;
    }
    if (extenderData.getVpType() == null) {
      return 0;
    }
    return extenderData.getVpType().getBuilder().getVpconId(extenderData.getId(), extenderData.getConsoleData().getId());
  }

  /**
   * isAssignedToConDevice.
   */
  public static boolean isAssignedToConDevice(ExtenderData extenderData) {
    boolean valid = false;
    if (extenderData != null) {
      ConsoleData consoleData = extenderData.getConsoleData();
      if (consoleData != null) {
        for (ExtenderData ed : consoleData.getExtenderDatas()) {
          if (ed.equals(extenderData)) {
            valid = true;
            break;
          }
        }
      }
    }
    return valid;
  }

  /**
   * isAssignedToCpuDevice.
   */
  public static boolean isAssignedToCpuDevice(ExtenderData extenderData) {
    boolean valid = false;
    if (extenderData != null) {
      CpuData cpuData = extenderData.getCpuData();
      if (cpuData != null) {
        for (ExtenderData ed : cpuData.getExtenderDatas()) {
          if (ed.equals(extenderData)) {
            valid = true;
            break;
          }
        }
      }
    }
    return valid;
  }

  /**
   * getExternalRwModel.
   */
  public static CaesarSwitchDataModel getExternalRwModel(CaesarSwitchDataModel switchDataModel,
      int level1, int level2, int level3) throws BusyException {
    Logger log = Logger.getLogger(Utilities.class.getName());
    ConfigData configData = switchDataModel.getConfigData();
    boolean isGridEnabled =
        configData.getSystemConfigData().getMatrixGridData().isMatrixGridEnabled();
    CaesarSwitchDataModel model = switchDataModel;
    int moduleOffset = 0;
    if (isGridEnabled) {
      for (MatrixData md : switchDataModel.getConfigDataManager().getActiveMatrices()) {
        if (level1 >= md.getFirstModule() && level1 <= md.getLastModule()
            || level1 == 0 && level3 == md.getOid()) {
          if (!md.getDevice()
              .equals(configData.getSystemConfigData().getSystemData().getDevice())) {
            try {
              model =
                  getExternalModel(switchDataModel, IpUtil.getAddressString(md.getHostAddress()));
            } catch (ConfigException ex) {
              log.log(Level.SEVERE, ex.getMessage());
            }
          }
          moduleOffset = md.getFirstModule();
          break;
        }
      }
    }
    if (model.getConfigData().getSystemConfigData().isMasterCpu()) {
      if (level1 >= moduleOffset + 36 || level1 == 0 && (level2 & 0xF0) != 0) {
        String slaveAddress = IpUtil.getAddressString(
            model.getConfigData().getSystemConfigData().getNetworkDataCurrent2().getAddress());
        try {
          model = getExternalModel(switchDataModel, slaveAddress);
        } catch (ConfigException ex) {
          log.log(Level.SEVERE, null, ex);
          model = null;
        }
      }
    } else if (model.getConfigData().getSystemConfigData().isSlaveCpu()
        && level1 < moduleOffset + 36 && (level2 & 0xF0) == 0) {
      String masterAddress = IpUtil.getAddressString(
          model.getConfigData().getSystemConfigData().getNetworkDataCurrent1().getAddress());
      try {
        model = getExternalModel(switchDataModel, masterAddress);
      } catch (ConfigException ex) {
        log.log(Level.SEVERE, null, ex);
        model = null;
      }
    }
    if (model == null) {
      log.log(Level.WARNING,
          String.format("(2) %d_%d_%d: no model available", level1, level2, level3));
    }
    return model;
  }

  /**
   * 获取外部的model.
   *
   * @param model   当前的model
   * @param address 需要获取的model的address
   * @return 如果当前model的address跟输入的address一样，返回当前model，否则根据address来查找model
   * @throws BusyException   BusyException
   * @throws ConfigException ConfigException
   */
  public static CaesarSwitchDataModel getExternalModel(CaesarSwitchDataModel model, String address)
      throws BusyException, ConfigException {
    boolean demoGridSupport = false;

    String currentAddress = getIpFromModel(model);
    if (currentAddress.equals(address) || demoGridSupport) {
      return model;
    }
    CaesarSwitchDataModel result = getExternalModel(address, model.isDemo());
    if (model.isDemo()) {
      // 复制数据
      ByteArrayOutputStream baos = new ByteArrayOutputStream();
      CfgWriter writer = new CfgWriter(baos);
      model.getConfigData().writeData(writer, IoMode.Available);

      ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
      CfgReader cfgReader = new CfgReader(bais);
      result.getConfigData().readData(cfgReader, IoMode.Available);
      result.getConfigData().getSystemConfigData().getNetworkDataCurrent1()
          .setAddress(IpUtil.getAddressByte(address));
      result.getConfigData().getSystemConfigData().getSystemData().setName("");
      result.getConfigData().getSystemConfigData().getSystemData().setDevice("");
    }
    return result;
  }

  private static CaesarSwitchDataModel getExternalModel(String address, boolean isDemo)
      throws BusyException, ConfigException {
    if ("s0.0.0.0".substring(1).equals(address)) {
      throw new ConfigException(0, "address is 0.0.0.0");
    }
    CaesarSwitchDataModel externalModel =
        CaesarSwitchDataModelManager.getInstance().getModel(address, isDemo);
    if (externalModel.getConfigMetaData().getVersion() == 0) {
      externalModel.getConfigDataManager().getConfigMetaData()
          .setVersion(externalModel.getConfigVersion());
    }
    return externalModel;
  }

  /**
   * 获取主矩阵的model.
   *
   * @param model 本机model
   * @return 主矩阵的model
   * @throws ConfigException 数据异常
   * @throws BusyException   主机正忙
   */
  public static CaesarSwitchDataModel getMasterModel(CaesarSwitchDataModel model)
      throws ConfigException, BusyException {
    if (model.getConfigData().getSystemConfigData().isMatrixGridEnabled()) {
      int index = model.getConfigData().getGridIndex() - 1;
      if (index < 0 || index >= model.getConfigData().getConfigDataManager().getActiveMatrices()
          .size()) {
        return model;
      } else {
        return getExternalModel(model,
            model.getConfigData().getMatrixData(index).getHostAddressString());
      }
    } else {
      return model;
    }
  }

  /**
   * 获取ip地址.
   *
   * @param model model
   * @return ip地址字符串
   */
  public static String getIpFromModel(CaesarSwitchDataModel model) {
    boolean isSlaveCpu = model.getConfigData().getSystemConfigData().isSlaveCpu()
        || model.getConfigData().getSystemConfigData().isSecondaryCpu();
    String currentAddress;
    if (isSlaveCpu) {
      currentAddress = IpUtil.getAddressString(
          model.getConfigData().getSystemConfigData().getNetworkDataCurrent2().getAddress());
    } else {
      currentAddress = IpUtil.getAddressString(
          model.getConfigData().getSystemConfigData().getNetworkDataCurrent1().getAddress());
    }
    return currentAddress;
  }

  /**
   * 获取级联矩阵中最大的端口.
   *
   * @param switchDataModel data model
   * @return 最大的端口
   */
  public static int getMatricesMaxPort(CaesarSwitchDataModel switchDataModel) {
    int result = 0;
    for (MatrixDefinitionData data : getActiveMatrices(switchDataModel)) {
      result = Math.max(result, data.getLastPort());
    }
    return result;
  }

  /**
   * 获取所有有效的矩阵的定义信息，至少有一个当前矩阵的信息.
   *
   * @param switchDataModel switchDataModel
   */
  public static Collection<MatrixDefinitionData> getActiveMatrices(
      CaesarSwitchDataModel switchDataModel) {
    boolean isGridEnabled =
        switchDataModel.getConfigData().getSystemConfigData().isMatrixGridEnabled();
    List<MatrixDefinitionData> activeMatrices = new ArrayList<>();
    if (isGridEnabled) {
      int lastPort = 0;
      for (MatrixData matrixData : switchDataModel.getConfigData().getMatrixDatas()) {
        if (matrixData.isStatusActive()) {
          int matrixFirstPort = matrixData.getFirstPort();
          int matrixLastPort = matrixData.getLastPort();
          if (switchDataModel.isOnlyConfig()) {
            // 只有配置下first port跟last port肯定为0，要做处理
            matrixFirstPort = lastPort + 1;
            matrixLastPort = lastPort + matrixData.getPortCount();
            lastPort = matrixLastPort;
          }
          activeMatrices.add(new MatrixDefinitionData(matrixData.getOid(), matrixData.getDevice(),
              IpUtil.getAddressString(matrixData.getHostAddress()), matrixData.getPortCount(),
              matrixData.getFirstModule(), matrixData.getLastModule(), matrixFirstPort,
              matrixLastPort, isGridEnabled));
        }
      }
    }
    if (activeMatrices.isEmpty()) {
      byte[] host;
      host = switchDataModel.getConfigData().getSystemConfigData().getNetworkDataCurrent1()
          .getAddress();
      int ports = switchDataModel.getSwitchModuleData().getModuleData(0).getPorts();
      if (ports == 0 && switchDataModel.isOnlyConfig()) {
        ports = switchDataModel.getConfigMetaData().getPortCount();
      }
      int portsPerIo = switchDataModel.getPortsPerIo();
      int lastModule;
      int firstModule;
      if (switchDataModel.getConfigMetaData().isSnmpVersion()) {
        firstModule = 1;
        lastModule = switchDataModel.getConfigMetaData().getModuleCount();
      } else {
        firstModule = switchDataModel.isDemo() ? 1
            : switchDataModel.getConfigData().getGridData().getFirstModule();
        lastModule = switchDataModel.isDemo() ? (int) Math.ceil(1.0 * ports / portsPerIo)
            : switchDataModel.getConfigData().getGridData().getLastModule();
      }

      if (lastModule == 0 && switchDataModel.isOnlyConfig()) {
        lastModule = switchDataModel.getConfigMetaData().getModuleCount();
      }
      String device =
          switchDataModel.getConfigData().getSystemConfigData().getSystemData().getDevice();
      activeMatrices.add(new MatrixDefinitionData(0, device, IpUtil.getAddressString(host), ports,
          firstModule, lastModule, 1, ports, isGridEnabled));
    }
    return activeMatrices;
  }

  /**
   * 获取矩阵信息与其对应的model.
   *
   * @param model 当前model
   * @return 矩阵信息与其对应的model.
   */
  public static Collection<Pair<MatrixDefinitionData, CaesarSwitchDataModel>> getMatrixModels(
      CaesarSwitchDataModel model) {
    List<Pair<MatrixDefinitionData, CaesarSwitchDataModel>> result = new ArrayList<>();
    Collection<MatrixDefinitionData> matrices = Utilities.getActiveMatrices(model);
    for (MatrixDefinitionData mdd : matrices) {
      if (mdd == null) {
        continue;
      }
      String address = mdd.getAddress();
      try {
        CaesarSwitchDataModel matModel = Utilities.getExternalModel(model, address);
        result.add(new Pair<>(mdd, matModel));
      } catch (BusyException | ConfigException exception) {
        LOG.log(Level.WARNING, "Fail to get model for " + address);
      }
    }
    return result;
  }

  /**
   * 矩阵是否为主矩阵.
   *
   * @param model model
   * @param data  矩阵数据.
   * @return 如果为主矩阵，返回true
   */
  public static boolean isMatrixMaster(CaesarSwitchDataModel model, MatrixDefinitionData data) {
    if (model.getConfigData().getSystemConfigData().isMatrixGridEnabled()) {
      return data.getId() + 1 == model.getConfigData().getGridIndex();
    } else {
      return false;
    }
  }

  /**
   * 矩阵是否为从矩阵.
   *
   * @param model model
   * @param data  矩阵数据，如果为null，表示判断当前是否为从矩阵.
   * @return 如果为从矩阵，返回true
   */
  public static boolean isMatrixSlave(CaesarSwitchDataModel model, MatrixDefinitionData data) {
    if (model.getConfigData().getSystemConfigData().isMatrixGridEnabled()) {
      if (data != null) {
        return data.getId() + 1 != model.getConfigData().getGridIndex();
      } else {
        for (MatrixDefinitionData defData : Utilities.getActiveMatrices(model)) {
          if (defData.getAddress()
              .equals(IpUtil.getAddressString(
                  model.getConfigData().getSystemConfigData().getNetworkDataCurrent1()
                      .getAddress()))) {
            return defData.getId() + 1 != model.getConfigData().getGridIndex();
          }
        }
        // 找不到，就当成从矩阵
        return true;
      }
    } else {
      return false;
    }
  }

  /**
   * getLevel1.
   *
   * @param model        model
   * @param extenderData extenderData
   */
  public static int getLevel1(CaesarSwitchDataModel model, ExtenderData extenderData) {
    int port = extenderData.getPort();
    if (extenderData.getPort() == 0 && extenderData.getRdPort() != 0) {
      port = extenderData.getRdPort();
    }
    Pair<Integer, Integer> levels = getPortLevel(model, port);
    return levels.getKey();
  }

  /**
   * getLevel2.
   *
   * @param model        model
   * @param extenderData extenderData
   */
  public static int getLevel2(CaesarSwitchDataModel model, ExtenderData extenderData) {
    int port = extenderData.getPort();
    if (extenderData.getPort() == 0 && extenderData.getRdPort() != 0) {
      port = extenderData.getRdPort();
    }
    Pair<Integer, Integer> levels = getPortLevel(model, port);
    return levels.getValue();
  }

  /**
   * 获取已经使用的端口的集合.
   *
   * @param model 设备数据模型
   * @return 已用的端口的集合
   */
  public static Collection<Integer> getUsedPorts(CaesarSwitchDataModel model) {
    Set<Integer> result = new HashSet<>();
    for (PortData portData : model.getConfigData().getPortDatas()) {
      if (portData.isStatusAvailable() && (portData.getExtender() > 0 || portData
          .isStatusMatrix())) {
        result.add(portData.getOid() + 1);
      }
    }
    result.remove(0);
    return result;
  }


  /**
   * 获取外设升级需要信息.
   *
   * @param model         model
   * @param extenderDatas extenderDatas
   */
  public static Collection<ExtenderUpdateInfo> getExtenderUpdateInfos(CaesarSwitchDataModel model,
      ExtenderData... extenderDatas) {
    Collection<MatrixDefinitionData> matrices = getActiveMatrices(model);
    List<ExtenderUpdateInfo> result = new ArrayList<>();
    for (MatrixDefinitionData mat : matrices) {
      List<Integer> indexs = new ArrayList<>();
      List<ExtenderData> updateExtenders = new ArrayList<>();
      int maxIndex = -1;
      // 获取所有在此mat下的extender的端口的oid
      for (ExtenderData extenderData : extenderDatas) {
        PortData portData = extenderData.getPortData();
        if (portData == null) {
          portData = extenderData.getRdPortData();
        }
        if (portData == null) {
          continue;
        }
        int port = portData.getOid() + 1;

        if (port < mat.getFirstPort() || port > mat.getLastPort()) {
          continue;
        }
        int index = port;
        indexs.add(index);
        updateExtenders.add(extenderData);
        if (index > maxIndex) {
          maxIndex = index;
        }
      } // end for
      if (indexs.isEmpty()) {
        continue;
      }
      byte[] bytes = new byte[0];
      ModuleData module = model.getSwitchModuleData().getModuleData(0);
      if (module != null) {
        CaesarConstants.Module.Type type = CaesarConstants.Module.Type.valueOf(module.getType());
        if (CaesarConstants.Module.Type.PLUGIN_144.equals(type)) {
          // 把oid组成byte数组
          int byteUnit = 24;
          int unitCount = 18; //IO板的数量
          int unitByteLen = (int) (Math.ceil(byteUnit / 16.0) * 2); //一个IO板所占的字节数
          int byteLen = unitCount * unitByteLen; //总字节数
          bytes = new byte[byteLen];
          for (Integer port : indexs) {
            port = port - mat.getFirstPort();
            int unitIndex = port / 8 + mat.getFirstModule() - 1;
            int unitOffset = port % 8;
            int byteIndex = unitIndex * unitByteLen + unitOffset / 8;
            int byteOffset = unitOffset % 8;
            bytes[byteIndex] |= 1 << (byteOffset);
          }
        }
      }
      if (bytes.length == 0) {
        // 把oid组成byte数组
        int byteUnit = getPortsPerIo(model, mat);
        int unitCount = mat.getFirstModule() + (maxIndex - mat.getFirstPort()) / byteUnit; //IO板的数量
        int unitByteLen = (int) (Math.ceil(byteUnit / 16.0) * 2); //一个IO板所占的字节数
        int byteLen = unitCount * unitByteLen; //总字节数
        bytes = new byte[byteLen];
        for (Integer port : indexs) {
          port = port - mat.getFirstPort();
          int unitIndex = port / byteUnit + mat.getFirstModule() - 1;
          int unitOffset = port % byteUnit;
          int byteIndex = unitIndex * unitByteLen + unitOffset / 8;
          int byteOffset = unitOffset % 8;
          bytes[byteIndex] |= 1 << (byteOffset);
        }
      }

      try {
        result.add(new ExtenderUpdateInfo(getExternalModel(model, mat.getAddress()),
            mat.getAddress(), updateExtenders, bytes));
      } catch (BusyException | ConfigException ex) {
        LOG.log(Level.WARNING, "Fail to get extender update info!", ex);
      }
    } // end for
    return result;
  }

  /**
   * 复制数据.
   *
   * @param from  要复制的数据
   * @param to    接收复制的数据
   * @param clazz 数据的类型的class
   * @throws ConfigException 复制出错
   */
  public static <T extends CaesarCommunicatable> void copyCommunicable(T from, T to,
      Class<? extends T> clazz) throws ConfigException {
    T newValue = to;
    T value = from;
    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    CfgWriter writer = new CfgWriter(baos);
    value.writeData(writer);

    ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
    CfgReader reader = new CfgReader(bais);
    newValue.readData(reader);
  }

  /**
   * 获取全连接权限的cpu.
   *
   * @param object      object
   * @param dataManager data manager
   * @return 全连接权限的cpu列表
   */
  public static Collection<CpuData> getFullAccessCpus(AccessControlObject object,
      ConfigDataManager dataManager) {
    List<CpuData> result = new ArrayList<>(dataManager.getActiveCpus());
    result.removeAll(object.getVideoAccessCpuDatas());
    result.removeAll(object.getNoAccessCpuDatas());
    return result;
  }

  /**
   * getGridInfo.
   *
   * @param broadcastAddress broadcastAddress
   * @param defaultBroadCast defaultBroadCast
   * @throws SocketException      SocketException
   * @throws UnknownHostException UnknownHostException
   * @throws IOException          IOException
   */
  public static void getGridInfo(String broadcastAddress, String defaultBroadCast)
      throws SocketException, UnknownHostException, IOException {
    Enumeration<NetworkInterface> eni = NetworkInterface.getNetworkInterfaces();
    while (eni.hasMoreElements()) {
      NetworkInterface ni = eni.nextElement();
      List<InterfaceAddress> inetAddresses = ni.getInterfaceAddresses();
      if (!ni.isLoopback()) {
        for (InterfaceAddress interfaceAddress : inetAddresses) {
          String hostAddr = interfaceAddress.getAddress().getHostAddress();
          if (!IpUtil.isValidIp(hostAddr)) {
            continue;
          }
          IoController.BroadcastController bc = IoController.createBroadcast(interfaceAddress,
              CaesarConstants.GRID_PORT, InetAddress.getByName(broadcastAddress));
          bc.open();

          byte[] src = IpUtil.getAddressByte(hostAddr);
          byte[] dest = IpUtil.getAddressByte(broadcastAddress);
          if ((dest[0] & 0xFF) < 224 || (dest[0] & 0xFF) >= 240) {
            dest = IpUtil.getAddressByte(defaultBroadCast);
          }
          CommunicationBuilder.newRequestBuilder()
              .setRequest(CaesarControllerConstants.BroadcastRequest.GET_GRIDINFO).add(src)
              .add(dest).transmit(bc);
          bc.close();
        }
      }
    }
  }

  /**
   * 根据level获取外设.
   *
   * @param model  data model
   * @param level1 level1
   * @param level2 level2
   * @return 外设数据
   */
  public static ExtenderData getExtenderDataByLevel(CaesarSwitchDataModel model, int level1,
      int level2) {
    int port = getLevelPort(model, level1, level2);
    PortData portData = model.getConfigDataManager().getPortData(port - 1);
    if (portData == null) {
      return null;
    } else {
      return portData.getExtenderData();
    }
  }

  /**
   * 外设是否有连矩阵的端口.
   *
   * @param model        model
   * @param matrixData   矩阵数据
   * @param extenderData 外设数据
   * @return 如果有，返回true
   */
  public static boolean isExtenderInMatrix(CaesarSwitchDataModel model,
      MatrixDefinitionData matrixData, ExtenderData extenderData) {
    int port = extenderData.getPort();
    int rdPort = extenderData.getRdPort();
    return port != 0 && port >= matrixData.getFirstPort() && port <= matrixData.getLastPort()
        || rdPort != 0 && rdPort >= matrixData.getFirstPort() && rdPort <= matrixData.getLastPort();
  }

  /**
   * 获取外设数据在TX或者RX的索引.
   *
   * @param data EXT
   * @return 索引值
   */
  public static int getExtenderIdx(ExtenderData data) {
    return data.isUsbType() ? 1 : 0;
  }

  /**
   * 提取外设参数.
   *
   * @param data 外设参数的集合
   * @param id   参数id
   * @param type 参数值类型
   * @param <T>  参数值类型
   * @return 如果提取成功，返回对应的值，否则返回null。
   */
  public static <T extends ExtArgObject> T extractExtArg(Map<Integer, byte[]> data, int id,
      Class<T> type) {
    if (!data.containsKey(id)) {
      return null;
    }
    byte[] bytes = data.get(id);
    if (type.isEnum()) {
      for (T item : type.getEnumConstants()) {
        if (Arrays.equals(item.toBytes(), bytes)) {
          return item;
        }
      }
    }
    return null;
  }

  /**
   * 提取外设参数.
   *
   * @param data 外设参数的集合
   * @param id   参数id
   * @return 如果提取成功，返回对应的值，否则返回null。
   */
  public static Number extractExtArg(Map<Integer, byte[]> data, int id) {
    if (!data.containsKey(id)) {
      return null;
    }
    byte[] bytes = data.get(id);
    System.out.println(Arrays.toString(bytes));
    int res = 0;
    for (int index = 0; index < bytes.length; index++) {
      res += (((int) (bytes[index])) & 0xFF) << (index * 8);
    }
    return res;
  }
}

