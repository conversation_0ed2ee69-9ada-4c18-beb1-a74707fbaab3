package com.mc.tool.caesar.vpm.pages.splicingscreen;

import java.io.IOException;
import java.net.URL;
import javafx.fxml.FXMLLoader;
import javafx.scene.layout.AnchorPane;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Getter
@Slf4j
public final class CaesarSplicingScreenPageView extends AnchorPane {
  private final CaesarSplicingScreenController controllable;

  /** Constructor. */
  public CaesarSplicingScreenPageView() {
    URL location = getClass().getResource(
        "/com/mc/tool/caesar/vpm/pages/splicingscreen/splicingscreen_view.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    loader.setRoot(this);
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load splicingscreen_view.fxml", exception);
    }
    this.controllable = loader.getController();
  }

  /**
   * refresh.
   */
  public void refresh() {
    controllable.refresh();
  }
}
