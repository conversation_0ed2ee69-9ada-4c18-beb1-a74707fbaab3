ExtenderUpdate.updateDate.text=\u5347\u7EA7
HostUpdate.alert.warning.packageError.text=\u5347\u7EA7\u5305\u4E0D\u5339\u914D
HostUpdate.alert.warning.title=\u5347\u7EA7\u5305\u9519\u8BEF
HostUpdate.alert.warning.versionError.text=\u7248\u672C\u4FE1\u606F\u4E22\u5931
HostUpdate.alert.warning.matrixError.text=\u5347\u7EA7\u5305\u4E0E\u786C\u4EF6\u7248\u672C\u4E0D\u5339\u914D
HostUpdate.currentDateCol.text=\u5F53\u524D\u7248\u672C\u65E5\u671F
HostUpdate.currentVersionCol.text=\u5F53\u524D\u7248\u672C
HostUpdate.hardwareVerCol.text=\u786C\u4EF6\u7248\u672C
HostUpdate.idCol.text=ID
HostUpdate.nameCol.text=\u540D\u79F0
HostUpdate.pageTitle.text=\u4E3B\u673A\u5347\u7EA7
HostUpdate.portCol.text=\u7AEF\u53E3
HostUpdate.progressCol.text=\u5347\u7EA7\u8FDB\u5EA6
HostUpdate.seleteAllButton.text=\u5168\u9009
HostUpdate.seleteAllButton.text.opposite=\u53D6\u6D88\u5168\u9009
HostUpdate.serialCol.text=\u5E8F\u5217\u53F7
HostUpdate.typeCol.text=\u7C7B\u578B
HostUpdate.updateDate.text=\u5347\u7EA7
HostUpdate.updateDateCol.text=\u5347\u7EA7\u7248\u672C\u65E5\u671F
HostUpdate.updateVersionCol.text=\u5347\u7EA7\u7248\u672C
cancel_btn=\u53D6\u6D88
load_file=\u8F7D\u5165\u6587\u4EF6
update_btn=\u5347\u7EA7
update_log=\u66F4\u65B0\u65E5\u5FD7
