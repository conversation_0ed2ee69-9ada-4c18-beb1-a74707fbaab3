package com.mc.tool.caesar.vpm.pages.hotkeyconfiguration;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants.FunctionKey.Cmd.Command;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.macrokey.view.MacroKeyView;
import com.mc.tool.framework.interfaces.DeviceControllable;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.Predicate;
import javafx.application.Platform;

/**
 * .
 */
public class ConsoleMacroKeyView extends MacroKeyView<ConsoleData> {

  /** Constructor. */
  public ConsoleMacroKeyView() {
    sourceHeader.set(CaesarI18nCommonResource.getString("copy_macro_dialog.con_source_header"));
    targetHeader.set(CaesarI18nCommonResource.getString("copy_macro_dialog.con_target_header"));
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    super.setDeviceController(deviceController);
    if (this.deviceController != null) {
      sourceRawList.setAll(
          this.deviceController.getDataModel().getConfigDataManager().getActiveConsoles());
      sourceRawList.removeIf(ConsoleData::isStatusVpcon);
      updateDeviceDataList();

      this.deviceController
          .getDataModel()
          .addPropertyChangeListener(
              new String[] {
                FunctionKeyData.PROPERTY_HOST_SUBSCRIPT,
                FunctionKeyData.PROPERTY_HOTKEY,
                FunctionKeyData.PROPERTY_KEYLINE,
                FunctionKeyData.PROPERTY_MACROCMD,
                FunctionKeyData.PROPERTY_MACROTYPE,
                FunctionKeyData.PROPERTY_STATUS,
                FunctionKeyData.PROPERTY_MACRO_INTPARAM,
                FunctionKeyData.PROPERTY_MACRO_STRPARAM,
                ConsoleData.PROPERTY_STATUS,
                FunctionKeyData.PROPERTY_MACRO_NAME
              },
              weakAdapter.wrap(
                  (PropertyChangeListener)
                      evt -> PlatformUtility.runInFxThread(this::updateDeviceDataList)));

      this.deviceController
          .getDataModel()
          .addPropertyChangeListener(
              ConsoleData.PROPERTY_STATUS,
              weakAdapter.wrap(
                  (PropertyChangeListener)
                      evt ->
                          Platform.runLater(
                              () ->
                                  sourceRawList.setAll(
                                      this.deviceController.getDataModel()
                                          .getConfigDataManager().getActiveConsoles()))));
    }
  }

  @Override
  protected Collection<FunctionKeyData> getFunctionKeyDatasForSource(ConsoleData consoleData) {
    List<FunctionKeyData> newList = new ArrayList<>();
    for (FunctionKeyData functionKeyData :
        deviceController.getDataModel().getConfigData().getFunctionKeyDatas()) {
      try {
        Command.valueOf(functionKeyData.getMacroCmd());
      } catch (Exception exception) {
        continue;
      }

      if (!functionKeyData.isConsole()) {
        continue;
      }

      if (functionKeyData.getHostSubscript() == consoleData.getOid() + 1) {
        newList.add(functionKeyData);
      }
    }
    return newList;
  }

  @Override
  protected String getSourceListTitle() {
    return CaesarI18nCommonResource.getString("macro_key.con_source_list_title");
  }

  @Override
  protected String getAccessBlockListTitle() {
    return CaesarI18nCommonResource.getString("macro_key.con_access_block_title");
  }

  @Override
  protected Predicate<ConsoleData> getSourcePredicate() {
    return (item) -> !item.isStatusVpcon();
  }
}
