<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.CheckBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<VBox alignment="CENTER_RIGHT" maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity"
  minWidth="-Infinity" prefHeight="400.0" prefWidth="600.0" xmlns="http://javafx.com/javafx"
  xmlns:fx="http://javafx.com/fxml/1">
  <children>
    <HBox VBox.vgrow="ALWAYS">
      <children>
        <Label text="%save_config.page3.title"/>
      </children>
    </HBox>
    <Separator prefHeight="0.0" prefWidth="600.0"/>
    <TableView fx:id="tableView" VBox.vgrow="ALWAYS">
      <columns>
        <TableColumn fx:id="indexCol" prefWidth="40.0"/>
        <TableColumn fx:id="fileCol" prefWidth="122.0" text="%save_config.page3.fileCol"/>
        <TableColumn fx:id="nameCol" prefWidth="84.0" text="%save_config.page3.nameCol"/>
        <TableColumn fx:id="infoCol" prefWidth="89.0" text="%save_config.page3.infoCol"/>
        <TableColumn fx:id="addressCol" prefWidth="91.0" text="%save_config.page3.addressCol"/>
        <TableColumn fx:id="versionCol" prefWidth="90.0" text="%save_config.page3.versionCol"/>
      </columns>
    </TableView>
    <HBox VBox.vgrow="ALWAYS">
      <children>
        <CheckBox fx:id="activateCkb" mnemonicParsing="false"
          text="%save_config.page3.activate_checkbox"/>
        <Region HBox.hgrow="ALWAYS"/>
        <Button fx:id="btn" mnemonicParsing="false"/>
      </children>
    </HBox>
  </children>
</VBox>
